.annotationLayer{--annotation-unfocused-field-background:url("data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>");--input-focus-border-color:Highlight;--input-focus-outline:1px solid Canvas;--input-unfocused-border-color:transparent;--input-disabled-border-color:transparent;--input-hover-border-color:black;--link-outline:none}@media screen and (forced-colors:active){.annotationLayer{--input-focus-border-color:CanvasText;--input-unfocused-border-color:ActiveText;--input-disabled-border-color:GrayText;--input-hover-border-color:Highlight;--link-outline:1.5px solid LinkText}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input:required,.annotationLayer .choiceWidgetAnnotation select:required,.annotationLayer .textWidgetAnnotation :is(input,textarea):required{outline:1.5px solid selectedItem}.annotationLayer .linkAnnotation{outline:var(--link-outline)}:is(.annotationLayer .linkAnnotation):hover{-webkit-backdrop-filter:var(--hcm-highlight-filter);backdrop-filter:var(--hcm-highlight-filter)}:is(.annotationLayer .linkAnnotation)>a:hover{opacity:0!important;background:0 0!important;box-shadow:none}.annotationLayer .popupAnnotation .popup{outline:calc(1.5px * var(--scale-factor)) solid CanvasText!important;background-color:ButtonFace!important;color:ButtonText!important}.annotationLayer .highlightArea:hover:after{position:absolute;top:0;left:0;width:100%;height:100%;-webkit-backdrop-filter:var(--hcm-highlight-filter);backdrop-filter:var(--hcm-highlight-filter);content:"";pointer-events:none}.annotationLayer .popupAnnotation.focused .popup{outline:calc(3px * var(--scale-factor)) solid Highlight!important}}.annotationLayer{position:absolute;top:0;left:0;pointer-events:none;transform-origin:0 0}.annotationLayer[data-main-rotation="90"] .norotate{transform:rotate(270deg) translate(-100%)}.annotationLayer[data-main-rotation="180"] .norotate{transform:rotate(180deg) translate(-100%,-100%)}.annotationLayer[data-main-rotation="270"] .norotate{transform:rotate(90deg) translateY(-100%)}.annotationLayer.disabled .popup,.annotationLayer.disabled section{pointer-events:none}.annotationLayer .annotationContent{position:absolute;width:100%;height:100%;pointer-events:none}.freetext:is(.annotationLayer .annotationContent){background:0 0;border:none;top:0;right:0;bottom:0;left:0;overflow:visible;white-space:nowrap;font:10px sans-serif;line-height:1.35;-webkit-user-select:none;-moz-user-select:none;user-select:none}.annotationLayer section{position:absolute;text-align:initial;pointer-events:auto;box-sizing:border-box;transform-origin:0 0}:is(.annotationLayer section):has(div.annotationContent) canvas.annotationContent{display:none}.textLayer.selecting~.annotationLayer section{pointer-events:none}.annotationLayer :is(.linkAnnotation,.buttonWidgetAnnotation.pushButton)>a{position:absolute;font-size:1em;top:0;left:0;width:100%;height:100%}.annotationLayer :is(.linkAnnotation,.buttonWidgetAnnotation.pushButton):not(.hasBorder)>a:hover{opacity:.2;background-color:#ff0;box-shadow:0 2px 10px #ff0}.annotationLayer .linkAnnotation.hasBorder:hover{background-color:#ff03}.annotationLayer .hasBorder{background-size:100% 100%}.annotationLayer .textAnnotation img{position:absolute;cursor:pointer;width:100%;height:100%;top:0;left:0}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input,.annotationLayer .choiceWidgetAnnotation select,.annotationLayer .textWidgetAnnotation :is(input,textarea){background-image:var(--annotation-unfocused-field-background);border:2px solid var(--input-unfocused-border-color);box-sizing:border-box;font:calc(9px * var(--scale-factor)) sans-serif;height:100%;margin:0;vertical-align:top;width:100%}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input:required,.annotationLayer .choiceWidgetAnnotation select:required,.annotationLayer .textWidgetAnnotation :is(input,textarea):required{outline:1.5px solid red}.annotationLayer .choiceWidgetAnnotation select option{padding:0}.annotationLayer .buttonWidgetAnnotation.radioButton input{border-radius:50%}.annotationLayer .textWidgetAnnotation textarea{resize:none}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input[disabled],.annotationLayer .choiceWidgetAnnotation select[disabled],.annotationLayer .textWidgetAnnotation [disabled]:is(input,textarea){background:0 0;border:2px solid var(--input-disabled-border-color);cursor:not-allowed}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input:hover,.annotationLayer .choiceWidgetAnnotation select:hover,.annotationLayer .textWidgetAnnotation :is(input,textarea):hover{border:2px solid var(--input-hover-border-color)}.annotationLayer .buttonWidgetAnnotation.checkBox input:hover,.annotationLayer .choiceWidgetAnnotation select:hover,.annotationLayer .textWidgetAnnotation :is(input,textarea):hover{border-radius:2px}.annotationLayer .choiceWidgetAnnotation select:focus,.annotationLayer .textWidgetAnnotation :is(input,textarea):focus{background:0 0;border:2px solid var(--input-focus-border-color);border-radius:2px;outline:var(--input-focus-outline)}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) :focus{background-image:none;background-color:transparent}.annotationLayer .buttonWidgetAnnotation.checkBox :focus{border:2px solid var(--input-focus-border-color);border-radius:2px;outline:var(--input-focus-outline)}.annotationLayer .buttonWidgetAnnotation.radioButton :focus{border:2px solid var(--input-focus-border-color);outline:var(--input-focus-outline)}.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after,.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before,.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before{background-color:CanvasText;content:"";display:block;position:absolute}.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after,.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before{height:80%;left:45%;width:1px}.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before{transform:rotate(45deg)}.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after{transform:rotate(-45deg)}.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before{border-radius:50%;height:50%;left:25%;top:25%;width:50%}.annotationLayer .textWidgetAnnotation input.comb{font-family:monospace;padding-left:2px;padding-right:0}.annotationLayer .textWidgetAnnotation input.comb:focus{width:103%}.annotationLayer .buttonWidgetAnnotation:is(.checkBox,.radioButton) input{-webkit-appearance:none;-moz-appearance:none;appearance:none}.annotationLayer .fileAttachmentAnnotation .popupTriggerArea{height:100%;width:100%}.annotationLayer .popupAnnotation{position:absolute;font-size:calc(9px * var(--scale-factor));pointer-events:none;width:-moz-max-content;width:max-content;max-width:45%;height:auto}.annotationLayer .popup{background-color:#ff9;box-shadow:0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor)) #888;border-radius:calc(2px * var(--scale-factor));outline:1.5px solid rgb(255 255 74);padding:calc(6px * var(--scale-factor));cursor:pointer;font:message-box;white-space:normal;word-wrap:break-word;pointer-events:auto}.annotationLayer .popupAnnotation.focused .popup{outline-width:3px}.annotationLayer .popup *{font-size:calc(9px * var(--scale-factor))}.annotationLayer .popup>.header{display:inline-block}.annotationLayer .popup>.header h1{display:inline}.annotationLayer .popup>.header .popupDate{display:inline-block;margin-left:calc(5px * var(--scale-factor));width:-moz-fit-content;width:fit-content}.annotationLayer .popupContent{border-top:1px solid rgb(51 51 51);margin-top:calc(2px * var(--scale-factor));padding-top:calc(2px * var(--scale-factor))}.annotationLayer .richText>*{white-space:pre-wrap;font-size:calc(9px * var(--scale-factor))}.annotationLayer .popupTriggerArea{cursor:pointer}.annotationLayer section svg{position:absolute;width:100%;height:100%;top:0;left:0}.annotationLayer .annotationTextContent{position:absolute;width:100%;height:100%;opacity:0;color:transparent;-webkit-user-select:none;-moz-user-select:none;user-select:none;pointer-events:none}:is(.annotationLayer .annotationTextContent) span{width:100%;display:inline-block}.annotationLayer svg.quadrilateralsContainer{contain:strict;width:0;height:0;position:absolute;top:0;left:0;z-index:-1}[data-main-rotation="90"]{transform:rotate(90deg) translateY(-100%)}[data-main-rotation="180"]{transform:rotate(180deg) translate(-100%,-100%)}[data-main-rotation="270"]{transform:rotate(270deg) translate(-100%)}#hiddenCopyElement,.hiddenCanvasElement{position:absolute;top:0;left:0;width:0;height:0;display:none}.textLayer{position:absolute;text-align:initial;top:0;right:0;bottom:0;left:0;overflow:clip;opacity:1;line-height:1;-webkit-text-size-adjust:none;-moz-text-size-adjust:none;text-size-adjust:none;forced-color-adjust:none;transform-origin:0 0;caret-color:CanvasText;z-index:0}.textLayer.highlighting{touch-action:none}.textLayer :is(span,br){color:transparent;position:absolute;white-space:pre;cursor:text;transform-origin:0% 0%}.textLayer .markedContent span:not(.markedContent),.textLayer>:not(.markedContent){z-index:1}.textLayer span.markedContent{top:0;height:0}.textLayer span[role=img]{-webkit-user-select:none;-moz-user-select:none;user-select:none;cursor:default}.textLayer .highlight{--highlight-bg-color:rgb(180 0 170 / .25);--highlight-selected-bg-color:rgb(0 100 0 / .25);--highlight-backdrop-filter:none;--highlight-selected-backdrop-filter:none}@media screen and (forced-colors:active){.textLayer .highlight{--highlight-bg-color:transparent;--highlight-selected-bg-color:transparent;--highlight-backdrop-filter:var(--hcm-highlight-filter);--highlight-selected-backdrop-filter:var(--hcm-highlight-selected-filter)}}.textLayer .highlight{margin:-1px;padding:1px;background-color:var(--highlight-bg-color);-webkit-backdrop-filter:var(--highlight-backdrop-filter);backdrop-filter:var(--highlight-backdrop-filter);border-radius:4px}.appended:is(.textLayer .highlight){position:initial}.begin:is(.textLayer .highlight){border-radius:4px 0 0 4px}.end:is(.textLayer .highlight){border-radius:0 4px 4px 0}.middle:is(.textLayer .highlight){border-radius:0}.selected:is(.textLayer .highlight){background-color:var(--highlight-selected-bg-color);-webkit-backdrop-filter:var(--highlight-selected-backdrop-filter);backdrop-filter:var(--highlight-selected-backdrop-filter)}.textLayer ::-moz-selection{background:#0000ff40;background:color-mix(in srgb,AccentColor,transparent 75%)}.textLayer ::selection{background:#0000ff40;background:color-mix(in srgb,AccentColor,transparent 75%)}.textLayer br::-moz-selection{background:0 0}.textLayer br::selection{background:0 0}.textLayer .endOfContent{display:block;position:absolute;top:100%;right:0;bottom:0;left:0;z-index:0;cursor:default;-webkit-user-select:none;-moz-user-select:none;user-select:none}.textLayer.selecting .endOfContent{top:0}.pdf-preview[data-v-d739a947]{position:relative;height:100%;width:100%;box-sizing:border-box;background-color:#e9e9e9;overflow:hidden}.pdf-content[data-v-d739a947]{height:100%;overflow-y:auto;overflow-x:hidden;box-sizing:border-box}.vue-pdf-embed[data-v-d739a947]{text-align:center;width:515px;border:1px solid #e5e5e5;margin:0 auto;box-sizing:border-box}.page_tool[data-v-d739a947]{position:absolute;bottom:35px;left:50%;transform:translate(-50%);padding:8px 15px;display:flex;align-items:center;background:#424242;color:#fff;border-radius:19px;z-index:100;cursor:pointer;box-shadow:0 2px 10px #0000004d}.page_tool-item[data-v-d739a947]{padding:8px 15px 8px 10px;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.page_tool-item[data-v-d739a947]:hover{background-color:#ffffff1a;border-radius:4px}
