import{d,e as f,o as u,c as g,l as t,n as p,u as a,t as s,U as r,k as e,w as m,V as h,W as v,p as w,X as _,_ as b}from"./index-C8b06LRn.js";import{_ as n}from"./logo-BB9UjfGS.js";import{_ as y,a as j}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-YKnCp4Oa.js";import{T as k}from"./ThemeSwitch-CpOMjpIf.js";import I from"./LoginForm-C1s1SHnQ.js";import L from"./MobileForm-6N3Zwhmx.js";import"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";import T from"./RegisterForm-BLC5hKXE.js";import{_ as X}from"./QrCodeForm.vue_vue_type_script_setup_true_lang-DMU3LI4T.js";import{_ as C}from"./SSOLogin.vue_vue_type_script_setup_true_lang-C7-0EI0B.js";import"./useIcon-CwemBubV.js";import"./Verify-BrbFSGD0.js";import"./XButton-BOgar_Ex.js";import"./formRules-Upspu04w.js";import"./el-card-CaOo8U9P.js";const P={class:"relative mx-auto h-full flex"},R={class:"relative flex items-center text-white"},U={class:"text-20px font-bold"},V={class:"h-[calc(100%-60px)] flex items-center justify-center"},W={key:"2",class:"text-3xl text-white"},B={key:"3",class:"mt-5 text-14px font-normal text-white"},D={class:"relative flex-1 p-30px dark:bg-[var(--login-bg-color)] lt-sm:p-10px overflow-x-hidden overflow-y-auto"},F={class:"flex items-center justify-between at-2xl:justify-end at-xl:justify-end",style:{color:"var(--el-text-color-primary)"}},Q={class:"flex items-center at-2xl:hidden at-xl:hidden"},S={class:"text-20px font-bold"},$={class:"flex items-center justify-end space-x-10px h-48px"},q={class:"m-auto h-[calc(100%-60px)] w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"},z=b(d({name:"Login",__name:"Login",setup(A){const{t:i}=w(),x=f(),{getPrefixCls:c}=_(),o=c("login");return(E,l)=>(u(),g("div",{class:p([a(o),"relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"])},[t("div",P,[t("div",{class:p(`${a(o)}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden overflow-x-hidden overflow-y-auto`)},[t("div",R,[l[0]||(l[0]=t("img",{alt:"",class:"mr-10px h-48px w-48px",src:n},null,-1)),t("span",U,s(a(r)(a(x).getTitle)),1)]),t("div",V,[e(h,{appear:"","enter-active-class":"animate__animated animate__bounceInLeft",tag:"div"},{default:m(()=>[l[1]||(l[1]=t("img",{key:"1",alt:"",class:"w-350px",src:y},null,-1)),t("div",W,s(a(i)("login.welcome")),1),t("div",B,s(a(i)("login.message")),1)]),_:1})])],2),t("div",D,[t("div",F,[t("div",Q,[l[2]||(l[2]=t("img",{alt:"",class:"mr-10px h-48px w-48px",src:n},null,-1)),t("span",S,s(a(r)(a(x).getTitle)),1)]),t("div",$,[e(a(k)),e(a(j))])]),e(v,{appear:"","enter-active-class":"animate__animated animate__bounceInRight"},{default:m(()=>[t("div",q,[e(a(I),{class:"m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)"}),e(a(L),{class:"m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)"}),e(a(X),{class:"m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)"}),e(a(T),{class:"m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)"}),e(a(C),{class:"m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)"})])]),_:1})])])],2))}}),[["__scopeId","data-v-53329e7b"]]);export{z as default};
