import{aG as v,d as q,j as p,y as D,f as J,o as i,c as g,k as o,w as t,x as $,h as w,u as r,l as K,m as I,v as u,F as z,g as E,t as b,Z as Q,ax as W,S as X,H as Y,a8 as aa,ab as ea,L as la,_ as ta}from"./index-C8b06LRn.js";import{E as oa}from"./el-drawer-C5TFtzfV.js";import{E as sa,a as ra}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as ia}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ua}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as na}from"./index-COdQIXZX.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";const da={class:"flex"},pa={key:1},ca=q({__name:"index",setup(fa){const h=p(!1),c=p(),N=p([]),O=p(0),V=p([]),n=p(!1),C=D(),m=p({pageNo:1,pageSize:30}),f=p(""),P=(l,a)=>{for(let s in l)l[s]?m.value[s]=l[s]:m.value.hasOwnProperty(s)&&delete m.value[s];a||(m.value.pageNo=1),y()},y=async()=>{n.value=!0;try{let l=W(m.value);for(let s in l)Array.isArray(l[s])&&(l[s]=`${l[s].join(",")}`);const a=await(async s=>await v.get({url:"/butt-joint/ekuaibao/order/page",params:s}))(l);N.value=a.list,O.value=a.total}finally{n.value=!1}},A=async()=>{const l=await(async()=>await v.get({url:"/butt-joint/ekuaibao/order/get-columns"}))();V.value=l},H=async l=>{await C.confirm(`\u786E\u5B9A\u8981\u66F4\u65B0\u5355\u636E${l.code}\u5417\uFF1F`),n.value=!0;try{await(async a=>await v.get({url:"/butt-joint/ekuaibao/order/update-order/"+a}))(l.id),y()}finally{n.value=!1}},L=async l=>{await C.confirm(`\u786E\u5B9A\u8981\u624B\u52A8\u540C\u6B65\u5355\u636E${l.code}\u5230ERP\u5417\uFF1F`),n.value=!0;try{await(async a=>await v.get({url:"/butt-joint/ekuaibao/order/sync-order/"+a}))(l.id),y()}finally{n.value=!1}},R=async()=>{f.value&&(await(async l=>await v.get({url:"/butt-joint/ekuaibao/order/download-order/"+l}))(f.value),C.success("\u6210\u529F"),f.value="",y())},U=async l=>{await C.confirm(`\u786E\u5B9A\u8981\u4E0D\u540C\u6B65\u5355\u636E${l.code}\u5230ERP\u5417\uFF1F`),n.value=!0;try{await(async a=>await v.get({url:"/butt-joint/ekuaibao/order/un-sync-order/"+a}))(l.id),y()}finally{n.value=!1}};return J(()=>{A(),y()}),(l,a)=>{const s=X,_=Y,B=na,F=ua,G=ia,x=sa,j=aa,S=ea,M=ra,T=oa,Z=la;return i(),g(z,null,[o(F,null,{default:t(()=>[$((i(),w(B,{data:r(N),total:r(O),columns:r(V),page:r(m),onSearch:P,onPagination:a[1]||(a[1]=e=>P(e,!0)),stripe:"","highlight-current":"",height:"calc(100vh - 350px)","operation-width":180},{"x-table-header":t(()=>[K("div",da,[o(s,{placeholder:"\u8BF7\u8F93\u5165\u5355\u636E\u7F16\u53F7",size:"small",modelValue:r(f),"onUpdate:modelValue":a[0]||(a[0]=e=>I(f)?f.value=e:null)},null,8,["modelValue"]),o(_,{type:"primary",size:"small",onClick:R},{default:t(()=>a[3]||(a[3]=[u("\u624B\u52A8\u4E0B\u8F7D")])),_:1})])]),operation:t(({row:e})=>[o(_,{type:"warning",size:"small",link:"",onClick:d=>(k=>{h.value=!0,c.value=k})(e)},{default:t(()=>a[4]||(a[4]=[u("\u8BE6\u60C5")])),_:2},1032,["onClick"]),o(_,{type:"primary",size:"small",link:"",onClick:d=>H(e)},{default:t(()=>a[5]||(a[5]=[u("\u66F4\u65B0")])),_:2},1032,["onClick"]),o(_,{type:"success",size:"small",link:"",onClick:d=>L(e)},{default:t(()=>a[6]||(a[6]=[u("\u540C\u6B65")])),_:2},1032,["onClick"]),o(_,{type:"danger",size:"small",link:"",onClick:d=>U(e)},{default:t(()=>a[7]||(a[7]=[u("\u4E0D\u540C\u6B65")])),_:2},1032,["onClick"])]),_:1},8,["data","total","columns","page"])),[[Z,r(n)]])]),_:1}),o(T,{title:"\u5355\u636E\u8BE6\u60C5",modelValue:r(h),"onUpdate:modelValue":a[2]||(a[2]=e=>I(h)?h.value=e:null),size:"40%"},{default:t(()=>[o(M,{column:3,border:""},{default:t(()=>[(i(!0),g(z,null,E(r(V),e=>$((i(),w(x,{label:e.label,key:e.prop},{default:t(()=>[e.dict?(i(),w(G,{key:0,type:e.dict,value:r(c)[e.prop]},null,8,["type","value"])):(i(),g("span",pa,b(r(c)[e.prop]),1))]),_:2},1032,["label"])),[[Q,e.prop!=="errorMsg"]])),128)),o(x,{label:"\u8D39\u7528\u90E8\u95E8",span:3},{default:t(()=>[u(b(r(c).deptName),1)]),_:1}),o(x,{label:"\u8D39\u7528\u660E\u7EC6",span:3},{default:t(()=>{var e;return[(i(!0),g(z,null,E((e=r(c))==null?void 0:e.feeItems,(d,k)=>(i(),w(S,{gutter:10,key:k,style:{"border-bottom":"1px solid #ebeef5"}},{default:t(()=>[o(j,{span:16},{default:t(()=>[u(b(d.feeTypeName),1)]),_:2},1024),o(j,{span:8},{default:t(()=>[u(b(d.amount),1)]),_:2},1024)]),_:2},1024))),128))]}),_:1}),o(x,{label:"\u6838\u9500\u660E\u7EC6",span:3},{default:t(()=>{var e;return[(i(!0),g(z,null,E((e=r(c))==null?void 0:e.writeOffItems,(d,k)=>(i(),w(S,{gutter:10,key:k,style:{"border-bottom":"1px solid #ebeef5"}},{default:t(()=>[o(j,{span:16},{default:t(()=>[u(b(d.writeOffCode),1)]),_:2},1024),o(j,{span:8},{default:t(()=>[u(b(d.amount),1)]),_:2},1024)]),_:2},1024))),128))]}),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}}}),ma=ta(ca,[["__scopeId","data-v-f8d98000"]]);export{ma as default};
