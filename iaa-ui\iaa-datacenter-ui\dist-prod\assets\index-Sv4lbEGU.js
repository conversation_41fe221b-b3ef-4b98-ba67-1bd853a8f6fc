import{d as ne,j as P,o as l,c as u,k as i,w as n,l as a,u as e,m as de,$ as w,as as he,G as fe,at as Pa,au as qe,av as _e,aw as Ha,ax as fa,h as y,x as ye,ay as ge,v as $,t as q,g as z,F as L,E as ea,ap as Ge,aq as ze,q as aa,az as Ke,H as He,T as Te,n as Ye,Y as Ce,r as la,aA as ya,B as ta,z as oa,A as da,S as na,aB as La,aC as Ea,aD as Fa,I as ia,_ as ha,y as _a,aE as ga,aF as Ma,aG as Na,f as ja,aH as Ba,aI as qa,aJ as Ga,aK as za,a as Ka,aL as Ve,L as Ya,O as $a}from"./index-C8b06LRn.js";import{_ as Wa}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{N as U,a as $e,A as Ne,R as Le,b as sa,c as ba,C as b,F as Z,S as Xa,d as xe,T as Ee,e as Ta,f as Fe,g as Sa,M as Qa,h as Ja,i as Za,j as el,k as al,l as ll,m as tl,O as ka,D as ol,n as Ra,o as wa,p as dl,q as Ae,r as Ua}from"./consts-DWqigJ3H.js";import{E as We}from"./el-drawer-C5TFtzfV.js";import{E as Va}from"./el-tree-select-E9FCZb0j.js";import{E as nl}from"./el-space-CqmKDeRz.js";import{E as il}from"./el-card-CaOo8U9P.js";import{g as sl}from"./index-C4DFiyBm.js";import{g as ul}from"./index-CBsqkafF.js";import{g as rl}from"./index-C0LhU1J1.js";import{g as cl}from"./index-Dz9lR_me.js";import{g as pl}from"./index-Cgv48ZKs.js";const vl={class:"node-handler-wrapper"},ml={key:0,class:"node-handler"},fl={class:"handler-item-wrapper"},yl={class:"add-icon"},Ie=ne({name:"NodeHandler",__name:"NodeHandler",props:{childNode:{type:Object,default:null},showAdd:{type:Boolean,default:!0}},emits:["update:childNode"],setup(A,{emit:O}){const _=P(!1),p=A,S=O,g=m=>{if(_.value=!1,m===U.USER_TASK_NODE){const t={id:"Activity_"+he(),name:$e.get(U.USER_TASK_NODE),showText:"",type:U.USER_TASK_NODE,approveMethod:Ne.SEQUENTIAL_APPROVE,rejectHandler:{type:Le.FINISH_PROCESS},timeoutHandler:{enable:!1},assignEmptyHandler:{type:sa.APPROVE},assignStartUserHandlerType:ba.START_USER_AUDIT,childNode:p.childNode};S("update:childNode",t)}if(m===U.COPY_TASK_NODE){const t={id:"Activity_"+he(),name:$e.get(U.COPY_TASK_NODE),showText:"",type:U.COPY_TASK_NODE,childNode:p.childNode};S("update:childNode",t)}if(m===U.CONDITION_BRANCH_NODE){const t={name:"\u6761\u4EF6\u5206\u652F",type:U.CONDITION_BRANCH_NODE,id:"GateWay_"+he(),childNode:p.childNode,conditionNodes:[{id:"Flow_"+he(),name:"\u6761\u4EF61",showText:"",type:U.CONDITION_NODE,childNode:void 0,conditionType:1,defaultFlow:!1},{id:"Flow_"+he(),name:"\u5176\u5B83\u60C5\u51B5",showText:"\u5176\u5B83\u60C5\u51B5\u8FDB\u5165\u6B64\u6D41\u7A0B",type:U.CONDITION_NODE,childNode:void 0,conditionType:void 0,defaultFlow:!0}]};S("update:childNode",t)}if(m===U.PARALLEL_BRANCH_NODE){const t={name:"\u5E76\u884C\u5206\u652F",type:U.PARALLEL_BRANCH_NODE,id:"GateWay_"+he(),childNode:p.childNode,conditionNodes:[{id:"Flow_"+he(),name:"\u5E76\u884C1",showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:U.CONDITION_NODE,childNode:void 0},{id:"Flow_"+he(),name:"\u5E76\u884C2",showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:U.CONDITION_NODE,childNode:void 0}]};S("update:childNode",t)}};return(m,t)=>{const r=fe,N=Pa;return l(),u("div",vl,[p.showAdd?(l(),u("div",ml,[i(N,{trigger:"hover",visible:e(_),"onUpdate:visible":t[4]||(t[4]=c=>de(_)?_.value=c:null),placement:"right-start",width:"auto"},{reference:n(()=>[a("div",yl,[i(r,{icon:"ep:plus"})])]),default:n(()=>[a("div",fl,[a("div",{class:"handler-item",onClick:t[0]||(t[0]=c=>g(e(U).USER_TASK_NODE))},t[5]||(t[5]=[a("div",{class:"approve handler-item-icon"},[a("span",{class:"iconfont icon-approve icon-size"})],-1),a("div",{class:"handler-item-text"},"\u5BA1\u6279\u4EBA",-1)])),a("div",{class:"handler-item",onClick:t[1]||(t[1]=c=>g(e(U).COPY_TASK_NODE))},t[6]||(t[6]=[a("div",{class:"handler-item-icon copy"},[a("span",{class:"iconfont icon-size icon-copy"})],-1),a("div",{class:"handler-item-text"},"\u6284\u9001",-1)])),a("div",{class:"handler-item",onClick:t[2]||(t[2]=c=>g(e(U).CONDITION_BRANCH_NODE))},t[7]||(t[7]=[a("div",{class:"handler-item-icon condition"},[a("span",{class:"iconfont icon-size icon-exclusive"})],-1),a("div",{class:"handler-item-text"},"\u6761\u4EF6\u5206\u652F",-1)])),a("div",{class:"handler-item",onClick:t[3]||(t[3]=c=>g(e(U).PARALLEL_BRANCH_NODE))},t[8]||(t[8]=[a("div",{class:"handler-item-icon condition"},[a("span",{class:"iconfont icon-size icon-parallel"})],-1),a("div",{class:"handler-item-text"},"\u5E76\u884C\u5206\u652F",-1)]))])]),_:1},8,["visible"])])):w("",!0)])}}});function Oe(A){const O=P(A.flowNode);return qe(()=>A.flowNode,_=>{O.value=_}),O}function ua(A){const O=P([]),_=_e("formType"),p=_e("formFields"),S=m=>{const t=[];return m&&m.forEach(r=>{g(JSON.parse(r),t)}),t},g=(m,t,r="")=>{const{field:N,title:c,children:E}=m;if(N&&c){let R=c;r&&(R=`${r}.${c}`),t.push({field:N,title:R,permission:A})}E&&Array.isArray(E)&&E.forEach(R=>{g(R,t)})};return{formType:_,fieldsPermissionConfig:O,getNodeConfigFormFields:m=>{m=Ha(m),O.value=fa(m)||S(e(p))}}}function Da(A){const O=_e("roleList"),_=_e("postList"),p=_e("userList"),S=_e("deptList"),g=_e("userGroupList"),m=_e("deptTree"),t=P();return A===U.USER_TASK_NODE?t.value={candidateStrategy:b.USER,approveMethod:Ne.SEQUENTIAL_APPROVE,approveRatio:100,rejectHandlerType:Le.FINISH_PROCESS,assignStartUserHandlerType:ba.START_USER_AUDIT,returnNodeId:"",timeoutHandlerEnable:!1,timeoutHandlerType:1,timeDuration:6,maxRemindCount:1,buttonsSetting:[]}:t.value={candidateStrategy:b.USER},{configForm:t,roleOptions:O,postOptions:_,userOptions:p,userGroupOptions:g,deptTreeOptions:m,handleCandidateParam:()=>{let r;if(!t.value)return r;switch(t.value.candidateStrategy){case b.USER:r=t.value.userIds.join(",");break;case b.ROLE:r=t.value.roleIds.join(",");break;case b.POST:r=t.value.postIds.join(",");break;case b.USER_GROUP:r=t.value.userGroups.join(",");break;case b.EXPRESSION:r=t.value.expression;break;case b.DEPT_MEMBER:case b.DEPT_LEADER:r=t.value.deptIds.join(",");break;case b.START_USER_DEPT_LEADER:case b.START_USER_MULTI_LEVEL_DEPT_LEADER:r=t.value.deptLevel+"";break;case b.MULTI_LEVEL_DEPT_LEADER:r=t.value.deptIds.join(",").concat("|"+t.value.deptLevel);break}return r},parseCandidateParam:(r,N)=>{if(t.value&&N)switch(r){case b.USER:t.value.userIds=N.split(",").map(c=>+c);break;case b.ROLE:t.value.roleIds=N.split(",").map(c=>+c);break;case b.POST:t.value.postIds=N.split(",").map(c=>+c);break;case b.USER_GROUP:t.value.userGroups=N.split(",").map(c=>+c);break;case b.EXPRESSION:t.value.expression=N;break;case b.DEPT_MEMBER:case b.DEPT_LEADER:t.value.deptIds=N.split(",").map(c=>+c);break;case b.START_USER_DEPT_LEADER:case b.START_USER_MULTI_LEVEL_DEPT_LEADER:t.value.deptLevel=+N;break;case b.MULTI_LEVEL_DEPT_LEADER:{const c=N.split("|");t.value.deptIds=c[0].split(",").map(E=>+E),t.value.deptLevel=+c[1];break}}},getShowText:()=>{var N,c,E,R,I,k,C,F,T,Y,B,H,h,v,j;let r="";if(((N=t.value)==null?void 0:N.candidateStrategy)===b.USER&&((c=t.value)==null?void 0:c.userIds.length)>0){const M=[];p==null||p.value.forEach(G=>{var f;(f=t.value)!=null&&f.userIds.includes(G.id)&&M.push(G.nickname)}),r=`\u6307\u5B9A\u6210\u5458\uFF1A${M.join(",")}`}if(((E=t.value)==null?void 0:E.candidateStrategy)===b.ROLE&&t.value.roleIds.length>0){const M=[];O==null||O.value.forEach(G=>{var f;(f=t.value)!=null&&f.roleIds.includes(G.id)&&M.push(G.name)}),r=`\u6307\u5B9A\u89D2\u8272\uFF1A${M.join(",")}`}if((((R=t.value)==null?void 0:R.candidateStrategy)===b.DEPT_MEMBER||((I=t.value)==null?void 0:I.candidateStrategy)===b.DEPT_LEADER||((k=t.value)==null?void 0:k.candidateStrategy)===b.MULTI_LEVEL_DEPT_LEADER)&&((C=t.value)==null?void 0:C.deptIds.length)>0){const M=[];S==null||S.value.forEach(G=>{var f;(f=t.value)!=null&&f.deptIds.includes(G.id)&&M.push(G.name)}),r=t.value.candidateStrategy===b.DEPT_MEMBER?`\u90E8\u95E8\u6210\u5458\uFF1A${M.join(",")}`:t.value.candidateStrategy===b.DEPT_LEADER?`\u90E8\u95E8\u7684\u8D1F\u8D23\u4EBA\uFF1A${M.join(",")}`:`\u591A\u7EA7\u90E8\u95E8\u7684\u8D1F\u8D23\u4EBA\uFF1A${M.join(",")}`}if(((F=t.value)==null?void 0:F.candidateStrategy)===b.POST&&t.value.postIds.length>0){const M=[];_==null||_.value.forEach(G=>{var f;(f=t.value)!=null&&f.postIds.includes(G.id)&&M.push(G.name)}),r=`\u6307\u5B9A\u5C97\u4F4D: ${M.join(",")}`}if(((T=t.value)==null?void 0:T.candidateStrategy)===b.USER_GROUP&&((Y=t.value)==null?void 0:Y.userGroups.length)>0){const M=[];g==null||g.value.forEach(G=>{var f;(f=t.value)!=null&&f.userGroups.includes(G.id)&&M.push(G.name)}),r=`\u6307\u5B9A\u7528\u6237\u7EC4: ${M.join(",")}`}return((B=t.value)==null?void 0:B.candidateStrategy)===b.START_USER_SELECT&&(r="\u53D1\u8D77\u4EBA\u81EA\u9009"),((H=t.value)==null?void 0:H.candidateStrategy)===b.START_USER&&(r="\u53D1\u8D77\u4EBA\u81EA\u5DF1"),((h=t.value)==null?void 0:h.candidateStrategy)===b.START_USER_DEPT_LEADER&&(r="\u53D1\u8D77\u4EBA\u7684\u90E8\u95E8\u8D1F\u8D23\u4EBA"),((v=t.value)==null?void 0:v.candidateStrategy)===b.START_USER_MULTI_LEVEL_DEPT_LEADER&&(r="\u53D1\u8D77\u4EBA\u8FDE\u7EED\u90E8\u95E8\u8D1F\u8D23\u4EBA"),((j=t.value)==null?void 0:j.candidateStrategy)===b.EXPRESSION&&(r=`\u6D41\u7A0B\u8868\u8FBE\u5F0F\uFF1A${t.value.expression}`),r}}}function ra(){const A=P(!1);return{settingVisible:A,closeDrawer:()=>{A.value=!1},openDrawer:()=>{A.value=!0}}}function ca(A){const O=P(),_=P(!1);return{nodeName:O,showInput:_,clickIcon:()=>{_.value=!0},blurEvent:()=>{_.value=!1,O.value=O.value||$e.get(A)}}}function pa(A,O){const _=P(!1);return{showInput:_,clickTitle:()=>{_.value=!0},blurEvent:()=>{_.value=!1,A.value.name=A.value.name||$e.get(O)}}}const El={class:"config-header"},hl=["placeholder"],_l={key:1,class:"node-name"},gl={class:"field-setting-pane"},Nl={class:"field-setting-item-label"},bl={class:"item-radio-wrap"},Tl={class:"item-radio-wrap"},Sl={class:"item-radio-wrap"},kl=ne({name:"StartUserNodeConfig",__name:"StartUserNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(A,{expose:O}){const _=A,{settingVisible:p,closeDrawer:S,openDrawer:g}=ra(),m=Oe(_),{nodeName:t,showInput:r,clickIcon:N,blurEvent:c}=ca(U.COPY_TASK_NODE),E=P("user"),{formType:R,fieldsPermissionConfig:I,getNodeConfigFormFields:k}=ua(Z.WRITE),C=async()=>(E.value="user",m.value.name=t.value,m.value.showText="\u5DF2\u8BBE\u7F6E",m.value.fieldsPermission=I.value,m.value.buttonsSetting=Xa,p.value=!1,!0);return O({openDrawer:g,showStartUserNodeConfig:F=>{t.value=F.name,k(F.fieldsPermission)}}),(F,T)=>{const Y=fe,B=ea,H=Ge,h=ze,v=aa,j=Ke,M=He,G=We,f=Te("mountedFocus");return l(),y(G,{"append-to-body":!0,modelValue:e(p),"onUpdate:modelValue":T[4]||(T[4]=W=>de(p)?p.value=W:null),"show-close":!1,size:550,"before-close":C},{header:n(()=>[a("div",El,[e(r)?ye((l(),u("input",{key:0,type:"text",class:"config-editable-input",onBlur:T[0]||(T[0]=W=>e(c)()),"onUpdate:modelValue":T[1]||(T[1]=W=>de(t)?t.value=W:null),placeholder:e(t)},null,40,hl)),[[f],[ge,e(t)]]):(l(),u("div",_l,[$(q(e(t))+" ",1),i(Y,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:T[2]||(T[2]=W=>e(N)())})])),T[5]||(T[5]=a("div",{class:"divide-line"},null,-1))])]),footer:n(()=>[i(j),a("div",null,[i(M,{type:"primary",onClick:C},{default:n(()=>T[12]||(T[12]=[$("\u786E \u5B9A")])),_:1}),i(M,{onClick:e(S)},{default:n(()=>T[13]||(T[13]=[$("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:n(()=>[i(v,{type:"border-card",modelValue:e(E),"onUpdate:modelValue":T[3]||(T[3]=W=>de(E)?E.value=W:null)},{default:n(()=>[i(B,{label:"\u6743\u9650",name:"user"},{default:n(()=>T[6]||(T[6]=[a("div",null," \u5F85\u5B9E\u73B0 ",-1)])),_:1}),e(R)===10?(l(),y(B,{key:0,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:n(()=>[a("div",gl,[T[10]||(T[10]=a("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),T[11]||(T[11]=a("div",{class:"field-permit-title"},[a("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 "),a("div",{class:"other-titles"},[a("span",{class:"setting-title-label"},"\u53EA\u8BFB"),a("span",{class:"setting-title-label"},"\u53EF\u7F16\u8F91"),a("span",{class:"setting-title-label"},"\u9690\u85CF")])],-1)),(l(!0),u(L,null,z(e(I),(W,ie)=>(l(),u("div",{class:"field-setting-item",key:ie},[a("div",Nl,q(W.title),1),i(h,{class:"field-setting-item-group",modelValue:W.permission,"onUpdate:modelValue":ce=>W.permission=ce},{default:n(()=>[a("div",bl,[i(H,{value:e(Z).READ,size:"large",label:e(Z).READ},{default:n(()=>T[7]||(T[7]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",Tl,[i(H,{value:e(Z).WRITE,size:"large",label:e(Z).WRITE},{default:n(()=>T[8]||(T[8]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",Sl,[i(H,{value:e(Z).NONE,size:"large",label:e(Z).NONE},{default:n(()=>T[9]||(T[9]=[a("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):w("",!0)]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),Rl={class:"node-wrapper"},wl={class:"node-container"},Ul={class:"node-title-container"},Vl=["placeholder"],Dl=["title"],Il={key:1,class:"node-text"},Ol=ne({name:"StartEventNode",__name:"StartUserNode",props:{flowNode:{type:Object,default:()=>null}},emits:["update:modelValue"],setup(A,{emit:O}){const _=Oe(A),{showInput:p,blurEvent:S,clickTitle:g}=pa(_,U.START_USER_NODE),m=P(),t=()=>{m.value.showStartUserNodeConfig(_.value),m.value.openDrawer()};return(r,N)=>{const c=fe,E=Te("mountedFocus");return l(),u(L,null,[a("div",Rl,[a("div",wl,[a("div",{class:Ye(["node-box",{"node-config-error":!e(_).showText}])},[a("div",Ul,[N[4]||(N[4]=a("div",{class:"node-title-icon start-user"},[a("span",{class:"iconfont icon-start-user"})],-1)),e(p)?ye((l(),u("input",{key:0,type:"text",class:"editable-title-input",onBlur:N[0]||(N[0]=R=>e(S)()),"onUpdate:modelValue":N[1]||(N[1]=R=>e(_).name=R),placeholder:e(_).name},null,40,Vl)),[[E],[ge,e(_).name]]):(l(),u("div",{key:1,class:"node-title",onClick:N[2]||(N[2]=(...R)=>e(g)&&e(g)(...R))},q(e(_).name),1))]),a("div",{class:"node-content",onClick:t},[e(_).showText?(l(),u("div",{key:0,class:"node-text",title:e(_).showText},q(e(_).showText),9,Dl)):(l(),u("div",Il,q(e(xe).get(e(U).START_USER_NODE)),1)),i(c,{icon:"ep:arrow-right-bold"})])],2),e(_)?(l(),y(Ie,{key:0,"child-node":e(_).childNode,"onUpdate:childNode":N[3]||(N[3]=R=>e(_).childNode=R)},null,8,["child-node"])):w("",!0)])]),e(_)?(l(),y(kl,{key:0,ref_key:"nodeSetting",ref:m,"flow-node":e(_)},null,8,["flow-node"])):w("",!0)],64)}}}),Cl={class:"end-node-wrapper"},xl=ne({name:"EndEventNode",__name:"EndEventNode",setup:A=>(O,_)=>(l(),u("div",Cl,_[0]||(_[0]=[a("div",{class:"end-node-box"},[a("span",{class:"node-fixed-name",title:"\u7ED3\u675F"},"\u7ED3\u675F")],-1)])))}),Ia=(A,O)=>O?"\u5176\u5B83\u60C5\u51B5":"\u6761\u4EF6"+(A+1),Al={class:"config-header"},Pl=["placeholder"],Hl={key:1,class:"node-name"},Ll={class:"flex flex-items-center mb-3"},Fl={class:"flex-col"},Ml={class:"flex-col"},jl={class:"flex-col"},Bl={class:"flex-col"},ql={class:"button-setting-pane"},Gl={class:"button-setting-item-label"},zl={class:"button-setting-item-label"},Kl=["onBlur","onUpdate:modelValue","placeholder"],Yl={class:"button-setting-item-label"},$l={class:"field-setting-pane"},Wl={class:"field-setting-item-label"},Xl={class:"item-radio-wrap"},Ql={class:"item-radio-wrap"},Jl={class:"item-radio-wrap"},Zl=ha(ne({name:"UserTaskNodeConfig",__name:"UserTaskNodeConfig",props:{flowNode:{type:Object,required:!0}},emits:["find:returnTaskNodes"],setup(A,{expose:O,emit:_}){const p=A,S=_,g=Ce(()=>{let V="\u90E8\u95E8\u8D1F\u8D23\u4EBA\u6765\u6E90";return o.value.candidateStrategy==b.MULTI_LEVEL_DEPT_LEADER?V+="(\u6307\u5B9A\u90E8\u95E8\u5411\u4E0A)":V+="(\u53D1\u8D77\u4EBA\u90E8\u95E8\u5411\u4E0A)",V}),m=Oe(p),{settingVisible:t,closeDrawer:r,openDrawer:N}=ra(),{nodeName:c,showInput:E,clickIcon:R,blurEvent:I}=ca(U.USER_TASK_NODE),k=P("user"),{formType:C,fieldsPermissionConfig:F,getNodeConfigFormFields:T}=ua(Z.READ),{buttonsSetting:Y,btnDisplayNameEdit:B,changeBtnDisplayName:H,btnDisplayNameBlurEvent:h}=function(){const V=P(),s=P([]);return{buttonsSetting:V,btnDisplayNameEdit:s,changeBtnDisplayName:te=>{s.value[te]=!0},btnDisplayNameBlurEvent:te=>{s.value[te]=!1;const J=V.value[te];J.displayName=J.displayName||ka.get(J.id)}}}(),v=P(Fe.USER),j=P(),M=la({candidateStrategy:[{required:!0,message:"\u5BA1\u6279\u4EBA\u8BBE\u7F6E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],roleIds:[{required:!0,message:"\u89D2\u8272\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],deptIds:[{required:!0,message:"\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userGroups:[{required:!0,message:"\u7528\u6237\u7EC4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],postIds:[{required:!0,message:"\u5C97\u4F4D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],expression:[{required:!0,message:"\u6D41\u7A0B\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],approveMethod:[{required:!0,message:"\u591A\u4EBA\u5BA1\u6279\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],approveRatio:[{required:!0,message:"\u901A\u8FC7\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnNodeId:[{required:!0,message:"\u9A73\u56DE\u8282\u70B9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeoutHandlerEnable:[{required:!0}],timeoutHandlerType:[{required:!0}],timeDuration:[{required:!0,message:"\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxRemindCount:[{required:!0,message:"\u63D0\u9192\u6B21\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],assignEmptyHandlerType:[{required:!0}],assignEmptyHandlerUserIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],assignStartUserHandlerType:[{required:!0}]}),{configForm:G,roleOptions:f,postOptions:W,userOptions:ie,userGroupOptions:ce,deptTreeOptions:X,handleCandidateParam:x,parseCandidateParam:Pe,getShowText:Se}=Da(U.USER_TASK_NODE),o=G,ae=P(!1),ke=()=>{o.value.userIds=[],o.value.deptIds=[],o.value.roleIds=[],o.value.postIds=[],o.value.userGroups=[],o.value.deptLevel=1,o.value.approveMethod=Ne.SEQUENTIAL_APPROVE,o.value.candidateStrategy===b.START_USER||o.value.candidateStrategy===b.USER?ae.value=!0:ae.value=!1},Re=()=>{var V;o.value.userIds&&((V=o.value.userIds)==null?void 0:V.length)<=1&&o.value.candidateStrategy===b.USER?(o.value.approveMethod=Ne.RANDOM_SELECT_ONE_APPROVE,o.value.rejectHandlerType=Le.FINISH_PROCESS,ae.value=!0):ae.value=!1},K=()=>{o.value.rejectHandlerType=Le.FINISH_PROCESS,o.value.approveMethod===Ne.APPROVE_BY_RATIO&&(o.value.approveRatio=100),j.value.clearValidate("approveRatio")},ue=P([]),{timeoutHandlerChange:se,cTimeoutType:pe,timeoutHandlerTypeChanged:le,timeUnit:re,timeUnitChange:we,isoTimeDuration:Xe,cTimeoutMaxRemindCount:Qe}=function(){const V=P(Ee.HOUR),s=Ce(()=>{if(o.value.timeoutHandlerEnable)return o.value.timeoutHandlerType}),te=Ce(()=>{if(!o.value.timeoutHandlerEnable)return;let oe="PT";return V.value===Ee.MINUTE&&(oe+=o.value.timeDuration+"M"),V.value===Ee.HOUR&&(oe+=o.value.timeDuration+"H"),V.value===Ee.DAY&&(oe+=o.value.timeDuration+"D"),oe}),J=Ce(()=>{if(o.value.timeoutHandlerEnable&&o.value.timeoutHandlerType===Ra.REMINDER)return o.value.maxRemindCount});return{timeoutHandlerChange:()=>{o.value.timeoutHandlerEnable&&(V.value=2,o.value.timeDuration=6,o.value.timeoutHandlerType=1,o.value.maxRemindCount=1)},cTimeoutType:s,timeoutHandlerTypeChanged:()=>{o.value.timeoutHandlerType===Ra.REMINDER&&(o.value.maxRemindCount=1)},timeUnit:V,timeUnitChange:()=>{V.value===Ee.MINUTE&&(o.value.timeDuration=60),V.value===Ee.HOUR&&(o.value.timeDuration=6),V.value===Ee.DAY&&(o.value.timeDuration=1)},isoTimeDuration:te,cTimeoutMaxRemindCount:J}}(),D=async()=>{if(k.value="user",m.value.name=c.value,m.value.approveType=v.value,v.value!==Fe.USER)return m.value.showText=(s=>{let te="";return Ta.forEach(J=>{J.value!==s||(te=J.label)}),te})(v.value),t.value=!1,!0;if(!j||!await j.value.validate())return!1;const V=Se();return!!V&&(m.value.candidateStrategy=o.value.candidateStrategy,m.value.candidateParam=x(),m.value.approveMethod=o.value.approveMethod,o.value.approveMethod===Ne.APPROVE_BY_RATIO&&(m.value.approveRatio=o.value.approveRatio),m.value.rejectHandler={type:o.value.rejectHandlerType,returnNodeId:o.value.returnNodeId},m.value.timeoutHandler={enable:o.value.timeoutHandlerEnable,type:pe.value,timeDuration:Xe.value,maxRemindCount:Qe.value},m.value.assignEmptyHandler={type:o.value.assignEmptyHandlerType,userIds:o.value.assignEmptyHandlerType===sa.ASSIGN_USER?o.value.assignEmptyHandlerUserIds:void 0},m.value.assignStartUserHandlerType=o.value.assignStartUserHandlerType,m.value.fieldsPermission=F.value,m.value.buttonsSetting=Y.value,m.value.showText=V,t.value=!1,!0)};return O({openDrawer:N,showUserTaskNodeConfig:V=>{var J,oe,Q,ve,me,Me,je;if(c.value=V.name,v.value=V.approveType?V.approveType:Fe.USER,v.value!==Fe.USER)return;o.value.candidateStrategy=V.candidateStrategy,Pe(V.candidateStrategy,V==null?void 0:V.candidateParam),o.value.userIds&&o.value.userIds.length>1?ae.value=!0:ae.value=!1,o.value.approveMethod=V.approveMethod,V.approveMethod==Ne.APPROVE_BY_RATIO&&(o.value.approveRatio=V.approveRatio),o.value.rejectHandlerType=V.rejectHandler.type,o.value.returnNodeId=(J=V.rejectHandler)==null?void 0:J.returnNodeId;const s=[];if(S("find:returnTaskNodes",s),ue.value=s,o.value.timeoutHandlerEnable=V.timeoutHandler.enable,((oe=V.timeoutHandler)==null?void 0:oe.enable)&&((Q=V.timeoutHandler)==null?void 0:Q.timeDuration)){const Ue=V.timeoutHandler.timeDuration;let De=Ue.slice(2,Ue.length-1),Be=Ue.slice(Ue.length-1);o.value.timeDuration=parseInt(De),re.value=(te=Be)==="M"?Ee.MINUTE:te==="H"?Ee.HOUR:te==="D"?Ee.DAY:Ee.HOUR}var te;o.value.timeoutHandlerType=(ve=V.timeoutHandler)==null?void 0:ve.type,o.value.maxRemindCount=(me=V.timeoutHandler)==null?void 0:me.maxRemindCount,o.value.assignEmptyHandlerType=(Me=V.assignEmptyHandler)==null?void 0:Me.type,o.value.assignEmptyHandlerUserIds=(je=V.assignEmptyHandler)==null?void 0:je.userIds,o.value.assignStartUserHandlerType=V.assignStartUserHandlerType,Y.value=fa(V.buttonsSetting)||ol,T(V.fieldsPermission)}}),(V,s)=>{const te=fe,J=Ge,oe=ze,Q=ta,ve=oa,me=da,Me=Va,je=na,Ue=La,De=Ke,Be=Ea,Oa=Fa,Ca=ia,Je=ea,Ze=He,xa=aa,Aa=We,ma=Te("mountedFocus");return l(),y(Aa,{"append-to-body":!0,modelValue:e(t),"onUpdate:modelValue":s[25]||(s[25]=d=>de(t)?t.value=d:null),"show-close":!1,size:550,"before-close":D,class:"justify-start"},{header:n(()=>[a("div",Al,[e(E)?ye((l(),u("input",{key:0,type:"text",class:"config-editable-input",onBlur:s[0]||(s[0]=d=>e(I)()),"onUpdate:modelValue":s[1]||(s[1]=d=>de(c)?c.value=d:null),placeholder:e(c)},null,40,Pl)),[[ma],[ge,e(c)]]):(l(),u("div",Hl,[$(q(e(c))+" ",1),i(te,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:s[2]||(s[2]=d=>e(R)())})])),s[26]||(s[26]=a("div",{class:"divide-line"},null,-1))])]),footer:n(()=>[i(De),a("div",null,[i(Ze,{type:"primary",onClick:D},{default:n(()=>s[41]||(s[41]=[$("\u786E \u5B9A")])),_:1}),i(Ze,{onClick:e(r)},{default:n(()=>s[42]||(s[42]=[$("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:n(()=>[a("div",Ll,[s[27]||(s[27]=a("span",{class:"font-size-16px mr-3"},"\u5BA1\u6279\u7C7B\u578B :",-1)),i(oe,{modelValue:e(v),"onUpdate:modelValue":s[3]||(s[3]=d=>de(v)?v.value=d:null)},{default:n(()=>[(l(!0),u(L,null,z(e(Ta),(d,ee)=>(l(),y(J,{key:ee,value:d.value,label:d.value},{default:n(()=>[$(q(d.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),e(v)===e(Fe).USER?(l(),y(xa,{key:0,type:"border-card",modelValue:e(k),"onUpdate:modelValue":s[24]||(s[24]=d=>de(k)?k.value=d:null)},{default:n(()=>[i(Je,{label:"\u5BA1\u6279\u4EBA",name:"user"},{default:n(()=>[a("div",null,[i(Ca,{ref_key:"formRef",ref:j,model:e(o),"label-position":"top",rules:e(M)},{default:n(()=>[i(Q,{label:"\u5BA1\u6279\u4EBA\u8BBE\u7F6E",prop:"candidateStrategy"},{default:n(()=>[i(oe,{modelValue:e(o).candidateStrategy,"onUpdate:modelValue":s[4]||(s[4]=d=>e(o).candidateStrategy=d),onChange:ke},{default:n(()=>[(l(!0),u(L,null,z(e(Sa),(d,ee)=>(l(),y(J,{key:ee,value:d.value,label:d.value},{default:n(()=>[$(q(d.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o).candidateStrategy==e(b).ROLE?(l(),y(Q,{key:0,label:"\u6307\u5B9A\u89D2\u8272",prop:"roleIds"},{default:n(()=>[i(me,{modelValue:e(o).roleIds,"onUpdate:modelValue":s[5]||(s[5]=d=>e(o).roleIds=d),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(f),d=>(l(),y(ve,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(o).candidateStrategy==e(b).DEPT_MEMBER||e(o).candidateStrategy==e(b).DEPT_LEADER||e(o).candidateStrategy==e(b).MULTI_LEVEL_DEPT_LEADER?(l(),y(Q,{key:1,label:"\u6307\u5B9A\u90E8\u95E8",prop:"deptIds",span:"24"},{default:n(()=>[i(Me,{ref:"treeRef",modelValue:e(o).deptIds,"onUpdate:modelValue":s[6]||(s[6]=d=>e(o).deptIds=d),data:e(X),props:e(ya),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E",multiple:"","node-key":"id","check-strictly":!0,style:{width:"100%"},"show-checkbox":""},null,8,["modelValue","data","props"])]),_:1})):w("",!0),e(o).candidateStrategy==e(b).MULTI_LEVEL_DEPT_LEADER||e(o).candidateStrategy==e(b).START_USER_DEPT_LEADER||e(o).candidateStrategy==e(b).START_USER_MULTI_LEVEL_DEPT_LEADER?(l(),y(Q,{key:2,label:e(g),prop:"deptLevel",span:"24"},{default:n(()=>[i(me,{modelValue:e(o).deptLevel,"onUpdate:modelValue":s[7]||(s[7]=d=>e(o).deptLevel=d),clearable:""},{default:n(()=>[(l(!0),u(L,null,z(e(Qa),(d,ee)=>(l(),y(ve,{key:ee,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):w("",!0),e(o).candidateStrategy==e(b).POST?(l(),y(Q,{key:3,label:"\u6307\u5B9A\u5C97\u4F4D",prop:"postIds",span:"24"},{default:n(()=>[i(me,{modelValue:e(o).postIds,"onUpdate:modelValue":s[8]||(s[8]=d=>e(o).postIds=d),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(W),d=>(l(),y(ve,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(o).candidateStrategy==e(b).USER?(l(),y(Q,{key:4,label:"\u6307\u5B9A\u7528\u6237",prop:"userIds",span:"24"},{default:n(()=>[i(me,{modelValue:e(o).userIds,"onUpdate:modelValue":s[9]||(s[9]=d=>e(o).userIds=d),clearable:"",multiple:"",style:{width:"100%"},onChange:Re},{default:n(()=>[(l(!0),u(L,null,z(e(ie),d=>(l(),y(ve,{key:d.id,label:d.nickname,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(o).candidateStrategy===e(b).USER_GROUP?(l(),y(Q,{key:5,label:"\u6307\u5B9A\u7528\u6237\u7EC4",prop:"userGroups"},{default:n(()=>[i(me,{modelValue:e(o).userGroups,"onUpdate:modelValue":s[10]||(s[10]=d=>e(o).userGroups=d),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(ce),d=>(l(),y(ve,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(o).candidateStrategy===e(b).EXPRESSION?(l(),y(Q,{key:6,label:"\u6D41\u7A0B\u8868\u8FBE\u5F0F",prop:"expression"},{default:n(()=>[i(je,{type:"textarea",modelValue:e(o).expression,"onUpdate:modelValue":s[11]||(s[11]=d=>e(o).expression=d),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):w("",!0),i(Q,{label:"\u591A\u4EBA\u5BA1\u6279\u65B9\u5F0F",prop:"approveMethod"},{default:n(()=>[i(oe,{modelValue:e(o).approveMethod,"onUpdate:modelValue":s[13]||(s[13]=d=>e(o).approveMethod=d),onChange:K},{default:n(()=>[a("div",Fl,[(l(!0),u(L,null,z(e(Ja),(d,ee)=>(l(),u("div",{key:ee,class:"flex items-center"},[i(J,{value:d.value,label:d.value,disabled:d.value!==e(Ne).RANDOM_SELECT_ONE_APPROVE&&e(ae)},{default:n(()=>[$(q(d.label),1)]),_:2},1032,["value","label","disabled"]),i(Q,{prop:"approveRatio"},{default:n(()=>[d.value===e(Ne).APPROVE_BY_RATIO&&e(o).approveMethod===e(Ne).APPROVE_BY_RATIO?(l(),y(Ue,{key:0,modelValue:e(o).approveRatio,"onUpdate:modelValue":s[12]||(s[12]=be=>e(o).approveRatio=be),min:10,max:100,step:10,size:"small"},null,8,["modelValue"])):w("",!0)]),_:2},1024)]))),128))])]),_:1},8,["modelValue"])]),_:1}),i(De,{"content-position":"left"},{default:n(()=>s[28]||(s[28]=[$("\u5BA1\u6279\u4EBA\u62D2\u7EDD\u65F6")])),_:1}),i(Q,{prop:"rejectHandlerType"},{default:n(()=>[i(oe,{modelValue:e(o).rejectHandlerType,"onUpdate:modelValue":s[14]||(s[14]=d=>e(o).rejectHandlerType=d)},{default:n(()=>[a("div",Ml,[(l(!0),u(L,null,z(e(Za),(d,ee)=>(l(),u("div",{key:ee},[(l(),y(J,{key:d.value,value:d.value,label:d.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1}),e(o).rejectHandlerType==e(Le).RETURN_USER_TASK?(l(),y(Q,{key:7,label:"\u9A73\u56DE\u8282\u70B9",prop:"returnNodeId"},{default:n(()=>[i(me,{modelValue:e(o).returnNodeId,"onUpdate:modelValue":s[15]||(s[15]=d=>e(o).returnNodeId=d),clearable:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(ue),d=>(l(),y(ve,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),i(De,{"content-position":"left"},{default:n(()=>s[29]||(s[29]=[$("\u5BA1\u6279\u4EBA\u8D85\u65F6\u672A\u5904\u7406\u65F6")])),_:1}),i(Q,{label:"\u542F\u7528\u5F00\u5173",prop:"timeoutHandlerEnable"},{default:n(()=>[i(Be,{modelValue:e(o).timeoutHandlerEnable,"onUpdate:modelValue":s[16]||(s[16]=d=>e(o).timeoutHandlerEnable=d),"active-text":"\u5F00\u542F","inactive-text":"\u5173\u95ED",onChange:e(se)},null,8,["modelValue","onChange"])]),_:1}),e(o).timeoutHandlerEnable?(l(),y(Q,{key:8,label:"\u6267\u884C\u52A8\u4F5C",prop:"timeoutHandlerType"},{default:n(()=>[i(oe,{modelValue:e(o).timeoutHandlerType,"onUpdate:modelValue":s[17]||(s[17]=d=>e(o).timeoutHandlerType=d),onChange:e(le)},{default:n(()=>[(l(!0),u(L,null,z(e(el),d=>(l(),y(Oa,{key:d.value,value:d.value,label:d.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1})):w("",!0),e(o).timeoutHandlerEnable?(l(),y(Q,{key:9,label:"\u8D85\u65F6\u65F6\u95F4\u8BBE\u7F6E"},{default:n(()=>[s[30]||(s[30]=a("span",{class:"mr-2"},"\u5F53\u8D85\u8FC7",-1)),i(Q,{prop:"timeDuration"},{default:n(()=>[i(Ue,{class:"mr-2",style:{width:"100px"},modelValue:e(o).timeDuration,"onUpdate:modelValue":s[18]||(s[18]=d=>e(o).timeDuration=d),min:1,"controls-position":"right"},null,8,["modelValue"])]),_:1}),i(me,{modelValue:e(re),"onUpdate:modelValue":s[19]||(s[19]=d=>de(re)?re.value=d:null),class:"mr-2",style:{width:"100px"},onChange:e(we)},{default:n(()=>[(l(!0),u(L,null,z(e(al),d=>(l(),y(ve,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),s[31]||(s[31]=$(" \u672A\u5904\u7406 "))]),_:1})):w("",!0),e(o).timeoutHandlerEnable&&e(o).timeoutHandlerType===1?(l(),y(Q,{key:10,label:"\u6700\u5927\u63D0\u9192\u6B21\u6570",prop:"maxRemindCount"},{default:n(()=>[i(Ue,{modelValue:e(o).maxRemindCount,"onUpdate:modelValue":s[20]||(s[20]=d=>e(o).maxRemindCount=d),min:1,max:10},null,8,["modelValue"])]),_:1})):w("",!0),i(De,{"content-position":"left"},{default:n(()=>s[32]||(s[32]=[$("\u5BA1\u6279\u4EBA\u4E3A\u7A7A\u65F6")])),_:1}),i(Q,{prop:"assignEmptyHandlerType"},{default:n(()=>[i(oe,{modelValue:e(o).assignEmptyHandlerType,"onUpdate:modelValue":s[21]||(s[21]=d=>e(o).assignEmptyHandlerType=d)},{default:n(()=>[a("div",jl,[(l(!0),u(L,null,z(e(ll),(d,ee)=>(l(),u("div",{key:ee},[(l(),y(J,{key:d.value,value:d.value,label:d.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1}),e(o).assignEmptyHandlerType==e(sa).ASSIGN_USER?(l(),y(Q,{key:11,label:"\u6307\u5B9A\u7528\u6237",prop:"assignEmptyHandlerUserIds",span:"24"},{default:n(()=>[i(me,{modelValue:e(o).assignEmptyHandlerUserIds,"onUpdate:modelValue":s[22]||(s[22]=d=>e(o).assignEmptyHandlerUserIds=d),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(ie),d=>(l(),y(ve,{key:d.id,label:d.nickname,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),i(De,{"content-position":"left"},{default:n(()=>s[33]||(s[33]=[$("\u5BA1\u6279\u4EBA\u4E0E\u63D0\u4EA4\u4EBA\u4E3A\u540C\u4E00\u4EBA\u65F6")])),_:1}),i(Q,{prop:"assignStartUserHandlerType"},{default:n(()=>[i(oe,{modelValue:e(o).assignStartUserHandlerType,"onUpdate:modelValue":s[23]||(s[23]=d=>e(o).assignStartUserHandlerType=d)},{default:n(()=>[a("div",Bl,[(l(!0),u(L,null,z(e(tl),(d,ee)=>(l(),u("div",{key:ee},[(l(),y(J,{key:d.value,value:d.value,label:d.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1}),i(Je,{label:"\u64CD\u4F5C\u6309\u94AE\u8BBE\u7F6E",name:"buttons"},{default:n(()=>[a("div",ql,[s[34]||(s[34]=a("div",{class:"button-setting-desc"},"\u64CD\u4F5C\u6309\u94AE",-1)),s[35]||(s[35]=a("div",{class:"button-setting-title"},[a("div",{class:"button-title-label"},"\u64CD\u4F5C\u6309\u94AE"),a("div",{class:"pl-4 button-title-label"},"\u663E\u793A\u540D\u79F0"),a("div",{class:"button-title-label"},"\u542F\u7528")],-1)),(l(!0),u(L,null,z(e(Y),(d,ee)=>(l(),u("div",{class:"button-setting-item",key:ee},[a("div",Gl,q(e(ka).get(d.id)),1),a("div",zl,[e(B)[ee]?ye((l(),u("input",{key:0,type:"text",class:"editable-title-input",onBlur:be=>e(h)(ee),"onUpdate:modelValue":be=>d.displayName=be,placeholder:d.displayName},null,40,Kl)),[[ma],[ge,d.displayName]]):(l(),y(Ze,{key:1,text:"",onClick:be=>e(H)(ee)},{default:n(()=>[$(q(d.displayName)+" \xA0",1),i(te,{icon:"ep:edit"})]),_:2},1032,["onClick"]))]),a("div",Yl,[i(Be,{modelValue:d.enable,"onUpdate:modelValue":be=>d.enable=be},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])]),_:1}),e(C)===10?(l(),y(Je,{key:0,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:n(()=>[a("div",$l,[s[39]||(s[39]=a("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),s[40]||(s[40]=a("div",{class:"field-permit-title"},[a("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 "),a("div",{class:"other-titles"},[a("span",{class:"setting-title-label"},"\u53EA\u8BFB"),a("span",{class:"setting-title-label"},"\u53EF\u7F16\u8F91"),a("span",{class:"setting-title-label"},"\u9690\u85CF")])],-1)),(l(!0),u(L,null,z(e(F),(d,ee)=>(l(),u("div",{class:"field-setting-item",key:ee},[a("div",Wl,q(d.title),1),i(oe,{class:"field-setting-item-group",modelValue:d.permission,"onUpdate:modelValue":be=>d.permission=be},{default:n(()=>[a("div",Xl,[i(J,{value:e(Z).READ,size:"large",label:e(Z).READ},{default:n(()=>s[36]||(s[36]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",Ql,[i(J,{value:e(Z).WRITE,size:"large",label:e(Z).WRITE},{default:n(()=>s[37]||(s[37]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",Jl,[i(J,{value:e(Z).NONE,size:"large",label:e(Z).NONE},{default:n(()=>s[38]||(s[38]=[a("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):w("",!0)]),_:1},8,["modelValue"])):w("",!0)]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-2cb4bb15"]]),et={class:"node-wrapper"},at={class:"node-container"},lt={class:"node-title-container"},tt=["placeholder"],ot=["title"],dt={key:1,class:"node-text"},nt={class:"node-toolbar"},it={class:"toolbar-icon"},st=ne({name:"UserTaskNode",__name:"UserTaskNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode","find:parentNode"],setup(A,{emit:O}){const _=O,p=Oe(A),{showInput:S,blurEvent:g,clickTitle:m}=pa(p,U.START_USER_NODE),t=P(),r=()=>{t.value.showUserTaskNodeConfig(p.value),t.value.openDrawer()},N=()=>{_("update:flowNode",p.value.childNode)},c=E=>{_("find:parentNode",E,U.USER_TASK_NODE)};return(E,R)=>{const I=fe,k=Te("mountedFocus");return l(),u(L,null,[a("div",et,[a("div",at,[a("div",{class:Ye(["node-box",{"node-config-error":!e(p).showText}])},[a("div",lt,[R[4]||(R[4]=a("div",{class:"node-title-icon user-task"},[a("span",{class:"iconfont icon-approve"})],-1)),e(S)?ye((l(),u("input",{key:0,type:"text",class:"editable-title-input",onBlur:R[0]||(R[0]=C=>e(g)()),"onUpdate:modelValue":R[1]||(R[1]=C=>e(p).name=C),placeholder:e(p).name},null,40,tt)),[[k],[ge,e(p).name]]):(l(),u("div",{key:1,class:"node-title",onClick:R[2]||(R[2]=(...C)=>e(m)&&e(m)(...C))},q(e(p).name),1))]),a("div",{class:"node-content",onClick:r},[e(p).showText?(l(),u("div",{key:0,class:"node-text",title:e(p).showText},q(e(p).showText),9,ot)):(l(),u("div",dt,q(e(xe).get(e(U).USER_TASK_NODE)),1)),i(I,{icon:"ep:arrow-right-bold"})]),a("div",nt,[a("div",it,[i(I,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:N})])])],2),e(p)?(l(),y(Ie,{key:0,"child-node":e(p).childNode,"onUpdate:childNode":R[3]||(R[3]=C=>e(p).childNode=C)},null,8,["child-node"])):w("",!0)])]),e(p)?(l(),y(Zl,{key:0,ref_key:"nodeSetting",ref:t,"flow-node":e(p),"onFind:returnTaskNodes":c},null,8,["flow-node"])):w("",!0)],64)}}}),ut={class:"config-header"},rt=["placeholder"],ct={key:1,class:"node-name"},pt={class:"field-setting-pane"},vt={class:"field-setting-item-label"},mt={class:"item-radio-wrap"},ft={class:"item-radio-wrap"},yt={class:"item-radio-wrap"},Et=ne({name:"CopyTaskNodeConfig",__name:"CopyTaskNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(A,{expose:O}){const _=A,{settingVisible:p,closeDrawer:S,openDrawer:g}=ra(),m=Oe(_),{nodeName:t,showInput:r,clickIcon:N,blurEvent:c}=ca(U.COPY_TASK_NODE),E=P("user"),{formType:R,fieldsPermissionConfig:I,getNodeConfigFormFields:k}=ua(Z.READ),C=P(),F=la({candidateStrategy:[{required:!0,message:"\u6284\u9001\u4EBA\u8BBE\u7F6E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],roleIds:[{required:!0,message:"\u89D2\u8272\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],deptIds:[{required:!0,message:"\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userGroups:[{required:!0,message:"\u7528\u6237\u7EC4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],postIds:[{required:!0,message:"\u5C97\u4F4D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],expression:[{required:!0,message:"\u6D41\u7A0B\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),{configForm:T,roleOptions:Y,postOptions:B,userOptions:H,userGroupOptions:h,deptTreeOptions:v,getShowText:j,handleCandidateParam:M,parseCandidateParam:G}=Da(U.COPY_TASK_NODE),f=T,W=Ce(()=>Sa.filter(X=>X.value!==b.START_USER_SELECT&&X.value!==b.START_USER)),ie=()=>{f.value.userIds=[],f.value.deptIds=[],f.value.roleIds=[],f.value.postIds=[],f.value.userGroups=[],f.value.deptLevel=1},ce=async()=>{if(E.value="user",!C||!await C.value.validate())return!1;const X=j();return!!X&&(m.value.name=t.value,m.value.candidateParam=M(),m.value.candidateStrategy=f.value.candidateStrategy,m.value.showText=X,m.value.fieldsPermission=I.value,p.value=!1,!0)};return O({openDrawer:g,showCopyTaskNodeConfig:X=>{t.value=X.name,f.value.candidateStrategy=X.candidateStrategy,G(X.candidateStrategy,X==null?void 0:X.candidateParam),k(X.fieldsPermission)}}),(X,x)=>{const Pe=fe,Se=Ge,o=ze,ae=ta,ke=oa,Re=da,K=Va,ue=na,se=ia,pe=ea,le=aa,re=Ke,we=He,Xe=We,Qe=Te("mountedFocus");return l(),y(Xe,{"append-to-body":!0,modelValue:e(p),"onUpdate:modelValue":x[11]||(x[11]=D=>de(p)?p.value=D:null),"show-close":!1,size:550,"before-close":ce},{header:n(()=>[a("div",ut,[e(r)?ye((l(),u("input",{key:0,type:"text",class:"config-editable-input",onBlur:x[0]||(x[0]=D=>e(c)()),"onUpdate:modelValue":x[1]||(x[1]=D=>de(t)?t.value=D:null),placeholder:e(t)},null,40,rt)),[[Qe],[ge,e(t)]]):(l(),u("div",ct,[$(q(e(t))+" ",1),i(Pe,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:x[2]||(x[2]=D=>e(N)())})])),x[12]||(x[12]=a("div",{class:"divide-line"},null,-1))])]),footer:n(()=>[i(re),a("div",null,[i(we,{type:"primary",onClick:ce},{default:n(()=>x[18]||(x[18]=[$("\u786E \u5B9A")])),_:1}),i(we,{onClick:e(S)},{default:n(()=>x[19]||(x[19]=[$("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:n(()=>[i(le,{type:"border-card",modelValue:e(E),"onUpdate:modelValue":x[10]||(x[10]=D=>de(E)?E.value=D:null)},{default:n(()=>[i(pe,{label:"\u6284\u9001\u4EBA",name:"user"},{default:n(()=>[a("div",null,[i(se,{ref_key:"formRef",ref:C,model:e(f),"label-position":"top",rules:e(F)},{default:n(()=>[i(ae,{label:"\u6284\u9001\u4EBA\u8BBE\u7F6E",prop:"candidateStrategy"},{default:n(()=>[i(o,{modelValue:e(f).candidateStrategy,"onUpdate:modelValue":x[3]||(x[3]=D=>e(f).candidateStrategy=D),onChange:ie},{default:n(()=>[(l(!0),u(L,null,z(e(W),(D,V)=>(l(),y(Se,{key:V,value:D.value,label:D.value},{default:n(()=>[$(q(D.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f).candidateStrategy==e(b).ROLE?(l(),y(ae,{key:0,label:"\u6307\u5B9A\u89D2\u8272",prop:"roleIds"},{default:n(()=>[i(Re,{modelValue:e(f).roleIds,"onUpdate:modelValue":x[4]||(x[4]=D=>e(f).roleIds=D),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(Y),D=>(l(),y(ke,{key:D.id,label:D.name,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(f).candidateStrategy==e(b).DEPT_MEMBER||e(f).candidateStrategy==e(b).DEPT_LEADER?(l(),y(ae,{key:1,label:"\u6307\u5B9A\u90E8\u95E8",prop:"deptIds",span:"24"},{default:n(()=>[i(K,{ref:"treeRef",modelValue:e(f).deptIds,"onUpdate:modelValue":x[5]||(x[5]=D=>e(f).deptIds=D),data:e(v),props:e(ya),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E",multiple:"","node-key":"id",style:{width:"100%"},"show-checkbox":""},null,8,["modelValue","data","props"])]),_:1})):w("",!0),e(f).candidateStrategy==e(b).POST?(l(),y(ae,{key:2,label:"\u6307\u5B9A\u5C97\u4F4D",prop:"postIds",span:"24"},{default:n(()=>[i(Re,{modelValue:e(f).postIds,"onUpdate:modelValue":x[6]||(x[6]=D=>e(f).postIds=D),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(B),D=>(l(),y(ke,{key:D.id,label:D.name,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(f).candidateStrategy==e(b).USER?(l(),y(ae,{key:3,label:"\u6307\u5B9A\u7528\u6237",prop:"userIds",span:"24"},{default:n(()=>[i(Re,{modelValue:e(f).userIds,"onUpdate:modelValue":x[7]||(x[7]=D=>e(f).userIds=D),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(H),D=>(l(),y(ke,{key:D.id,label:D.nickname,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(f).candidateStrategy===e(b).USER_GROUP?(l(),y(ae,{key:4,label:"\u6307\u5B9A\u7528\u6237\u7EC4",prop:"userGroups"},{default:n(()=>[i(Re,{modelValue:e(f).userGroups,"onUpdate:modelValue":x[8]||(x[8]=D=>e(f).userGroups=D),clearable:"",multiple:"",style:{width:"100%"}},{default:n(()=>[(l(!0),u(L,null,z(e(h),D=>(l(),y(ke,{key:D.id,label:D.name,value:D.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):w("",!0),e(f).candidateStrategy===e(b).EXPRESSION?(l(),y(ae,{key:5,label:"\u6D41\u7A0B\u8868\u8FBE\u5F0F",prop:"expression"},{default:n(()=>[i(ue,{type:"textarea",modelValue:e(f).expression,"onUpdate:modelValue":x[9]||(x[9]=D=>e(f).expression=D),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):w("",!0)]),_:1},8,["model","rules"])])]),_:1}),e(R)===10?(l(),y(pe,{key:0,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:n(()=>[a("div",pt,[x[16]||(x[16]=a("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),x[17]||(x[17]=a("div",{class:"field-permit-title"},[a("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 "),a("div",{class:"other-titles"},[a("span",{class:"setting-title-label"},"\u53EA\u8BFB"),a("span",{class:"setting-title-label"},"\u53EF\u7F16\u8F91"),a("span",{class:"setting-title-label"},"\u9690\u85CF")])],-1)),(l(!0),u(L,null,z(e(I),(D,V)=>(l(),u("div",{class:"field-setting-item",key:V},[a("div",vt,q(D.title),1),i(o,{class:"field-setting-item-group",modelValue:D.permission,"onUpdate:modelValue":s=>D.permission=s},{default:n(()=>[a("div",mt,[i(Se,{value:e(Z).READ,size:"large",label:e(Z).WRITE},{default:n(()=>x[13]||(x[13]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",ft,[i(Se,{value:e(Z).WRITE,size:"large",label:e(Z).WRITE,disabled:""},{default:n(()=>x[14]||(x[14]=[a("span",null,null,-1)])),_:1},8,["value","label"])]),a("div",yt,[i(Se,{value:e(Z).NONE,size:"large",label:e(Z).NONE},{default:n(()=>x[15]||(x[15]=[a("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):w("",!0)]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),ht={class:"node-wrapper"},_t={class:"node-container"},gt={class:"node-title-container"},Nt=["placeholder"],bt=["title"],Tt={key:1,class:"node-text"},St={class:"node-toolbar"},kt={class:"toolbar-icon"},Rt=ne({name:"CopyTaskNode",__name:"CopyTaskNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(A,{emit:O}){const _=O,p=Oe(A),{showInput:S,blurEvent:g,clickTitle:m}=pa(p,U.COPY_TASK_NODE),t=P(),r=()=>{t.value.showCopyTaskNodeConfig(p.value),t.value.openDrawer()},N=()=>{_("update:flowNode",p.value.childNode)};return(c,E)=>{const R=fe,I=Te("mountedFocus");return l(),u("div",ht,[a("div",_t,[a("div",{class:Ye(["node-box",{"node-config-error":!e(p).showText}])},[a("div",gt,[E[4]||(E[4]=a("div",{class:"node-title-icon copy-task"},[a("span",{class:"iconfont icon-copy"})],-1)),e(S)?ye((l(),u("input",{key:0,type:"text",class:"editable-title-input",onBlur:E[0]||(E[0]=k=>e(g)()),"onUpdate:modelValue":E[1]||(E[1]=k=>e(p).name=k),placeholder:e(p).name},null,40,Nt)),[[I],[ge,e(p).name]]):(l(),u("div",{key:1,class:"node-title",onClick:E[2]||(E[2]=(...k)=>e(m)&&e(m)(...k))},q(e(p).name),1))]),a("div",{class:"node-content",onClick:r},[e(p).showText?(l(),u("div",{key:0,class:"node-text",title:e(p).showText},q(e(p).showText),9,bt)):(l(),u("div",Tt,q(e(xe).get(e(U).COPY_TASK_NODE)),1)),i(R,{icon:"ep:arrow-right-bold"})]),a("div",St,[a("div",kt,[i(R,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:N})])])],2),e(p)?(l(),y(Ie,{key:0,"child-node":e(p).childNode,"onUpdate:childNode":E[3]||(E[3]=k=>e(p).childNode=k)},null,8,["child-node"])):w("",!0)]),e(p)?(l(),y(Et,{key:0,ref_key:"nodeSetting",ref:t,"flow-node":e(p)},null,8,["flow-node"])):w("",!0)])}}}),wt={class:"config-header"},Ut=["placeholder"],Vt={key:1,class:"node-name"},Dt={key:0,class:"mb-3 font-size-16px"},It={key:1},Ot={class:"condition-group-tool"},Ct={class:"flex items-center"},xt={key:0,class:"condition-group-delete"},At={class:"flex items-center justify-between"},Pt={class:"flex"},Ht={class:"mr-2"},Lt={class:"mr-2"},Ft={class:"mr-2"},Mt={key:0,class:"mr-1 flex items-center"},jt={class:"flex items-center"},Bt={title:"\u6DFB\u52A0\u6761\u4EF6\u7EC4",class:"mt-4 cursor-pointer"},qt=ha(ne({name:"ConditionNodeConfig",__name:"ConditionNodeConfig",props:{conditionNode:{type:Object,required:!0},nodeIndex:{type:Number,required:!0}},setup(A,{expose:O}){const _=_a(),p=_e("formType"),S=Ce(()=>dl.filter(h=>(p==null?void 0:p.value)===10||h.value===Ae.RULE)),g=A,m=P(!1);qe(()=>g.conditionNode,h=>{r.value=h});const t=P(!1),r=P(g.conditionNode);O({open:()=>{r.value.conditionType===Ae.RULE&&r.value.conditionGroups&&(F.value=r.value.conditionGroups),m.value=!0}});const N=()=>{m.value=!1},c=async h=>{await I()?h():h(!0)},E=la({conditionType:[{required:!0,message:"\u914D\u7F6E\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],conditionExpression:[{required:!0,message:"\u6761\u4EF6\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),R=P(),I=async()=>{if(!r.value.defaultFlow){if(!R||!await R.value.validate())return!1;const h=k();if(!h)return!1;r.value.showText=h,r.value.conditionType===Ae.EXPRESSION&&(r.value.conditionGroups=void 0),r.value.conditionType===Ae.RULE&&(r.value.conditionExpression=void 0,r.value.conditionGroups=F.value)}return m.value=!1,!0},k=()=>{let h="";if(r.value.conditionType===Ae.EXPRESSION&&r.value.conditionExpression&&(h=`\u8868\u8FBE\u5F0F\uFF1A${r.value.conditionExpression}`),r.value.conditionType===Ae.RULE){const v=F.value.and;let j;const M=F.value.conditions.map(G=>"("+G.rules.map(f=>f.leftSide&&f.rightSide?B(f.leftSide)+" "+H(f.opCode)+" "+f.rightSide:(j="\u8BF7\u5B8C\u5584\u6761\u4EF6\u89C4\u5219","")).join(G.and?" \u4E14 ":" \u6216 ")+" ) ");j?(_.warning(j),h=""):h=M.join(v?" \u4E14 ":" \u6216 ")}return h},C=()=>{},F=P({and:!0,conditions:[{and:!0,rules:[{type:1,opName:"\u7B49\u4E8E",opCode:"==",leftSide:"",rightSide:""}]}]}),T=()=>{F.value.conditions.push({and:!0,rules:[{type:1,opName:"\u7B49\u4E8E",opCode:"==",leftSide:"",rightSide:""}]})},Y=function(){const h=_e("formFields"),v=(j,M,G="")=>{const{field:f,title:W,children:ie,type:ce}=j;if(f&&W){let X=W;G&&(X=`${G}.${W}`),M.push({field:f,title:X,type:ce})}ie&&Array.isArray(ie)&&ie.forEach(X=>{v(X,M)})};return(()=>{const j=[];return h&&h.value.forEach(M=>{v(JSON.parse(M),j)}),j})()}(),B=h=>{const v=Y.find(j=>j.field===h);return v==null?void 0:v.title},H=h=>{const v=wa.find(j=>j.value===h);return v==null?void 0:v.label};return(h,v)=>{const j=fe,M=Ge,G=ze,f=ta,W=na,ie=Ea,ce=oa,X=da,x=il,Pe=nl,Se=ia,o=Ke,ae=He,ke=We,Re=Te("mountedFocus");return l(),y(ke,{"append-to-body":!0,modelValue:e(m),"onUpdate:modelValue":v[6]||(v[6]=K=>de(m)?m.value=K:null),"show-close":!1,size:588,"before-close":c},{header:n(()=>[a("div",wt,[e(t)?ye((l(),u("input",{key:0,type:"text",class:"config-editable-input",onBlur:v[0]||(v[0]=K=>{var ue;return t.value=!1,void(r.value.name=r.value.name||Ia(g.nodeIndex,(ue=r.value)==null?void 0:ue.defaultFlow))}),"onUpdate:modelValue":v[1]||(v[1]=K=>e(r).name=K),placeholder:e(r).name},null,40,Ut)),[[Re],[ge,e(r).name]]):(l(),u("div",Vt,[$(q(e(r).name)+" ",1),i(j,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:v[2]||(v[2]=K=>{t.value=!0})})])),v[7]||(v[7]=a("div",{class:"divide-line"},null,-1))])]),footer:n(()=>[i(o),a("div",null,[i(ae,{type:"primary",onClick:I},{default:n(()=>v[11]||(v[11]=[$("\u786E \u5B9A")])),_:1}),i(ae,{onClick:N},{default:n(()=>v[12]||(v[12]=[$("\u53D6 \u6D88")])),_:1})])]),default:n(()=>[a("div",null,[e(r).defaultFlow?(l(),u("div",Dt,"\u5176\u5B83\u6761\u4EF6\u4E0D\u6EE1\u8DB3\u8FDB\u5165\u6B64\u5206\u652F\uFF08\u8BE5\u5206\u652F\u4E0D\u53EF\u7F16\u8F91\u548C\u5220\u9664\uFF09")):(l(),u("div",It,[i(Se,{ref_key:"formRef",ref:R,model:e(r),rules:e(E),"label-position":"top"},{default:n(()=>[i(f,{label:"\u914D\u7F6E\u65B9\u5F0F",prop:"conditionType"},{default:n(()=>[i(G,{modelValue:e(r).conditionType,"onUpdate:modelValue":v[3]||(v[3]=K=>e(r).conditionType=K),onChange:C},{default:n(()=>[(l(!0),u(L,null,z(e(S),(K,ue)=>(l(),y(M,{key:ue,value:K.value,label:K.value},{default:n(()=>[$(q(K.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r).conditionType===1?(l(),y(f,{key:0,label:"\u6761\u4EF6\u8868\u8FBE\u5F0F",prop:"conditionExpression"},{default:n(()=>[i(W,{type:"textarea",modelValue:e(r).conditionExpression,"onUpdate:modelValue":v[4]||(v[4]=K=>e(r).conditionExpression=K),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):w("",!0),e(r).conditionType===2?(l(),y(f,{key:1,label:"\u6761\u4EF6\u89C4\u5219"},{default:n(()=>[a("div",Ot,[a("div",Ct,[v[8]||(v[8]=a("div",{class:"mr-4"},"\u6761\u4EF6\u7EC4\u5173\u7CFB",-1)),i(ie,{modelValue:e(F).and,"onUpdate:modelValue":v[5]||(v[5]=K=>e(F).and=K),"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue"])])]),i(Pe,{direction:"vertical",spacer:e(F).and?"\u4E14":"\u6216"},{default:n(()=>[(l(!0),u(L,null,z(e(F).conditions,(K,ue)=>(l(),y(x,{class:"condition-group",style:{width:"530px"},key:ue},{header:n(()=>[a("div",At,[v[10]||(v[10]=a("div",null,"\u6761\u4EF6\u7EC4",-1)),a("div",Pt,[v[9]||(v[9]=a("div",{class:"mr-4"},"\u89C4\u5219\u5173\u7CFB",-1)),i(ie,{modelValue:K.and,"onUpdate:modelValue":se=>K.and=se,"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue","onUpdate:modelValue"])])])]),default:n(()=>[e(F).conditions.length>1?(l(),u("div",xt,[i(j,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:se=>{return pe=ue,void F.value.conditions.splice(pe,1);var pe}},null,8,["onClick"])])):w("",!0),(l(!0),u(L,null,z(K.rules,(se,pe)=>(l(),u("div",{class:"flex pt-2",key:pe},[a("div",Ht,[i(X,{style:{width:"160px"},modelValue:se.leftSide,"onUpdate:modelValue":le=>se.leftSide=le},{default:n(()=>[(l(!0),u(L,null,z(e(Y),(le,re)=>(l(),y(ce,{key:re,label:le.title,value:le.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),a("div",Lt,[i(X,{modelValue:se.opCode,"onUpdate:modelValue":le=>se.opCode=le,style:{width:"100px"}},{default:n(()=>[(l(!0),u(L,null,z(e(wa),le=>(l(),y(ce,{key:le.value,label:le.label,value:le.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),a("div",Ft,[i(W,{modelValue:se.rightSide,"onUpdate:modelValue":le=>se.rightSide=le,style:{width:"160px"}},null,8,["modelValue","onUpdate:modelValue"])]),K.rules.length>1?(l(),u("div",Mt,[i(j,{icon:"ep:delete",size:18,onClick:le=>((re,we)=>{re.rules.splice(we,1)})(K,pe)},null,8,["onClick"])])):w("",!0),a("div",jt,[i(j,{icon:"ep:plus",size:18,onClick:le=>((re,we)=>{re.rules.splice(we+1,0,{type:1,opName:"\u7B49\u4E8E",opCode:"==",leftSide:"",rightSide:""})})(K,pe)},null,8,["onClick"])])]))),128))]),_:2},1024))),128))]),_:1},8,["spacer"]),a("div",Bt,[i(j,{color:"#0089ff",icon:"ep:plus",size:24,onClick:T})])]),_:1})):w("",!0)]),_:1},8,["model","rules"])]))])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-e493008e"]]),Gt={class:"branch-node-wrapper"},zt={class:"branch-node-container"},Kt={class:"node-wrapper"},Yt={class:"node-container"},$t={class:"branch-node-title-container"},Wt={key:0},Xt=["onBlur","onUpdate:modelValue"],Qt=["onClick"],Jt={class:"branch-priority"},Zt=["onClick"],eo=["title"],ao={key:1,class:"branch-node-text"},lo={key:0,class:"node-toolbar"},to={class:"toolbar-icon"},oo=["onClick"],no=["onClick"],io=ne({name:"ExclusiveNode",__name:"ExclusiveNode",props:{flowNode:{type:Object,required:!0}},emits:["update:modelValue","find:parentNode","find:recursiveFindParentNode"],setup(A,{emit:O}){const{proxy:_}=ga(),p=A,S=O,g=P(p.flowNode);qe(()=>p.flowNode,c=>{g.value=c});const m=P([]),t=()=>{const c=g.value.conditionNodes;if(c){const E=c.length;let R=E-1;const I={id:"Flow_"+he(),name:"\u6761\u4EF6"+E,showText:"",type:U.CONDITION_NODE,childNode:void 0,conditionNodes:[],conditionType:1,defaultFlow:!1};c.splice(R,0,I)}},r=(c,E)=>{g.value.conditionNodes&&(g.value.conditionNodes[c]=g.value.conditionNodes.splice(c+E,1,g.value.conditionNodes[c])[0])},N=(c,E,R)=>{E&&E.type!==U.START_EVENT_NODE&&(E.type===R&&c.push(E),S("find:parentNode",c,R))};return(c,E)=>{const R=fe,I=Te("mountedFocus");return l(),u("div",Gt,[a("div",zt,[a("div",{class:"branch-node-add",onClick:t},"\u6DFB\u52A0\u6761\u4EF6"),(l(!0),u(L,null,z(e(g).conditionNodes,(k,C)=>{var F,T,Y;return l(),u("div",{class:"branch-node-item",key:C},[C==0?(l(),u(L,{key:0},[E[1]||(E[1]=a("div",{class:"branch-line-first-top"},null,-1)),E[2]||(E[2]=a("div",{class:"branch-line-first-bottom"},null,-1))],64)):w("",!0),C+1==((F=e(g).conditionNodes)==null?void 0:F.length)?(l(),u(L,{key:1},[E[3]||(E[3]=a("div",{class:"branch-line-last-top"},null,-1)),E[4]||(E[4]=a("div",{class:"branch-line-last-bottom"},null,-1))],64)):w("",!0),a("div",Kt,[a("div",Yt,[a("div",{class:Ye(["node-box",{"node-config-error":!k.showText}])},[a("div",$t,[e(m)[C]?(l(),u("div",Wt,[ye(a("input",{type:"text",class:"input-max-width editable-title-input",onBlur:B=>(H=>{var v;m.value[H]=!1;const h=(v=g.value.conditionNodes)==null?void 0:v.at(H);h.name=h.name||Ia(H,h.defaultFlow)})(C),"onUpdate:modelValue":B=>k.name=B},null,40,Xt),[[I],[ge,k.name]])])):(l(),u("div",{key:1,class:"branch-title",onClick:B=>(H=>{m.value[H]=!0})(C)},q(k.name),9,Qt)),a("div",Jt," \u4F18\u5148\u7EA7"+q(C+1),1)]),a("div",{class:"branch-node-content",onClick:B=>{return H=k.id,void _.$refs[H][0].open();var H}},[k.showText?(l(),u("div",{key:0,class:"branch-node-text",title:k.showText},q(k.showText),9,eo)):(l(),u("div",ao,q(e(xe).get(e(U).CONDITION_NODE)),1))],8,Zt),C+1!==((T=e(g).conditionNodes)==null?void 0:T.length)?(l(),u("div",lo,[a("div",to,[i(R,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:B=>(H=>{const h=g.value.conditionNodes;if(h&&(h.splice(H,1),h.length==1)){const v=g.value.childNode;S("update:modelValue",v)}})(C)},null,8,["onClick"])])])):w("",!0),C!=0&&C+1!==((Y=e(g).conditionNodes)==null?void 0:Y.length)?(l(),u("div",{key:1,class:"branch-node-move move-node-left",onClick:B=>r(C,-1)},[i(R,{icon:"ep:arrow-left"})],8,oo)):w("",!0),e(g).conditionNodes&&C<e(g).conditionNodes.length-2?(l(),u("div",{key:2,class:"branch-node-move move-node-right",onClick:B=>r(C,1)},[i(R,{icon:"ep:arrow-right"})],8,no)):w("",!0)],2),i(Ie,{"child-node":k.childNode,"onUpdate:childNode":B=>k.childNode=B},null,8,["child-node","onUpdate:childNode"])])]),i(qt,{"node-index":C,"condition-node":k,ref_for:!0,ref:k.id},null,8,["node-index","condition-node"]),k&&k.childNode?(l(),y(va,{key:2,"parent-node":k,"flow-node":k.childNode,"onUpdate:flowNode":B=>k.childNode=B,"onFind:recursiveFindParentNode":N},null,8,["parent-node","flow-node","onUpdate:flowNode"])):w("",!0)])}),128))]),e(g)?(l(),y(Ie,{key:0,"child-node":e(g).childNode,"onUpdate:childNode":E[0]||(E[0]=k=>e(g).childNode=k)},null,8,["child-node"])):w("",!0)])}}}),so={class:"branch-node-wrapper"},uo={class:"branch-node-container"},ro={class:"node-wrapper"},co={class:"node-container"},po={class:"node-box"},vo={class:"branch-node-title-container"},mo={key:0},fo=["onBlur","onUpdate:modelValue"],yo=["onClick"],Eo=["onClick"],ho=["title"],_o={key:1,class:"branch-node-text"},go={class:"node-toolbar"},No={class:"toolbar-icon"},bo=ne({name:"ParallelNode",__name:"ParallelNode",props:{flowNode:{type:Object,required:!0}},emits:["update:modelValue","find:parentNode","find:recursiveFindParentNode"],setup(A,{emit:O}){const{proxy:_}=ga(),p=A,S=O,g=P(p.flowNode);qe(()=>p.flowNode,N=>{g.value=N});const m=P([]),t=()=>{const N=g.value.conditionNodes;if(N){const c=N.length;let E=c-1;const R={id:"Flow_"+he(),name:"\u5E76\u884C"+c,showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:U.CONDITION_NODE,childNode:void 0,conditionNodes:[]};N.splice(E,0,R)}},r=(N,c,E)=>{c&&c.type!==U.START_EVENT_NODE&&(c.type===E&&N.push(c),S("find:parentNode",N,E))};return(N,c)=>{const E=fe,R=Te("mountedFocus");return l(),u("div",so,[a("div",uo,[a("div",{class:"branch-node-add",onClick:t},"\u6DFB\u52A0\u5206\u652F"),(l(!0),u(L,null,z(e(g).conditionNodes,(I,k)=>{var C;return l(),u("div",{class:"branch-node-item",key:k},[k==0?(l(),u(L,{key:0},[c[1]||(c[1]=a("div",{class:"branch-line-first-top"},null,-1)),c[2]||(c[2]=a("div",{class:"branch-line-first-bottom"},null,-1))],64)):w("",!0),k+1==((C=e(g).conditionNodes)==null?void 0:C.length)?(l(),u(L,{key:1},[c[3]||(c[3]=a("div",{class:"branch-line-last-top"},null,-1)),c[4]||(c[4]=a("div",{class:"branch-line-last-bottom"},null,-1))],64)):w("",!0),a("div",ro,[a("div",co,[a("div",po,[a("div",vo,[e(m)[k]?(l(),u("div",mo,[ye(a("input",{type:"text",class:"input-max-width editable-title-input",onBlur:F=>(T=>{var B;m.value[T]=!1;const Y=(B=g.value.conditionNodes)==null?void 0:B.at(T);Y.name=Y.name||`\u5E76\u884C${T+1}`})(k),"onUpdate:modelValue":F=>I.name=F},null,40,fo),[[R],[ge,I.name]])])):(l(),u("div",{key:1,class:"branch-title",onClick:F=>(T=>{m.value[T]=!0})(k)},q(I.name),9,yo)),c[5]||(c[5]=a("div",{class:"branch-priority"},"\u65E0\u4F18\u5148\u7EA7",-1))]),a("div",{class:"branch-node-content",onClick:F=>{return T=I.id,void _.$refs[T][0].open();var T}},[I.showText?(l(),u("div",{key:0,class:"branch-node-text",title:I.showText},q(I.showText),9,ho)):(l(),u("div",_o,q(e(xe).get(e(U).CONDITION_NODE)),1))],8,Eo),a("div",go,[a("div",No,[i(E,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:F=>(T=>{const Y=g.value.conditionNodes;if(Y&&(Y.splice(T,1),Y.length==1)){const B=g.value.childNode;S("update:modelValue",B)}})(k)},null,8,["onClick"])])])]),i(Ie,{"child-node":I.childNode,"onUpdate:childNode":F=>I.childNode=F},null,8,["child-node","onUpdate:childNode"])])]),I&&I.childNode?(l(),y(va,{key:2,"parent-node":I,"flow-node":I.childNode,"onUpdate:flowNode":F=>I.childNode=F,"onFind:recursiveFindParentNode":r},null,8,["parent-node","flow-node","onUpdate:flowNode"])):w("",!0)])}),128))]),e(g)?(l(),y(Ie,{key:0,"child-node":e(g).childNode,"onUpdate:childNode":c[0]||(c[0]=I=>e(g).childNode=I)},null,8,["child-node"])):w("",!0)])}}}),va=ne({name:"ProcessNodeTree",__name:"ProcessNodeTree",props:{parentNode:{type:Object,default:()=>null},flowNode:{type:Object,default:()=>null}},emits:["update:flowNode","find:recursiveFindParentNode"],setup(A,{emit:O}){const _=A,p=O,S=Oe(_),g=r=>{p("update:flowNode",r)},m=(r,N)=>{p("find:recursiveFindParentNode",r,_.parentNode,N)},t=(r,N,c)=>{N&&(N.type!==U.START_USER_NODE?(N.type===c&&r.push(N),p("find:recursiveFindParentNode",r,_.parentNode,c)):r.push(N))};return(r,N)=>{const c=Ma("ProcessNodeTree",!0);return l(),u(L,null,[e(S)&&e(S).type===e(U).START_USER_NODE?(l(),y(Ol,{key:0,"flow-node":e(S)},null,8,["flow-node"])):w("",!0),e(S)&&e(S).type===e(U).USER_TASK_NODE?(l(),y(st,{key:1,"flow-node":e(S),"onUpdate:flowNode":g,"onFind:parentNode":m},null,8,["flow-node"])):w("",!0),e(S)&&e(S).type===e(U).COPY_TASK_NODE?(l(),y(Rt,{key:2,"flow-node":e(S),"onUpdate:flowNode":g},null,8,["flow-node"])):w("",!0),e(S)&&e(S).type===e(U).CONDITION_BRANCH_NODE?(l(),y(io,{key:3,"flow-node":e(S),"onUpdate:modelValue":g,"onFind:parentNode":m},null,8,["flow-node"])):w("",!0),e(S)&&e(S).type===e(U).PARALLEL_BRANCH_NODE?(l(),y(bo,{key:4,"flow-node":e(S),"onUpdate:modelValue":g,"onFind:parentNode":m},null,8,["flow-node"])):w("",!0),e(S)&&e(S).childNode?(l(),y(c,{key:5,"flow-node":e(S).childNode,"onUpdate:flowNode":N[0]||(N[0]=E=>e(S).childNode=E),"parent-node":e(S),"onFind:recursiveFindParentNode":t},null,8,["flow-node","parent-node"])):w("",!0),e(S)&&e(S).type===e(U).END_EVENT_NODE?(l(),y(xl,{key:6})):w("",!0)],64)}}}),To={class:"simple-flow-canvas"},So={class:"simple-flow-container"},ko={class:"top-area-container"},Ro={class:"top-actions"},wo={class:"canvas-control"},Uo={class:"control-scale-group"},Vo={class:"control-scale-button"},Do={class:"control-scale-label"},Io={class:"control-scale-button"},Oo=ne({name:"SimpleProcessDesigner",__name:"SimpleProcessDesigner",props:{modelId:{type:String,required:!0}},setup(A){const O=Ka(),_=A,p=P(!0),S=P([]),g=P(20),m=P([]),t=P([]),r=P([]),N=P([]),c=P(),E=P([]);Ve("formFields",S),Ve("formType",g),Ve("roleList",m),Ve("postList",t),Ve("userList",r),Ve("deptList",N),Ve("userGroupList",E),Ve("deptTree",c);const R=_a(),I=P(),k=P(!1);let C=[];const F=async()=>{if(!_.modelId)return void R.error("\u7F3A\u5C11\u6A21\u578B modelId \u7F16\u53F7");if(C=[],T(I.value,C),C.length>0)return void(k.value=!0);const H={id:_.modelId,simpleModel:I.value};await(async v=>await Na.post({url:"/bpm/model/simple/update",data:v}))(H)?(R.success("\u4FEE\u6539\u6210\u529F"),Y()):R.alert("\u4FEE\u6539\u5931\u8D25")},T=(H,h)=>{if(H){const{type:v,showText:j,conditionNodes:M}=H;if(v==U.END_EVENT_NODE)return;v==U.START_USER_NODE&&T(H.childNode,h),v===U.USER_TASK_NODE&&(j||h.push(H),T(H.childNode,h)),v===U.COPY_TASK_NODE&&(j||h.push(H),T(H.childNode,h)),v===U.CONDITION_NODE&&(j||h.push(H),T(H.childNode,h)),v==U.CONDITION_BRANCH_NODE&&(M==null||M.forEach(G=>{T(G,h)}),T(H.childNode,h))}},Y=()=>{O.push({path:"/bpm/manager/model"})};let B=P(100);return ja(async()=>{try{p.value=!0;const H=await sl(_.modelId);if(H&&(g.value=H.formType,g.value===10)){const v=await ul(H.formId);S.value=v==null?void 0:v.fields}m.value=await rl(),t.value=await cl(),r.value=await Ba(),N.value=await qa(),c.value=Ga(N.value,"id"),E.value=await pl();const h=await(async v=>await Na.get({url:"/bpm/model/simple/get?id="+v}))(_.modelId);I.value=h||{name:"\u53D1\u8D77\u4EBA",type:U.START_USER_NODE,id:Ua.START_USER_NODE_ID,childNode:{id:Ua.END_EVENT_NODE_ID,name:"\u7ED3\u675F",type:U.END_EVENT_NODE}}}finally{p.value=!1}}),(H,h)=>{const v=fe,j=He,M=Wa,G=Ya;return ye((l(),u("div",To,[a("div",So,[a("div",ko,[a("div",Ro,[a("div",wo,[a("span",Uo,[a("span",Vo,[i(v,{icon:"ep:plus",onClick:h[0]||(h[0]=f=>{B.value!=200&&(B.value+=10)})})]),a("span",Do,q(e(B))+"%",1),a("span",Io,[i(v,{icon:"ep:minus",onClick:h[1]||(h[1]=f=>{B.value!=50&&(B.value-=10)})})])])]),i(j,{type:"primary",onClick:F},{default:n(()=>h[5]||(h[5]=[$("\u4FDD\u5B58")])),_:1})])]),a("div",{class:"scale-container",style:za(`transform: scale(${e(B)/100});`)},[e(I)?(l(),y(va,{key:0,"flow-node":e(I),"onUpdate:flowNode":h[2]||(h[2]=f=>de(I)?I.value=f:null)},null,8,["flow-node"])):w("",!0)],4)]),i(M,{modelValue:e(k),"onUpdate:modelValue":h[4]||(h[4]=f=>de(k)?k.value=f:null),title:"\u4FDD\u5B58\u5931\u8D25",width:"400",fullscreen:!1},{footer:n(()=>[i(j,{type:"primary",onClick:h[3]||(h[3]=f=>k.value=!1)},{default:n(()=>h[6]||(h[6]=[$("\u77E5\u9053\u4E86")])),_:1})]),default:n(()=>[h[7]||(h[7]=a("div",{class:"mb-2"},"\u4EE5\u4E0B\u8282\u70B9\u5185\u5BB9\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u4FEE\u6539\u540E\u4FDD\u5B58",-1)),(l(!0),u(L,null,z(e(C),(f,W)=>(l(),u("div",{class:"mb-3 b-rounded-1 bg-gray-100 p-2 line-height-normal",key:W},q(f.name)+" : "+q(e(xe).get(f.type)),1))),128))]),_:1},8,["modelValue"])])),[[G,e(p)]])}}}),Co=ne({name:"SimpleWorkflowDesignEditor",__name:"index",setup(A){const{query:O}=$a(),_=O.modelId;return(p,S)=>(l(),y(e(Oo),{"model-id":e(_)},null,8,["model-id"]))}});export{Co as default};
