import{aG as p,d as N,j as u,y as P,p as U,f as A,o as c,h as m,w as r,x as B,u as i,k as w,c as F,F as G,g as H,v as L,z as O,A as q,H as D,L as E}from"./index-C8b06LRn.js";import{_ as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as J}from"./index-COdQIXZX.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";const K=N({__name:"index",setup(M){const d=u([]),v=u(0),y=u([]),g=u([]),n=u(!1),b=P(),{t:h}=U(),l=u({pageNo:1,pageSize:30}),f=(e,a)=>{for(let t in e)e[t]?l.value[t]=e[t]:l.value.hasOwnProperty(t)&&delete l.value[t];a||(l.value.pageNo=1),k()},k=async()=>{n.value=!0;try{const e=await(async a=>await p.get({url:"/butt-joint/ekuaibao/dept/page",params:a}))(l.value);d.value=e.list,v.value=e.total}finally{n.value=!1}},_=async()=>{const e=await(async()=>await p.get({url:"/butt-joint/ekuaibao/dept/get-columns"}))();y.value=e;const a=await(async()=>await p.get({url:"/butt-joint/ekuaibao/dept/get-erp-all-dept"}))();g.value=a},j=async e=>{n.value=!0;try{await(async a=>await p.post({url:"/butt-joint/ekuaibao/dept/save",data:a}))(e),b.success(h("common.updateSuccess"))}finally{n.value=!1}};return A(()=>{_(),k()}),(e,a)=>{const t=O,C=q,V=D,x=J,z=I,S=E;return c(),m(z,null,{default:r(()=>[B((c(),m(x,{data:i(d),total:i(v),columns:i(y),page:i(l),onSearch:f,onPagination:a[0]||(a[0]=o=>f(o,!0)),stripe:"","highlight-current":"",height:"calc(100vh - 350px)"},{u9cCode:r(({row:o})=>[w(C,{modelValue:o.u9cCode,"onUpdate:modelValue":s=>o.u9cCode=s,filterable:"",size:"small","collapse-tags":"",multiple:""},{default:r(()=>[(c(!0),F(G,null,H(i(g),s=>(c(),m(t,{key:s.key,label:s.value,value:s.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),operation:r(({row:o})=>[w(V,{type:"primary",size:"small",link:"",onClick:s=>j(o)},{default:r(()=>a[1]||(a[1]=[L(" \u4FDD\u5B58 ")])),_:2},1032,["onClick"])]),_:1},8,["data","total","columns","page"])),[[S,i(n)]])]),_:1})}}});export{K as default};
