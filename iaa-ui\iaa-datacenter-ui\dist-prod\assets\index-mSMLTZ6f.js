import{_ as T}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as A,j as d,r as N,f as R,o as h,c as y,k as l,w as s,l as z,u as a,ao as j,v as m,F as _,g as G,h as H,t as L,N as P,D as J,m as K,p as Q,y as W,a as X,O as Y,G as Z,H as $,S as ee,B as ae,ap as le,aq as se,I as oe}from"./index-C8b06LRn.js";import{_ as te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{C as re}from"./constants-D3f7Z3TX.js";import{g as ue,c as de,u as ne}from"./index-CBsqkafF.js";import{s as me,e as ie,a as pe}from"./formCreate-CdPDb26P.js";import{u as ce}from"./tagsView-D-HCnpxr.js";import{u as fe}from"./useFormCreateDesigner-5Md06TW6.js";import"./el-card-CaOo8U9P.js";import"./dict.type-Bqd7OQxQ.js";const he={class:"h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-2px)]"},ge=A({name:"BpmFormEditor",__name:"index",setup(ve){const{t:g}=Q(),v=W(),{push:V,currentRoute:b}=X(),{query:C}=Y(),{delView:k}=ce(),x=d({switchType:[],autoActive:!0,useTemplate:!1,formOptions:{},fieldReadonly:!1,hiddenDragMenu:!1,hiddenDragBtn:!1,hiddenMenu:[],hiddenItem:[],hiddenItemConfig:{},disabledItemConfig:{},showSaveBtn:!1,showConfig:!0,showBaseForm:!0,showControl:!0,showPropsForm:!0,showEventForm:!0,showValidateForm:!0,showFormConfig:!0,showInputData:!0,showDevice:!0,appendConfigData:[]}),n=d();fe(n);const u=d(!1),i=d(!1),t=d({name:"",status:re.ENABLE,remark:""}),F=N({name:[{required:!0,message:"\u8868\u5355\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=d(),B=()=>{u.value=!0},D=async()=>{if(p&&await p.value.validate()){i.value=!0;try{const r=t.value;r.conf=ie(n),r.fields=pe(n),r.id?(await ne(r),v.success(g("common.updateSuccess"))):(await de(r),v.success(g("common.createSuccess"))),u.value=!1,I()}finally{i.value=!1}}},I=()=>{k(a(b)),V("/bpm/manager/form")};return R(async()=>{const r=C.id;if(!r)return;const e=await ue(r);t.value=e,me(n,e.conf,e.fields)}),(r,e)=>{const S=Z,c=$,U=te,w=ee,f=ae,q=le,E=se,M=oe,O=T;return h(),y(_,null,[l(U,{"body-style":{padding:"0px"},class:"!mb-0"},{default:s(()=>[z("div",he,[l(a(j),{class:"my-designer",ref_key:"designer",ref:n,config:a(x)},{handle:s(()=>[l(c,{size:"small",type:"success",plain:"",onClick:B},{default:s(()=>[l(S,{class:"mr-5px",icon:"ep:plus"}),e[5]||(e[5]=m(" \u4FDD\u5B58 "))]),_:1})]),_:1},8,["config"])])]),_:1}),l(O,{modelValue:a(u),"onUpdate:modelValue":e[4]||(e[4]=o=>K(u)?u.value=o:null),title:"\u4FDD\u5B58\u8868\u5355",width:"600"},{footer:s(()=>[l(c,{disabled:a(i),type:"primary",onClick:D},{default:s(()=>e[6]||(e[6]=[m("\u786E \u5B9A")])),_:1},8,["disabled"]),l(c,{onClick:e[3]||(e[3]=o=>u.value=!1)},{default:s(()=>e[7]||(e[7]=[m("\u53D6 \u6D88")])),_:1})]),default:s(()=>[l(M,{ref_key:"formRef",ref:p,model:a(t),rules:a(F),"label-width":"80px"},{default:s(()=>[l(f,{label:"\u8868\u5355\u540D",prop:"name"},{default:s(()=>[l(w,{modelValue:a(t).name,"onUpdate:modelValue":e[0]||(e[0]=o=>a(t).name=o),placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D"},null,8,["modelValue"])]),_:1}),l(f,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[l(E,{modelValue:a(t).status,"onUpdate:modelValue":e[1]||(e[1]=o=>a(t).status=o)},{default:s(()=>[(h(!0),y(_,null,G(a(P)(a(J).COMMON_STATUS),o=>(h(),H(q,{key:o.value,value:o.value},{default:s(()=>[m(L(o.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[l(w,{modelValue:a(t).remark,"onUpdate:modelValue":e[2]||(e[2]=o=>a(t).remark=o),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}});export{ge as default};
