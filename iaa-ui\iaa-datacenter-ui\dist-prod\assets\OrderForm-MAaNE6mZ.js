import{N as V}from"./index-WiqCEeob.js";import{d as A,j as g,o as n,h as x,w as e,k as l,u as a,m as O,v as o,t as s,c as m,F as w,cj as y,l as k,g as j,$ as z,B as D,I as F,dH as N,aM as W,_ as I}from"./index-C8b06LRn.js";import{E,a as L}from"./el-collapse-item-JANV_ocl.js";import"./el-drawer-C5TFtzfV.js";const Q={style:{"min-height":"50px"}},U={style:{"min-height":"20px"}},B={style:{"min-height":"20px"}},H={style:{"min-height":"20px"}},q=I(A({__name:"OrderForm",setup(C,{expose:P}){const u=g(),h=g(""),v=g(!1),Y=g(["info","order1","order2","order3","order4","opportunity1","opportunity2","opportunity3","opportunity4","opportunity5","customer1","customer2"]),d=p=>typeof p=="number"?$(p.toFixed(0)):p&&typeof p.cellValue=="number"?$(p.cellValue.toFixed(0)):"",$=p=>parseFloat(p).toString().split(".").map((b,t)=>t?b:b.split("").reverse().map((c,i)=>!i||i%3?c:c+",").reverse().join("")).join(".");return P({open:(p,b)=>{if(u.value=p,v.value=!0,h.value=b,b==="order")for(let t in p.orderTotalList)Y.value.push(t)}}),(p,b)=>{const t=D,c=F,i=E,S=N,M=W,T=L,R=V;return n(),x(R,{modelValue:a(v),"onUpdate:modelValue":b[1]||(b[1]=_=>O(v)?v.value=_:null)},{default:e(()=>[l(T,{modelValue:a(Y),"onUpdate:modelValue":b[0]||(b[0]=_=>O(Y)?Y.value=_:null),ref:"collapseRef"},{default:e(()=>{var _;return[l(i,{title:"\u57FA\u672C\u4FE1\u606F",name:"info"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:"\u5BA2\u6237\u540D\u79F0:",class:"!w-100%"},{default:e(()=>[o(s(a(u).customerName),1)]),_:1}),l(t,{label:"\u4E1A\u52A1\u5458:",class:"!w-33%"},{default:e(()=>{var r;return[o(s((r=a(u).salesperson)==null?void 0:r.join(",")),1)]}),_:1}),l(t,{label:"\u6240\u5C5E\u7EC4\u522B:",class:"!w-33%"},{default:e(()=>[o(s(a(u).departmentName),1)]),_:1}),l(t,{label:"\u56FD\u5BB6/\u5730\u533A:",class:"!w-33%"},{default:e(()=>[o(s(a(u).countryName),1)]),_:1}),l(t,{label:"\u6D32/\u7701:",class:"!w-33%"},{default:e(()=>[o(s(a(u).regionOrProvince),1)]),_:1}),l(t,{label:"\u57CE\u5E02:",class:"!w-33%"},{default:e(()=>[o(s(a(u).city),1)]),_:1}),l(t,{label:"\u5BA2\u6237\u753B\u50CF:",class:"!w-33%"},{default:e(()=>[o(s(a(u).customerPortraits),1)]),_:1}),l(t,{label:"\u5BA2\u6237\u6027\u8D28:",class:"!w-33%"},{default:e(()=>[o(s(a(u).nature),1)]),_:1}),l(t,{label:"\u5408\u4F5C\u9636\u6BB5:",class:"!w-33%"},{default:e(()=>[o(s(a(u).trailStatus),1)]),_:1}),l(t,{label:"\u9884\u4F30\u5E74\u9500(\u4E07):",class:"!w-33%"},{default:e(()=>[o(s(a(u).estimatedAnnualSales),1)]),_:1}),l(t,{label:"\u8425\u6536\u89C4\u6A21:",class:"!w-33%"},{default:e(()=>[o(s(a(u).revenueScale),1)]),_:1}),l(t,{label:"\u9999\u6C1B\u89C4\u6A21:",class:"!w-33%"},{default:e(()=>[o(s(a(u).fragranceRevenueScale),1)]),_:1}),l(t,{label:"\u5BA2\u6237\u5B98\u7F51:",class:"!w-100%"},{default:e(()=>[o(s(a(u).homepage),1)]),_:1})]),_:1})]),_:1}),a(h)==="order"?(n(),m(w,{key:0},[l(i,{title:"\u8FD1\u4E09\u5E74\u9500\u552E\u989D",name:"order1"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:`${a(y)().subtract(2,"years").format("YYYY")}\u5E74\u9500\u552E:`,class:"!w-33%"},{default:e(()=>{var r,f;return[o(s(d((f=(r=a(u))==null?void 0:r.yearOrderMap)==null?void 0:f[a(y)().subtract(2,"years").format("YYYY")])),1)]}),_:1},8,["label"]),l(t,{label:`${a(y)().subtract(1,"years").format("YYYY")}\u5E74\u9500\u552E:`,class:"!w-33%"},{default:e(()=>{var r,f;return[o(s(d((f=(r=a(u))==null?void 0:r.yearOrderMap)==null?void 0:f[a(y)().subtract(1,"years").format("YYYY")])),1)]}),_:1},8,["label"]),l(t,{label:`${a(y)().format("YYYY")}\u5E74\u9500\u552E:`,class:"!w-33%"},{default:e(()=>{var r,f;return[o(s(d((f=(r=a(u))==null?void 0:r.yearOrderMap)==null?void 0:f[a(y)().format("YYYY")])),1)]}),_:1},8,["label"])]),_:1})]),_:1}),l(i,{title:"\u7ADF\u5BF9\u60C5\u51B5",name:"order2"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small"},{default:e(()=>[l(t,{class:"!w-100%"},{default:e(()=>[k("div",Q,s(a(u).corrival),1)]),_:1})]),_:1})]),_:1}),l(i,{title:"\u9500\u552E\u989D\u9884\u6D4B\u53CA\u8FBE\u6807\u60C5\u51B5",name:"order3"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:"2024\u9884\u6D4B:",class:"!w-50%"},{default:e(()=>[o(s(d(a(u).tztfourPredictionSales)),1)]),_:1}),l(t,{label:"2024\u8FBE\u6807:",class:"!w-50%"},{default:e(()=>[o(s(d(a(u).tztfourReachingStandard)),1)]),_:1}),l(t,{label:"2025\u9884\u6D4B:",class:"!w-50%"},{default:e(()=>[o(s(d(a(u).tztfivePredictionSales)),1)]),_:1}),l(t,{label:"2025\u8FBE\u6807:",class:"!w-50%"},{default:e(()=>[o(s(d(a(u).tztfiveReachingStandard)),1)]),_:1})]),_:1})]),_:1}),(n(!0),m(w,null,j((_=a(u))==null?void 0:_.orderTotalList,(r,f)=>(n(),x(i,{title:`${f}\u9500\u552E\u6570\u636E`,key:f,name:f},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:"\u9884\u6D4B\u9500\u91CF:",class:"!w-33%"},{default:e(()=>[o(s(d(r.forecastQty)),1)]),_:2},1024),l(t,{label:"\u9884\u6D4B\u91D1\u989D:",class:"!w-33%"},{default:e(()=>[o(s(d(r.forecastAmount)),1)]),_:2},1024),l(t,{label:"\u7B2C\u4E00\u5468:",class:"!w-33%"},{default:e(()=>[o(s(d(r.oneWeekAmount)),1)]),_:2},1024),l(t,{label:"\u7B2C\u4E8C\u5468:",class:"!w-33%"},{default:e(()=>[o(s(d(r.twoWeekAmount)),1)]),_:2},1024),l(t,{label:"\u7B2C\u4E09\u5468:",class:"!w-33%"},{default:e(()=>[o(s(d(r.threeWeekAmount)),1)]),_:2},1024),l(t,{label:"\u7B2C\u56DB\u5468:",class:"!w-33%"},{default:e(()=>[o(s(d(r.fourWeekAmount)),1)]),_:2},1024),l(t,{label:"\u603B\u9500\u91CF:",class:"!w-33%"},{default:e(()=>[o(s(d(r.totalQty)),1)]),_:2},1024),l(t,{label:"\u603B\u9500\u552E\u989D:",class:"!w-33%"},{default:e(()=>[o(s(d(r.totalAmount)),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["title","name"]))),128)),l(i,{title:"\u56DE\u6B3E\u60C5\u51B5",name:"order4"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[(n(!0),m(w,null,j(a(u).paymentMap,(r,f)=>(n(),x(t,{key:f,label:`${f}`,class:"!w-33%"},{default:e(()=>[o(s(d(r)),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1})],64)):a(h)==="opportunity"?(n(),m(w,{key:1},[l(i,{title:"\u7ADF\u5BF9\u673A\u5668\u63CF\u8FF0",name:"opportunity1"},{default:e(()=>[k("div",U,s(a(u).untoMachineRemark),1)]),_:1}),l(i,{title:"\u7ADF\u5BF9\u7CBE\u6CB9\u7C7B\u6570\u636E\u63CF\u8FF0",name:"opportunity2"},{default:e(()=>[k("div",B,s(a(u).untoEssentialOilRemark),1)]),_:1}),l(i,{title:"\u76F8\u5173\u6027\u4EA7\u54C1\u63CF\u8FF0",name:"opportunity3"},{default:e(()=>[k("div",H,s(a(u).relevantProductRemark),1)]),_:1}),l(i,{title:"\u5408\u4F5C\u673A\u5668\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143)",name:"opportunity4"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[(n(),m(w,null,j([1,2,3,4,5],(r,f)=>(n(),m(w,{key:f},[l(t,{label:`\u4EA7\u54C1${r}\u578B\u53F7:`,class:"!w-50%"},{default:e(()=>[o(s(a(u)[`machineModel${r}`]),1)]),_:2},1032,["label"]),l(t,{label:`\u4EA7\u54C1${r}\u91D1\u989D:`,class:"!w-50%"},{default:e(()=>[o(s(d(a(u)[`machinePrice${r}`])),1)]),_:2},1032,["label"])],64))),64))]),_:1})]),_:1}),l(i,{title:"\u5408\u4F5C\u7CBE\u6CB9\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143)",name:"opportunity5"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[(n(),m(w,null,j([1,2,3,4,5],(r,f)=>(n(),m(w,{key:f},[l(t,{label:`\u4EA7\u54C1${r}\u9999\u578B:`,class:"!w-50%"},{default:e(()=>[o(s(a(u)[`oilModel${r}`]),1)]),_:2},1032,["label"]),l(t,{label:`\u4EA7\u54C1${r}\u91D1\u989D:`,class:"!w-50%"},{default:e(()=>[o(s(d(a(u)[`oilModel${r}`])),1)]),_:2},1032,["label"])],64))),64))]),_:1})]),_:1})],64)):a(h)==="customer"?(n(),m(w,{key:2},[l(i,{title:"\u5F00\u53D1\u4FE1\u606F",name:"customer1"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:"\u5F00\u53D1\u7C7B\u578B:",class:"!w-33%"},{default:e(()=>[o(s(a(u).developmentType),1)]),_:1}),l(t,{label:"\u4F5C\u6218\u4EA7\u54C1:",class:"!w-33%"},{default:e(()=>[o(s(a(u).combatProduct),1)]),_:1}),l(t,{label:"\u5F00\u53D1\u76EE\u6807:",class:"!w-33%"},{default:e(()=>[o(s(a(u).developmentTarget),1)]),_:1}),l(t,{label:"\u9884\u8BA1\u5F00\u53D1\u5929\u6570:",class:"!w-33%"},{default:e(()=>[o(s(a(u).planDevelopmentDays),1)]),_:1}),l(t,{label:"\u5F00\u59CB\u65F6\u95F4:",class:"!w-33%"},{default:e(()=>[o(s(a(u).createTime),1)]),_:1}),l(t,{label:"\u5DF2\u5F00\u53D1\u5929\u6570:",class:"!w-33%"},{default:e(()=>[o(s(a(u).developmentDays),1)]),_:1}),l(t,{label:"\u65F6\u95F4\u8FDB\u5EA6:",class:"!w-100%"},{default:e(()=>[l(S,{class:"!w-100%",percentage:a(u).progress,color:a(u).hasOverdue?"red":"green"},null,8,["percentage","color"])]),_:1})]),_:1})]),_:1}),l(i,{title:"\u7ED3\u679C\u4FE1\u606F",name:"customer2"},{default:e(()=>[l(c,{class:"flex flex-wrap justify-between",size:"small","label-position":"left","label-width":"90px"},{default:e(()=>[l(t,{label:"\u8FDB\u5C55:",class:"!w-33%"},{default:e(()=>[o(s(a(u).stageInfo),1)]),_:1}),l(t,{label:"\u7ED3\u679C:",class:"!w-33%"},{default:e(()=>[a(u).result==="\u8D62\u5355"?(n(),x(M,{key:0,type:"success"},{default:e(()=>[o(s(a(u).result),1)]),_:1})):z("",!0),a(u).result==="\u8F93\u5355"?(n(),x(M,{key:1,type:"danger"},{default:e(()=>[o(s(a(u).result),1)]),_:1})):z("",!0)]),_:1}),l(t,{label:"\u5931\u8D25\u539F\u56E0:",class:"!w-33%"},{default:e(()=>[o(s(a(u).failTypeName),1)]),_:1}),l(t,{label:"\u5931\u8D25\u63CF\u8FF0:",class:"!w-100%"},{default:e(()=>[o(s(a(u).failRemark),1)]),_:1}),l(t,{label:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F:",class:"!w-33%"},{default:e(()=>[o(s(a(u).accountDate),1)]),_:1}),l(t,{label:"\u5B9E\u9645\u7528\u65F6(\u5929):",class:"!w-33%"},{default:e(()=>[o(s(a(u).accountDays),1)]),_:1})]),_:1})]),_:1})],64)):z("",!0)]}),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-859333c1"]]);export{q as default};
