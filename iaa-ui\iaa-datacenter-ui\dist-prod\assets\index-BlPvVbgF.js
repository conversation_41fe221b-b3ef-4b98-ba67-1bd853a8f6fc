import{d as N,a as Y,j as s,r as z,f as F,o as T,c as H,k as e,w as r,u as a,M as K,v as p,x as P,h as q,D as A,t as j,F as G,S as J,B as L,C as R,G as E,H as O,I as Q,J as W,K as X,L as Z}from"./index-C8b06LRn.js";import{_ as $}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ee}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as te}from"./index-CkzUfjB7.js";import{d as w,a as le}from"./formatTime-COZ9Bl52.js";import{h as re}from"./index-BwETMpJ2.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";const oe=N({name:"BpmTodoTask",__name:"index",setup(ne){const{push:x}=Y(),d=s(!0),_=s(0),b=s([]),n=z({pageNo:1,pageSize:10,name:"",createTime:[]}),v=s(),m=async()=>{d.value=!0;try{const u=await re(n);b.value=u.list,_.value=u.total}finally{d.value=!1}},c=()=>{n.pageNo=1,m()},I=()=>{v.value.resetFields(),c()};return F(()=>{m()}),(u,t)=>{const i=te,S=J,f=L,V=R,k=E,g=O,D=Q,y=ae,o=W,M=ee,U=X,B=$,C=Z;return T(),H(G,null,[e(i,{title:"\u5BA1\u6279\u901A\u8FC7\u3001\u4E0D\u901A\u8FC7\u3001\u9A73\u56DE",url:"https://doc.iocoder.cn/bpm/task-todo-done/"}),e(i,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(i,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),e(i,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(y,null,{default:r(()=>[e(D,{ref_key:"queryFormRef",ref:v,inline:!0,model:a(n),class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(f,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:r(()=>[e(S,{modelValue:a(n).name,"onUpdate:modelValue":t[0]||(t[0]=l=>a(n).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:K(c,["enter"])},null,8,["modelValue"])]),_:1}),e(f,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(V,{modelValue:a(n).createTime,"onUpdate:modelValue":t[1]||(t[1]=l=>a(n).createTime=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(f,null,{default:r(()=>[e(g,{onClick:c},{default:r(()=>[e(k,{class:"mr-5px",icon:"ep:search"}),t[4]||(t[4]=p(" \u641C\u7D22 "))]),_:1}),e(g,{onClick:I},{default:r(()=>[e(k,{class:"mr-5px",icon:"ep:refresh"}),t[5]||(t[5]=p(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(y,null,{default:r(()=>[P((T(),q(U,{data:a(b)},{default:r(()=>[e(o,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(o,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(o,{formatter:a(w),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(o,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(o,{formatter:a(w),align:"center",label:"\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(o,{formatter:a(w),align:"center",label:"\u4EFB\u52A1\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),e(o,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:r(l=>[e(M,{type:a(A).BPM_TASK_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(o,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"180"}),e(o,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:r(l=>[p(j(a(le)(l.row.durationInMillis)),1)]),_:1}),e(o,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(o,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(o,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:r(l=>[e(g,{link:"",type:"primary",onClick:ie=>{return h=l.row,void x({name:"BpmProcessInstanceDetail",query:{id:h.processInstance.id,taskId:h.id}});var h}},{default:r(()=>t[6]||(t[6]=[p("\u5386\u53F2")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[C,a(d)]]),e(B,{limit:a(n).pageSize,"onUpdate:limit":t[2]||(t[2]=l=>a(n).pageSize=l),page:a(n).pageNo,"onUpdate:page":t[3]||(t[3]=l=>a(n).pageNo=l),total:a(_),onPagination:m},null,8,["limit","page","total"])]),_:1})],64)}}});export{oe as default};
