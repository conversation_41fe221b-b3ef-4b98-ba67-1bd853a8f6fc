import{d as G,y as L,p as P,j as o,r as $,f as E,aH as Q,T as W,o as u,c as U,k as e,w as l,u as t,M as X,F as A,g as Y,N as Z,D as F,h as i,v as p,x as _,t as ee,$ as ae,ed as le,aJ as se,aO as te,ee as re,S as oe,B as ue,z as pe,A as ne,G as de,H as ie,I as me,J as ce,K as fe,L as ye}from"./index-C8b06LRn.js";import{_ as _e}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as ke}from"./formatTime-COZ9Bl52.js";import{_ as we}from"./DeptForm.vue_vue_type_script_setup_true_lang-Csm3SO4V.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./el-tree-select-E9FCZb0j.js";import"./constants-D3f7Z3TX.js";const be=G({name:"SystemDept",__name:"index",setup(xe){const S=L(),{t:K}=P(),v=o(!0),g=o(),r=$({pageNo:1,pageSize:100,name:void 0,status:void 0}),T=o(),k=o(!0),w=o(!0),V=o([]),m=async()=>{v.value=!0;try{const c=await le(r);g.value=se(c)}finally{v.value=!1}},j=()=>{w.value=!1,k.value=!k.value,te(()=>{w.value=!0})},b=()=>{m()},z=()=>{r.pageNo=1,T.value.resetFields(),b()},M=o(),N=(c,a)=>{M.value.open(c,a)};return E(async()=>{await m(),V.value=await Q()}),(c,a)=>{const D=oe,x=ue,H=pe,I=ne,f=de,n=ie,J=me,O=ve,d=ce,R=_e,q=fe,C=W("hasPermi"),B=ye;return u(),U(A,null,[e(O,null,{default:l(()=>[e(J,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"68px"},{default:l(()=>[e(x,{label:"\u90E8\u95E8\u540D\u79F0",prop:"name"},{default:l(()=>[e(D,{modelValue:t(r).name,"onUpdate:modelValue":a[0]||(a[0]=s=>t(r).name=s),placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0",clearable:"",onKeyup:X(b,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(x,{label:"\u90E8\u95E8\u72B6\u6001",prop:"status"},{default:l(()=>[e(I,{modelValue:t(r).status,"onUpdate:modelValue":a[1]||(a[1]=s=>t(r).status=s),placeholder:"\u8BF7\u9009\u62E9\u90E8\u95E8\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(u(!0),U(A,null,Y(t(Z)(t(F).COMMON_STATUS),s=>(u(),i(H,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(x,null,{default:l(()=>[e(n,{onClick:b},{default:l(()=>[e(f,{icon:"ep:search",class:"mr-5px"}),a[3]||(a[3]=p(" \u641C\u7D22"))]),_:1}),e(n,{onClick:z},{default:l(()=>[e(f,{icon:"ep:refresh",class:"mr-5px"}),a[4]||(a[4]=p(" \u91CD\u7F6E"))]),_:1}),_((u(),i(n,{type:"primary",plain:"",onClick:a[2]||(a[2]=s=>N("create"))},{default:l(()=>[e(f,{icon:"ep:plus",class:"mr-5px"}),a[5]||(a[5]=p(" \u65B0\u589E "))]),_:1})),[[C,["system:dept:create"]]]),e(n,{type:"danger",plain:"",onClick:j},{default:l(()=>[e(f,{icon:"ep:sort",class:"mr-5px"}),a[6]||(a[6]=p(" \u5C55\u5F00/\u6298\u53E0 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(O,null,{default:l(()=>[t(w)?_((u(),i(q,{key:0,data:t(g),"row-key":"id","default-expand-all":t(k)},{default:l(()=>[e(d,{prop:"name",label:"\u90E8\u95E8\u540D\u79F0"}),e(d,{prop:"leader",label:"\u8D1F\u8D23\u4EBA"},{default:l(s=>{var y;return[p(ee((y=t(V).find(h=>h.id===s.row.leaderUserId))==null?void 0:y.nickname),1)]}),_:1}),e(d,{prop:"sort",label:"\u6392\u5E8F"}),e(d,{prop:"status",label:"\u72B6\u6001"},{default:l(s=>[e(R,{type:t(F).COMMON_STATUS,value:s.row.status},null,8,["type","value"])]),_:1}),e(d,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(ke)},null,8,["formatter"]),e(d,{label:"\u64CD\u4F5C",align:"center"},{default:l(s=>[_((u(),i(n,{link:"",type:"primary",onClick:y=>N("update",s.row.id)},{default:l(()=>a[7]||(a[7]=[p(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[C,["system:dept:update"]]]),_((u(),i(n,{link:"",type:"danger",onClick:y=>(async h=>{try{await S.delConfirm(),await re(h),S.success(K("common.delSuccess")),await m()}catch{}})(s.row.id)},{default:l(()=>a[8]||(a[8]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[C,["system:dept:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[B,t(v)]]):ae("",!0)]),_:1}),e(we,{ref_key:"formRef",ref:M,onSuccess:m},null,512)],64)}}});export{be as default};
