import{d as oe,y as ue,j as s,Y as ne,au as re,o as C,c as F,k as e,w as l,v as c,u as h,x as Y,h as I,l as w,t as j,F as E,g as ie,N as de,D as me,m as H,S as se,B as ce,a8 as pe,ab as ve,az as he,H as fe,J as ye,K as be,z as _e,A as ge,I as we,cy as Ae,L as Ce,_ as xe}from"./index-C8b06LRn.js";import{C as J}from"./claim-BPjhJe4B.js";const Ne={class:"mt--8px"},Ve={class:"mt--8px"},ke={class:"mb-15px"},De=xe(oe({__name:"replacementDialogPC",props:{show:{type:Boolean},maxAmount:{},id:{},customerCode:{},claimDetail:{}},emits:["update:show","success"],setup(K,{emit:M}){const p=ue(),n=K,L=M,R=s(),W=s(!1),x=s(!1),V=s(-1),f=s([]),y=s([]),_=s(!1),A=s(""),k=s(!1),z=s([]),g=s(null),q=()=>{f.value.push({type:1,orderNo:"",orderAmount:0,amount:0})},D=async()=>{try{k.value=!0;const u=await J.getOrders({code:n.customerCode,DocNo:A.value});z.value=u||[]}catch{p.error("\u83B7\u53D6\u8BA2\u5355\u6570\u636E\u5931\u8D25")}finally{k.value=!1}},G=u=>{g.value=u},Q=()=>{if(!g.value||V.value<0)return void p.error("\u8BF7\u9009\u62E9\u8BA2\u5355");const u=f.value[V.value];u.orderNo=g.value.DocNo,u.orderAmount=g.value.salesPrice,u.remainingAmount=g.value.remainingAmount,u.amount=g.value.remainingAmount,_.value=!1},X=()=>{y.value.push({type:2,expenseType:"",amount:0})},Z=ne(()=>f.value.reduce((u,a)=>u+Number(a.amount||0),0)+y.value.reduce((u,a)=>u+Number(a.amount||0),0)),ee=async()=>{var N;if(x.value)return;const u=Z.value;if(Math.abs(u-n.maxAmount)>.01)return void p.error(`\u8BA2\u5355\u91D1\u989D\u4E0E\u8D39\u7528\u91D1\u989D\u4E4B\u548C\u5FC5\u987B\u7B49\u4E8E\u53EF\u8BA4\u9886\u91D1\u989D ${n.maxAmount}`);for(let t=0;t<f.value.length;t++){const v=f.value[t];if(!v.orderNo)return void p.error(`\u7B2C${t+1}\u6761\u8BA2\u5355\u660E\u7EC6\u7684\u8BA2\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A`);if(v.amount<=0)return void p.error(`\u7B2C${t+1}\u6761\u8BA2\u5355\u660E\u7EC6\u7684\u8BA4\u6B3E\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`)}for(let t=0;t<y.value.length;t++){const v=y.value[t];if(v.expenseType===""||v.expenseType===null||v.expenseType===void 0)return void p.error(`\u7B2C${t+1}\u6761\u8D39\u7528\u660E\u7EC6\u7684\u8D39\u7528\u7C7B\u522B\u4E0D\u80FD\u4E3A\u7A7A`);if(v.amount<=0)return void p.error(`\u7B2C${t+1}\u6761\u8D39\u7528\u660E\u7EC6\u7684\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`)}const a=(((N=n.claimDetail)==null?void 0:N.detailList)||[]).filter(t=>t.type!="3").map(t=>{const{id:v,claimId:U,...m}=t;return m}),b={id:n.id||void 0,claimDate:n.claimDetail.claimDate,type:n.claimDetail.type,status:n.claimDetail.status,salesmanName:n.claimDetail.salesmanName,customerName:n.claimDetail.customerName,customerCode:n.claimDetail.customerCode,currency:n.claimDetail.currency,currencyCode:n.claimDetail.currencyCode,totalAmount:n.claimDetail.totalAmount,detailList:[...a,...f.value.map(t=>({type:1,orderNo:t.orderNo,orderAmount:t.orderAmount,amount:Number(t.amount||0),remainingAmount:Number(t.remainingAmount||0),shipAmount:Number(t.shipAmount||0)})),...y.value.map(t=>({type:2,expenseType:t.expenseType,amount:Number(t.amount||0),remainingAmount:Number(t.remainingAmount||0)}))],collectionList:n.claimDetail.collectionList};try{x.value=!0,await J.createClaim(b),p.success("\u7EE7\u7EED\u8BA4\u9886\u6210\u529F"),L("success"),T()}catch{p.error("\u8BA4\u9886\u5931\u8D25")}finally{x.value=!1}},T=()=>{L("update:show",!1),P()},P=()=>{f.value=[],y.value=[]};return re(()=>n.show,u=>{u||P()}),(u,a)=>{const b=se,N=ce,t=pe,v=ve,U=he,m=fe,r=ye,$=be,ae=_e,le=ge,te=we,B=Ae,O=Ce;return C(),F(E,null,[e(B,{"model-value":u.show,title:"\u7EE7\u7EED\u8BA4\u9886",width:"40%","close-on-click-modal":!1,onClose:T},{footer:l(()=>[e(m,{onClick:T},{default:l(()=>a[9]||(a[9]=[c("\u53D6\u6D88")])),_:1}),e(m,{type:"primary",onClick:ee,loading:h(x)},{default:l(()=>a[10]||(a[10]=[c(" \u786E\u8BA4\u63D0\u4EA4 ")])),_:1},8,["loading"])]),default:l(()=>[Y((C(),I(te,{ref_key:"formRef",ref:R,"label-width":"100px"},{default:l(()=>[e(v,{gutter:20},{default:l(()=>[e(t,{span:12},{default:l(()=>[e(N,{label:"\u53EF\u8BA4\u9886\u91D1\u989D"},{default:l(()=>[e(b,{value:n.maxAmount,readonly:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),w("div",null,[e(U,{class:"p-2px"},{default:l(()=>a[3]||(a[3]=[c("\u8BA2\u5355\u660E\u7EC6")])),_:1}),w("div",Ne,[e(m,{type:"primary",plain:"",onClick:q,size:"small"},{default:l(()=>a[4]||(a[4]=[c("+ \u6DFB\u52A0\u8BA2\u5355")])),_:1})]),e($,{data:h(f),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:l(()=>[e(r,{label:"\u8BA2\u5355\u53F7","min-width":"200"},{default:l(({row:o,$index:i})=>[e(b,{modelValue:o.orderNo,"onUpdate:modelValue":d=>o.orderNo=d,readonly:"",onClick:d=>{return S=i,V.value=S,A.value="",_.value=!0,void D();var S}},null,8,["modelValue","onUpdate:modelValue","onClick"])]),_:1}),e(r,{label:"\u8BA2\u5355\u91D1\u989D","min-width":"100"},{default:l(({row:o})=>[w("span",null,j(o.orderAmount),1)]),_:1}),e(r,{label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D","min-width":"100"},{default:l(({row:o})=>[w("span",null,j(o.remainingAmount),1)]),_:1}),e(r,{label:"\u8BA4\u6B3E\u91D1\u989D","min-width":"100"},{default:l(({row:o})=>[e(b,{modelValue:o.amount,"onUpdate:modelValue":i=>o.amount=i,type:"number",step:1,min:0,max:o.orderAmount,"controls-position":"right",style:{width:"100%"},onChange:i=>(d=>{d.amount>d.remainingAmount&&(d.amount=d.remainingAmount,p.warning("\u8BA4\u6B3E\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5269\u4F59\u53EF\u8BA4\u6B3E\u91D1\u989D"))})(o)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])]),_:1}),e(r,{label:"\u64CD\u4F5C",width:"80"},{default:l(({$index:o})=>[e(m,{type:"danger",size:"small",onClick:i=>{return d=o,void f.value.splice(d,1);var d}},{default:l(()=>a[5]||(a[5]=[c(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e(U,{class:"p-2px"},{default:l(()=>a[6]||(a[6]=[c("\u8D39\u7528\u7C7B\u522B")])),_:1}),w("div",Ve,[e(m,{type:"primary",plain:"",onClick:X,size:"small"},{default:l(()=>a[7]||(a[7]=[c("+ \u6DFB\u52A0\u8D39\u7528")])),_:1})]),e($,{data:h(y),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:l(()=>[e(r,{label:"\u8D39\u7528\u7C7B\u522B","min-width":"40"},{default:l(({row:o})=>[e(le,{modelValue:o.expenseType,"onUpdate:modelValue":i=>o.expenseType=i,placeholder:"\u8BF7\u9009\u62E9\u8D39\u7528\u7C7B\u522B",style:{width:"100%"}},{default:l(()=>[(C(!0),F(E,null,ie(h(de)(h(me).FINANCIAL_COSTS_TYPE),i=>(C(),I(ae,{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{label:"\u91D1\u989D","min-width":"120"},{default:l(({row:o})=>[e(b,{modelValue:o.amount,"onUpdate:modelValue":i=>o.amount=i,precision:2,type:"number",min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{label:"\u64CD\u4F5C",width:"80"},{default:l(({$index:o})=>[e(m,{type:"danger",size:"small",onClick:i=>{return d=o,void y.value.splice(d,1);var d}},{default:l(()=>a[8]||(a[8]=[c(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),_:1})),[[O,h(W)]])]),_:1},8,["model-value"]),e(B,{modelValue:h(_),"onUpdate:modelValue":a[2]||(a[2]=o=>H(_)?_.value=o:null),title:"\u9009\u62E9\u8BA2\u5355",width:"40%","append-to-body":""},{footer:l(()=>[e(m,{onClick:a[1]||(a[1]=o=>_.value=!1)},{default:l(()=>a[12]||(a[12]=[c("\u53D6\u6D88")])),_:1}),e(m,{type:"primary",onClick:Q},{default:l(()=>a[13]||(a[13]=[c("\u786E\u8BA4")])),_:1})]),default:l(()=>[w("div",ke,[e(b,{modelValue:h(A),"onUpdate:modelValue":a[0]||(a[0]=o=>H(A)?A.value=o:null),placeholder:"\u8F93\u5165\u8BA2\u5355\u53F7\u68C0\u7D22",clearable:"",onInput:D},{append:l(()=>[e(m,{onClick:D},{default:l(()=>a[11]||(a[11]=[c("\u641C\u7D22")])),_:1})]),_:1},8,["modelValue"])]),Y((C(),I($,{data:h(z),height:"300","highlight-current-row":"",onCurrentChange:G},{default:l(()=>[e(r,{prop:"DocNo",label:"\u8BA2\u5355\u53F7",width:"150"}),e(r,{prop:"currency",label:"\u5E01\u79CD",width:"60"}),e(r,{prop:"salesPrice",label:"\u8BA2\u5355\u603B\u91D1\u989D",width:"110"}),e(r,{prop:"shipPrice",label:"\u5DF2\u51FA\u8D27\u91D1\u989D",width:"110"}),e(r,{prop:"claimedAmount",label:"\u5DF2\u8BA4\u9886\u91D1\u989D",width:"110"}),e(r,{prop:"remainingAmount",label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D",width:"110"})]),_:1},8,["data"])),[[O,h(k)]])]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-12b9007a"]]);export{De as default};
