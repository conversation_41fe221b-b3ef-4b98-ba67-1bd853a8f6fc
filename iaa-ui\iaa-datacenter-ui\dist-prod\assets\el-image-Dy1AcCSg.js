import{b6 as W,ba as k,cm as X,b8 as ee,d as N,bK as ae,bb as le,bi as se,Y as n,dr as ie,ds as te,j as u,bs as p,au as oe,f as ne,o as r,c as d,bf as z,l as _,t as re,u as a,n as E,F as K,ar as M,$ as y,h as ce,w as ue,dt as de,bg as ve,aO as fe,du as pe,bc as ye,dv as me,br as T,dw as ge,dx as be,bh as we}from"./index-C8b06LRn.js";const he=W({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:k([String,Object])},previewSrcList:{type:k(Array),default:()=>X([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:<PERSON>olean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:k(String)}}),xe={load:t=>t instanceof Event,error:t=>t instanceof Event,switch:t=>ee(t),close:()=>!0,show:()=>!0},Se=N({name:"ElImage",inheritAttrs:!1}),ke=we(ve(N({...Se,props:he,emits:xe,setup(t,{emit:v}){const l=t;let O="";const{t:Y}=ae(),o=le("image"),F=se(),L=n(()=>ie(Object.entries(F).filter(([e])=>/^(data-|on[A-Z])/i.test(e)||["id","style"].includes(e)))),P=te({excludeListeners:!0,excludeKeys:n(()=>Object.keys(L.value))}),g=u(),f=u(!1),c=u(!0),b=u(!1),m=u(),i=u(),R=p&&"loading"in HTMLImageElement.prototype;let w,h;const q=n(()=>[o.e("inner"),x.value&&o.e("preview"),c.value&&o.is("loading")]),D=n(()=>{const{fit:e}=l;return p&&e?{objectFit:e}:{}}),x=n(()=>{const{previewSrcList:e}=l;return Array.isArray(e)&&e.length>0}),H=n(()=>{const{previewSrcList:e,initialIndex:s}=l;let B=s;return s>e.length-1&&(B=0),B}),j=n(()=>l.loading!=="eager"&&(!R&&l.loading==="lazy"||l.lazy)),S=()=>{p&&(c.value=!0,f.value=!1,g.value=l.src)};function Z(e){c.value=!1,f.value=!1,v("load",e)}function G(e){c.value=!1,f.value=!0,v("error",e)}function C(){ge(m.value,i.value)&&(S(),A())}const I=be(C,200,!0);async function $(){var e;if(!p)return;await fe();const{scrollContainer:s}=l;pe(s)?i.value=s:ye(s)&&s!==""?i.value=(e=document.querySelector(s))!=null?e:void 0:m.value&&(i.value=me(m.value)),i.value&&(w=T(i,"scroll",I),setTimeout(()=>C(),100))}function A(){p&&i.value&&I&&(w==null||w(),i.value=void 0)}function J(e){if(e.ctrlKey)return e.deltaY<0||e.deltaY>0?(e.preventDefault(),!1):void 0}function Q(){x.value&&(h=T("wheel",J,{passive:!1}),O=document.body.style.overflow,document.body.style.overflow="hidden",b.value=!0,v("show"))}function U(){h==null||h(),document.body.style.overflow=O,b.value=!1,v("close")}function V(e){v("switch",e)}return oe(()=>l.src,()=>{j.value?(c.value=!0,f.value=!1,A(),$()):S()}),ne(()=>{j.value?$():S()}),(e,s)=>(r(),d("div",M({ref_key:"container",ref:m},a(L),{class:[a(o).b(),e.$attrs.class]}),[f.value?z(e.$slots,"error",{key:0},()=>[_("div",{class:E(a(o).e("error"))},re(a(Y)("el.image.error")),3)]):(r(),d(K,{key:1},[g.value!==void 0?(r(),d("img",M({key:0},a(P),{src:g.value,loading:e.loading,style:a(D),class:a(q),crossorigin:e.crossorigin,onClick:Q,onLoad:Z,onError:G}),null,16,["src","loading","crossorigin"])):y("v-if",!0),c.value?(r(),d("div",{key:1,class:E(a(o).e("wrapper"))},[z(e.$slots,"placeholder",{},()=>[_("div",{class:E(a(o).e("placeholder"))},null,2)])],2)):y("v-if",!0)],64)),a(x)?(r(),d(K,{key:2},[b.value?(r(),ce(a(de),{key:0,"z-index":e.zIndex,"initial-index":a(H),infinite:e.infinite,"zoom-rate":e.zoomRate,"min-scale":e.minScale,"max-scale":e.maxScale,"url-list":e.previewSrcList,crossorigin:e.crossorigin,"hide-on-click-modal":e.hideOnClickModal,teleported:e.previewTeleported,"close-on-press-escape":e.closeOnPressEscape,onClose:U,onSwitch:V},{default:ue(()=>[e.$slots.viewer?(r(),d("div",{key:0},[z(e.$slots,"viewer")])):y("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","crossorigin","hide-on-click-modal","teleported","close-on-press-escape"])):y("v-if",!0)],64)):y("v-if",!0)],16))}}),[["__file","image.vue"]]));export{ke as E};
