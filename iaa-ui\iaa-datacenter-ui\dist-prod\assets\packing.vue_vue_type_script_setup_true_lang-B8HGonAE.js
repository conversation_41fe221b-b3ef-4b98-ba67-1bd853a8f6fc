import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as I,j as b,y as L,f as G,aO as J,u as a,aF as M,o as p,c as h,k as o,w as d,v as U,F as D,g as C,N as Y,h as w,l as s,di as K,t as W,dk as X,dl as _,d2 as Z,ax as ee,H as le,ap as ae,aq as te,C as oe,z as de,A as ie,aC as ne,S as ue}from"./index-C8b06LRn.js";import{C as B}from"./index-B2m4kQ_G.js";import se from"./DetailForm-BVwfkXTG.js";import{c as me}from"./vxeCustom-D2Re1O-c.js";import{d as re}from"./download-D5Lb_h0f.js";import{c as ce}from"./permission-DVzqLl93.js";const pe={class:"h-[calc(100vh-260px)]"},ve=I({__name:"packing",setup(fe){const N=b(),y=b(),T=b(),t=b({pageNo:1,pageSize:100,type:"packing",seller:void 0,businessDate:void 0,customerName:void 0,description:void 0,custom:void 0,itemCode:void 0,itemName:void 0,spec:void 0,planReceiptDate:void 0,actualReceiptDate:void 0,planDesignDate:void 0,actualDesignDate:void 0,planQuotationDate:void 0,actualQuotationDate:void 0,planTestingDate:void 0,actualTestingDate:void 0,planAdmitDate:void 0,actualAdmitDate:void 0,planBomDate:void 0,actualBomDate:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,remark:void 0,showAll:!0,overdue:void 0,overdueType:1,receipt:void 0,design:void 0,quotation:void 0,test:void 0,complete:void 0,admit:void 0,planBomCompleteDate:void 0,planBomCompleteDateChange:void 0,taskStatus:void 0}),S=b([]),A=b(0),z=b(!1),k=async()=>{z.value=!0;try{const n=await B.pageDetail(t.value);S.value=n.list,A.value=n.total}finally{z.value=!1}},i=()=>{t.value.pageNo=1,k()},Q=({row:n,column:e})=>{let c,f,g;if(e.field.includes("plan")){if(!n[e.field]&&!n[e.field.replace("plan","actual")])return{};c=_(n[e.field]),f=n[e.field.replace("plan","actual")]?_(n[e.field.replace("plan","actual")]):_(),g=n[e.field.replace("plan","actual")]}else{if(!e.field.includes("actual"))return{};if(!n[n[e.field.replace("actual","plan")]]&&!n[e.field])return{};c=_(n[e.field.replace("actual","plan")]),f=n[e.field]?_(n[e.field]):_(),g=n[e.field]}const r=c.startOf("day"),m=f.startOf("day"),v=r.diff(m,"days");return g?v>=0?{backgroundColor:"var(--el-color-success-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"var(--el-color-error-light-3)",color:"#ffffff",cursor:"pointer"}:v>=2?{cursor:"pointer"}:v>=0?{backgroundColor:"var(--el-color-warning-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"var(--el-color-error-light-3)",color:"#ffffff",cursor:"pointer"}},O=()=>{t.value={pageNo:1,pageSize:100,type:"packing",seller:void 0,businessDate:void 0,customerName:void 0,description:void 0,custom:void 0,itemCode:void 0,itemName:void 0,spec:void 0,planReceiptDate:void 0,actualReceiptDate:void 0,planDesignDate:void 0,actualDesignDate:void 0,planQuotationDate:void 0,actualQuotationDate:void 0,planTestingDate:void 0,actualTestingDate:void 0,planAdmitDate:void 0,actualAdmitDate:void 0,planBomDate:void 0,actualBomDate:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,remark:void 0,showAll:!0,overdue:void 0,overdueType:1,receipt:void 0,design:void 0,quotation:void 0,test:void 0,complete:void 0,admit:void 0,planBomCompleteDate:void 0,planBomCompleteDateChange:void 0,taskStatus:void 0},i()},F=async({options:n})=>{const e=await B.exportDetail({...t.value,exportColumns:n.columns.map(c=>c.field)});re.excel(e,n.filename+".xlsx")},x=L(),q=async({menu:n,row:e,column:c})=>{var r;const f=(r=a(y))==null?void 0:r.getCheckboxRecords();if(f.length===0)return void x.error("\u8BF7\u9009\u62E9\u8981\u6279\u91CF\u8BBE\u7F6E\u7684\u884C\uFF01");if(!c.field.includes("plan")&&!c.field.includes("actual"))return void x.error("\u5F53\u524D\u5217\u4E0D\u80FD\u4FEE\u6539");let g="";if(f.filter(m=>m.id!==e.id).some(m=>m[c.field])){const{value:m}=await Z.prompt("\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0","\u4FEE\u6539\u539F\u56E0",{confirmButtonText:"\u786E\u8BA4",cancelButtonText:"\u53D6\u6D88",inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});g=m}if(n.code==="batchBottom"){for(const m of f){if(m.id===e.id)continue;const v=ee(m[c.field]);m[c.field]=e[c.field],await B.saveDetail({...m,modifyInfo:g?`${c.title}\u4ECE${v}\u4FEE\u6539\u4E3A${e[c.field]}`:"",reason:g})}x.success("\u6279\u91CF\u8BBE\u7F6E\u6210\u529F")}};return G(()=>{i(),J(()=>{var n;(n=a(y))==null||n.connect(a(N))})}),(n,e)=>{const c=le,f=ae,g=te,r=oe,m=de,v=ie,$=ne,j=M("vxe-toolbar"),u=M("vxe-column"),V=ue,H=M("vxe-table"),P=E;return p(),h(D,null,[o(j,{ref_key:"toolbarRef",ref:N,custom:"",size:"mini",export:""},{buttons:d(()=>[o(c,{type:"primary",size:"small",plain:"",onClick:i},{default:d(()=>e[36]||(e[36]=[U("\u67E5\u8BE2")])),_:1}),o(c,{type:"warning",size:"small",plain:"",onClick:O},{default:d(()=>e[37]||(e[37]=[U("\u6E05\u7A7A\u7B5B\u9009")])),_:1}),o(g,{size:"small",class:"ml-10px",modelValue:a(t).overdueType,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).overdueType=l)},{default:d(()=>[o(f,{label:"\u81F3\u4ECA",value:1}),o(f,{label:"\u5F53\u5929",value:2})]),_:1},8,["modelValue"]),o(r,{modelValue:a(t).overdue,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).overdue=l),class:"ml-10px !w-100px",size:"small",placeholder:"\u672A\u5B8C\u6210\u6570\u636E",onChange:i,"value-format":"YYYY-MM-DD"},null,8,["modelValue"]),o(v,{placeholder:"\u63A5\u6536\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).receipt,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).receipt=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u8BBE\u8BA1\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).design,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).design=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u8BBE\u8BA1\u7A3F\u786E\u8BA4\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).quotation,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).quotation=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u6253\u6837\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).test,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).test=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u786E\u8BA4\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).admit,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).admit=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u5B8C\u6210\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).complete,"onUpdate:modelValue":e[7]||(e[7]=l=>a(t).complete=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),h(D,null,C(a(Y)("eng_bom_status"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),tools:d(()=>[o($,{"active-text":"\u9690\u85CF\u5DF2\u5B8C\u6210","inactive-text":"\u663E\u793A\u6240\u6709",size:"small",modelValue:a(t).showAll,"onUpdate:modelValue":e[8]||(e[8]=l=>a(t).showAll=l),onChange:i},null,8,["modelValue"])]),_:1},512),s("div",pe,[o(H,{"row-config":{height:34,isCurrent:!0,isHover:!0},"row-style":{cursor:"pointer"},id:"packingTable","custom-config":a(me),"header-cell-style":{padding:0,height:"30px"},"column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},align:"center",border:"","show-overflow":"",height:"100%",data:a(S),ref_key:"tableRef",ref:y,size:"small","cell-style":Q,onCellClick:e[33]||(e[33]=l=>{var R;return(R=a(T))==null?void 0:R.openForm(l.row)}),loading:a(z),"export-config":{remote:!0,exportMethod:F},"checkbox-config":{labelField:"check",range:!0},"menu-config":{enabled:a(ce)(["order-bom:task:packing-edit"]),body:{options:[[{code:"batchBottom",name:"\u6279\u91CF\u8BBE\u7F6E"}]]}},onMenuClick:q},{default:d(()=>[o(u,{type:"checkbox",width:"50",field:"check",align:"center",fixed:"left"}),o(u,{field:"businessDate",width:"200",title:"\u65E5\u671F"},{header:d(()=>[e[38]||(e[38]=s("div",null,"\u65E5\u671F",-1)),o(r,{modelValue:a(t).businessDate,"onUpdate:modelValue":e[9]||(e[9]=l=>a(t).businessDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"},{header:d(()=>[e[39]||(e[39]=s("div",null,"\u4E1A\u52A1\u5458",-1)),o(V,{modelValue:a(t).seller,"onUpdate:modelValue":e[10]||(e[10]=l=>a(t).seller=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"customerName",width:"120",title:"\u5BA2\u6237"},{header:d(()=>[e[40]||(e[40]=s("div",null,"\u5BA2\u6237",-1)),o(V,{modelValue:a(t).customerName,"onUpdate:modelValue":e[11]||(e[11]=l=>a(t).customerName=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"itemCode",width:"120",title:"\u54C1\u53F7"},{header:d(()=>[e[41]||(e[41]=s("div",null,"\u54C1\u53F7",-1)),o(V,{modelValue:a(t).itemCode,"onUpdate:modelValue":e[12]||(e[12]=l=>a(t).itemCode=l),clearable:"",placeholder:"\u5DE6\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"itemName",width:"140",title:"\u54C1\u540D"},{header:d(()=>[e[42]||(e[42]=s("div",null,"\u54C1\u540D",-1)),o(V,{modelValue:a(t).itemName,"onUpdate:modelValue":e[13]||(e[13]=l=>a(t).itemName=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"spec","min-width":"200",title:"\u89C4\u683C"},{header:d(()=>[e[43]||(e[43]=s("div",null,"\u89C4\u683C",-1)),o(V,{modelValue:a(t).spec,"onUpdate:modelValue":e[14]||(e[14]=l=>a(t).spec=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{header:d(()=>[e[44]||(e[44]=s("div",null,"\u5B9A\u5236\u5185\u5BB9",-1)),o(v,{size:"small",modelValue:a(t).custom,"onUpdate:modelValue":e[15]||(e[15]=l=>a(t).custom=l),clearable:"",multiple:"","collapse-tags":""},{default:d(()=>[(p(!0),h(D,null,C(a(K)("eng_packing_dict"),l=>(p(),w(m,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:d(({row:l})=>[U(W(a(X)("eng_packing_dict",l.custom)),1)]),_:1}),o(u,{field:"description",width:"200",title:"\u4EFB\u52A1"},{header:d(()=>[e[45]||(e[45]=s("div",null,"\u4EFB\u52A1",-1)),o(V,{modelValue:a(t).description,"onUpdate:modelValue":e[16]||(e[16]=l=>a(t).description=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"},{header:d(()=>[e[46]||(e[46]=s("div",null,"\u8BA1\u5212\u63A5\u6536\u65E5\u671F",-1)),o(r,{modelValue:a(t).planReceiptDate,"onUpdate:modelValue":e[17]||(e[17]=l=>a(t).planReceiptDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"},{header:d(()=>[e[47]||(e[47]=s("div",null,"\u5B9E\u9645\u63A5\u6536\u65E5\u671F",-1)),o(r,{modelValue:a(t).actualReceiptDate,"onUpdate:modelValue":e[18]||(e[18]=l=>a(t).actualReceiptDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"},{header:d(()=>[e[48]||(e[48]=s("div",null,"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F",-1)),o(r,{modelValue:a(t).planDesignDate,"onUpdate:modelValue":e[19]||(e[19]=l=>a(t).planDesignDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"},{header:d(()=>[e[49]||(e[49]=s("div",null,"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F",-1)),o(r,{modelValue:a(t).actualDesignDate,"onUpdate:modelValue":e[20]||(e[20]=l=>a(t).actualDesignDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planQuotationDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u7A3F\u786E\u8BA4"},{header:d(()=>[e[50]||(e[50]=s("div",null,"\u8BA1\u5212\u8BBE\u8BA1\u7A3F\u786E\u8BA4",-1)),o(r,{modelValue:a(t).planQuotationDate,"onUpdate:modelValue":e[21]||(e[21]=l=>a(t).planQuotationDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualQuotationDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u7A3F\u786E\u8BA4"},{header:d(()=>[e[51]||(e[51]=s("div",null,"\u5B9E\u9645\u8BBE\u8BA1\u7A3F\u786E\u8BA4",-1)),o(r,{modelValue:a(t).actualQuotationDate,"onUpdate:modelValue":e[22]||(e[22]=l=>a(t).actualQuotationDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"},{header:d(()=>[e[52]||(e[52]=s("div",null,"\u8BA1\u5212\u6253\u6837\u65E5\u671F",-1)),o(r,{modelValue:a(t).planTestingDate,"onUpdate:modelValue":e[23]||(e[23]=l=>a(t).planTestingDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"},{header:d(()=>[e[53]||(e[53]=s("div",null,"\u5B9E\u9645\u6253\u6837\u65E5\u671F",-1)),o(r,{modelValue:a(t).actualTestingDate,"onUpdate:modelValue":e[24]||(e[24]=l=>a(t).actualTestingDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u786E\u8BA4\u65E5\u671F"},{header:d(()=>[e[54]||(e[54]=s("div",null,"\u8BA1\u5212\u786E\u8BA4\u65E5\u671F",-1)),o(r,{modelValue:a(t).planAdmitDate,"onUpdate:modelValue":e[25]||(e[25]=l=>a(t).planAdmitDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u786E\u8BA4\u65E5\u671F"},{header:d(()=>[e[55]||(e[55]=s("div",null,"\u5B9E\u9645\u786E\u8BA4\u65E5\u671F",-1)),o(r,{modelValue:a(t).actualAdmitDate,"onUpdate:modelValue":e[26]||(e[26]=l=>a(t).actualAdmitDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"},{header:d(()=>[e[56]||(e[56]=s("div",null,"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F",-1)),o(r,{modelValue:a(t).planCompleteDate,"onUpdate:modelValue":e[27]||(e[27]=l=>a(t).planCompleteDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"},{header:d(()=>[e[57]||(e[57]=s("div",null,"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F",-1)),o(r,{modelValue:a(t).actualCompleteDate,"onUpdate:modelValue":e[28]||(e[28]=l=>a(t).actualCompleteDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planBomCompleteDate",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"},{header:d(()=>[e[58]||(e[58]=s("div",null,"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F",-1)),o(r,{modelValue:a(t).planBomCompleteDate,"onUpdate:modelValue":e[29]||(e[29]=l=>a(t).planBomCompleteDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"planBomCompleteDateChange",width:"200",title:"BOM\u8BA1\u5212\u53D8\u66F4\u65E5\u671F"},{header:d(()=>[e[59]||(e[59]=s("div",null,"BOM\u8BA1\u5212\u53D8\u66F4\u65E5\u671F",-1)),o(r,{modelValue:a(t).planBomCompleteDateChange,"onUpdate:modelValue":e[30]||(e[30]=l=>a(t).planBomCompleteDateChange=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(u,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[e[60]||(e[60]=s("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),o(v,{modelValue:a(t).taskStatus,"onUpdate:modelValue":e[31]||(e[31]=l=>a(t).taskStatus=l),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:k},{default:d(()=>[o(m,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),o(m,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),o(m,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),o(m,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),o(u,{field:"remark",width:"200",title:"\u5907\u6CE8"},{header:d(()=>[e[61]||(e[61]=s("div",null,"\u5907\u6CE8",-1)),o(V,{modelValue:a(t).remark,"onUpdate:modelValue":e[32]||(e[32]=l=>a(t).remark=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1})]),_:1},8,["custom-config","data","loading","export-config","menu-config"])]),o(P,{limit:a(t).pageSize,"onUpdate:limit":e[34]||(e[34]=l=>a(t).pageSize=l),page:a(t).pageNo,"onUpdate:page":e[35]||(e[35]=l=>a(t).pageNo=l),total:a(A),onPagination:k,size:"small"},null,8,["limit","page","total"]),o(se,{ref_key:"detailFormRef",ref:T,onSuccess:i},null,512)],64)}}});export{ve as _};
