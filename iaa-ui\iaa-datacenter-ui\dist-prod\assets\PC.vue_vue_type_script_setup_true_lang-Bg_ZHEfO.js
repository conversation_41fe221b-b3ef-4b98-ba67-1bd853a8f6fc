import{_ as fe}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as pe}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as ge,j as r,y as ye,r as ee,f as he,aO as D,u as l,aF as T,T as ve,o as m,c as ke,k as i,w as a,m as q,x as E,h as g,v as y,$ as b,l as P,t as le,F as we,dV as _e,E as be,q as xe,H as Ce,aM as ze}from"./index-C8b06LRn.js";import{I as R}from"./index-BX2KAvdx.js";import{C as H}from"./claim-BPjhJe4B.js";import{_ as Se}from"./DatabaseUploadForm.vue_vue_type_script_setup_true_lang-C15tI4yC.js";import{_ as Ee}from"./PermissionForm.vue_vue_type_script_setup_true_lang-BbCkMcuR.js";import{d as te}from"./Filter-Dzz2caxb.js";import{d as Ae}from"./download-D5Lb_h0f.js";import Fe from"./ClaimDialogPC-DR2FIPAd.js";const Re={class:"h-[calc(100vh-220px)]"},Ve={class:"h-[calc(100%-50px)]"},Ie=ge({__name:"PC",setup(Le,{expose:ie}){const ae=r([{data:[]}]),ne=r([{data:[]}]),B=r(),v=ye(),_=r(!0),V=r([]),G=r(0),J=r(),k=r("pending"),u=ee({pageNo:1,pageSize:30,isMe:!1,collectionAccount:void 0,dateStr:void 0,claimDate:void 0,status:[0]}),$=r(!1),C=r(!1),A=r([]),F=r(null),I=r(!1),K=r(!1),Q=n=>{const e=["classification"],p={};n.filterList.forEach(c=>{const{field:o,values:N,datas:s}=c;e.includes(o)&&N.length>0?p[o]=N:s.length>0&&(p[o]=s[0])}),Object.keys(u).forEach(c=>{["pageNo","pageSize","status","isMe"].includes(c)||n.filterList.some(o=>o.field===c)||(u[c]=void 0)}),Object.assign(u,p),h()},z=r(),W=r(),M=r(),h=async()=>{_.value=!0;try{let n;n=k.value==="mine"?await H.getClaimPage({...u,type:1}):await R.getInformationPage(u),V.value=n.list,G.value=n.total}finally{_.value=!1}},oe=n=>{n==="mine"?(u.isMe=!0,u.status=[1,2],D(()=>{var e;(e=l(W))==null||e.connect(l(M))})):(u.isMe=!1,u.status=[0],D(()=>{var e;(e=l(z))==null||e.connect(l(M))})),u.pageNo=1,h()},X=async n=>{let e=[],p=[];if(n)e=[n],p=V.value.filter(c=>c.id===n);else{const c=z.value;c&&(p=c.getCheckboxRecords()||[],e=p.map(o=>o.id))}if(!e.length)return void v.alertError("\u8BF7\u9009\u62E9\u8981\u8BA4\u9886\u7684\u6570\u636E");[...new Set(p.map(c=>c.currency))].length>1?v.alertError("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u4E00\u8D77\u8BA4\u9886"):(A.value=e,F.value=null,I.value=!1,C.value=!0)};ee({dateStr:"",collectionAccount:"",collectionId:""});const L=r(!1),j=r([]),re=n=>{let e="";if(n.collectionList&&n.collectionList.length>0){const p=n.collectionList.map(c=>`${c.collectionAccount}-${c.collectionAmount}`).join(", ");p&&(e+=` ${p}`)}return e},se=()=>{C.value=!1,A.value=[],F.value=null,h()};return he(()=>{h(),D(()=>{var n;z.value&&z.value.$el.setAttribute("tabindex","0"),(n=l(z))==null||n.connect(l(M))})}),ie({getList:h}),(n,e)=>{const p=be,c=xe,o=Ce,N=T("vxe-toolbar"),s=T("vxe-column"),Y=T("vxe-table"),Z=ze,ce=pe,de=me,ue=fe,S=ve("hasPermi");return m(),ke(we,null,[i(de,null,{default:a(()=>[i(c,{modelValue:l(k),"onUpdate:modelValue":e[0]||(e[0]=t=>q(k)?k.value=t:null),onTabChange:oe,class:"mb-20px"},{default:a(()=>[i(p,{label:"\u5F85\u8BA4\u9886",name:"pending"}),i(p,{label:"\u6211\u7684",name:"mine"})]),_:1},8,["modelValue"]),i(N,{custom:"",ref_key:"toolbarRef",ref:M,size:"mini"},{buttons:a(()=>[l(k)==="pending"?E((m(),g(o,{key:0,plain:"",type:"success",class:"mr-10px",size:"small",onClick:e[1]||(e[1]=t=>{var d;return(d=l(B))==null?void 0:d.open()}),title:"\u4E0A\u4F20"},{default:a(()=>e[12]||(e[12]=[y(" \u4E0A\u4F20 ")])),_:1})),[[S,["collection:information:create"]]]):b("",!0),i(o,{type:"info",plain:"",class:"mr-10px",size:"small",onClick:e[2]||(e[2]=t=>(async()=>{try{await v.exportConfirm(),$.value=!0;const d=await R.exportInformation(u);Ae.excel(d,"\u6536\u6B3E\u4FE1\u606F.xlsx")}catch{}finally{$.value=!1}})()),title:"\u5BFC\u51FA",loading:l($)},{default:a(()=>e[13]||(e[13]=[y(" \u5BFC\u51FA ")])),_:1},8,["loading"]),l(k)==="pending"?E((m(),g(o,{key:1,type:"primary",plain:"",class:"mr-10px",size:"small",onClick:e[3]||(e[3]=t=>X()),title:"\u8BA4\u9886"},{default:a(()=>e[14]||(e[14]=[y(" \u8BA4\u9886 ")])),_:1})),[[S,["collection:information:claim"]]]):b("",!0)]),tools:a(()=>[E((m(),g(o,{type:"primary",link:"",class:"mr-10px",onClick:e[4]||(e[4]=t=>{var d;return(d=l(J))==null?void 0:d.openForm()}),size:"small"},{default:a(()=>e[15]||(e[15]=[y(" \u6743\u9650\u7BA1\u7406 ")])),_:1})),[[S,["receiving:payment:permission"]]])]),_:1},512),P("div",Re,[P("div",Ve,[l(k)==="pending"?(m(),g(Y,{key:0,"row-config":{height:25,keyField:"id"},ref_key:"tableRef",ref:z,data:l(V),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:l(_),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},onFilterChange:Q,tabindex:"0",size:"mini"},{default:a(()=>[i(s,{type:"checkbox",width:"40",field:"id",fixed:"left"}),i(s,{field:"dateStr",width:"200",title:"\u6536\u6B3E\u65E5\u671F",filters:l(ne),"filter-render":te},null,8,["filters","filter-render"]),i(s,{field:"collectionAccount",title:"\u6536\u6B3E\u8D26\u53F7","min-width":"200"}),i(s,{field:"payer",title:"\u4ED8\u6B3E\u4EBA","min-width":"200"}),i(s,{field:"collectionAmount",title:"\u6536\u6B3E\u91D1\u989D",width:"120"}),i(s,{field:"currency",title:"\u5E01\u79CD",width:"120"}),i(s,{title:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:a(({row:t})=>[l(k)==="pending"?E((m(),g(o,{key:0,onClick:d=>X(t.id),link:"",type:"primary","collection:information:claim":""},{default:a(()=>e[16]||(e[16]=[y(" \u8BA4\u9886 ")])),_:2},1032,["onClick"])),[[S,["collection:information:claim"]]]):b("",!0),t.status!==1?E((m(),g(o,{key:1,onClick:d=>(async f=>{v.confirm("\u662F\u5426\u5220\u9664\u6536\u6B3E\u4FE1\u606F?").then(()=>{_.value=!0,H.deleteClaimInformation(f),_.value=!1,v.success("\u5220\u9664\u6536\u6B3E\u4FE1\u606F\u6210\u529F"),h()})})([t.id]),link:"",type:"danger"},{default:a(()=>e[17]||(e[17]=[y(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[S,["collection:information:delete"]]]):b("",!0)]),_:1})]),_:1},8,["data","loading"])):(m(),g(Y,{key:1,"row-config":{height:25,keyField:"id"},ref_key:"tableRef1",ref:W,data:l(V),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:l(_),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},onFilterChange:Q,tabindex:"0",size:"mini"},{default:a(()=>[i(s,{type:"checkbox",width:"40",field:"id",fixed:"left"}),i(s,{field:"claimDate",width:"200",title:"\u6536\u6B3E\u65E5\u671F",filters:l(ae),"filter-render":te},null,8,["filters","filter-render"]),i(s,{field:"collectionAccount",title:"\u5173\u8054\u7684\u6536\u6B3E\u4FE1\u606F","min-width":"200"},{default:a(({row:t})=>[P("span",null,le(re(t)),1)]),_:1}),i(s,{field:"totalAmount",title:"\u603B\u91D1\u989D",width:"120"}),i(s,{field:"currency",title:"\u5E01\u79CD",width:"120"}),l(k)==="mine"?(m(),g(s,{key:0,field:"status",title:"\u72B6\u6001",width:"120"},{default:a(({row:t})=>[t.status===2?(m(),g(Z,{key:0,type:"primary"},{default:a(()=>e[18]||(e[18]=[y("\u8BA4\u9886\u4E2D")])),_:1})):t.status===0?(m(),g(Z,{key:1,type:"success"},{default:a(()=>e[19]||(e[19]=[y("\u5DF2\u8BA4\u9886")])),_:1})):b("",!0)]),_:1})):b("",!0),i(s,{field:"salesmanName",title:"\u4E1A\u52A1\u5458",width:"120"}),i(s,{title:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:a(({row:t})=>[l(k)==="mine"&&t.status===2?E((m(),g(o,{key:0,onClick:d=>(async f=>{try{const w=await R.getSuspended(f);A.value=[f],F.value=w||null,I.value=!1,C.value=!0}catch{h()}})(t.id),link:"",type:"primary"},{default:a(()=>e[20]||(e[20]=[y(" \u7EE7\u7EED ")])),_:2},1032,["onClick"])),[[S,["collection:information:claim"]]]):b("",!0),t.status===0?E((m(),g(o,{key:1,onClick:d=>(async f=>{try{const w=await R.getSuspended(f);A.value=[f],F.value=w||null,I.value=!1,C.value=!0,K.value=!0}catch{h()}})(t.id),link:"",type:"warning"},{default:a(()=>e[21]||(e[21]=[y(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[S,["record:money:admin"]]]):b("",!0),l(k)==="mine"&&t.status===0?(m(),g(o,{key:2,onClick:d=>(async f=>{let w=[],U=[];if(f)w=[f],U=V.value.filter(x=>x.id===f);else{const x=z.value;x&&(U=(x.getCheckboxRecords()||[]).filter(O=>O.status===2),w=U.map(O=>O.id))}if(!w.length)return void v.alertError('\u8BF7\u9009\u62E9\u72B6\u6001\u4E3A"\u5DF2\u8BA4\u9886"\u7684\u6570\u636E\u8FDB\u884C\u67E5\u770B');if([...new Set(U.map(x=>x.currency))].length>1)v.alertError("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u4E00\u8D77\u67E5\u770B");else try{const x=await R.getSuspended(w[0]);A.value=[w[0]],F.value={...x,readOnly:!0},I.value=!0,C.value=!0}catch{h()}})(t.id),link:"",type:"info"},{default:a(()=>e[22]||(e[22]=[y(" \u67E5\u770B ")])),_:2},1032,["onClick"])):b("",!0),l(k)==="mine"?E((m(),g(o,{key:3,onClick:d=>(async f=>{v.confirm("\u662F\u5426\u53D6\u6D88\u8BA4\u9886?").then(async()=>{_.value=!0,await H.deleteClaim([f]),_.value=!1,v.success("\u53D6\u6D88\u8BA4\u9886\u6210\u529F"),await h()})})(t.id),link:"",type:"danger"},{default:a(()=>e[23]||(e[23]=[y(" \u53D6\u6D88 ")])),_:2},1032,["onClick"])),[[S,["collection:information:cancel"]]]):b("",!0)]),_:1})]),_:1},8,["data","loading"]))]),i(ce,{total:l(G),page:l(u).pageNo,"onUpdate:page":e[5]||(e[5]=t=>l(u).pageNo=t),limit:l(u).pageSize,"onUpdate:limit":e[6]||(e[6]=t=>l(u).pageSize=t),onPagination:h,size:"small"},null,8,["total","page","limit"])])]),_:1}),i(Ee,{ref_key:"permissionFormRef",ref:J},null,512),i(Se,{ref_key:"databaseUploadFormRef",ref:B,onSuccess:e[7]||(e[7]=t=>h())},null,512),i(ue,{title:"\u6279\u91CF\u5220\u9664\u786E\u8BA4",modelValue:l(L),"onUpdate:modelValue":e[10]||(e[10]=t=>q(L)?L.value=t:null)},{footer:a(()=>[i(o,{type:"danger",onClick:e[8]||(e[8]=t=>(async()=>{_.value=!0;try{if(!_e(["collection:information:delete"]))return void v.error("\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01");{if(!z.value)return;const d=[];j.value.forEach(f=>{d.push(f.id)}),d.length>0&&await R.deleteInformation(d),L.value=!1,j.value=[],await h(),v.success("\u6279\u91CF\u5220\u9664\u6210\u529F")}}catch{v.error("\u6279\u91CF\u5220\u9664\u5931\u8D25")}finally{_.value=!1}})())},{default:a(()=>e[24]||(e[24]=[y("\u786E\u8BA4\u5220\u9664")])),_:1}),i(o,{onClick:e[9]||(e[9]=t=>L.value=!1)},{default:a(()=>e[25]||(e[25]=[y("\u53D6\u6D88")])),_:1})]),default:a(()=>[P("div",null,"\u5F53\u524D\u9009\u4E2D\uFF1A"+le(l(j).length)+" \u6761\u6570\u636E\uFF0C\u786E\u8BA4\u8981\u5220\u9664\u5417\uFF1F",1)]),_:1},8,["modelValue"]),i(Fe,{show:l(C),"onUpdate:show":e[11]||(e[11]=t=>q(C)?C.value=t:null),ids:l(A),prefill:l(F),"read-only":l(I),isEdit:l(K),onSuccess:se},null,8,["show","ids","prefill","read-only","isEdit"])],64)}}});export{Ie as _};
