import{_ as a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as e}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as m}from"./index-CkzUfjB7.js";import{d as p,o as i,c as n,k as r,w as d,F as c}from"./index-C8b06LRn.js";import"./el-card-CaOo8U9P.js";const l=p({name:"GoView",__name:"index",setup:u=>(_,f)=>{const t=m,o=e,s=a;return i(),n(c,null,[r(t,{title:"\u5927\u5C4F\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/screen/"}),r(s,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:d(()=>[r(o,{src:"http://127.0.0.1:3000"})]),_:1})],64)}});export{l as default};
