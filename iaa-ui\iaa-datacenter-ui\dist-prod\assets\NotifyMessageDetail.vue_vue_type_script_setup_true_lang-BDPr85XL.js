import{_ as c}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as E,j as r,o as N,h as S,w as l,k as a,v as s,t as m,u as e,D as _,m as I}from"./index-C8b06LRn.js";import{E as g,a as h}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as M}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as i}from"./formatTime-COZ9Bl52.js";const P=E({name:"SystemNotifyMessageDetail",__name:"NotifyMessageDetail",setup(Y,{expose:y}){const o=r(!1),p=r(!1),t=r({});return y({open:async f=>{o.value=!0,p.value=!0;try{t.value=f}finally{p.value=!1}}}),(f,n)=>{const u=g,d=M,b=h,v=c;return N(),S(v,{modelValue:e(o),"onUpdate:modelValue":n[0]||(n[0]=T=>I(o)?o.value=T:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5"},{default:l(()=>[a(b,{column:1,border:""},{default:l(()=>[a(u,{label:"\u7F16\u53F7","min-width":"120"},{default:l(()=>[s(m(e(t).id),1)]),_:1}),a(u,{label:"\u7528\u6237\u7C7B\u578B"},{default:l(()=>[a(d,{type:e(_).USER_TYPE,value:e(t).userType},null,8,["type","value"])]),_:1}),a(u,{label:"\u7528\u6237\u7F16\u53F7"},{default:l(()=>[s(m(e(t).userId),1)]),_:1}),a(u,{label:"\u6A21\u7248\u7F16\u53F7"},{default:l(()=>[s(m(e(t).templateId),1)]),_:1}),a(u,{label:"\u6A21\u677F\u7F16\u7801"},{default:l(()=>[s(m(e(t).templateCode),1)]),_:1}),a(u,{label:"\u53D1\u9001\u4EBA\u540D\u79F0"},{default:l(()=>[s(m(e(t).templateNickname),1)]),_:1}),a(u,{label:"\u6A21\u7248\u5185\u5BB9"},{default:l(()=>[s(m(e(t).templateContent),1)]),_:1}),a(u,{label:"\u6A21\u7248\u53C2\u6570"},{default:l(()=>[s(m(e(t).templateParams),1)]),_:1}),a(u,{label:"\u6A21\u7248\u7C7B\u578B"},{default:l(()=>[a(d,{type:e(_).SYSTEM_NOTIFY_TEMPLATE_TYPE,value:e(t).templateType},null,8,["type","value"])]),_:1}),a(u,{label:"\u662F\u5426\u5DF2\u8BFB"},{default:l(()=>[a(d,{type:e(_).INFRA_BOOLEAN_STRING,value:e(t).readStatus},null,8,["type","value"])]),_:1}),a(u,{label:"\u9605\u8BFB\u65F6\u95F4"},{default:l(()=>[s(m(e(i)(e(t).readTime)),1)]),_:1}),a(u,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:l(()=>[s(m(e(i)(e(t).createTime)),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{P as _};
