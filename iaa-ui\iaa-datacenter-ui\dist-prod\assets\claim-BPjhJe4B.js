import{aG as t}from"./index-C8b06LRn.js";const e={getClaimPage:async a=>await t.post({url:"/collection/claim/page",data:a}),createClaim:async a=>await t.post({url:"/collection/claim/create",data:a}),updateClaim:async a=>await t.post({url:"/collection/claim/update",data:a}),deleteClaim:async a=>await t.post({url:"/collection/claim/deletes",data:a}),deleteClaimInformation:async a=>await t.post({url:"/collection/information/deletes",data:a}),getCurrency:async()=>await t.get({url:"/collection/information/getCurrency"}),getCustomer:async a=>await t.get({url:`/collection/reconciliation/getCustomers?customersName=${a}`}),getOrders:async a=>await t.get({url:"/collection/claim/getOrders",params:{code:a.code,claimId:a.claimId,DocNo:a.DocNo}}),getClaimDetail:async a=>await t.get({url:`/collection/claim/get?id=${a}`}),getClaimPrefill:async a=>await t.post({url:"/collection/claim/prefill",data:a})};export{e as C};
