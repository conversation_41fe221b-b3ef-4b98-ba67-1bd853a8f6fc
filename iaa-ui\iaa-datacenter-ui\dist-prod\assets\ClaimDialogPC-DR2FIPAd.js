import{d as Be,y as Fe,aN as qe,j as d,r as He,cj as H,Y as me,au as de,o as i,c as C,k as a,w as t,u as r,l as U,t as w,v as h,x as ce,h as N,m as T,$ as J,F as _e,g as Je,N as Ke,D as Ge,aO as Ae,C as Qe,B as We,a8 as Xe,S as Ze,ab as el,az as ll,H as al,J as ol,K as tl,z as ul,A as rl,I as nl,cy as ml,L as dl,_ as cl}from"./index-C8b06LRn.js";import{C as K}from"./claim-BPjhJe4B.js";import{C as il}from"./index-DVzg-3-A.js";import{I as Ce}from"./index-BX2KAvdx.js";const sl={key:0,class:"mt--8px"},pl={key:1},yl={key:1},vl={key:1},fl={key:1,class:"mt--8px"},hl={key:1},gl={key:1},bl={key:1},_l={key:0},Al={class:"mr-15px"},Cl={key:1},kl={class:"mr-15px"},Nl={key:2},Vl={class:"mb-15px"},wl={class:"mb-15px"},xl=cl(Be({__name:"ClaimDialogPC",props:{show:{type:Boolean},ids:{},prefill:{},readOnly:{type:Boolean},isEdit:{type:Boolean}},emits:["update:show","success"],setup(ke,{emit:Ne}){var ge;const m=Fe(),ie=qe(),V=ke,G=Ne,se=d(!1),Ve=d(),we=d(!1),E=d(!1),O=d(!1),R=d(null),Q=d(-1),z=d([]),u=He({claimDate:H().format("YYYY-MM-DD"),type:1,status:0,salesmanName:((ge=ie.user)==null?void 0:ge.nickname)||"",customerName:"",customerCode:"",totalAmount:0,currency:"",currencyCode:"",remark:"",id:""}),_=d([]),k=d([]),D=d([{type:3,amount:0}]),W=d([]),p=d([]),Y=d(!1),P=d(""),X=d(!1),pe=d([]),j=d(null),S=d(!1),L=d(""),Z=d(!1),ye=d([]),x=d(null),M=d(!1),ee=d([]),y=me(()=>V.readOnly||V.prefill&&V.prefill.readOnly),le=me(()=>u.customerName&&u.customerCode?`${u.customerName}`:""),xe=me(()=>_.value.reduce((l,e)=>l+Number(e.amount||0),0)+k.value.reduce((l,e)=>l+Number(e.amount||0),0)+D.value.reduce((l,e)=>l+Number(e.amount||0),0)),ae=()=>{W.value=p.value.map(l=>`${l.collectionAccount}-${l.collectionAmount}`)},oe=async()=>{try{X.value=!0;const l=await K.getCustomer(P.value||"");pe.value=l||[]}catch{m.error("\u83B7\u53D6\u5BA2\u6237\u6570\u636E\u5931\u8D25")}finally{X.value=!1}},De=l=>{j.value=l},Ue=()=>{j.value?(u.customerName=j.value.name,u.customerCode=j.value.code,Y.value=!1,_.value=[]):m.error("\u8BF7\u9009\u62E9\u5BA2\u6237")},te=async()=>{try{Z.value=!0;const l=await K.getOrders({code:u.customerCode,claimId:u.id,DocNo:L.value});ye.value=l||[]}catch{m.error("\u83B7\u53D6\u8BA2\u5355\u6570\u636E\u5931\u8D25")}finally{Z.value=!1}},Re=l=>{x.value=l},Ye=()=>{if(!x.value||Q.value<0)return void m.error("\u8BF7\u9009\u62E9\u8BA2\u5355");if(u.currency&&x.value.currency&&u.currency!==x.value.currency)return void m.error(`\u6240\u9009\u8BA2\u5355\u5E01\u79CD\u4E3A ${x.value.currency}\uFF0C\u4E0E\u5F53\u524D\u8BA4\u9886\u5E01\u79CD ${u.currency} \u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u9009\u62E9`);const l=_.value[Q.value];l.orderNo=x.value.DocNo,l.orderAmount=x.value.salesPrice,l.remainingAmount=x.value.remainingAmount,l.amount=x.value.remainingAmount,l.claimRatio=Math.round(l.amount/l.orderAmount*100),S.value=!1},B=d(),Se=l=>{if(l.length>0&&[...new Set(l.map(e=>e.currency))].length>1)return m.alertError("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u4E00\u8D77\u8BA4\u9886"),void Ae(()=>{var v;const e=document.querySelector(".el-dialog .el-table");e&&e.__vue__&&((v=e.__vue__)==null||v.clearSelection())});z.value=l},Me=()=>{y.value||_.value.push({type:1,orderNo:"",orderAmount:0,amount:0,claimRatio:100})},Ie=()=>{y.value||k.value.push({type:2,expenseType:"",amount:0})},$e=()=>{if(!z.value.length)return void m.error("\u8BF7\u9009\u62E9\u6536\u6B3E\u4FE1\u606F");[...new Set(z.value.map(l=>l.currency))].length>1?m.alertError("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u4E00\u8D77\u8BA4\u9886"):(p.value=z.value.map(l=>({id:l.id,dateStr:l.dateStr,collectionAccount:l.collectionAccount,collectionId:l.collectionId,collectionAmount:l.collectionAmount,currency:l.currency,currencyCode:l.currencyCode})),p.value.length>0&&(u.currency=p.value[0].currency,u.currencyCode=p.value[0].currencyCode),u.totalAmount=p.value.reduce((l,e)=>l+e.collectionAmount,0),ae(),M.value=!1)},ue=d(0);de([_,k,D],()=>{ue.value=xe.value},{deep:!0}),de(()=>V.prefill,async l=>{if(l){if(R.value=l.id||null,u.claimDate=l.claimDate,u.salesmanName=l.salesmanName||u.salesmanName,u.customerName=l.customerName||"",u.customerCode=l.customerCode||"",u.totalAmount=Number(l.totalAmount||0),u.currency=l.currency||"",u.currencyCode=l.currencyCode||"",u.remark=l.remark||"",u.id=l.id,p.value=(l.collectionList||[]).map(e=>({id:e.id,dateStr:e.dateStr,collectionAccount:e.collectionAccount,collectionId:e.collectionId,collectionAmount:e.collectionAmount,currency:e.currency,currencyCode:e.currencyCode})),p.value.length>0&&[...new Set(p.value.map(e=>e.currency))].length>1)return void m.alertError("\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4");ae(),_.value=[],k.value=[],D.value=[{type:3,amount:0}],(l.detailList||[]).forEach(e=>{if(e.type===1){let v=100;e.orderAmount>0&&(v=Math.round(Number(e.amount||0)/e.orderAmount*100)),_.value.push({type:1,orderNo:e.orderNo,orderAmount:Number(e.orderAmount||0),currency:e.currency||"\u7F8E\u5143",amount:Number(e.amount||0),remainingAmount:Number(e.remainingAmount||0),shipAmount:Number(e.shipAmount||0),claimRatio:v})}else e.type===2?k.value.push({type:2,expenseType:e.expenseType,currency:e.currency||"\u7F8E\u5143",amount:Number(e.amount||0),remainingAmount:Number(e.remainingAmount||0),shipAmount:Number(e.shipAmount||0),expenseRemark:e.expenseRemark}):e.type===3&&(D.value[0].amount=Number(e.amount||0))})}},{immediate:!0});const ve=()=>[..._.value.map(l=>({type:1,orderNo:l.orderNo,orderAmount:l.orderAmount,amount:Number(l.amount||0),remainingAmount:Number(l.remainingAmount||0),shipAmount:Number(l.shipAmount||0)})),...k.value.map(l=>({type:2,expenseType:l.expenseType,amount:Number(l.amount||0),remainingAmount:Number(l.remainingAmount||0),expenseRemark:l.expenseRemark})),...D.value.map(l=>({type:3,amount:Number(l.amount||0)})).filter(l=>l.amount>0)],Te=async()=>{if(O.value)return;if(!p.value.length)return void m.error("\u672A\u9009\u62E9\u4EFB\u4F55\u6536\u6B3E\u8BB0\u5F55");if(!he())return;const l={id:R.value||void 0,claimDate:u.claimDate||H().format("YYYY-MM-DD"),type:u.type,status:2,salesmanName:u.salesmanName,customerName:u.customerName,customerCode:u.customerCode,currency:u.currency,currencyCode:u.currencyCode,totalAmount:u.totalAmount,remark:u.remark,detailList:ve(),collectionList:p.value};try{O.value=!0;const e=await K.createClaim(l);e&&e==="\u64CD\u4F5C\u6210\u529F"?(R.value=null,m.success("\u6682\u5B58\u6210\u529F"),G("success"),F()):m.error(e)}catch{m.error("\u6682\u5B58\u5931\u8D25")}finally{O.value=!1}},fe=async()=>{if(E.value)return;if(!p.value.length)return void m.error("\u672A\u9009\u62E9\u4EFB\u4F55\u6536\u6B3E\u8BB0\u5F55");const l=_.value.reduce((f,A)=>f+Number(A.amount||0),0)+k.value.reduce((f,A)=>f+Number(A.amount||0),0)+D.value.reduce((f,A)=>f+Number(A.amount||0),0),e=u.totalAmount;if(Math.abs(l-e)>.01)return void m.alertError(`\u8BA4\u6B3E\u603B\u91D1\u989D\uFF08${l}\uFF09\u5FC5\u987B\u7B49\u4E8E\u6536\u6B3E\u603B\u91D1\u989D\uFF08${e}\uFF09`);if(!he())return;const v={id:R.value||void 0,claimDate:u.claimDate||H().format("YYYY-MM-DD"),type:u.type,status:u.status,salesmanName:u.salesmanName,customerName:u.customerName,customerCode:u.customerCode,currency:u.currency,currencyCode:u.currencyCode,totalAmount:u.totalAmount,remark:u.remark,detailList:ve(),collectionList:p.value};try{E.value=!0;const f=await K.createClaim(v);f&&f==="\u64CD\u4F5C\u6210\u529F"?(R.value=null,m.success("\u8BA4\u9886\u6210\u529F"),G("success"),F()):m.error(f)}catch{m.error("\u8BA4\u9886\u5931\u8D25")}finally{E.value=!1}},F=()=>{G("update:show",!1),Ee()},Ee=()=>{var l;Object.assign(u,{claimDate:H().format("YYYY-MM-DD"),type:1,status:0,salesmanName:((l=ie.user)==null?void 0:l.nickname)||"",customerName:"",customerCode:"",totalAmount:0,currency:"",id:""}),_.value=[],k.value=[],p.value=[],W.value=[],R.value=null,D.value=[{type:3,amount:0}]};de(()=>V.ids,async l=>{if(l&&l.length&&!V.prefill)try{const e=await Ce.getInformation(l);if([...new Set(e.map(v=>v.currency))].length>1)return void m.alertError("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u4E00\u8D77\u8BA4\u9886");p.value=e.map(v=>({id:v.id,dateStr:v.dateStr,collectionAccount:v.collectionAccount,collectionId:v.collectionId,collectionAmount:v.collectionAmount,currency:v.currency,currencyCode:v.currencyCode})),p.value.length>0&&(u.currency=p.value[0].currency,u.currencyCode=p.value[0].currencyCode),u.totalAmount=p.value.reduce((v,f)=>v+f.collectionAmount,0),ae()}catch{m.error("\u52A0\u8F7D\u6536\u6B3E\u4FE1\u606F\u5931\u8D25")}se.value=await il.getDate()},{immediate:!0});const he=()=>{if(!u.customerName||!u.customerCode)return m.error("\u5BA2\u6237\u540D\u79F0\u548C\u5BA2\u6237\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"),!1;for(let l=0;l<_.value.length;l++){const e=_.value[l];if(!e.orderNo)return m.error(`\u7B2C${l+1}\u6761\u8BA2\u5355\u660E\u7EC6\u7684\u8BA2\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A`),!1;if(e.amount<=0)return m.error(`\u7B2C${l+1}\u6761\u8BA2\u5355\u660E\u7EC6\u7684\u8BA4\u6B3E\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`),!1}for(let l=0;l<k.value.length;l++){const e=k.value[l];if(e.expenseType===void 0||e.expenseType===null||e.expenseType==="")return m.error(`\u7B2C${l+1}\u6761\u8D39\u7528\u660E\u7EC6\u7684\u8D39\u7528\u7C7B\u522B\u4E0D\u80FD\u4E3A\u7A7A`),!1;if(e.amount<=0)return m.error(`\u7B2C${l+1}\u6761\u8D39\u7528\u660E\u7EC6\u7684\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`),!1}return!0};return(l,e)=>{const v=Qe,f=We,A=Xe,b=Ze,Le=el,re=ll,g=al,s=ol,I=tl,Oe=ul,ze=rl,Pe=nl,q=ml,ne=dl;return i(),C(_e,null,[a(q,{"model-value":l.show,title:r(y)?"\u8BA4\u6B3E\u8BE6\u60C5":"\u6536\u6B3E\u8BA4\u9886",width:"40%",top:"15vh","close-on-click-modal":!1,draggable:!0,style:{"margin-top":"1vh"},onClose:F},{footer:t(()=>[r(y)||V.isEdit?V.isEdit?(i(),C("div",Cl,[U("span",kl,"\u5DF2\u586B\u5199\u603B\u91D1\u989D\uFF1A"+w(r(ue)),1),a(g,{type:"primary",onClick:fe,loading:r(E)},{default:t(()=>e[26]||(e[26]=[h(" \u786E\u8BA4\u4FEE\u6539 ")])),_:1},8,["loading"])])):(i(),C("div",Nl,[a(g,{type:"primary",onClick:F},{default:t(()=>e[27]||(e[27]=[h("\u5173\u95ED")])),_:1})])):(i(),C("div",_l,[U("span",Al,"\u5DF2\u586B\u5199\u603B\u91D1\u989D\uFF1A"+w(r(ue)),1),a(g,{onClick:Te,loading:r(O)},{default:t(()=>e[24]||(e[24]=[h("\u6682\u5B58")])),_:1},8,["loading"]),a(g,{type:"primary",onClick:fe,loading:r(E)},{default:t(()=>e[25]||(e[25]=[h(" \u786E\u8BA4\u63D0\u4EA4 ")])),_:1},8,["loading"])]))]),default:t(()=>[ce((i(),N(Pe,{ref_key:"formRef",ref:Ve,model:r(u),"label-width":"100px",disabled:r(y)},{default:t(()=>[a(Le,{gutter:20},{default:t(()=>[a(A,{span:12},{default:t(()=>[a(f,{label:"\u8BA4\u9886\u65E5\u671F",prop:"claimDate"},{default:t(()=>[a(v,{modelValue:r(u).claimDate,"onUpdate:modelValue":e[0]||(e[0]=o=>r(u).claimDate=o),type:"date","value-format":"YYYY-MM-DD",placeholder:"\u9009\u62E9\u65E5\u671F",style:{width:"100%"},disabled:!r(se)},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u4E1A\u52A1\u5458",prop:"salesmanName"},{default:t(()=>[a(b,{modelValue:r(u).salesmanName,"onUpdate:modelValue":e[1]||(e[1]=o=>r(u).salesmanName=o),placeholder:"\u9ED8\u8BA4\u5F53\u524D\u767B\u5F55\u4EBA"},null,8,["modelValue"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName"},{default:t(()=>[a(b,{modelValue:r(le),"onUpdate:modelValue":e[2]||(e[2]=o=>T(le)?le.value=o:null),placeholder:"\u8F93\u5165\u9009\u62E9",readonly:"",onClick:e[3]||(e[3]=o=>!r(y)&&(async()=>{y.value||(Y.value=!0,await oe())})())},null,8,["modelValue"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u5BA2\u6237\u7F16\u7801",prop:"customerCode"},{default:t(()=>[a(b,{modelValue:r(u).customerCode,"onUpdate:modelValue":e[4]||(e[4]=o=>r(u).customerCode=o),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u5E01\u79CD",prop:"currency"},{default:t(()=>[a(b,{modelValue:r(u).currency,"onUpdate:modelValue":e[5]||(e[5]=o=>r(u).currency=o),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u603B\u91D1\u989D",prop:"totalAmount"},{default:t(()=>[a(b,{modelValue:r(u).totalAmount,"onUpdate:modelValue":e[6]||(e[6]=o=>r(u).totalAmount=o),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u5DF2\u9009\u62E9"},{default:t(()=>[a(b,{"model-value":r(W).join(", "),readonly:"",onClick:e[7]||(e[7]=o=>!r(y)&&(async()=>{if(!y.value){M.value=!0;try{const c={pageNo:-1,pageSize:30,status:[0],isMe:!1};V.prefill&&(c.isMe=!0);const n=await Ce.getInformationPage(c);ee.value=n.list||[],Ae(()=>{p.value.length>0&&B.value&&(B.value.clearSelection(),p.value.forEach($=>{const be=ee.value.find(je=>je.id===$.id);be&&B.value.toggleRowSelection(be,!0)}))})}catch{m.error("\u83B7\u53D6\u6536\u6B3E\u4FE1\u606F\u5931\u8D25")}}})())},null,8,["model-value"])]),_:1})]),_:1}),a(A,{span:12},{default:t(()=>[a(f,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[a(b,{modelValue:r(u).remark,"onUpdate:modelValue":e[8]||(e[8]=o=>r(u).remark=o)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),U("div",null,[a(re,{class:"p-2px"},{default:t(()=>e[17]||(e[17]=[h("\u8BA2\u5355\u660E\u7EC6")])),_:1}),r(y)?J("",!0):(i(),C("div",sl,[a(g,{type:"primary",plain:"",onClick:Me,size:"small"},{default:t(()=>e[18]||(e[18]=[h("+ \u6DFB\u52A0\u8BA2\u5355")])),_:1})])),a(I,{data:r(_),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[a(s,{label:"\u8BA2\u5355\u53F7","min-width":"160"},{default:t(({row:o,$index:c})=>[r(y)?(i(),C("span",pl,w(o.orderNo),1)):(i(),N(b,{key:0,modelValue:o.orderNo,"onUpdate:modelValue":n=>o.orderNo=n,readonly:"",onClick:n=>{return $=c,void(y.value||(u.customerCode?(Q.value=$,L.value="",S.value=!0,te()):m.error("\u8BF7\u5148\u9009\u62E9\u5BA2\u6237")));var $}},null,8,["modelValue","onUpdate:modelValue","onClick"]))]),_:1}),a(s,{label:"\u8BA2\u5355\u91D1\u989D","min-width":"100"},{default:t(({row:o})=>[U("span",null,w(o.orderAmount),1)]),_:1}),a(s,{label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D","min-width":"100"},{default:t(({row:o})=>[U("span",null,w(o.remainingAmount),1)]),_:1}),a(s,{label:"\u6536\u6B3E\u6BD4\u4F8B(%)","min-width":"90"},{default:t(({row:o})=>[r(y)?(i(),C("span",yl,w(o.claimRatio),1)):(i(),N(b,{key:0,modelValue:o.claimRatio,"onUpdate:modelValue":c=>o.claimRatio=c,type:"number",step:1,min:0,max:100,style:{width:"100%"},onChange:c=>(n=>{if(n.claimRatio!==void 0&&n.claimRatio!==null){const $=Math.round(n.orderAmount*n.claimRatio/100);n.amount=$,n.amount>n.remainingAmount&&(n.amount=n.remainingAmount,n.orderAmount>0&&(n.claimRatio=Math.round(n.amount/n.orderAmount*100)),m.warning("\u8BA4\u6B3E\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5269\u4F59\u53EF\u8BA4\u6B3E\u91D1\u989D\uFF0C\u6536\u6B3E\u6BD4\u4F8B\u5DF2\u81EA\u52A8\u8C03\u6574"))}})(o)},null,8,["modelValue","onUpdate:modelValue","onChange"]))]),_:1}),a(s,{label:"\u8BA4\u6B3E\u91D1\u989D","min-width":"120"},{default:t(({row:o})=>[r(y)?(i(),C("span",vl,w(o.amount),1)):(i(),N(b,{key:0,modelValue:o.amount,"onUpdate:modelValue":c=>o.amount=c,type:"number",step:1,min:0,max:o.orderAmount,style:{width:"100%"},onChange:c=>(n=>{n.amount>n.remainingAmount?(n.amount=n.remainingAmount,n.orderAmount>0&&(n.claimRatio=Math.round(n.amount/n.orderAmount*100)),m.warning("\u8BA4\u6B3E\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5269\u4F59\u53EF\u8BA4\u6B3E\u91D1\u989D\uFF0C\u6536\u6B3E\u6BD4\u4F8B\u5DF2\u81EA\u52A8\u8C03\u6574")):n.orderAmount>0&&(n.claimRatio=Math.round(n.amount/n.orderAmount*100))})(o)},null,8,["modelValue","onUpdate:modelValue","max","onChange"]))]),_:1}),r(y)?J("",!0):(i(),N(s,{key:0,label:"\u64CD\u4F5C",width:"80"},{default:t(({$index:o})=>[a(g,{type:"danger",size:"small",onClick:c=>{return n=o,void(y.value||_.value.splice(n,1));var n}},{default:t(()=>e[19]||(e[19]=[h(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),a(re,{class:"p-2px"},{default:t(()=>e[20]||(e[20]=[h("\u8D39\u7528\u7C7B\u522B")])),_:1}),r(y)?J("",!0):(i(),C("div",fl,[a(g,{type:"primary",plain:"",onClick:Ie,size:"small"},{default:t(()=>e[21]||(e[21]=[h("+ \u6DFB\u52A0\u8D39\u7528")])),_:1})])),a(I,{data:r(k),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[a(s,{label:"\u8D39\u7528\u7C7B\u522B","min-width":"80"},{default:t(({row:o})=>[a(ze,{modelValue:o.expenseType,"onUpdate:modelValue":c=>o.expenseType=c,placeholder:"\u8BF7\u9009\u62E9\u8D39\u7528\u7C7B\u522B",style:{width:"100%"}},{default:t(()=>[(i(!0),C(_e,null,Je(r(Ke)(r(Ge).FINANCIAL_COSTS_TYPE),c=>(i(),N(Oe,{key:c.value,label:c.label,value:c.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(s,{label:"\u91D1\u989D","min-width":"80"},{default:t(({row:o})=>[r(y)?(i(),C("span",hl,w(o.amount),1)):(i(),N(b,{key:0,modelValue:o.amount,"onUpdate:modelValue":c=>o.amount=c,precision:2,type:"number",min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),a(s,{label:"\u8D39\u7528\u5907\u6CE8","min-width":"120"},{default:t(({row:o})=>[V.readOnly?(i(),C("span",gl,w(o.expenseRemark),1)):(i(),N(b,{key:0,modelValue:o.expenseRemark,"onUpdate:modelValue":c=>o.expenseRemark=c},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),r(y)?J("",!0):(i(),N(s,{key:0,label:"\u64CD\u4F5C",width:"80"},{default:t(({$index:o})=>[a(g,{type:"danger",size:"small",onClick:c=>{return n=o,void(y.value||k.value.splice(n,1));var n}},{default:t(()=>e[22]||(e[22]=[h(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),a(re,{class:"p-2px"},{default:t(()=>e[23]||(e[23]=[h("\u8BA2\u5355\u672A\u4E0B")])),_:1}),a(I,{data:r(D),border:"","max-height":150,height:"100","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:t(()=>[a(s,{label:"\u91D1\u989D","min-width":"120"},{default:t(({row:o})=>[r(y)?(i(),C("span",bl,w(o.amount),1)):(i(),N(b,{key:0,modelValue:o.amount,"onUpdate:modelValue":c=>o.amount=c,precision:2,type:"number",min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"]))]),_:1})]),_:1},8,["data"])])]),_:1},8,["model","disabled"])),[[ne,r(we)]])]),_:1},8,["model-value","title"]),a(q,{modelValue:r(Y),"onUpdate:modelValue":e[11]||(e[11]=o=>T(Y)?Y.value=o:null),title:"\u9009\u62E9\u5BA2\u6237",width:"600px","append-to-body":""},{footer:t(()=>[a(g,{onClick:e[10]||(e[10]=o=>Y.value=!1)},{default:t(()=>e[29]||(e[29]=[h("\u53D6\u6D88")])),_:1}),a(g,{type:"primary",onClick:Ue},{default:t(()=>e[30]||(e[30]=[h("\u786E\u8BA4")])),_:1})]),default:t(()=>[U("div",Vl,[a(b,{modelValue:r(P),"onUpdate:modelValue":e[9]||(e[9]=o=>T(P)?P.value=o:null),placeholder:"\u8F93\u5165\u5BA2\u6237\u540D\u79F0\u68C0\u7D22",clearable:"",onInput:oe},{append:t(()=>[a(g,{onClick:oe},{default:t(()=>e[28]||(e[28]=[h("\u641C\u7D22")])),_:1})]),_:1},8,["modelValue"])]),ce((i(),N(I,{data:r(pe),height:"300","highlight-current-row":"",onCurrentChange:De},{default:t(()=>[a(s,{prop:"name",label:"\u5BA2\u6237\u540D\u79F0"}),a(s,{prop:"code",label:"\u5BA2\u6237\u7F16\u7801"})]),_:1},8,["data"])),[[ne,r(X)]])]),_:1},8,["modelValue"]),a(q,{modelValue:r(S),"onUpdate:modelValue":e[14]||(e[14]=o=>T(S)?S.value=o:null),title:"\u9009\u62E9\u8BA2\u5355",width:"40%","append-to-body":""},{footer:t(()=>[a(g,{onClick:e[13]||(e[13]=o=>S.value=!1)},{default:t(()=>e[32]||(e[32]=[h("\u53D6\u6D88")])),_:1}),a(g,{type:"primary",onClick:Ye},{default:t(()=>e[33]||(e[33]=[h("\u786E\u8BA4")])),_:1})]),default:t(()=>[U("div",wl,[a(b,{modelValue:r(L),"onUpdate:modelValue":e[12]||(e[12]=o=>T(L)?L.value=o:null),placeholder:"\u8F93\u5165\u8BA2\u5355\u53F7\u68C0\u7D22",clearable:"",onInput:te},{append:t(()=>[a(g,{onClick:te},{default:t(()=>e[31]||(e[31]=[h("\u641C\u7D22")])),_:1})]),_:1},8,["modelValue"])]),ce((i(),N(I,{data:r(ye),height:"300","highlight-current-row":"",onCurrentChange:Re},{default:t(()=>[a(s,{prop:"DocNo",label:"\u8BA2\u5355\u53F7",width:"160"}),a(s,{prop:"currency",label:"\u5E01\u79CD",width:"110"}),a(s,{prop:"salesPrice",label:"\u8BA2\u5355\u603B\u91D1\u989D",width:"110"}),a(s,{prop:"shipPrice",label:"\u5DF2\u51FA\u8D27\u91D1\u989D",width:"110"}),a(s,{prop:"claimedAmount",label:"\u5DF2\u8BA4\u9886\u91D1\u989D",width:"120"}),a(s,{prop:"remainingAmount",label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D",width:"120"})]),_:1},8,["data"])),[[ne,r(Z)]])]),_:1},8,["modelValue"]),a(q,{modelValue:r(M),"onUpdate:modelValue":e[16]||(e[16]=o=>T(M)?M.value=o:null),title:"\u9009\u62E9\u6536\u6B3E\u4FE1\u606F",width:"35%","append-to-body":""},{footer:t(()=>[a(g,{onClick:e[15]||(e[15]=o=>M.value=!1)},{default:t(()=>e[34]||(e[34]=[h("\u53D6\u6D88")])),_:1}),a(g,{type:"primary",onClick:$e},{default:t(()=>e[35]||(e[35]=[h("\u786E\u8BA4")])),_:1})]),default:t(()=>[a(I,{ref_key:"collectionTableRef",ref:B,data:r(ee),height:"300",onSelectionChange:Se},{default:t(()=>[a(s,{type:"selection","min-width":"15"}),a(s,{prop:"collectionAccount",label:"\u6536\u6B3E\u8D26\u6237","min-width":"130"}),a(s,{prop:"collectionAmount",label:"\u6536\u6B3E\u91D1\u989D","min-width":"50"}),a(s,{prop:"currency",label:"\u5E01\u79CD","min-width":"30"}),a(s,{prop:"dateStr",label:"\u6536\u6B3E\u65E5\u671F","min-width":"50"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-e2a4399e"]]);export{xl as default};
