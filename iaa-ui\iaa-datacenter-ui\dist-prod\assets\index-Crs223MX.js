import{d as K,j as s,r as E,f as Q,o as i,c as Y,k as e,w as o,u as l,M as W,F as C,g as X,h as m,N as Z,D as I,v as c,x as h,l as j,t as z,y as $,O as ee,S as ae,B as le,C as te,z as oe,A as re,G as ne,H as se,I as ie,J as de,K as pe,T as ue,L as me}from"./index-C8b06LRn.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as _e}from"./index-CkzUfjB7.js";import{f as D}from"./formatTime-COZ9Bl52.js";import{d as ge}from"./download-D5Lb_h0f.js";import{_ as ve}from"./JobLogDetail.vue_vue_type_script_setup_true_lang-BSbBpm7W.js";import{g as ye,e as he}from"./index-GtctjSt3.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./el-descriptions-item-Ctb8GMnZ.js";const xe=K({name:"InfraJobLog",__name:"index",setup(we){const M=$(),{query:q}=ee(),f=s(!0),x=s(0),w=s([]),r=E({pageNo:1,pageSize:10,jobId:q.id,handlerName:void 0,beginTime:void 0,endTime:void 0,status:void 0}),T=s(),b=s(!1),_=async()=>{f.value=!0;try{const d=await ye(r);w.value=d.list,x.value=d.total}finally{f.value=!1}},g=()=>{r.pageNo=1,_()},A=()=>{T.value.resetFields(),g()},N=s(),F=async()=>{try{await M.exportConfirm(),b.value=!0;const d=await he(r);ge.excel(d,"\u5B9A\u65F6\u4EFB\u52A1\u6267\u884C\u65E5\u5FD7.xls")}catch{}finally{b.value=!1}};return Q(()=>{_()}),(d,t)=>{const v=_e,H=ae,p=le,V=te,J=oe,O=re,y=ne,u=se,B=ie,k=be,n=de,L=fe,R=pe,G=ce,S=ue("hasPermi"),P=me;return i(),Y(C,null,[e(v,{title:"\u5B9A\u65F6\u4EFB\u52A1",url:"https://doc.iocoder.cn/job/"}),e(v,{title:"\u5F02\u6B65\u4EFB\u52A1",url:"https://doc.iocoder.cn/async-task/"}),e(v,{title:"\u6D88\u606F\u961F\u5217",url:"https://doc.iocoder.cn/message-queue/"}),e(k,null,{default:o(()=>[e(B,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"120px"},{default:o(()=>[e(p,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",prop:"handlerName"},{default:o(()=>[e(H,{modelValue:l(r).handlerName,"onUpdate:modelValue":t[0]||(t[0]=a=>l(r).handlerName=a),placeholder:"\u8BF7\u8F93\u5165\u5904\u7406\u5668\u7684\u540D\u5B57",clearable:"",onKeyup:W(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(p,{label:"\u5F00\u59CB\u6267\u884C\u65F6\u95F4",prop:"beginTime"},{default:o(()=>[e(V,{modelValue:l(r).beginTime,"onUpdate:modelValue":t[1]||(t[1]=a=>l(r).beginTime=a),type:"date","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u9009\u62E9\u5F00\u59CB\u6267\u884C\u65F6\u95F4",clearable:"",class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(p,{label:"\u7ED3\u675F\u6267\u884C\u65F6\u95F4",prop:"endTime"},{default:o(()=>[e(V,{modelValue:l(r).endTime,"onUpdate:modelValue":t[2]||(t[2]=a=>l(r).endTime=a),type:"date","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u9009\u62E9\u7ED3\u675F\u6267\u884C\u65F6\u95F4",clearable:"","default-time":new Date("1 23:59:59"),class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(p,{label:"\u4EFB\u52A1\u72B6\u6001",prop:"status"},{default:o(()=>[e(O,{modelValue:l(r).status,"onUpdate:modelValue":t[3]||(t[3]=a=>l(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(i(!0),Y(C,null,X(l(Z)(l(I).INFRA_JOB_LOG_STATUS),a=>(i(),m(J,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(p,null,{default:o(()=>[e(u,{onClick:g},{default:o(()=>[e(y,{icon:"ep:search",class:"mr-5px"}),t[6]||(t[6]=c(" \u641C\u7D22"))]),_:1}),e(u,{onClick:A},{default:o(()=>[e(y,{icon:"ep:refresh",class:"mr-5px"}),t[7]||(t[7]=c(" \u91CD\u7F6E"))]),_:1}),h((i(),m(u,{type:"success",plain:"",onClick:F,loading:l(b)},{default:o(()=>[e(y,{icon:"ep:download",class:"mr-5px"}),t[8]||(t[8]=c(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[S,["infra:job:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,null,{default:o(()=>[h((i(),m(R,{data:l(w)},{default:o(()=>[e(n,{label:"\u65E5\u5FD7\u7F16\u53F7",align:"center",prop:"id"}),e(n,{label:"\u4EFB\u52A1\u7F16\u53F7",align:"center",prop:"jobId"}),e(n,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",align:"center",prop:"handlerName"}),e(n,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570",align:"center",prop:"handlerParam"}),e(n,{label:"\u7B2C\u51E0\u6B21\u6267\u884C",align:"center",prop:"executeIndex"}),e(n,{label:"\u6267\u884C\u65F6\u95F4",align:"center",width:"170s"},{default:o(a=>[j("span",null,z(l(D)(a.row.beginTime)+" ~ "+l(D)(a.row.endTime)),1)]),_:1}),e(n,{label:"\u6267\u884C\u65F6\u957F",align:"center",prop:"duration"},{default:o(a=>[j("span",null,z(a.row.duration+" \u6BEB\u79D2"),1)]),_:1}),e(n,{label:"\u4EFB\u52A1\u72B6\u6001",align:"center",prop:"status"},{default:o(a=>[e(L,{type:l(I).INFRA_JOB_LOG_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(n,{label:"\u64CD\u4F5C",align:"center"},{default:o(a=>[h((i(),m(u,{type:"primary",link:"",onClick:Te=>{return U=a.row.id,void N.value.open(U);var U}},{default:o(()=>t[9]||(t[9]=[c(" \u8BE6\u7EC6 ")])),_:2},1032,["onClick"])),[[S,["infra:job:query"]]])]),_:1})]),_:1},8,["data"])),[[P,l(f)]]),e(G,{total:l(x),page:l(r).pageNo,"onUpdate:page":t[4]||(t[4]=a=>l(r).pageNo=a),limit:l(r).pageSize,"onUpdate:limit":t[5]||(t[5]=a=>l(r).pageSize=a),onPagination:_},null,8,["total","page","limit"])]),_:1}),e(ve,{ref_key:"detailRef",ref:N},null,512)],64)}}});export{xe as default};
