import{aG as a}from"./index-C8b06LRn.js";const t={getInformationPage:async o=>await a.post({url:"/collection/information/page",data:o}),getInformation:async o=>await a.post({url:"/collection/information/get",data:o}),getSuspended:async o=>await a.get({url:"/collection/information/getSuspended",params:{id:o}}),createInformation:async o=>await a.post({url:"/collection/information/create",data:o}),deleteInformation:async o=>await a.post({url:"/collection/information/deletes",data:o}),exportInformation:async o=>await a.download({url:"/collection/information/export-excel",params:o}),exportTemplate:()=>a.download({url:"/collection/information/export-template"})};export{t as I};
