import{d as p,j as o,f as u,o as n,c as f,k as e,w as d,u as t,x as k,h as y,$ as _,F as g,L as h}from"./index-C8b06LRn.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as v}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as w}from"./index-CkzUfjB7.js";import{b}from"./index-C78nCjiS.js";import"./el-card-CaOo8U9P.js";const S=p({name:"InfraSkyWalking",__name:"index",setup(j){const a=o(!0),r=o("http://skywalking.shop.iocoder.cn");return u(async()=>{try{const s=await b("url.skywalking");s&&s.length>0&&(r.value=s)}finally{a.value=!1}}),(s,F)=>{const l=w,i=v,m=x,c=h;return n(),f(g,null,[e(l,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(m,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:d(()=>[t(a)?_("",!0):k((n(),y(i,{key:0,src:t(r)},null,8,["src"])),[[c,t(a)]])]),_:1})],64)}}});export{S as default};
