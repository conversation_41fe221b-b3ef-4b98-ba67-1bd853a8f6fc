import{m as u}from"./index-C8b06LRn.js";const o=e=>JSON.stringify(e.value.getOption()),n=e=>{const a=e.value.getRule(),t=[];return a.forEach(r=>{t.push(JSON.stringify(r))}),t},s=e=>{const a=[];return e.forEach(t=>{a.push(JSON.parse(t))}),a},p=(e,a,t)=>{e.value.setOption(JSON.parse(a)),e.value.setRule(s(t))},l=(e,a,t,r)=>{u(e)&&(e=e.value),e.option=JSON.parse(a),e.rule=s(t),r&&(e.value=r)};export{n as a,l as b,o as e,p as s};
