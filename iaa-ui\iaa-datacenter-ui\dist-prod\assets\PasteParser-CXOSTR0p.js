import{d as J,aj as F,j as _,Y as L,au as A,o as i,c as d,l as s,k as g,w as f,v as j,t as h,F as z,g as V,h as O,$ as T,d1 as p,H as B,S as H,z as K,A as M,J as Y,K as G,_ as Q}from"./index-C8b06LRn.js";const X={class:"paste-parser"},Z={class:"flex justify-between items-center mb-4"},ee={class:"flex gap-2"},ae={class:"mb-4"},le={class:"text-xs text-gray-500 mt-1"},te={key:0,class:"mb-4"},se={class:"grid grid-cols-6 md:grid-cols-6 lg:grid-cols-6 gap-4 p-4 bg-gray-50 rounded"},re={class:"text-xs text-gray-600 mb-1"},ie=["title"],ue={key:1,class:"mb-4"},ne={class:"flex justify-between items-center mb-2"},oe={class:"flex gap-2"},de={class:"max-h-96 overflow-auto border rounded"},ce={key:2,class:"mb-4"},me={class:"bg-red-50 border border-red-200 rounded p-3"},ve={class:"text-sm text-red-600"},ge=Q(J({__name:"PasteParser",props:{targetFields:F.oneOfType([]).isRequired,mappings:F.oneOfType([]).isRequired},emits:["data-parsed"],setup(S,{emit:U}){const q=U,$=S,b=_(""),u=_([]),n=_([]),y=_([]),v=L(()=>b.value.trim()?b.value.trim().split(`
`).filter(a=>a.trim()):[]);A(v,a=>{a.length>0&&C(a[0])},{immediate:!0});const C=a=>{const e=a.split("	");y.value=e.map((r,l)=>({target:$.mappings[l]||"",sample:r.trim().substring(0,20)+(r.trim().length>20?"...":"")}))},E=()=>{setTimeout(()=>{v.value.length>0&&p.success(`\u68C0\u6D4B\u5230 ${v.value.length} \u884C\u6570\u636E`)},100)},I=()=>{u.value=[],n.value=[]},N=()=>{if(n.value=[],u.value=[],v.value.length===0)return void p.warning("\u8BF7\u5148\u8F93\u5165\u6570\u636E");const a=[];v.value.forEach((e,r)=>{try{const l=e.split("	"),o={};if(y.value.forEach((m,k)=>{if(m.target&&l[k]!==void 0){const c=l[k].trim();if(m.target==="quantity"){const t=parseFloat(c);isNaN(t)||(o[m.target]=t)}else m.target==="requireDate"?c&&c!==""&&(o[m.target]=P(c)):c&&c!==""&&(o[m.target]=c)}}),!o.model)return void n.value.push({row:r+1,message:"\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A"});if(!o.classification)return void n.value.push({row:r+1,message:"\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A"});a.push(o)}catch(l){n.value.push({row:r+1,message:`\u89E3\u6790\u5931\u8D25: ${l}`})}}),u.value=a,a.length>0&&p.success(`\u6210\u529F\u89E3\u6790 ${a.length} \u6761\u6570\u636E`),n.value.length>0&&p.warning(`\u6709 ${n.value.length} \u6761\u6570\u636E\u89E3\u6790\u5931\u8D25`)},P=a=>{const e=new Date(a);return isNaN(e.getTime())?a:e.toISOString().split("T")[0]},R=()=>{b.value="",u.value=[],n.value=[],y.value=[]},D=()=>{if(u.value.length===0)return void p.warning("\u6CA1\u6709\u53EF\u5BFC\u51FA\u7684\u6570\u636E");const a=[["\u578B\u53F7","\u5206\u7C7B","\u7EC4\u88C5\u6807\u51C6\u5DE5\u65F6","\u5305\u88C5\u6807\u51C6\u5DE5\u65F6","\u7EC4\u5305\u6807\u51C6\u5DE5\u65F6","\u8FC7\u819C","\u7EC4\u5305\u819C","\u5907\u6CE8"].join(","),...u.value.map(l=>[l.model||"",l.classification||"",l.assembledWork||"",l.packagingWork||"",l.standardWork||"",l.throughMembrane||"",l.capsule||"",l.remark||""].map(o=>`"${o}"`).join(","))].join(`
`),e=new Blob([a],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");r.href=URL.createObjectURL(e),r.download=`\u6807\u51C6\u5DE5\u65F6\u5F55\u5165\u8868_${new Date().toISOString().split("T")[0]}.csv`,r.click(),p.success("\u6570\u636E\u5BFC\u51FA\u6210\u529F")},W=()=>{u.value.length!==0?q("data-parsed",u.value):p.warning("\u6CA1\u6709\u53EF\u786E\u8BA4\u7684\u6570\u636E")};return(a,e)=>{const r=B,l=H,o=K,m=M,k=Y,c=G;return i(),d("div",X,[s("div",Z,[s("div",ee,[g(r,{size:"small",onClick:R},{default:f(()=>e[1]||(e[1]=[j("\u6E05\u7A7A")])),_:1}),g(r,{type:"primary",size:"small",onClick:N,disabled:!b.value.trim()},{default:f(()=>e[2]||(e[2]=[j(" \u89E3\u6790\u6570\u636E ")])),_:1},8,["disabled"])])]),s("div",ae,[e[3]||(e[3]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," \u7C98\u8D34\u6570\u636E (\u652F\u6301\u4ECEExcel/\u8868\u683C\u76F4\u63A5\u590D\u5236\u7C98\u8D34): ",-1)),g(l,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=t=>b.value=t),type:"textarea",rows:4,placeholder:"\u8BF7\u7C98\u8D34\u8868\u683C\u6570\u636E\uFF0C\u652F\u6301\u5236\u8868\u7B26\u5206\u9694\u7684\u6570\u636E\u683C\u5F0F...",onPaste:E,onInput:I},null,8,["modelValue"]),s("div",le," \u68C0\u6D4B\u5230 "+h(v.value.length)+" \u884C\u6570\u636E ",1)]),v.value.length>0?(i(),d("div",te,[e[4]||(e[4]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"\u5B57\u6BB5\u6620\u5C04\u914D\u7F6E:",-1)),s("div",se,[(i(!0),d(z,null,V(y.value,(t,w)=>(i(),d("div",{key:w,class:"flex flex-col"},[s("label",re,"\u5217 "+h(w+1),1),g(m,{modelValue:t.target,"onUpdate:modelValue":x=>t.target=x,size:"small",clearable:""},{default:f(()=>[(i(!0),d(z,null,V($.targetFields,x=>(i(),O(o,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),s("div",{class:"text-xs text-gray-400 mt-1 truncate",title:t.sample}," \u793A\u4F8B: "+h(t.sample),9,ie)]))),128))])])):T("",!0),u.value.length>0?(i(),d("div",ue,[s("div",ne,[e[6]||(e[6]=s("label",{class:"text-sm font-medium text-gray-700"},"\u89E3\u6790\u7ED3\u679C\u9884\u89C8:",-1)),s("div",oe,[g(r,{size:"small",onClick:D},{default:f(()=>e[5]||(e[5]=[j("\u5BFC\u51FA\u6570\u636E")])),_:1}),g(r,{type:"success",size:"small",onClick:W},{default:f(()=>[j(" \u786E\u8BA4\u5BFC\u5165 ("+h(u.value.length)+" \u6761) ",1)]),_:1})])]),s("div",de,[g(c,{data:u.value,size:"small",stripe:""},{default:f(()=>[(i(!0),d(z,null,V($.targetFields,t=>(i(),O(k,{key:t.value,prop:t.value,label:t.label,"show-overflow-tooltip":""},null,8,["prop","label"]))),128))]),_:1},8,["data"])])])):T("",!0),n.value.length>0?(i(),d("div",ce,[e[7]||(e[7]=s("label",{class:"text-sm font-medium text-red-600 mb-2 block"},"\u89E3\u6790\u9519\u8BEF:",-1)),s("div",me,[s("ul",ve,[(i(!0),d(z,null,V(n.value,(t,w)=>(i(),d("li",{key:w,class:"mb-1"}," \u7B2C "+h(t.row)+" \u884C: "+h(t.message),1))),128))])])])):T("",!0)])}}}),[["__scopeId","data-v-697971fe"]]);export{ge as default};
