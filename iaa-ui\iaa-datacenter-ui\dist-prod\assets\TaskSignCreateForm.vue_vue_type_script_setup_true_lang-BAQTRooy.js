import{d as U,j as o,o as m,h as c,w as r,k as d,v as f,u as l,x as q,c as A,g as H,F as j,m as z,y as B,aH as L,z as R,A as D,B as E,S as G,I as J,H as K,L as M}from"./index-C8b06LRn.js";import{_ as N}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{s as O}from"./index-BwETMpJ2.js";const P=U({name:"TaskSignCreateForm",__name:"TaskSignCreateForm",emits:["success"],setup(Q,{expose:b,emit:g}){const k=B(),t=o(!1),i=o(!1),s=o({id:"",userIds:[],type:"",reason:""}),V=o({userIds:[{required:!0,message:"\u52A0\u7B7E\u5904\u7406\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u52A0\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),n=o(),v=o([]);b({open:async u=>{t.value=!0,h(),s.value.id=u,v.value=await L()}});const w=g,y=async u=>{if(n&&await n.value.validate()){i.value=!0,s.value.type=u;try{await O(s.value),k.success("\u52A0\u7B7E\u6210\u529F"),t.value=!1,w("success")}finally{i.value=!1}}},h=()=>{var u;s.value={id:"",userIds:[],type:"",reason:""},(u=n.value)==null||u.resetFields()};return(u,e)=>{const I=R,C=D,_=E,x=G,F=J,p=K,S=N,T=M;return m(),c(S,{modelValue:l(t),"onUpdate:modelValue":e[5]||(e[5]=a=>z(t)?t.value=a:null),title:"\u52A0\u7B7E",width:"500"},{footer:r(()=>[d(p,{disabled:l(i),type:"primary",onClick:e[2]||(e[2]=a=>y("before"))},{default:r(()=>e[6]||(e[6]=[f(" \u5411\u524D\u52A0\u7B7E ")])),_:1},8,["disabled"]),d(p,{disabled:l(i),type:"primary",onClick:e[3]||(e[3]=a=>y("after"))},{default:r(()=>e[7]||(e[7]=[f(" \u5411\u540E\u52A0\u7B7E ")])),_:1},8,["disabled"]),d(p,{onClick:e[4]||(e[4]=a=>t.value=!1)},{default:r(()=>e[8]||(e[8]=[f("\u53D6 \u6D88")])),_:1})]),default:r(()=>[q((m(),c(F,{ref_key:"formRef",ref:n,model:l(s),rules:l(V),"label-width":"110px"},{default:r(()=>[d(_,{label:"\u52A0\u7B7E\u5904\u7406\u4EBA",prop:"userIds"},{default:r(()=>[d(C,{modelValue:l(s).userIds,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).userIds=a),multiple:"",clearable:"",style:{width:"100%"}},{default:r(()=>[(m(!0),A(j,null,H(l(v),a=>(m(),c(I,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(_,{label:"\u52A0\u7B7E\u7406\u7531",prop:"reason"},{default:r(()=>[d(x,{modelValue:l(s).reason,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).reason=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u52A0\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[T,l(i)]])]),_:1},8,["modelValue"])}}});export{P as _};
