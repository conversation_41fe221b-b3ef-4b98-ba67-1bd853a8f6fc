import{d as m,j as n,f as u,o as e,c as p,k as t,w as f,u as r,x as y,h,$ as x,F as _,L as b}from"./index-C8b06LRn.js";import{_ as j}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as v}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as g}from"./index-CkzUfjB7.js";import{b as k}from"./index-C78nCjiS.js";import"./el-card-CaOo8U9P.js";const w=m({name:"InfraDruid",__name:"index",setup(B){const s=n(!0),o=n("https://sj.iaa360.cn:13141/druid/index.html");return u(async()=>{try{const a=await k("url.druid");a&&a.length>0&&(o.value=a)}finally{s.value=!1}}),(a,D)=>{const i=g,d=v,l=j,c=b;return e(),p(_,null,[t(i,{title:"\u6570\u636E\u5E93 MyBatis",url:"https://doc.iocoder.cn/mybatis/"}),t(i,{title:"\u591A\u6570\u636E\u6E90\uFF08\u8BFB\u5199\u5206\u79BB\uFF09",url:"https://doc.iocoder.cn/dynamic-datasource/"}),t(l,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[r(s)?x("",!0):y((e(),h(d,{key:0,src:r(o)},null,8,["src"])),[[c,r(s)]])]),_:1})],64)}}});export{w as default};
