import{_ as V}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{aG as z,d as C,r as O,dl as x,j as f,f as N,bj as U,aF as D,o as k,c as E,k as e,w as p,u as l,l as _,aO as I,dj as $,aD as A,aq as B,C as P,B as Q,I as R,_ as q}from"./index-C8b06LRn.js";import{w as F,av as j}from"./echarts-cfVEL83D.js";import"./index-Cl43piKd.js";const G=u=>z.get({url:"/butt-joint/erp/project/get-order-delivery-list",params:u}),H=u=>z.get({url:"/butt-joint/erp/project/get-order-delivery-rate",params:u}),W={class:"h-[calc(100vh-180px)] overflow-auto"},J={class:"h-[calc(100%-300px-60px-54px)]"},K=q(C({__name:"OrderDelivery",setup(u){const a=O({pageNo:1,pageSize:50,dateType:"year",sTime:x().format("YYYY-MM-DD")}),b=f([]),h=f(0),v=f(!1),y=f({}),i=f(null),c=async()=>{v.value=!0;try{const s=await G(a);b.value=s.list,h.value=s.total,await w()}finally{v.value=!1}},w=async()=>{y.value=await H(a),await I(),T()},T=()=>{const s=document.getElementById("order-delivery-chart");if(!s)return;i.value&&i.value.dispose();const t=Object.keys(y.value).sort(),m=t.map(r=>y.value[r]||0);if(t.length===0)return void(s.innerHTML=`
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">\u8BA2\u5355\u4EA4\u4ED8\u7387</div>
          <div class="text-sm mt-2">\u6682\u65E0\u6570\u636E</div>
        </div>
      </div>
    `);i.value=$(F(s));const g={title:{text:"\u8BA2\u5355\u4EA4\u4ED8\u7387\u7EDF\u8BA1",left:"center",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:r=>{const n=r[0];return`${n.axisValue}<br/>${n.marker}\u4EA4\u4ED8\u7387: ${n.value}%`}},grid:{left:"3%",right:"4%",bottom:"10%",top:"20%",containLabel:!0},xAxis:{type:"category",data:t.map(r=>a.dateType==="year"?x(r).format("MM\u6708"):x(r).format("DD\u65E5")),axisLabel:{fontSize:12,color:"#666"},axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",name:"\u4EA4\u4ED8\u7387(%)",nameTextStyle:{color:"#666",fontSize:12},axisLabel:{fontSize:12,color:"#666",formatter:"{value}%"},axisLine:{lineStyle:{color:"#e0e0e0"}},splitLine:{lineStyle:{color:"#f0f0f0",type:"dashed"}}},series:[{name:"\u4EA4\u4ED8\u7387",type:"bar",data:m,itemStyle:{color:new j(0,0,0,1,[{offset:0,color:"#4facfe"},{offset:1,color:"#00f2fe"}]),borderRadius:[4,4,0,0]},emphasis:{itemStyle:{color:new j(0,0,0,1,[{offset:0,color:"#667eea"},{offset:1,color:"#764ba2"}])}},label:{show:!0,position:"top",formatter:"{c}%",fontSize:11,color:"#666"}}]};i.value.setOption(g)},S=()=>{i.value&&i.value.resize()};return N(()=>{c(),w(),window.addEventListener("resize",S)}),U(()=>{window.removeEventListener("resize",S),i.value&&(i.value.dispose(),i.value=null)}),(s,t)=>{const m=A,g=B,r=P,n=Q,L=R,o=D("vxe-column"),M=D("vxe-table"),Y=V;return k(),E("div",W,[e(L,{inline:"",class:"custom-form",size:"small"},{default:p(()=>[e(n,{label:"\u65E5\u671F"},{default:p(()=>[e(g,{modelValue:l(a).dateType,"onUpdate:modelValue":t[0]||(t[0]=d=>l(a).dateType=d),onChange:c},{default:p(()=>[e(m,{label:"\u5E74",value:"year"}),e(m,{label:"\u6708",value:"month"})]),_:1},8,["modelValue"]),e(r,{class:"!w-100px",type:l(a).dateType,modelValue:l(a).sTime,"onUpdate:modelValue":t[1]||(t[1]=d=>l(a).sTime=d),"value-format":"YYYY-MM-DD",clearable:!1,onChange:c},null,8,["type","modelValue"])]),_:1})]),_:1}),t[4]||(t[4]=_("div",{id:"order-delivery-chart",class:"h-300px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"},null,-1)),_("div",J,[e(M,{height:"100%",align:"center",border:"",loading:l(v),data:l(b),"show-overflow":"",stripe:""},{default:p(()=>[e(o,{title:"\u9500\u552E\u8BA2\u5355\u53F7",field:"salesOrderNo"}),e(o,{title:"\u6599\u53F7",field:"itemCode"}),e(o,{title:"\u6240\u5C5E\u6708\u4EFD",field:"deliveryMonth"}),e(o,{title:"\u4E0B\u5355\u65E5\u671F",field:"orderDate"}),e(o,{title:"\u9884\u8BA1\u4EA4\u671F",field:"deliveryDate"}),e(o,{title:"\u9500\u552E\u6570\u91CF",field:"orderQty"}),e(o,{title:"\u7D2F\u8BA1\u5B8C\u6210\u6570\u91CF",field:"totalCompletionQty"}),e(o,{title:"\u6700\u540E\u62A5\u5DE5\u65E5\u671F",field:"lastCompletionDate"}),e(o,{title:"\u8BA2\u5355\u72B6\u6001",field:"orderStatus"}),e(o,{title:"\u4EA4\u4ED8\u7387",field:"deliveryRate"})]),_:1},8,["loading","data"])]),e(Y,{limit:l(a).pageSize,"onUpdate:limit":t[2]||(t[2]=d=>l(a).pageSize=d),page:l(a).pageNo,"onUpdate:page":t[3]||(t[3]=d=>l(a).pageNo=d),total:l(h),onPagination:c,size:"small"},null,8,["limit","page","total"])])}}}),[["__scopeId","data-v-8fbfcf5e"]]);export{K as default};
