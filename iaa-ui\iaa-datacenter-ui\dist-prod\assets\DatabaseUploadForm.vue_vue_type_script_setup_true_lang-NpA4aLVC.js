import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as B,y as F,j as n,o as H,h as O,w as l,k as u,u as r,v as c,m as f,l as p,dP as P,aV as A,d2 as D,aO as G,G as I,aa as M,bn as R,H as S}from"./index-C8b06LRn.js";import{d as q}from"./download-D5Lb_h0f.js";import{P as E}from"./publicity-DYNFYfr5.js";const J={class:"el-upload__tip text-center"},K=B({__name:"DatabaseUploadForm",emits:["success"],setup(N,{expose:_,emit:y}){const i=F(),t=n(!1),o=n([]),s=n(),x=n(),b=y,m=n();_({open:()=>{t.value=!0,o.value=[],V()}});const g=()=>{i.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),s.value=!1},h=()=>{i.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},k=async()=>{if(o.value.length==0)return void i.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6");const a=o.value[0].name,e=a.slice(a.lastIndexOf(".")).toLowerCase();[".xls",".xlsx"].includes(e)?(x.value={Authorization:"Bearer "+P(),"tenant-id":A()},s.value=!0,m.value.submit()):i.error("\u53EA\u80FD\u4E0A\u4F20 xls \u6216 xlsx \u683C\u5F0F\u7684\u6587\u4EF6")},C=a=>{if(a.code!==0)return i.error(a.msg),void(s.value=!1);let e=a.data.join(";<br/>");D.alert(e,"\u63D0\u793A",{dangerouslyUseHTMLString:!0,confirmButtonText:"\u786E\u5B9A"}),s.value=!1,t.value=!1,b("success")},U=async()=>{const a=await E.exportTemplate();q.excel(a,"\u4EA7\u54C1\u5BFC\u5165\u6A21\u677F.xlsx")},V=async()=>{var a;s.value=!1,await G(),(a=m.value)==null||a.clearFiles()};return(a,e)=>{const j=I,w=M,L=R,v=S,T=z;return H(),O(T,{modelValue:r(t),"onUpdate:modelValue":e[2]||(e[2]=d=>f(t)?t.value=d:null),title:"\u5BFC\u5165\u4EA7\u54C1"},{footer:l(()=>[u(v,{disabled:r(s),type:"primary",loading:r(s),onClick:k},{default:l(()=>e[6]||(e[6]=[c("\u786E \u5B9A")])),_:1},8,["disabled","loading"]),u(v,{onClick:e[1]||(e[1]=d=>t.value=!1)},{default:l(()=>e[7]||(e[7]=[c("\u53D6 \u6D88")])),_:1})]),default:l(()=>[u(L,{ref_key:"uploadRef",ref:m,"file-list":r(o),"onUpdate:fileList":e[0]||(e[0]=d=>f(o)?o.value=d:null),action:"https://sj.iaa360.cn:13141/admin-api/report/products/import","auto-upload":!1,disabled:r(s),headers:r(x),limit:1,"on-error":g,"on-exceed":h,"on-success":C,accept:".xlsx, .xls",drag:""},{tip:l(()=>[p("div",J,[e[4]||(e[4]=p("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),u(w,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:U},{default:l(()=>e[3]||(e[3]=[c(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:l(()=>[u(j,{icon:"ep:upload"}),e[5]||(e[5]=p("div",{class:"el-upload__text"},[c("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),p("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","disabled","headers"])]),_:1},8,["modelValue"])}}});export{K as _};
