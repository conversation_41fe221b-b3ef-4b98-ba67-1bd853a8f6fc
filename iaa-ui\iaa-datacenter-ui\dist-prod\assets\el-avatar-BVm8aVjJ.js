import{b6 as z,b7 as E,b8 as b,b9 as k,ba as _,d as p,bb as j,j as q,Y as l,bc as x,bd as w,au as A,o as c,c as f,aK as v,u as i,h as d,w as B,i as F,be as K,bf as N,n as V,bg as Y,bh as $}from"./index-C8b06LRn.js";const C=z({size:{type:[Number,String],values:E,default:"",validator:s=>b(s)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:k},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:_(String),default:"cover"}}),D={error:s=>s instanceof Event},G=p({name:"ElAvatar"}),H=$(Y(p({...G,props:C,emits:D,setup(s,{emit:m}){const t=s,e=j("avatar"),n=q(!1),y=l(()=>{const{size:a,icon:o,shape:u}=t,r=[e.b()];return x(a)&&r.push(e.m(a)),o&&r.push(e.m("icon")),u&&r.push(e.m(u)),r}),S=l(()=>{const{size:a}=t;return b(a)?e.cssVarBlock({size:w(a)||""}):void 0}),g=l(()=>({objectFit:t.fit}));function h(a){n.value=!0,m("error",a)}return A(()=>t.src,()=>n.value=!1),(a,o)=>(c(),f("span",{class:V(i(y)),style:v(i(S))},[!a.src&&!a.srcSet||n.value?a.icon?(c(),d(i(K),{key:1},{default:B(()=>[(c(),d(F(a.icon)))]),_:1})):N(a.$slots,"default",{key:2}):(c(),f("img",{key:0,src:a.src,alt:a.alt,srcset:a.srcSet,style:v(i(g)),onError:h},null,44,["src","alt","srcset"]))],6))}}),[["__file","avatar.vue"]]));export{H as E};
