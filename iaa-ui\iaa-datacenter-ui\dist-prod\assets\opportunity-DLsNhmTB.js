import{_ as ee}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as le,aj as h,j as f,y as te,cj as T,f as ae,aF as y,T as ie,o as g,c as C,l as U,k as l,w as r,u as t,v as s,x as re,t as c,h as V,$ as L,F as z,g as E,ax as oe,B as ne,H as se,I as de,aa as fe,S as ue,_ as me}from"./index-C8b06LRn.js";import{a as ce,b as pe,_ as he}from"./report-C-l2KLXX.js";import{c as ye,t as ge}from"./vxeCustom-D2Re1O-c.js";import ve from"./OrderForm-MAaNE6mZ.js";import{d as _e}from"./download-D5Lb_h0f.js";import"./index-Cl43piKd.js";import"./index-WiqCEeob.js";import"./el-drawer-C5TFtzfV.js";import"./el-collapse-item-JANV_ocl.js";const we={style:{height:"50px"}},xe={style:{height:"calc(100vh - 200px - 60px)"}},Te=me(le({__name:"opportunity",props:{userList:h.oneOfType([Array]).isRequired,departmentList:h.oneOfType([Array]).isRequired,customerPortrait:h.oneOfType([Array]).isRequired,customerStage:h.oneOfType([Array]).isRequired,customerRevenue:h.oneOfType([Array]).isRequired,customerOilRevenue:h.oneOfType([Array]).isRequired},setup(H){const O=f(),I=f(),N=f(),b=f(),B=te(),n=f({pageSize:30,pageNo:1,customerTime:[T().subtract(3,"months").format("YYYY-MM-DD"),T().format("YYYY-MM-DD")],salesTime:[T().subtract(1,"years").format("YYYY-MM-DD"),T().format("YYYY-MM-DD")],salesperson:[],departmentName:[],customerName:"",countryName:"",regionOrProvince:"",city:"",customerPortraits:[],nature:"",homepage:"",revenueScale:[],fragranceRevenueScale:[],untoMachineRemark:"",untoEssentialOilRemark:"",relevantProductRemark:"",machine:"",oil:""}),p=H,Y=f([]),$=f({}),j=f(0),S=f(!1),P=()=>{n.value.pageNo=1,v()},v=async()=>{S.value=!0;try{const a=await ce(n.value);Y.value=a.list,a.list&&a.list.length>0&&($.value=oe(a.list[a.list.length-1]),Y.value.pop()),j.value=a.total}finally{S.value=!1}},m=a=>typeof a=="number"?A(a.toFixed(0)):a&&typeof a.cellValue=="number"?A(a.cellValue.toFixed(0)):"",A=a=>{var i;return(i=parseFloat(a).toString().split(".").map((_,w)=>{var k;return w?_:(k=_.split("").reverse().map((M,R)=>!R||R%3?M:M+",").reverse())==null?void 0:k.join("")}))==null?void 0:i.join(".")},u=f([{data:""}]),G=a=>{typeof n.value[a.field]=="string"?n.value[a.field]=a.datas.join(","):n.value[a.field]=a.values,P()},W=async()=>{try{await B.exportConfirm(),b.value=!0;const a=await pe(n.value);_e.excel(a,`\u5C0F\u6EE1\u9500\u552E\u4F5C\u6218\u673A\u4F1A\u70B9\u660E\u7EC6\u8868${T().unix()}.xls`)}catch{}finally{b.value=!1}};return ae(async()=>{P(),(()=>{const a=O.value,i=I.value;a&&i&&a.connect(i)})()}),(a,i)=>{const _=he,w=ne,k=se,M=de,R=y("vxe-button"),J=y("vxe-tooltip"),K=y("vxe-toolbar"),o=y("vxe-column"),D=fe,q=ue,F=y("vxe-colgroup"),Q=y("vxe-table"),X=ee,Z=ie("hasPermi");return g(),C(z,null,[U("div",we,[l(K,{ref_key:"toolbarRef",ref:I,custom:"",size:"small",class:"h-full"},{buttons:r(()=>[l(M,{inline:""},{default:r(()=>[l(w,{label:"\u5BA2\u6237\u66F4\u65B0\u65F6\u95F4\u8303\u56F4"},{default:r(()=>[l(_,{modelValue:t(n).customerTime,"onUpdate:modelValue":i[0]||(i[0]=e=>t(n).customerTime=e),selected:"last3Month",onChange:v},null,8,["modelValue"])]),_:1}),l(w,{label:"\u9500\u552E\u6570\u636E\u65F6\u95F4\u8303\u56F4"},{default:r(()=>[l(_,{modelValue:t(n).salesTime,"onUpdate:modelValue":i[1]||(i[1]=e=>t(n).salesTime=e),selected:"last1Year",onChange:v},null,8,["modelValue"])]),_:1}),l(w,null,{default:r(()=>[l(k,{onClick:v},{default:r(()=>i[7]||(i[7]=[s("\u67E5\u8BE2")])),_:1})]),_:1})]),_:1})]),tools:r(()=>[l(J,{content:"\u5BFC\u51FA"},{default:r(()=>[re(l(R,{icon:"vxe-icon-save",circle:"",loading:t(b),onClick:W},null,8,["loading"]),[[Z,["xiaoman:report:export-opportunity"]]])]),_:1})]),_:1},512)]),U("div",xe,[l(Q,{"header-cell-style":{padding:0,fontSize:"12px",height:"24px"},"cell-style":{padding:0,fontSize:"12px"},"row-config":{isCurrent:!0,isHover:!0,height:30},"column-config":{isCurrent:!0},"custom-config":t(ye),"export-config":{},data:t(Y),"show-footer":!0,"footer-data":[t($)],"footer-cell-style":{padding:0,fontSize:"12px",fontWeight:"bold",height:"30px",backgroundColor:"var(--el-color-warning-light-8)"},loading:t(S)||t(b),"filter-config":{remote:!0},onFilterChange:G,onCellClick:i[4]||(i[4]=e=>t(ge)(e,t(O))),height:"100%",border:"",align:"center",stripe:"",ref_key:"tableRef",ref:O,id:"vxe-table-opportunity"},{default:r(()=>[l(o,{title:"\u4E1A\u52A1\u5458",width:"100",field:"salesperson",fixed:"left",filters:p.userList},{default:r(({row:e})=>{var d;return[s(c((d=e.salesperson)==null?void 0:d.join(",")),1)]}),footer:r(({row:e})=>{var d;return[s(c((d=e.salesperson)==null?void 0:d.join(",")),1)]}),_:1},8,["filters"]),l(o,{title:"\u5BA2\u6237\u6240\u5C5E\u7EC4\u522B",field:"departmentName",filters:p.departmentList,width:"110",fixed:"left"},null,8,["filters"]),(g(),V(o,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName",align:"left",width:"140",key:3,fixed:"left",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},{default:r(({row:e})=>[e.customerName?(g(),V(D,{key:0,type:"primary",size:"small",link:"",onClick:d=>(x=>{N.value.open(x,"opportunity")})(e),underline:!1},{default:r(()=>[s(c(e.customerName),1)]),_:2},1032,["onClick"])):L("",!0)]),_:1},8,["filters"])),l(o,{title:"\u56FD\u5BB6/\u5730\u533A",field:"countryName",width:"100",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u6D32/\u7701",field:"regionOrProvince",width:"100",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u57CE\u5E02",field:"city",width:"100",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u5BA2\u6237\u753B\u50CF",field:"customerPortraits",width:"100",filters:p.customerPortrait},null,8,["filters"]),l(o,{title:"\u5BA2\u6237\u6027\u8D28",field:"nature",width:"100",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u5408\u4F5C\u9636\u6BB5",field:"trailStatus",width:"100",filters:p.customerStage},null,8,["filters"]),l(o,{title:"\u5BA2\u6237\u5B98\u7F51",field:"homepage",align:"left",width:"200",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},{default:r(({row:e})=>[e.homepage?(g(),V(D,{key:0,size:"small",link:"",underline:"",href:e.homepage,target:"_blank"},{default:r(()=>[s(c(e.homepage),1)]),_:2},1032,["href"])):L("",!0)]),_:1},8,["filters"]),l(o,{title:"\u5BA2\u6237\u8425\u6536\u89C4\u6A21\u8BC4\u4F30",field:"revenueScale",width:"150",filters:p.customerRevenue},null,8,["filters"]),l(o,{title:"\u9999\u6C1B\u4EA7\u54C1\u89C4\u6A21\u8BC4\u4F30",field:"fragranceRevenueScale",width:"150",filters:p.customerOilRevenue},null,8,["filters"]),l(o,{title:"\u9884\u4F30\u5E74\u9500\u552E\u989D(\u4E07\u5143)",field:"estimatedAnnualSales",width:"100"}),l(o,{title:"\u7ADF\u5BF9\u673A\u5668\u7C7B\u6570\u636E\u63CF\u8FF0",field:"untoMachineRemark",width:"200",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u7ADF\u5BF9\u7CBE\u6CB9\u7C7B\u6570\u636E\u63CF\u8FF0",field:"untoEssentialOilRemark",width:"200",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(o,{title:"\u76F8\u5173\u6027\u4EA7\u54C1\u63CF\u8FF0",field:"relevantProductRemark",width:"200",filters:t(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),l(F,{title:"\u5408\u4F5C\u673A\u5668\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143)",field:"machineProducts","header-class-name":"dynamic_0"},{header:r(()=>[i[8]||(i[8]=s(" \u5408\u4F5C\u673A\u5668\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143) ")),l(q,{modelValue:t(n).machine,"onUpdate:modelValue":i[2]||(i[2]=e=>t(n).machine=e),size:"small",placeholder:"\u8F93\u5165\u578B\u53F7\u56DE\u8F66\u540E\u8FDB\u884C\u7B5B\u9009",clearable:"",onChange:P,class:"!w-200px"},null,8,["modelValue"])]),default:r(()=>[(g(),C(z,null,E([1,2,3,4,5],(e,d)=>l(F,{title:`\u4EA7\u54C1-${e}`,field:`machineProduct${e}`,key:d,"header-class-name":"dynamic_0"},{default:r(()=>[l(o,{title:"\u578B\u53F7",field:`machineModel${e}`,width:"90","header-class-name":"dynamic_0"},null,8,["field"]),l(o,{title:"\u91D1\u989D",field:`machinePrice${e}`,width:"90",formatter:m,"header-class-name":"dynamic_0"},{footer:r(({row:x})=>[s(c(m(x[`machinePrice${e}`])),1)]),_:2},1032,["field"])]),_:2},1032,["title","field"])),64)),l(o,{title:"\u673A\u5668\u5408\u8BA1\u91D1\u989D",formatter:m,field:"machineTotalPrice",width:"90","header-class-name":"dynamic_0"},{footer:r(({row:e})=>[s(c(m(e.machineTotalPrice)),1)]),_:1})]),_:1}),l(F,{title:"\u5408\u4F5C\u7CBE\u6CB9\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143)",field:"oilProducts","header-class-name":"dynamic_1"},{header:r(()=>[i[9]||(i[9]=s(" \u5408\u4F5C\u7CBE\u6CB9\u7C7B\u4EA7\u54C1TOP5(\u4EBA\u6C11\u5E01\u5143) ")),l(q,{modelValue:t(n).oil,"onUpdate:modelValue":i[3]||(i[3]=e=>t(n).oil=e),size:"small",placeholder:"\u8F93\u5165\u9999\u578B\u56DE\u8F66\u540E\u8FDB\u884C\u7B5B\u9009",clearable:"",onChange:P,class:"!w-200px"},null,8,["modelValue"])]),default:r(()=>[(g(),C(z,null,E([1,2,3,4,5],(e,d)=>l(F,{title:`\u9999\u578B-${e}`,field:`oilProduct${e}`,key:d,"header-class-name":"dynamic_1"},{default:r(()=>[l(o,{title:"\u9999\u578B",field:`oilModel${e}`,width:"90","header-class-name":"dynamic_1"},null,8,["field"]),l(o,{title:"\u91D1\u989D",field:`oilPrice${e}`,width:"90",formatter:m,"header-class-name":"dynamic_1"},{footer:r(({row:x})=>[s(c(m(x[`oilPrice${e}`])),1)]),_:2},1032,["field"])]),_:2},1032,["title","field"])),64)),l(o,{title:"\u7CBE\u6CB9\u5408\u8BA1\u91D1\u989D",formatter:m,field:"oilTotalPrice",width:"90","header-class-name":"dynamic_1"},{footer:r(({row:e})=>[s(c(m(e.oilTotalPrice)),1)]),_:1})]),_:1})]),_:1},8,["custom-config","data","footer-data","loading"]),l(X,{total:t(j),size:"small",page:t(n).pageNo,"onUpdate:page":i[5]||(i[5]=e=>t(n).pageNo=e),limit:t(n).pageSize,"onUpdate:limit":i[6]||(i[6]=e=>t(n).pageSize=e),onPagination:v,class:"!float-left"},null,8,["total","page","limit"])]),l(ve,{ref_key:"orderFormRef",ref:N},null,512)],64)}}}),[["__scopeId","data-v-6258d245"]]);export{Te as default};
