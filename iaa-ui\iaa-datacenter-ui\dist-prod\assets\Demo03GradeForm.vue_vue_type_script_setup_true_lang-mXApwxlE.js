import{d as C,p as F,y as B,j as r,r as D,o as y,h as V,w as t,k as i,u as a,v as b,x as G,m as H,S as L,B as R,I as z,H as A,L as E}from"./index-C8b06LRn.js";import{_ as J}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{h as K,i as M,j as N}from"./index-D7MUd3Bn.js";const O=C({__name:"Demo03GradeForm",emits:["success"],setup(P,{expose:g,emit:w}){const{t:c}=F(),v=B(),u=r(!1),p=r(""),d=r(!1),f=r(""),l=r({id:void 0,studentId:void 0,name:void 0,teacher:void 0}),I=D({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],teacher:[{required:!0,message:"\u73ED\u4E3B\u4EFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r();g({open:async(s,e,m)=>{if(u.value=!0,p.value=c("action."+s),f.value=s,q(),l.value.studentId=m,e){d.value=!0;try{l.value=await K(e)}finally{d.value=!1}}}});const k=w,x=async()=>{await n.value.validate(),d.value=!0;try{const s=l.value;f.value==="create"?(await M(s),v.success(c("common.createSuccess"))):(await N(s),v.success(c("common.updateSuccess"))),u.value=!1,k("success")}finally{d.value=!1}},q=()=>{var s;l.value={id:void 0,studentId:void 0,name:void 0,teacher:void 0},(s=n.value)==null||s.resetFields()};return(s,e)=>{const m=L,h=R,S=z,_=A,U=J,j=E;return y(),V(U,{title:a(p),modelValue:a(u),"onUpdate:modelValue":e[3]||(e[3]=o=>H(u)?u.value=o:null)},{footer:t(()=>[i(_,{onClick:x,type:"primary",disabled:a(d)},{default:t(()=>e[4]||(e[4]=[b("\u786E \u5B9A")])),_:1},8,["disabled"]),i(_,{onClick:e[2]||(e[2]=o=>u.value=!1)},{default:t(()=>e[5]||(e[5]=[b("\u53D6 \u6D88")])),_:1})]),default:t(()=>[G((y(),V(S,{ref_key:"formRef",ref:n,model:a(l),rules:a(I),"label-width":"100px"},{default:t(()=>[i(h,{label:"\u540D\u5B57",prop:"name"},{default:t(()=>[i(m,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=o=>a(l).name=o),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),i(h,{label:"\u73ED\u4E3B\u4EFB",prop:"teacher"},{default:t(()=>[i(m,{modelValue:a(l).teacher,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).teacher=o),placeholder:"\u8BF7\u8F93\u5165\u73ED\u4E3B\u4EFB"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,a(d)]])]),_:1},8,["title","modelValue"])}}});export{O as _};
