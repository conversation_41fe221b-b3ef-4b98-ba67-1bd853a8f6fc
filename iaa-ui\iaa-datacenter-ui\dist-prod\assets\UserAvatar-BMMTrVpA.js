import{d as Ct,aj as S,bi as ui,j as P,Y as Wt,f as mi,bj as gi,u as v,c as st,x as fi,Z as vi,l as et,aK as be,n as F,X as Ht,bk as bi,o as U,bl as we,k as B,w as R,m as wi,v as ye,t as xe,h as Dt,$ as ht,bm as yi,bn as xi,H as Me,b3 as Mi,au as Ci,y as Di,_ as Ce,aN as ki}from"./index-C8b06LRn.js";import{b as Bi}from"./profile-BAixQBws.js";import{E as De}from"./el-avatar-BVm8aVjJ.js";import{_ as Oi}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as Ti}from"./el-space-CqmKDeRz.js";import{_ as Ei}from"./XButton-BOgar_Ex.js";import{d as zi}from"./filt-CBkj7zaY.js";import{a as Wi}from"./avatar-CPqUN878.js";/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function ke(t,i){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),e.push.apply(e,n)}return e}function Be(t){for(var i=1;i<arguments.length;i++){var e=arguments[i]!=null?arguments[i]:{};i%2?ke(Object(e),!0).forEach(function(n){Hi(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ke(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function Oe(t){var i=function(e,n){if(typeof e!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}(t,"string");return typeof i=="symbol"?i:i+""}function Nt(t){return Nt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Nt(t)}function Te(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Oe(n.key),n)}}function Hi(t,i,e){return(i=Oe(i))in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}function Ee(t){return function(i){if(Array.isArray(i))return Rt(i)}(t)||function(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}(t)||function(i,e){if(i){if(typeof i=="string")return Rt(i,e);var n=Object.prototype.toString.call(i).slice(8,-1);if(n==="Object"&&i.constructor&&(n=i.constructor.name),n==="Map"||n==="Set")return Array.from(i);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rt(i,e)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Rt(t,i){(i==null||i>t.length)&&(i=t.length);for(var e=0,n=new Array(i);e<i;e++)n[e]=t[e];return n}var kt=typeof window<"u"&&window.document!==void 0,$=kt?window:{},Lt=!(!kt||!$.document.documentElement)&&"ontouchstart"in $.document.documentElement,Yt=!!kt&&"PointerEvent"in $,O="cropper",St="all",ze="crop",We="move",He="zoom",it="e",at="w",ct="s",Q="n",gt="ne",ft="nw",vt="se",bt="sw",Xt="".concat(O,"-crop"),Ne="".concat(O,"-disabled"),L="".concat(O,"-hidden"),Re="".concat(O,"-hide"),Ni="".concat(O,"-invisible"),Bt="".concat(O,"-modal"),_t="".concat(O,"-move"),wt="".concat(O,"Action"),Ot="".concat(O,"Preview"),At="crop",Le="move",Ye="none",It="crop",jt="cropend",Pt="cropmove",Ut="cropstart",Se="dblclick",Xe=Yt?"pointerdown":Lt?"touchstart":"mousedown",_e=Yt?"pointermove":Lt?"touchmove":"mousemove",Ae=Yt?"pointerup pointercancel":Lt?"touchend touchcancel":"mouseup",Ie="ready",je="resize",Pe="wheel",$t="zoom",Ue="image/jpeg",Ri=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Li=/^data:/,Yi=/^data:image\/jpeg;base64,/,Si=/^img|canvas$/i,$e={viewMode:0,dragMode:At,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Xi=Number.isNaN||$.isNaN;function M(t){return typeof t=="number"&&!Xi(t)}var qe=function(t){return t>0&&t<1/0};function qt(t){return t===void 0}function nt(t){return Nt(t)==="object"&&t!==null}var _i=Object.prototype.hasOwnProperty;function lt(t){if(!nt(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&_i.call(e,"isPrototypeOf")}catch{return!1}}function Y(t){return typeof t=="function"}var Ai=Array.prototype.slice;function Ve(t){return Array.from?Array.from(t):Ai.call(t)}function z(t,i){return t&&Y(i)&&(Array.isArray(t)||M(t.length)?Ve(t).forEach(function(e,n){i.call(t,e,n,t)}):nt(t)&&Object.keys(t).forEach(function(e){i.call(t,t[e],e,t)})),t}var T=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return nt(t)&&e.length>0&&e.forEach(function(a){nt(a)&&Object.keys(a).forEach(function(r){t[r]=a[r]})}),t},Ii=/\.\d*(?:0|9){12}\d*$/;function dt(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return Ii.test(t)?Math.round(t*i)/i:t}var ji=/^width|height|left|top|marginLeft|marginTop$/;function Z(t,i){var e=t.style;z(i,function(n,a){ji.test(a)&&M(n)&&(n="".concat(n,"px")),e[a]=n})}function W(t,i){if(i)if(M(t.length))z(t,function(n){W(n,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i}}function q(t,i){i&&(M(t.length)?z(t,function(e){q(e,i)}):t.classList?t.classList.remove(i):t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,"")))}function pt(t,i,e){i&&(M(t.length)?z(t,function(n){pt(n,i,e)}):e?W(t,i):q(t,i))}var Pi=/([a-z\d])([A-Z])/g;function Vt(t){return t.replace(Pi,"$1-$2").toLowerCase()}function Ft(t,i){return nt(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Vt(i)))}function yt(t,i,e){nt(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(Vt(i)),e)}var Fe=/\s\s*/,Ke=function(){var t=!1;if(kt){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(a){i=a}});$.addEventListener("test",e,n),$.removeEventListener("test",e,n)}return t}();function I(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Fe).forEach(function(r){if(!Ke){var c=t.listeners;c&&c[r]&&c[r][e]&&(a=c[r][e],delete c[r][e],Object.keys(c[r]).length===0&&delete c[r],Object.keys(c).length===0&&delete t.listeners)}t.removeEventListener(r,a,n)})}function X(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Fe).forEach(function(r){if(n.once&&!Ke){var c=t.listeners,s=c===void 0?{}:c;a=function(){delete s[r][e],t.removeEventListener(r,a,n);for(var h=arguments.length,o=new Array(h),u=0;u<h;u++)o[u]=arguments[u];e.apply(t,o)},s[r]||(s[r]={}),s[r][e]&&t.removeEventListener(r,s[r][e],n),s[r][e]=a,t.listeners=s}t.addEventListener(r,a,n)})}function ut(t,i,e){var n;return Y(Event)&&Y(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function Qe(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var Kt=$.location,Ui=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Ze(t){var i=t.match(Ui);return i!==null&&(i[1]!==Kt.protocol||i[2]!==Kt.hostname||i[3]!==Kt.port)}function Ge(t){var i="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+i}function xt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,a=t.translateX,r=t.translateY,c=[];M(a)&&a!==0&&c.push("translateX(".concat(a,"px)")),M(r)&&r!==0&&c.push("translateY(".concat(r,"px)")),M(i)&&i!==0&&c.push("rotate(".concat(i,"deg)")),M(e)&&e!==1&&c.push("scaleX(".concat(e,")")),M(n)&&n!==1&&c.push("scaleY(".concat(n,")"));var s=c.length?c.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Tt(t,i){var e=t.pageX,n=t.pageY,a={endX:e,endY:n};return i?a:Be({startX:e,startY:n},a)}function G(t){var i=t.aspectRatio,e=t.height,n=t.width,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=qe(n),c=qe(e);if(r&&c){var s=e*i;a==="contain"&&s>n||a==="cover"&&s<n?e=n/i:n=e*i}else r?e=n/i:c&&(n=e*i);return{width:n,height:e}}var Je=String.fromCharCode,$i=/^data:.*,/;function qi(t){var i,e=new DataView(t);try{var n,a,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var c=e.byteLength,s=2;s+1<c;){if(e.getUint8(s)===255&&e.getUint8(s+1)===225){a=s;break}s+=1}if(a){var h=a+10;if(function(f,g,b){var C="";b+=g;for(var w=g;w<b;w+=1)C+=Je(f.getUint8(w));return C}(e,a+4,4)==="Exif"){var o=e.getUint16(h);if(((n=o===18761)||o===19789)&&e.getUint16(h+2,n)===42){var u=e.getUint32(h+4,n);u>=8&&(r=h+u)}}}if(r){var d,l,m=e.getUint16(r,n);for(l=0;l<m;l+=1)if(d=r+12*l+2,e.getUint16(d,n)===274){d+=8,i=e.getUint16(d,n),e.setUint16(d,1,n);break}}}catch{i=1}return i}var Vi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,a=Number(i.minContainerWidth),r=Number(i.minContainerHeight);W(n,L),q(t,L);var c={width:Math.max(e.offsetWidth,a>=0?a:200),height:Math.max(e.offsetHeight,r>=0?r:100)};this.containerData=c,Z(n,{width:c.width,height:c.height}),W(t,L),q(n,L)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180==90,a=n?i.naturalHeight:i.naturalWidth,r=n?i.naturalWidth:i.naturalHeight,c=a/r,s=t.width,h=t.height;t.height*c>t.width?e===3?s=t.height*c:h=t.width/c:e===3?h=t.width/c:s=t.height*c;var o={aspectRatio:c,naturalWidth:a,naturalHeight:r,width:s,height:h};this.canvasData=o,this.limited=e===1||e===2,this.limitCanvas(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.left=(t.width-o.width)/2,o.top=(t.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCanvasData=T({},o)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,c=e.viewMode,s=a.aspectRatio,h=this.cropped&&r;if(t){var o=Number(e.minCanvasWidth)||0,u=Number(e.minCanvasHeight)||0;c>1?(o=Math.max(o,n.width),u=Math.max(u,n.height),c===3&&(u*s>o?o=u*s:u=o/s)):c>0&&(o?o=Math.max(o,h?r.width:0):u?u=Math.max(u,h?r.height:0):h&&(o=r.width,(u=r.height)*s>o?o=u*s:u=o/s));var d=G({aspectRatio:s,width:o,height:u});o=d.width,u=d.height,a.minWidth=o,a.minHeight=u,a.maxWidth=1/0,a.maxHeight=1/0}if(i)if(c>(h?0:1)){var l=n.width-a.width,m=n.height-a.height;a.minLeft=Math.min(0,l),a.minTop=Math.min(0,m),a.maxLeft=Math.max(0,l),a.maxTop=Math.max(0,m),h&&this.limited&&(a.minLeft=Math.min(r.left,r.left+(r.width-a.width)),a.minTop=Math.min(r.top,r.top+(r.height-a.height)),a.maxLeft=r.left,a.maxTop=r.top,c===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,l),a.maxLeft=Math.max(0,l)),a.height>=n.height&&(a.minTop=Math.min(0,m),a.maxTop=Math.max(0,m))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var a=function(o){var u=o.width,d=o.height,l=o.degree;if((l=Math.abs(l)%180)==90)return{width:d,height:u};var m=l%90*Math.PI/180,f=Math.sin(m),g=Math.cos(m),b=u*g+d*f,C=u*f+d*g;return l>90?{width:C,height:b}:{width:b,height:C}}({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),r=a.width,c=a.height,s=e.width*(r/e.naturalWidth),h=e.height*(c/e.naturalHeight);e.left-=(s-e.width)/2,e.top-=(h-e.height)/2,e.width=s,e.height=h,e.aspectRatio=r/c,e.naturalWidth=r,e.naturalHeight=c,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,Z(this.canvas,T({width:e.width,height:e.height},xt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),a=e.naturalHeight*(i.height/i.naturalHeight);T(e,{width:n,height:a,left:(i.width-n)/2,top:(i.height-a)/2}),Z(this.image,T({width:e.width,height:e.height},xt(T({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,a={width:i.width,height:i.height};e&&(i.height*e>i.width?a.height=a.width/e:a.width=a.height*e),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=i.left+(i.width-a.width)/2,a.top=i.top+(i.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=T({},a)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,c=this.limited,s=e.aspectRatio;if(t){var h=Number(e.minCropBoxWidth)||0,o=Number(e.minCropBoxHeight)||0,u=c?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,d=c?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;h=Math.min(h,n.width),o=Math.min(o,n.height),s&&(h&&o?o*s>h?o=h/s:h=o*s:h?o=h/s:o&&(h=o*s),d*s>u?d=u/s:u=d*s),r.minWidth=Math.min(h,u),r.minHeight=Math.min(o,d),r.maxWidth=u,r.maxHeight=d}i&&(c?(r.minLeft=Math.max(0,a.left),r.minTop=Math.max(0,a.top),r.maxLeft=Math.min(n.width,a.left+a.width)-r.width,r.maxTop=Math.min(n.height,a.top+a.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=n.width-r.width,r.maxTop=n.height-r.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&yt(this.face,wt,e.width>=i.width&&e.height>=i.height?We:St),Z(this.cropBox,T({width:e.width,height:e.height},xt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ut(this.element,It,this.getData())}},Fi={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,a=t.alt||"The image to preview",r=document.createElement("img");if(i&&(r.crossOrigin=i),r.src=n,r.alt=a,this.viewBox.appendChild(r),this.viewBoxImage=r,e){var c=e;typeof e=="string"?c=t.ownerDocument.querySelectorAll(e):e.querySelector&&(c=[e]),this.previews=c,z(c,function(s){var h=document.createElement("img");yt(s,Ot,{width:s.offsetWidth,height:s.offsetHeight,html:s.innerHTML}),i&&(h.crossOrigin=i),h.src=n,h.alt=a,h.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',s.innerHTML="",s.appendChild(h)})}},resetPreview:function(){z(this.previews,function(t){var i=Ft(t,Ot);Z(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(e,n){if(nt(e[n]))try{delete e[n]}catch{e[n]=void 0}else if(e.dataset)try{delete e.dataset[n]}catch{e.dataset[n]=void 0}else e.removeAttribute("data-".concat(Vt(n)))}(t,Ot)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,a=e.height,r=t.width,c=t.height,s=e.left-i.left-t.left,h=e.top-i.top-t.top;this.cropped&&!this.disabled&&(Z(this.viewBoxImage,T({width:r,height:c},xt(T({translateX:-s,translateY:-h},t)))),z(this.previews,function(o){var u=Ft(o,Ot),d=u.width,l=u.height,m=d,f=l,g=1;n&&(f=a*(g=d/n)),a&&f>l&&(m=n*(g=l/a),f=l),Z(o,{width:m,height:f}),Z(o.getElementsByTagName("img")[0],T({width:r*g,height:c*g},xt(T({translateX:-s*g,translateY:-h*g},t))))}))}},Ki={bind:function(){var t=this.element,i=this.options,e=this.cropper;Y(i.cropstart)&&X(t,Ut,i.cropstart),Y(i.cropmove)&&X(t,Pt,i.cropmove),Y(i.cropend)&&X(t,jt,i.cropend),Y(i.crop)&&X(t,It,i.crop),Y(i.zoom)&&X(t,$t,i.zoom),X(e,Xe,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&X(e,Pe,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&X(e,Se,this.onDblclick=this.dblclick.bind(this)),X(t.ownerDocument,_e,this.onCropMove=this.cropMove.bind(this)),X(t.ownerDocument,Ae,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&X(window,je,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;Y(i.cropstart)&&I(t,Ut,i.cropstart),Y(i.cropmove)&&I(t,Pt,i.cropmove),Y(i.cropend)&&I(t,jt,i.cropend),Y(i.crop)&&I(t,It,i.crop),Y(i.zoom)&&I(t,$t,i.zoom),I(e,Xe,this.onCropStart),i.zoomable&&i.zoomOnWheel&&I(e,Pe,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&I(e,Se,this.onDblclick),I(t.ownerDocument,_e,this.onCropMove),I(t.ownerDocument,Ae,this.onCropEnd),i.responsive&&I(window,je,this.onResize)}},Qi={resize:function(){if(!this.disabled){var t,i,e=this.options,n=this.container,a=this.containerData,r=n.offsetWidth/a.width,c=n.offsetHeight/a.height,s=Math.abs(r-1)>Math.abs(c-1)?r:c;s!==1&&(e.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(z(t,function(h,o){t[o]=h*s})),this.setCropBoxData(z(i,function(h,o){i[o]=h*s}))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===Ye||this.setDragMode((t=this.dragBox,i=Xt,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?Le:At))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(M(i)&&i!==1||M(e)&&e!==0||t.ctrlKey))){var n,a=this.options,r=this.pointers;t.changedTouches?z(t.changedTouches,function(c){r[c.identifier]=Tt(c)}):r[t.pointerId||0]=Tt(t),n=Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?He:Ft(t.target,wt),Ri.test(n)&&ut(this.element,Ut,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===ze&&(this.cropping=!0,W(this.dragBox,Bt)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),ut(this.element,Pt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?z(t.changedTouches,function(n){T(e[n.identifier]||{},Tt(n,!0))}):T(e[t.pointerId||0]||{},Tt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?z(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,pt(this.dragBox,Bt,this.cropped&&this.options.modal)),ut(this.element,jt,{originalEvent:t,action:i}))}}},Zi={change:function(t){var i,e=this.options,n=this.canvasData,a=this.containerData,r=this.cropBoxData,c=this.pointers,s=this.action,h=e.aspectRatio,o=r.left,u=r.top,d=r.width,l=r.height,m=o+d,f=u+l,g=0,b=0,C=a.width,w=a.height,D=!0;!h&&t.shiftKey&&(h=d&&l?d/l:1),this.limited&&(g=r.minLeft,b=r.minTop,C=g+Math.min(a.width,n.width,n.left+n.width),w=b+Math.min(a.height,n.height,n.top+n.height));var x=c[Object.keys(c)[0]],p={x:x.endX-x.startX,y:x.endY-x.startY},y=function(k){switch(k){case it:m+p.x>C&&(p.x=C-m);break;case at:o+p.x<g&&(p.x=g-o);break;case Q:u+p.y<b&&(p.y=b-u);break;case ct:f+p.y>w&&(p.y=w-f)}};switch(s){case St:o+=p.x,u+=p.y;break;case it:if(p.x>=0&&(m>=C||h&&(u<=b||f>=w))){D=!1;break}y(it),(d+=p.x)<0&&(s=at,o-=d=-d),h&&(l=d/h,u+=(r.height-l)/2);break;case Q:if(p.y<=0&&(u<=b||h&&(o<=g||m>=C))){D=!1;break}y(Q),l-=p.y,u+=p.y,l<0&&(s=ct,u-=l=-l),h&&(d=l*h,o+=(r.width-d)/2);break;case at:if(p.x<=0&&(o<=g||h&&(u<=b||f>=w))){D=!1;break}y(at),d-=p.x,o+=p.x,d<0&&(s=it,o-=d=-d),h&&(l=d/h,u+=(r.height-l)/2);break;case ct:if(p.y>=0&&(f>=w||h&&(o<=g||m>=C))){D=!1;break}y(ct),(l+=p.y)<0&&(s=Q,u-=l=-l),h&&(d=l*h,o+=(r.width-d)/2);break;case gt:if(h){if(p.y<=0&&(u<=b||m>=C)){D=!1;break}y(Q),l-=p.y,u+=p.y,d=l*h}else y(Q),y(it),p.x>=0?m<C?d+=p.x:p.y<=0&&u<=b&&(D=!1):d+=p.x,p.y<=0?u>b&&(l-=p.y,u+=p.y):(l-=p.y,u+=p.y);d<0&&l<0?(s=bt,u-=l=-l,o-=d=-d):d<0?(s=ft,o-=d=-d):l<0&&(s=vt,u-=l=-l);break;case ft:if(h){if(p.y<=0&&(u<=b||o<=g)){D=!1;break}y(Q),l-=p.y,u+=p.y,d=l*h,o+=r.width-d}else y(Q),y(at),p.x<=0?o>g?(d-=p.x,o+=p.x):p.y<=0&&u<=b&&(D=!1):(d-=p.x,o+=p.x),p.y<=0?u>b&&(l-=p.y,u+=p.y):(l-=p.y,u+=p.y);d<0&&l<0?(s=vt,u-=l=-l,o-=d=-d):d<0?(s=gt,o-=d=-d):l<0&&(s=bt,u-=l=-l);break;case bt:if(h){if(p.x<=0&&(o<=g||f>=w)){D=!1;break}y(at),d-=p.x,o+=p.x,l=d/h}else y(ct),y(at),p.x<=0?o>g?(d-=p.x,o+=p.x):p.y>=0&&f>=w&&(D=!1):(d-=p.x,o+=p.x),p.y>=0?f<w&&(l+=p.y):l+=p.y;d<0&&l<0?(s=gt,u-=l=-l,o-=d=-d):d<0?(s=vt,o-=d=-d):l<0&&(s=ft,u-=l=-l);break;case vt:if(h){if(p.x>=0&&(m>=C||f>=w)){D=!1;break}y(it),l=(d+=p.x)/h}else y(ct),y(it),p.x>=0?m<C?d+=p.x:p.y>=0&&f>=w&&(D=!1):d+=p.x,p.y>=0?f<w&&(l+=p.y):l+=p.y;d<0&&l<0?(s=ft,u-=l=-l,o-=d=-d):d<0?(s=bt,o-=d=-d):l<0&&(s=gt,u-=l=-l);break;case We:this.move(p.x,p.y),D=!1;break;case He:this.zoom(function(k){var j=Be({},k),_=0;return z(k,function(N,K){delete j[K],z(j,function(H){var E=Math.abs(N.startX-H.startX),mt=Math.abs(N.startY-H.startY),J=Math.abs(N.endX-H.endX),rt=Math.abs(N.endY-H.endY),V=Math.sqrt(E*E+mt*mt),ot=(Math.sqrt(J*J+rt*rt)-V)/V;Math.abs(ot)>Math.abs(_)&&(_=ot)})}),_}(c),t),D=!1;break;case ze:if(!p.x||!p.y){D=!1;break}i=Qe(this.cropper),o=x.startX-i.left,u=x.startY-i.top,d=r.minWidth,l=r.minHeight,p.x>0?s=p.y>0?vt:gt:p.x<0&&(o-=d,s=p.y>0?bt:ft),p.y<0&&(u-=l),this.cropped||(q(this.cropBox,L),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}D&&(r.width=d,r.height=l,r.left=o,r.top=u,this.action=s,this.renderCropBox()),z(c,function(k){k.startX=k.endX,k.startY=k.endY})}},Gi={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&W(this.dragBox,Bt),q(this.cropBox,L),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=T({},this.initialImageData),this.canvasData=T({},this.initialCanvasData),this.cropBoxData=T({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(T(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),q(this.dragBox,Bt),W(this.cropBox,L)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,z(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,q(this.cropper,Ne)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,W(this.cropper,Ne)),this},destroy:function(){var t=this.element;return t[O]?(t[O]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,a=e.top;return this.moveTo(qt(t)?t:n+Number(t),qt(i)?i:a+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(M(t)&&(e.left=t,n=!0),M(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,a=this.canvasData,r=a.width,c=a.height,s=a.naturalWidth,h=a.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&n.zoomable){var o=s*t,u=h*t;if(ut(this.element,$t,{ratio:t,oldRatio:r/s,originalEvent:e})===!1)return this;if(e){var d=this.pointers,l=Qe(this.cropper),m=d&&Object.keys(d).length?function(f){var g=0,b=0,C=0;return z(f,function(w){var D=w.startX,x=w.startY;g+=D,b+=x,C+=1}),{pageX:g/=C,pageY:b/=C}}(d):{pageX:e.pageX,pageY:e.pageY};a.left-=(o-r)*((m.pageX-l.left-a.left)/r),a.top-=(u-c)*((m.pageY-l.top-a.top)/c)}else lt(i)&&M(i.x)&&M(i.y)?(a.left-=(o-r)*((i.x-a.left)/r),a.top-=(u-c)*((i.y-a.top)/c)):(a.left-=(o-r)/2,a.top-=(u-c)/2);a.width=o,a.height=u,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return M(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,M(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(M(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(M(t)&&(e.scaleX=t,n=!0),M(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.options,n=this.imageData,a=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var c=n.width/n.naturalWidth;if(z(t,function(o,u){t[u]=o/c}),i){var s=Math.round(t.y+t.height),h=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=h-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return e.rotatable&&(t.rotate=n.rotate||0),e.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&lt(t)){var r=!1;i.rotatable&&M(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,r=!0),i.scalable&&(M(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,r=!0),M(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var c=e.width/e.naturalWidth;M(t.x)&&(a.left=t.x*c+n.left),M(t.y)&&(a.top=t.y*c+n.top),M(t.width)&&(a.width=t.width*c),M(t.height)&&(a.height=t.height*c),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?T({},this.containerData):{}},getImageData:function(){return this.sized?T({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&z(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&lt(t)&&(M(t.left)&&(i.left=t.left),M(t.top)&&(i.top=t.top),M(t.width)?(i.width=t.width,i.height=t.width/e):M(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,e,n=this.cropBoxData,a=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&lt(t)&&(M(t.left)&&(n.left=t.left),M(t.top)&&(n.top=t.top),M(t.width)&&t.width!==n.width&&(i=!0,n.width=t.width),M(t.height)&&t.height!==n.height&&(e=!0,n.height=t.height),a&&(i?n.height=n.width/a:e&&(n.width=n.height*a)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=function(rt,V,ot,tt){var Qt=V.aspectRatio,ei=V.naturalWidth,ii=V.naturalHeight,Zt=V.rotate,ai=Zt===void 0?0:Zt,Gt=V.scaleX,ni=Gt===void 0?1:Gt,Jt=V.scaleY,ri=Jt===void 0?1:Jt,te=ot.aspectRatio,oi=ot.naturalWidth,si=ot.naturalHeight,ee=tt.fillColor,hi=ee===void 0?"transparent":ee,ie=tt.imageSmoothingEnabled,ci=ie===void 0||ie,ae=tt.imageSmoothingQuality,li=ae===void 0?"low":ae,ne=tt.maxWidth,re=ne===void 0?1/0:ne,oe=tt.maxHeight,se=oe===void 0?1/0:oe,he=tt.minWidth,ce=he===void 0?0:he,le=tt.minHeight,de=le===void 0?0:le,Mt=document.createElement("canvas"),A=Mt.getContext("2d"),pe=G({aspectRatio:te,width:re,height:se}),ue=G({aspectRatio:te,width:ce,height:de},"cover"),Et=Math.min(pe.width,Math.max(ue.width,oi)),zt=Math.min(pe.height,Math.max(ue.height,si)),me=G({aspectRatio:Qt,width:re,height:se}),ge=G({aspectRatio:Qt,width:ce,height:de},"cover"),fe=Math.min(me.width,Math.max(ge.width,ei)),ve=Math.min(me.height,Math.max(ge.height,ii)),di=[-fe/2,-ve/2,fe,ve];return Mt.width=dt(Et),Mt.height=dt(zt),A.fillStyle=hi,A.fillRect(0,0,Et,zt),A.save(),A.translate(Et/2,zt/2),A.rotate(ai*Math.PI/180),A.scale(ni,ri),A.imageSmoothingEnabled=ci,A.imageSmoothingQuality=li,A.drawImage.apply(A,[rt].concat(Ee(di.map(function(pi){return Math.floor(dt(pi))})))),A.restore(),Mt}(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),a=n.x,r=n.y,c=n.width,s=n.height,h=e.width/Math.floor(i.naturalWidth);h!==1&&(a*=h,r*=h,c*=h,s*=h);var o=c/s,u=G({aspectRatio:o,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=G({aspectRatio:o,width:t.minWidth||0,height:t.minHeight||0},"cover"),l=G({aspectRatio:o,width:t.width||(h!==1?e.width:c),height:t.height||(h!==1?e.height:s)}),m=l.width,f=l.height;m=Math.min(u.width,Math.max(d.width,m)),f=Math.min(u.height,Math.max(d.height,f));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=dt(m),g.height=dt(f),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,f);var C=t.imageSmoothingEnabled,w=C===void 0||C,D=t.imageSmoothingQuality;b.imageSmoothingEnabled=w,D&&(b.imageSmoothingQuality=D);var x,p,y,k,j,_,N=e.width,K=e.height,H=a,E=r;H<=-c||H>N?(H=0,x=0,y=0,j=0):H<=0?(y=-H,H=0,j=x=Math.min(N,c+H)):H<=N&&(y=0,j=x=Math.min(c,N-H)),x<=0||E<=-s||E>K?(E=0,p=0,k=0,_=0):E<=0?(k=-E,E=0,_=p=Math.min(K,s+E)):E<=K&&(k=0,_=p=Math.min(s,K-E));var mt=[H,E,x,p];if(j>0&&_>0){var J=m/c;mt.push(y*J,k*J,j*J,_*J)}return b.drawImage.apply(b,[e].concat(Ee(mt.map(function(rt){return Math.floor(dt(rt))})))),g},setAspectRatio:function(t){var i=this.options;return this.disabled||qt(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=t===At,r=i.movable&&t===Le;t=a||r?t:Ye,i.dragMode=t,yt(e,wt,t),pt(e,Xt,a),pt(e,_t,r),i.cropBoxMovable||(yt(n,wt,t),pt(n,Xt,a),pt(n,_t,r))}return this}},Ji=$.Cropper,ti=function(){function t(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(function(c,s){if(!(c instanceof s))throw new TypeError("Cannot call a class as a function")}(this,t),!a||!Si.test(a.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=a,this.options=T({},$e,lt(r)&&r),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return i=t,n=[{key:"noConflict",value:function(){return window.Cropper=Ji,t}},{key:"setDefaults",value:function(a){T($e,lt(a)&&a)}}],(e=[{key:"init",value:function(){var a,r=this.element,c=r.tagName.toLowerCase();if(!r[O]){if(r[O]=this,c==="img"){if(this.isImg=!0,a=r.getAttribute("src")||"",this.originalUrl=a,!a)return;a=r.src}else c==="canvas"&&window.HTMLCanvasElement&&(a=r.toDataURL());this.load(a)}}},{key:"load",value:function(a){var r=this;if(a){this.url=a,this.imageData={};var c=this.element,s=this.options;if(s.rotatable||s.scalable||(s.checkOrientation=!1),s.checkOrientation&&window.ArrayBuffer)if(Li.test(a))Yi.test(a)?this.read((h=a.replace($i,""),o=atob(h),u=new ArrayBuffer(o.length),z(d=new Uint8Array(u),function(f,g){d[g]=o.charCodeAt(g)}),u)):this.clone();else{var h,o,u,d,l=new XMLHttpRequest,m=this.clone.bind(this);this.reloading=!0,this.xhr=l,l.onabort=m,l.onerror=m,l.ontimeout=m,l.onprogress=function(){l.getResponseHeader("content-type")!==Ue&&l.abort()},l.onload=function(){r.read(l.response)},l.onloadend=function(){r.reloading=!1,r.xhr=null},s.checkCrossOrigin&&Ze(a)&&c.crossOrigin&&(a=Ge(a)),l.open("GET",a,!0),l.responseType="arraybuffer",l.withCredentials=c.crossOrigin==="use-credentials",l.send()}else this.clone()}}},{key:"read",value:function(a){var r=this.options,c=this.imageData,s=qi(a),h=0,o=1,u=1;if(s>1){this.url=function(l,m){for(var f=[],g=new Uint8Array(l);g.length>0;)f.push(Je.apply(null,Ve(g.subarray(0,8192)))),g=g.subarray(8192);return"data:".concat(m,";base64,").concat(btoa(f.join("")))}(a,Ue);var d=function(l){var m=0,f=1,g=1;switch(l){case 2:f=-1;break;case 3:m=-180;break;case 4:g=-1;break;case 5:m=90,g=-1;break;case 6:m=90;break;case 7:m=90,f=-1;break;case 8:m=-90}return{rotate:m,scaleX:f,scaleY:g}}(s);h=d.rotate,o=d.scaleX,u=d.scaleY}r.rotatable&&(c.rotate=h),r.scalable&&(c.scaleX=o,c.scaleY=u),this.clone()}},{key:"clone",value:function(){var a=this.element,r=this.url,c=a.crossOrigin,s=r;this.options.checkCrossOrigin&&Ze(r)&&(c||(c="anonymous"),s=Ge(r)),this.crossOrigin=c,this.crossOriginUrl=s;var h=document.createElement("img");c&&(h.crossOrigin=c),h.src=s||r,h.alt=a.alt||"The image to crop",this.image=h,h.onload=this.start.bind(this),h.onerror=this.stop.bind(this),W(h,Re),a.parentNode.insertBefore(h,a.nextSibling)}},{key:"start",value:function(){var a=this,r=this.image;r.onload=null,r.onerror=null,this.sizing=!0;var c=$.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test($.navigator.userAgent),s=function(u,d){T(a.imageData,{naturalWidth:u,naturalHeight:d,aspectRatio:u/d}),a.initialImageData=T({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(!r.naturalWidth||c){var h=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=h,h.onload=function(){s(h.width,h.height),c||o.removeChild(h)},h.src=r.src,c||(h.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(h))}else s(r.naturalWidth,r.naturalHeight)}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var a=this.element,r=this.options,c=this.image,s=a.parentNode,h=document.createElement("div");h.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=h.querySelector(".".concat(O,"-container")),u=o.querySelector(".".concat(O,"-canvas")),d=o.querySelector(".".concat(O,"-drag-box")),l=o.querySelector(".".concat(O,"-crop-box")),m=l.querySelector(".".concat(O,"-face"));this.container=s,this.cropper=o,this.canvas=u,this.dragBox=d,this.cropBox=l,this.viewBox=o.querySelector(".".concat(O,"-view-box")),this.face=m,u.appendChild(c),W(a,L),s.insertBefore(o,a.nextSibling),q(c,Re),this.initPreview(),this.bind(),r.initialAspectRatio=Math.max(0,r.initialAspectRatio)||NaN,r.aspectRatio=Math.max(0,r.aspectRatio)||NaN,r.viewMode=Math.max(0,Math.min(3,Math.round(r.viewMode)))||0,W(l,L),r.guides||W(l.getElementsByClassName("".concat(O,"-dashed")),L),r.center||W(l.getElementsByClassName("".concat(O,"-center")),L),r.background&&W(o,"".concat(O,"-bg")),r.highlight||W(m,Ni),r.cropBoxMovable&&(W(m,_t),yt(m,wt,St)),r.cropBoxResizable||(W(l.getElementsByClassName("".concat(O,"-line")),L),W(l.getElementsByClassName("".concat(O,"-point")),L)),this.render(),this.ready=!0,this.setDragMode(r.dragMode),r.autoCrop&&this.crop(),this.setData(r.data),Y(r.ready)&&X(a,Ie,r.ready,{once:!0}),ut(a,Ie)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),q(this.element,L)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&Te(i.prototype,e),n&&Te(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,e,n}();T(ti.prototype,Vi,Fi,Ki,Qi,Zi,Gi);const ta=["alt","crossorigin","src"],ea=Ct({name:"Cropper",__name:"Cropper",props:{src:S.string.def(""),alt:S.string.def(""),circled:S.bool.def(!1),realTimePreview:S.bool.def(!0),height:S.string.def("360px"),crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},emits:["cropend","ready","cropendError"],setup(t,{emit:i}){const e={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},n=t,a=i,r=ui(),c=P(),s=P(),h=P(!1),{getPrefixCls:o}=Ht(),u=o("cropper-image"),d=bi(g,80),l=Wt(()=>({height:n.height,maxWidth:"100%",...n.imageStyle})),m=Wt(()=>[u,r.class,{[`${u}--circled`]:n.circled}]),f=Wt(()=>({height:`${n.height}`.replace(/px/,"")+"px"}));function g(){n.realTimePreview&&function(){if(!s.value)return;let b=s.value.getData();(n.circled?function(){const C=s.value.getCroppedCanvas(),w=document.createElement("canvas"),D=w.getContext("2d"),x=C.width,p=C.height;return w.width=x,w.height=p,D.imageSmoothingEnabled=!0,D.drawImage(C,0,0,x,p),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(x/2,p/2,Math.min(x,p)/2,0,2*Math.PI,!0),D.fill(),w}():s.value.getCroppedCanvas()).toBlob(C=>{if(!C)return;let w=new FileReader;w.readAsDataURL(C),w.onloadend=D=>{var x;a("cropend",{imgBase64:((x=D.target)==null?void 0:x.result)??"",imgInfo:b})},w.onerror=()=>{a("cropendError")}},"image/png")}()}return mi(async function(){const b=v(c);b&&(s.value=new ti(b,{...e,ready:()=>{h.value=!0,g(),a("ready",s.value)},crop(){d()},zoom(){d()},cropmove(){d()},...n.options}))}),gi(()=>{var b;(b=s.value)==null||b.destroy()}),(b,C)=>(U(),st("div",{class:F(v(m)),style:be(v(f))},[fi(et("img",{ref_key:"imgElRef",ref:c,alt:t.alt,crossorigin:t.crossorigin,src:t.src,style:be(v(l))},null,12,ta),[[vi,v(h)]])],6))}}),ia=["alt","src"],aa=Ct({name:"CopperModal",__name:"CopperModal",props:{srcValue:S.string.def(""),circled:S.bool.def(!0)},emits:["uploadSuccess"],setup(t,{expose:i,emit:e}){const n=t,a=e,{t:r}=we.useI18n(),{getPrefixCls:c}=Ht(),s=c("cropper-am"),h=P(n.srcValue),o=P(""),u=P(),d=P(!1);let l="",m=1,f=1;function g(x){const p=new FileReader;return p.readAsDataURL(x),h.value="",o.value="",p.onload=function(y){var k;h.value=((k=y.target)==null?void 0:k.result)??"",l=x.name},!1}function b({imgBase64:x}){o.value=x}function C(x){u.value=x}function w(x,p){var y,k;x==="scaleX"&&(m=p=m===-1?1:-1),x==="scaleY"&&(f=p=f===-1?1:-1),(k=(y=u==null?void 0:u.value)==null?void 0:y[x])==null||k.call(y,p)}async function D(){const x=zi(o.value);a("uploadSuccess",{source:o.value,data:x,filename:l})}return i({openModal:function(){d.value=!0},closeModal:function(){d.value=!1}}),(x,p)=>{const y=Ei,k=yi,j=xi,_=Ti,N=De,K=Me,H=Oi;return U(),st("div",null,[B(H,{modelValue:v(d),"onUpdate:modelValue":p[7]||(p[7]=E=>wi(d)?d.value=E:null),canFullscreen:!1,title:v(r)("cropper.modalTitle"),maxHeight:"380px",width:"800px"},{footer:R(()=>[B(K,{type:"primary",onClick:D},{default:R(()=>[ye(xe(v(r)("cropper.okText")),1)]),_:1})]),default:R(()=>[et("div",{class:F(v(s))},[et("div",{class:F(`${v(s)}-left`)},[et("div",{class:F(`${v(s)}-cropper`)},[v(h)?(U(),Dt(v(ea),{key:0,circled:t.circled,src:v(h),height:"300px",onCropend:b,onReady:C},null,8,["circled","src"])):ht("",!0)],2),et("div",{class:F(`${v(s)}-toolbar`)},[B(j,{beforeUpload:g,fileList:[],accept:"image/*"},{default:R(()=>[B(k,{content:v(r)("cropper.selectImage"),placement:"bottom"},{default:R(()=>[B(y,{preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["content"])]),_:1}),B(_,null,{default:R(()=>[B(k,{content:v(r)("cropper.btn_reset"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"ant-design:reload-outlined",size:"small",type:"primary",onClick:p[0]||(p[0]=E=>w("reset"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_left"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"ant-design:rotate-left-outlined",size:"small",type:"primary",onClick:p[1]||(p[1]=E=>w("rotate",-45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_right"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:p[2]||(p[2]=E=>w("rotate",45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_x"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"vaadin:arrows-long-h",size:"small",type:"primary",onClick:p[3]||(p[3]=E=>w("scaleX"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_y"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"vaadin:arrows-long-v",size:"small",type:"primary",onClick:p[4]||(p[4]=E=>w("scaleY"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_in"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"ant-design:zoom-in-outlined",size:"small",type:"primary",onClick:p[5]||(p[5]=E=>w("zoom",.1))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_out"),placement:"bottom"},{default:R(()=>[B(y,{disabled:!v(h),preIcon:"ant-design:zoom-out-outlined",size:"small",type:"primary",onClick:p[6]||(p[6]=E=>w("zoom",-.1))},null,8,["disabled"])]),_:1},8,["content"])]),_:1})],2)],2),et("div",{class:F(`${v(s)}-right`)},[et("div",{class:F(`${v(s)}-preview`)},[v(o)?(U(),st("img",{key:0,alt:v(r)("cropper.preview"),src:v(o)},null,8,ia)):ht("",!0)],2),v(o)?(U(),st("div",{key:0,class:F(`${v(s)}-group`)},[B(N,{src:v(o),size:"large"},null,8,["src"]),B(N,{size:48,src:v(o)},null,8,["src"]),B(N,{size:64,src:v(o)},null,8,["src"]),B(N,{size:80,src:v(o)},null,8,["src"])],2)):ht("",!0)],2)],2)]),_:1},8,["modelValue","title"])])}}}),na=Ce(Ct({name:"CropperAvatar",__name:"CropperAvatar",props:{width:S.string.def("200px"),value:S.string.def(""),showBtn:S.bool.def(!0),btnText:S.string.def("")},emits:["update:value","change"],setup(t,{expose:i,emit:e}){const n=t,a=e,r=P(n.value),{getPrefixCls:c}=Ht(),s=c("cropper-avatar"),h=Di(),{t:o}=we.useI18n(),u=P();function d({source:m,data:f,filename:g}){r.value=m,a("change",{source:m,data:f,filename:g}),h.success(o("cropper.uploadSuccess"))}function l(){u.value.openModal()}return Mi(()=>{r.value=n.value}),Ci(()=>r.value,m=>{a("update:value",m)}),i({open:l,close:function(){u.value.closeModal()}}),(m,f)=>{const g=De,b=Me;return U(),st("div",{class:"user-info-head",onClick:f[1]||(f[1]=C=>l())},[v(r)?(U(),Dt(g,{key:0,src:v(r),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])):ht("",!0),v(r)?ht("",!0):(U(),Dt(g,{key:1,src:v(Wi),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])),t.showBtn?(U(),Dt(b,{key:2,class:F(`${v(s)}-upload-btn`),onClick:f[0]||(f[0]=C=>l())},{default:R(()=>[ye(xe(t.btnText?t.btnText:v(o)("cropper.selectImage")),1)]),_:1},8,["class"])):ht("",!0),B(aa,{ref_key:"cropperModelRef",ref:u,srcValue:v(r),onUploadSuccess:d},null,8,["srcValue"])])}}}),[["__scopeId","data-v-e5ea03b5"]]),ra={class:"change-avatar"},oa=Ce(Ct({name:"UserAvatar",__name:"UserAvatar",props:{img:S.string.def("")},setup(t){const i=ki(),e=P(),n=async({data:a})=>{const r=await Bi({avatarFile:a});e.value.close(),i.setUserAvatarAction(r.data)};return(a,r)=>(U(),st("div",ra,[B(v(na),{ref_key:"cropperRef",ref:e,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},showBtn:!1,value:t.img,width:"120px",onChange:n},null,8,["value"])]))}}),[["__scopeId","data-v-47d4903b"]]);export{oa as default};
