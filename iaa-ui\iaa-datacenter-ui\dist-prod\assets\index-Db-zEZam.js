import{d as Q,a as W,y as X,p as Z,j as y,r as ee,bu as ae,f as le,T as te,o as s,c as b,k as a,w as o,u as t,M as A,F as g,g as C,h as p,N as oe,D as E,v as d,x as h,t as z,l as re,d2 as se,S as ne,B as ie,z as pe,A as ue,C as de,G as me,H as ce,I as fe,J as ye,K as _e,L as we}from"./index-C8b06LRn.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ge}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as he}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ve}from"./index-CkzUfjB7.js";import{d as F,a as ke}from"./formatTime-COZ9Bl52.js";import{e as xe,c as Ce}from"./index-czoBMNFJ.js";import{C as Se}from"./index-uSeXqrUH.js";import{a as Te}from"./index-DDzAA47d.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";const Ve=Q({name:"BpmProcessInstanceMy",__name:"index",setup(Ie){const S=W(),T=X(),{t:V}=Z(),v=y(!0),I=y(0),D=y([]),r=ee({pageNo:1,pageSize:10,name:"",processDefinitionKey:void 0,category:void 0,status:void 0,createTime:[]}),M=y(),N=y([]),c=async()=>{v.value=!0;try{const n=await xe(r);D.value=n.list,I.value=n.total}finally{v.value=!1}},_=()=>{r.pageNo=1,c()},R=()=>{M.value.resetFields(),_()},P=async n=>{if(n!=null&&n.id&&(await Te(n.processDefinitionId)).formType===20)return void T.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u8BE5\u6D41\u7A0B\u4F7F\u7528\u4E1A\u52A1\u8868\u5355\uFF0C\u4E0D\u652F\u6301\u91CD\u65B0\u53D1\u8D77");await S.push({name:"BpmProcessInstanceCreate",query:{processInstanceId:n==null?void 0:n.id}})};return ae(()=>{c()}),le(async()=>{await c(),N.value=await Se.getCategorySimpleList()}),(n,l)=>{const Y=ve,U=ne,m=ie,B=pe,K=ue,H=de,k=me,u=ce,L=fe,q=he,i=ye,O=ge,j=_e,G=be,x=te("hasPermi"),J=we;return s(),b(g,null,[a(Y,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(q,null,{default:o(()=>[a(L,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:M,inline:!0,"label-width":"68px"},{default:o(()=>[a(m,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:o(()=>[a(U,{modelValue:t(r).name,"onUpdate:modelValue":l[0]||(l[0]=e=>t(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:A(_,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(m,{label:"\u6240\u5C5E\u6D41\u7A0B",prop:"processDefinitionKey"},{default:o(()=>[a(U,{modelValue:t(r).processDefinitionKey,"onUpdate:modelValue":l[1]||(l[1]=e=>t(r).processDefinitionKey=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u5B9A\u4E49\u7684\u6807\u8BC6",clearable:"",onKeyup:A(_,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:o(()=>[a(K,{modelValue:t(r).category,"onUpdate:modelValue":l[2]||(l[2]=e=>t(r).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),b(g,null,C(t(N),e=>(s(),p(B,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status"},{default:o(()=>[a(K,{modelValue:t(r).status,"onUpdate:modelValue":l[3]||(l[3]=e=>t(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),b(g,null,C(t(oe)(t(E).BPM_PROCESS_INSTANCE_STATUS),e=>(s(),p(B,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime"},{default:o(()=>[a(H,{modelValue:t(r).createTime,"onUpdate:modelValue":l[4]||(l[4]=e=>t(r).createTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:o(()=>[a(u,{onClick:_},{default:o(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),l[8]||(l[8]=d(" \u641C\u7D22"))]),_:1}),a(u,{onClick:R},{default:o(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),l[9]||(l[9]=d(" \u91CD\u7F6E"))]),_:1}),h((s(),p(u,{type:"primary",plain:"",onClick:l[5]||(l[5]=e=>P(void 0))},{default:o(()=>[a(k,{icon:"ep:plus",class:"mr-5px"}),l[10]||(l[10]=d(" \u53D1\u8D77\u6D41\u7A0B "))]),_:1})),[[x,["bpm:process-instance:query"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(q,null,{default:o(()=>[h((s(),p(j,{data:t(D)},{default:o(()=>[a(i,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name","min-width":"200px",fixed:"left"}),a(i,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName","min-width":"100",fixed:"left"}),a(i,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status",width:"120"},{default:o(e=>[a(O,{type:t(E).BPM_PROCESS_INSTANCE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(i,{label:"\u53D1\u8D77\u65F6\u95F4",align:"center",prop:"startTime",width:"180",formatter:t(F)},null,8,["formatter"]),a(i,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:t(F)},null,8,["formatter"]),a(i,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:o(e=>[d(z(e.row.durationInMillis>0?t(ke)(e.row.durationInMillis):"-"),1)]),_:1}),a(i,{label:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",prop:"tasks","min-width":"120px"},{default:o(e=>[(s(!0),b(g,null,C(e.row.tasks,f=>(s(),p(u,{type:"primary",key:f.id,link:""},{default:o(()=>[re("span",null,z(f.name),1)]),_:2},1024))),128))]),_:1}),a(i,{label:"\u6D41\u7A0B\u7F16\u53F7",align:"center",prop:"id","min-width":"320px"}),a(i,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"180"},{default:o(e=>[h((s(),p(u,{link:"",type:"primary",onClick:f=>{return w=e.row,void S.push({name:"BpmProcessInstanceDetail",query:{id:w.id}});var w}},{default:o(()=>l[11]||(l[11]=[d(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])),[[x,["bpm:process-instance:cancel"]]]),e.row.status===1?h((s(),p(u,{key:0,link:"",type:"primary",onClick:f=>(async w=>{const{value:$}=await se.prompt("\u8BF7\u8F93\u5165\u53D6\u6D88\u539F\u56E0","\u53D6\u6D88\u6D41\u7A0B",{confirmButtonText:V("common.ok"),cancelButtonText:V("common.cancel"),inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});await Ce(w.id,$),T.success("\u53D6\u6D88\u6210\u529F"),await c()})(e.row)},{default:o(()=>l[12]||(l[12]=[d(" \u53D6\u6D88 ")])),_:2},1032,["onClick"])),[[x,["bpm:process-instance:query"]]]):(s(),p(u,{key:1,link:"",type:"primary",onClick:f=>P(e.row)},{default:o(()=>l[13]||(l[13]=[d(" \u91CD\u65B0\u53D1\u8D77 ")])),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data"])),[[J,t(v)]]),a(G,{total:t(I),page:t(r).pageNo,"onUpdate:page":l[6]||(l[6]=e=>t(r).pageNo=e),limit:t(r).pageSize,"onUpdate:limit":l[7]||(l[7]=e=>t(r).pageSize=e),onPagination:c},null,8,["total","page","limit"])]),_:1})],64)}}});export{Ve as default};
