import{aG as o}from"./index-C8b06LRn.js";const e={getRouteById:async t=>await o.get({url:"/report/technology-route/get/"+t}),saveRoute:async t=>await o.post({url:"/report/technology-route/save",data:t}),getRoutePage:async t=>await o.post({url:"/report/technology-route/page",data:t}),getRouteLinkPage:async t=>await o.post({url:"/report/technology-route/page-link",data:t}),getRouteAllVersionList:async()=>await o.get({url:"/report/technology-route/get-route-all-version"}),saveLink:async t=>await o.post({url:"/report/technology-route/save-link",data:t}),selectLinkCount:async t=>await o.post({url:"/report/technology-route/select-link-count",data:t}),exportLink:async t=>await o.downloadPost({url:"/report/technology-route/export-link",data:t}),saveLinkConditions:async t=>await o.post({url:"/report/technology-route/save-link-conditions",data:t}),exportTemplate:async()=>await o.downloadPost({url:"/report/technology-route/export-template"}),exportLinkAll:async t=>await o.downloadPost({url:"/report/technology-route/export-link-all",data:t})};export{e as R};
