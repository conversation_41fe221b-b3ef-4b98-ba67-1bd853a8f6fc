import{aG as t}from"./index-C8b06LRn.js";const e=async a=>await t.get({url:"/infra/demo03-student/page",params:a}),d=async a=>await t.get({url:"/infra/demo03-student/get?id="+a}),s=async a=>await t.post({url:"/infra/demo03-student/create",data:a}),n=async a=>await t.put({url:"/infra/demo03-student/update",data:a}),r=async a=>await t.delete({url:"/infra/demo03-student/delete?id="+a}),u=async a=>await t.download({url:"/infra/demo03-student/export-excel",params:a}),i=async a=>await t.get({url:"/infra/demo03-student/demo03-course/list-by-student-id?studentId="+a}),o=async a=>await t.get({url:"/infra/demo03-student/demo03-grade/get-by-student-id?studentId="+a});export{i as a,o as b,s as c,e as d,r as e,u as f,d as g,n as u};
