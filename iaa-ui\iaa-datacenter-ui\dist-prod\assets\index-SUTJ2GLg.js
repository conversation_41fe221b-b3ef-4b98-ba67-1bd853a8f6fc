import{aG as t}from"./index-C8b06LRn.js";const r={getDayPage:async a=>await t.post({url:"/report/production-day/page",data:a}),getDay:async a=>await t.get({url:"/report/production-day/get?batchesId="+a}),getOrderCodeNum:async a=>await t.post({url:"/report/production-day/getOrderCodeNum",data:a}),createDay:async a=>await t.post({url:"/report/production-day/add",data:a}),createAbnormalDay:async a=>await t.post({url:"/report/production-day/addAbnormal",data:a}),getSalesOrderInfo:async a=>await t.get({url:`/report/production-day/getSalesNo/${a}`}),getAbnormalByBatchesId:async a=>await t.get({url:"/report/production-day/getAbnormalByBatchesId?batchesId="+a}),deleteAbnormalDay:async a=>await t.get({url:`/report/production-day/deleteAbnormal/${a}`}),updateDisplayAbnormal:async a=>await t.post({url:"/report/production-day/updateDisplayAbnormal",data:a}),getAbnormalDay:async a=>await t.get({url:`/report/production-day/getAbnormal/${a}`}),getAttendanceDay:async a=>await t.get({url:`/report/production-day/getAttendance/${a}`}),createAttendance:async a=>await t.post({url:"/report/production-day/addAttendance",data:a}),deleteAttendanceDay:async a=>await t.get({url:`/report/production-day/deleteAttendance/${a}`}),batchUpdateDays:async a=>await t.post({url:"/report/production-day/batch-update",data:a}),batchUpdateAbnormalNum:async a=>await t.post({url:"/report/production-day/batch-update-abnormal-num",data:a}),generated:async a=>await t.post({url:"/report/production-day/generated",data:a}),deleteDay:async a=>await t.get({url:`/report/production-day/delete/${a}`}),deleteDays:async a=>await t.post({url:"/report/production-day/delete/ids",data:a}),getPrevious:async()=>await t.get({url:"/report/production-day/getPrevious"}),auditDay:async a=>await t.post({url:"/report/production-day/auditDay/all",data:a}),auditDays:async a=>await t.post({url:"/report/production-day/auditDay/ids",data:a}),exportDay:async a=>await t.download({url:"/report/production-day/export-excel",params:a}),exportAttendance:async a=>await t.download({url:"/report/production-day/export-attendance",method:"POST",data:a}),getWorkSalesOptions:async a=>await t.post({url:"/report/production-day/work-sales",data:a}),getProductNo:async a=>await t.post({url:"/report/production-day/getProductNo",data:a})};export{r as D};
