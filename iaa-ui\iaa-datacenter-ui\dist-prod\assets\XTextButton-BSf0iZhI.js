import{d,aj as n,Y as f,h as s,w as u,ar as m,u as I,H as k,o as a,$ as c,v as b,t as _,bi as g,G as x,_ as y}from"./index-C8b06LRn.js";const C=y(d({name:"XTextButton",__name:"XTextButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def("primary"),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(e){const i=e,r=f(()=>{const l=["title","preIcon","postIcon","onClick"],t={...g(),...i};for(const o in t)l.indexOf(o)!==-1&&delete t[o];return t});return(l,t)=>{const o=x,p=k;return a(),s(p,m({link:""},I(r),{onClick:e.onClick}),{default:u(()=>[e.preIcon?(a(),s(o,{key:0,icon:e.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),b(" "+_(e.title?e.title:"")+" ",1),e.postIcon?(a(),s(o,{key:1,icon:e.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-7561ab3f"]]);export{C as _};
