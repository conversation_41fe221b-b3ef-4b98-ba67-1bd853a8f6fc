<template>
  <div class="chunk-upload-demo">
    <ContentWrap title="大文件分片上传示例">
      <div class="demo-container">
        <div class="upload-section">
          <h3>分片上传</h3>
          <p class="description">
            支持大文件分片上传、断点续传、秒传功能。适用于视频、大型文档等大文件上传场景。
          </p>
          
          <ChunkUpload
            ref="chunkUploadRef"
            :max-size="2048"
            :chunk-size="5"
            accept="*"
            @success="handleUploadSuccess"
            @error="handleUploadError"
            @progress="handleUploadProgress"
          />
        </div>

        <div class="file-list-section">
          <h3>上传记录</h3>
          <el-table :data="uploadedFiles" style="width: 100%">
            <el-table-column prop="fileName" label="文件名" min-width="200" />
            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column prop="uploadTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.uploadTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="previewFile(row)"
                >
                  预览
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadFile(row)"
                >
                  下载
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteFile(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-if="uploadedFiles.length === 0" class="empty-state">
            <el-empty description="暂无上传记录" />
          </div>
        </div>
      </div>
    </ContentWrap>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      :title="previewData.fileName"
      width="80%"
      :before-close="closePreview"
    >
      <div class="preview-container">
        <!-- 图片预览 -->
        <div v-if="isImage(previewData.fileName)" class="image-preview">
          <img :src="previewData.fileUrl" alt="预览图片" style="max-width: 100%; max-height: 500px;" />
        </div>
        
        <!-- 视频预览 -->
        <div v-else-if="isVideo(previewData.fileName)" class="video-preview">
          <video :src="previewData.fileUrl" controls style="max-width: 100%; max-height: 500px;">
            您的浏览器不支持视频播放
          </video>
        </div>
        
        <!-- PDF预览 -->
        <div v-else-if="isPdf(previewData.fileName)" class="pdf-preview">
          <iframe :src="previewData.fileUrl" style="width: 100%; height: 500px; border: none;"></iframe>
        </div>
        
        <!-- 其他文件类型 -->
        <div v-else class="other-preview">
          <el-result
            icon="document"
            title="无法预览此文件类型"
            sub-title="请下载文件后查看"
          >
            <template #extra>
              <el-button type="primary" @click="downloadFile(previewFile)">
                下载文件
              </el-button>
            </template>
          </el-result>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ChunkUpload from '@/components/ChunkUpload/index.vue'
import * as ChunkUploadApi from '@/api/infra/chunkUpload'
import * as FileApi from '@/api/infra/file'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'ChunkUploadDemo' })

const chunkUploadRef = ref()

// 上传的文件列表
const uploadedFiles = ref<any[]>([])

// 文件预览
const previewVisible = ref(false)
// 文件预览数据
const previewData = reactive({
  fileName: '',
  fileUrl: '',
  fileSize: 0
})

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 判断文件类型
const isImage = (fileName: string): boolean => {
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExts.some(ext => fileName.toLowerCase().endsWith(ext))
}

const isVideo = (fileName: string): boolean => {
  const videoExts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
  return videoExts.some(ext => fileName.toLowerCase().endsWith(ext))
}

const isPdf = (fileName: string): boolean => {
  return fileName.toLowerCase().endsWith('.pdf')
}

// 上传成功处理
const handleUploadSuccess = (file: ChunkUploadApi.ChunkMergeRespVO) => {
  console.log('上传成功:', file)
  
  // 添加到上传记录
  uploadedFiles.value.unshift({
    fileId: file.fileId,
    fileName: file.fileName,
    fileUrl: file.fileUrl,
    fileSize: file.fileSize,
    uploadTime: new Date()
  })
  
  ElMessage.success(`文件 "${file.fileName}" 上传成功！`)
}

// 上传失败处理
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
}

// 上传进度处理
const handleUploadProgress = (progress: number) => {
  console.log('上传进度:', progress + '%')
}

// 预览文件函数
const previewFile = (file: any) => {
  previewData.fileName = file.fileName
  previewData.fileUrl = file.fileUrl
  previewData.fileSize = file.fileSize
  previewVisible.value = true
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
}

// 下载文件
const downloadFile = (file: any) => {
  // 创建一个临时的a标签来触发下载
  const link = document.createElement('a')
  link.href = file.fileUrl
  link.download = file.fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('开始下载文件')
}

// 删除文件
const deleteFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.fileName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    await FileApi.deleteFile(file.fileId)
    
    // 从列表中移除
    const index = uploadedFiles.value.findIndex(f => f.fileId === file.fileId)
    if (index > -1) {
      uploadedFiles.value.splice(index, 1)
    }
    
    ElMessage.success('文件删除成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('文件删除失败')
    }
  }
}
</script>

<style scoped>
.chunk-upload-demo {
  padding: 20px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

.upload-section {
  margin-bottom: 40px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-section h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.description {
  margin: 0 0 24px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.file-list-section {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-list-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.empty-state {
  padding: 40px 0;
}

.preview-container {
  text-align: center;
}

.image-preview img,
.video-preview video {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
