import{d as L,y as E,p as Q,j as n,r as W,f as X,aH as Z,T as $,o,c as b,k as e,w as r,u as t,M as ee,F as x,g as Y,N as ae,D as z,h as d,v as c,x as y,t as le,S as te,B as re,z as se,A as oe,C as pe,G as ne,H as ue,I as ie,J as me,K as de,L as ce}from"./index-C8b06LRn.js";import{_ as fe}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as _e}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as be}from"./index-CkzUfjB7.js";import{d as ye}from"./formatTime-COZ9Bl52.js";import{b as we,d as ke}from"./index-Cgv48ZKs.js";import{_ as ve}from"./UserGroupForm.vue_vue_type_script_setup_true_lang-CGBd7sl2.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./constants-D3f7Z3TX.js";const he=L({name:"BpmUserGroup",__name:"index",setup(xe){const C=E(),{t:H}=Q(),w=n(!0),S=n(0),T=n([]),s=W({pageNo:1,pageSize:10,name:null,status:null,createTime:[]}),V=n(),U=n([]),u=async()=>{w.value=!0;try{const i=await we(s);T.value=i.list,S.value=i.total}finally{w.value=!1}},k=()=>{s.pageNo=1,u()},O=()=>{V.value.resetFields(),k()},M=n(),N=(i,a)=>{M.value.open(i,a)};return X(async()=>{await u(),U.value=await Z()}),(i,a)=>{const A=be,F=te,f=re,I=se,B=oe,G=pe,v=ne,m=ue,K=ie,D=ge,p=me,P=_e,R=de,j=fe,h=$("hasPermi"),q=ce;return o(),b(x,null,[e(A,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),e(D,null,{default:r(()=>[e(K,{class:"-mb-15px",model:t(s),ref_key:"queryFormRef",ref:V,inline:!0,"label-width":"68px"},{default:r(()=>[e(f,{label:"\u7EC4\u540D",prop:"name"},{default:r(()=>[e(F,{modelValue:t(s).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(s).name=l),placeholder:"\u8BF7\u8F93\u5165\u7EC4\u540D",clearable:"",onKeyup:ee(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[e(B,{modelValue:t(s).status,"onUpdate:modelValue":a[1]||(a[1]=l=>t(s).status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(o(!0),b(x,null,Y(t(ae)(t(z).COMMON_STATUS),l=>(o(),d(I,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(G,{modelValue:t(s).createTime,"onUpdate:modelValue":a[2]||(a[2]=l=>t(s).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(f,null,{default:r(()=>[e(m,{onClick:k},{default:r(()=>[e(v,{icon:"ep:search",class:"mr-5px"}),a[6]||(a[6]=c(" \u641C\u7D22"))]),_:1}),e(m,{onClick:O},{default:r(()=>[e(v,{icon:"ep:refresh",class:"mr-5px"}),a[7]||(a[7]=c(" \u91CD\u7F6E"))]),_:1}),y((o(),d(m,{type:"primary",plain:"",onClick:a[3]||(a[3]=l=>N("create"))},{default:r(()=>[e(v,{icon:"ep:plus",class:"mr-5px"}),a[8]||(a[8]=c(" \u65B0\u589E "))]),_:1})),[[h,["bpm:user-group:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,null,{default:r(()=>[y((o(),d(R,{data:t(T)},{default:r(()=>[e(p,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(p,{label:"\u7EC4\u540D",align:"center",prop:"name"}),e(p,{label:"\u63CF\u8FF0",align:"center",prop:"description"}),e(p,{label:"\u6210\u5458",align:"center"},{default:r(l=>[(o(!0),b(x,null,Y(l.row.userIds,_=>{var g;return o(),b("span",{key:_,class:"pr-5px"},le((g=t(U).find(J=>J.id===_))==null?void 0:g.nickname),1)}),128))]),_:1}),e(p,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:r(l=>[e(P,{type:t(z).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(p,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(ye)},null,8,["formatter"]),e(p,{label:"\u64CD\u4F5C",align:"center"},{default:r(l=>[y((o(),d(m,{link:"",type:"primary",onClick:_=>N("update",l.row.id)},{default:r(()=>a[9]||(a[9]=[c(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[h,["bpm:user-group:update"]]]),y((o(),d(m,{link:"",type:"danger",onClick:_=>(async g=>{try{await C.delConfirm(),await ke(g),C.success(H("common.delSuccess")),await u()}catch{}})(l.row.id)},{default:r(()=>a[10]||(a[10]=[c(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[h,["bpm:user-group:delete"]]])]),_:1})]),_:1},8,["data"])),[[q,t(w)]]),e(j,{total:t(S),page:t(s).pageNo,"onUpdate:page":a[4]||(a[4]=l=>t(s).pageNo=l),limit:t(s).pageSize,"onUpdate:limit":a[5]||(a[5]=l=>t(s).pageSize=l),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(ve,{ref_key:"formRef",ref:M,onSuccess:u},null,512)],64)}}});export{he as default};
