import{d as ae,j as i,Y as le,o as n,h as M,w as r,l,k as s,u as C,dE as te,M as se,v as T,t as f,x as ie,c as o,F as H,g as U,n as ne,$ as w,S as re,C as oe,aM as ue,G as de,bm as ce,az as ve,_ as me}from"./index-C8b06LRn.js";import{E as pe}from"./el-infinite-scroll-fE_Jh_bm.js";import{E as fe}from"./el-drawer-C5TFtzfV.js";import{E as he}from"./el-empty-ag1-OZ0J.js";import{E as ge}from"./el-skeleton-item-DDp1oSSE.js";import{E as ye,a as _e}from"./el-collapse-item-JANV_ocl.js";import{E as Ve}from"./el-avatar-BVm8aVjJ.js";import{f as $}from"./dateUtil-D9m5ek6U.js";import{P as be}from"./publicity-DYNFYfr5.js";const ke={class:"drawer-content"},we={class:"search-bar"},Ye={class:"stats-bar"},xe=["infinite-scroll-disabled"],Ce={class:"item-header"},ze={class:"operator-info"},De={class:"operator-details"},Ee={class:"operator-name"},Me={class:"operation-time"},Te={class:"time-text"},He={class:"item-content"},Ue={class:"change-record"},$e={class:"change-description"},je={key:0,class:"change-details"},Ne={class:"details-content"},Se={class:"change-items"},Fe={class:"change-text"},Ge={key:0,class:"loading-container"},Ie={key:1,class:"no-more"},Ke=me(ae({name:"HistoryDrawer",__name:"HistoryDrawer",setup(Pe,{expose:j}){const V=i(!1),u=i(!1),b=i(""),h=i(null),g=i([]),v=i([]),Y=i(0),y=i(1),N=i(20),d=i(!1);le(()=>v.value.length);const S=()=>{V.value=!1,z()},z=()=>{g.value=[],v.value=[],y.value=1,d.value=!1,b.value="",h.value=null},k=async(t=!1)=>{var e,c;if(!u.value){u.value=!0;try{const m={pageNo:y.value,pageSize:N.value,editContent:b.value,startTime:(e=h.value)==null?void 0:e[0],endTime:(c=h.value)==null?void 0:c[1]},p=await be.getHistory(m);t?g.value.push(...p.list):g.value=p.list,Y.value=p.total,v.value=[...g.value],g.value.length>=Y.value&&(d.value=!0)}catch{}finally{u.value=!1}}},F=()=>{u.value||d.value||(y.value++,k(!0))},D=()=>{y.value=1,d.value=!1,k()},G=()=>{y.value=1,d.value=!1,k()},I=t=>({0:"\u65B0\u589E",1:"\u4FEE\u6539",2:"\u5220\u9664"})[t]||"\u64CD\u4F5C",K=t=>{if(!t)return"";const e=t.split(";").filter(m=>m.trim());if(e.length===0)return t;const c=e[0].trim();return e.length===1?c:`${c}${e.length>1?"...":""}`},P=t=>t?t.split(";").filter(e=>e.trim()).length>1:!1;return j({open:()=>{V.value=!0,z(),k()}}),(t,e)=>{const c=re,m=oe,p=ue,q=de,A=Ve,B=ce,J=ye,L=_e,O=ge,Q=ve,R=he,W=fe,X=pe;return n(),M(W,{modelValue:V.value,"onUpdate:modelValue":e[2]||(e[2]=a=>V.value=a),title:"\u5386\u53F2\u53D8\u66F4\u8BB0\u5F55",size:700,direction:"rtl","before-close":S,class:"history-drawer"},{default:r(()=>[l("div",ke,[l("div",we,[s(c,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=a=>b.value=a),placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009","prefix-icon":C(te),clearable:"",onKeyup:se(D,["enter"]),onClear:D,class:"search-input"},null,8,["modelValue","prefix-icon"]),s(m,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=a=>h.value=a),type:"daterange","range-separator":"\u81F3","start-placeholder":"\u5F00\u59CB\u65F6\u95F4","end-placeholder":"\u7ED3\u675F\u65F6\u95F4",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:G,class:"date-picker"},null,8,["modelValue"])]),l("div",Ye,[s(p,{type:"info",size:"small"},{default:r(()=>[T(" \u5171 "+f(Y.value)+" \u6761\u8BB0\u5F55 ",1)]),_:1})]),ie((n(),o("div",{class:"history-list","infinite-scroll-distance":20,"infinite-scroll-immediate":!1,"infinite-scroll-disabled":u.value||d.value},[(n(!0),o(H,null,U(v.value,(a,Z)=>{return n(),o("div",{key:a.id||Z,class:ne(["history-item",{highlight:a.highlight}])},[l("div",Ce,[l("div",ze,[s(A,{size:32,class:"operator-avatar"},{default:r(()=>[s(q,{icon:"ep:user"})]),_:1}),l("div",De,[l("span",Ee,f(a.userName||"\u7CFB\u7EDF"),1)])]),l("div",Me,[s(B,{content:C($)(a.createTime),placement:"top"},{default:r(()=>[l("span",Te,f(C($)(a.createTime)),1)]),_:2},1032,["content"])])]),l("div",He,[l("div",Ue,[s(p,{type:(E=a.editType,{0:"success",1:"warning",2:"danger"}[E]||"info"),size:"small",class:"change-tag"},{default:r(()=>[T(f(I(a.editType)),1)]),_:2},1032,["type"]),l("div",$e,f(K(a.editContent)),1)]),a.editContent&&P(a.editContent)?(n(),o("div",je,[s(L,null,{default:r(()=>[s(J,{title:"\u67E5\u770B\u8BE6\u7EC6\u53D8\u66F4",name:a.id},{default:r(()=>{return[l("div",Ne,[l("div",Se,[(n(!0),o(H,null,U((x=a.editContent,x?x.split(";").map(_=>_.trim()).filter(_=>_.length>0):[]),(_,ee)=>(n(),o("div",{key:ee,class:"change-item"},[e[3]||(e[3]=l("div",{class:"change-bullet"},"\u2022",-1)),l("div",Fe,f(_),1)]))),128))])])];var x}),_:2},1032,["name"])]),_:2},1024)])):w("",!0)])],2);var E}),128)),u.value?(n(),o("div",Ge,[s(O,{rows:3,animated:""})])):w("",!0),d.value&&v.value.length>0?(n(),o("div",Ie,[s(Q,null,{default:r(()=>e[4]||(e[4]=[l("span",{class:"no-more-text"},"\u6CA1\u6709\u66F4\u591A\u8BB0\u5F55\u4E86",-1)])),_:1})])):w("",!0),u.value||v.value.length!==0?w("",!0):(n(),M(R,{key:2,description:"\u6682\u65E0\u5386\u53F2\u8BB0\u5F55","image-size":120}))],8,xe)),[[X,F]])])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-677368c2"]]);export{Ke as default};
