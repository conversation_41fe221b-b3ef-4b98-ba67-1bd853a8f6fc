import{aG as s}from"./index-C8b06LRn.js";const t=async a=>await s.get({url:"/butt-joint/xiaoman/permissions/page-user",params:a}),i=async()=>await s.get({url:"/butt-joint/xiaoman/permissions/list-xiaoman-user"}),n=async a=>await s.get({url:"/butt-joint/xiaoman/permissions/get-view/"+a}),e=async a=>await s.post({url:"/butt-joint/xiaoman/permissions/set-view",data:a}),o=async()=>await s.get({url:"/butt-joint/xiaoman/permissions/get-view-user"});export{o as a,n as g,i as l,t as p,e as s};
