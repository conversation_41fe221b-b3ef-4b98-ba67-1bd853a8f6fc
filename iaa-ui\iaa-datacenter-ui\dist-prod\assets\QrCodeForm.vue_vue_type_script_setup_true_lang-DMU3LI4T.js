import{d as It,aj as X,j as Tt,Y as st,au as Gt,x as Mt,c as Pt,h as Nt,i as te,n as Rt,u as x,l as ut,k as _,t as xt,$ as ee,aK as ne,X as re,L as oe,o as W,G as ie,aO as ae,ax as se,aZ as Lt,_ as ue,Z as ce,w as H,v as le,p as fe,a8 as de,az as ge,ab as he}from"./index-C8b06LRn.js";import{_ as pe}from"./XButton-BOgar_Ex.js";import{E as me}from"./el-card-CaOo8U9P.js";import{_ as we}from"./logo-BB9UjfGS.js";import{u as ye,_ as Ee,L as ve}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";var Q={},St={},L={};let ct;const Ce=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];L.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},L.getSymbolTotalCodewords=function(t){return Ce[t]},L.getBCHDigit=function(t){let e=0;for(;t!==0;)e++,t>>>=1;return e},L.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');ct=t},L.isKanjiModeEnabled=function(){return ct!==void 0},L.toSJIS=function(t){return ct(t)};var U,G={};function Ut(){this.buffer=[],this.length=0}(U=G).L={bit:1},U.M={bit:0},U.Q={bit:3},U.H={bit:2},U.isValid=function(t){return t&&t.bit!==void 0&&t.bit>=0&&t.bit<4},U.from=function(t,e){if(U.isValid(t))return t;try{return function(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"l":case"low":return U.L;case"m":case"medium":return U.M;case"q":case"quartile":return U.Q;case"h":case"high":return U.H;default:throw new Error("Unknown EC Level: "+r)}}(t)}catch{return e}},Ut.prototype={get:function(t){const e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Ae=Ut;function V(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}V.prototype.set=function(t,e,r,n){const i=t*this.size+e;this.data[i]=r,n&&(this.reservedBit[i]=!0)},V.prototype.get=function(t,e){return this.data[t*this.size+e]},V.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},V.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var be=V,kt={};(function(t){const e=L.getSymbolSize;t.getRowColCoords=function(r){if(r===1)return[];const n=Math.floor(r/7)+2,i=e(r),s=i===145?26:2*Math.ceil((i-13)/(2*n-2)),o=[i-7];for(let l=1;l<n-1;l++)o[l]=o[l-1]-s;return o.push(6),o.reverse()},t.getPositions=function(r){const n=[],i=t.getRowColCoords(r),s=i.length;for(let o=0;o<s;o++)for(let l=0;l<s;l++)o===0&&l===0||o===0&&l===s-1||o===s-1&&l===0||n.push([i[o],i[l]]);return n}})(kt);var _t={};const Be=L.getSymbolSize;_t.getPositions=function(t){const e=Be(t);return[[0,0],[e-7,0],[0,e-7]]};var zt={};(function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,r=3,n=40,i=10;function s(o,l,f){switch(o){case t.Patterns.PATTERN000:return(l+f)%2==0;case t.Patterns.PATTERN001:return l%2==0;case t.Patterns.PATTERN010:return f%3==0;case t.Patterns.PATTERN011:return(l+f)%3==0;case t.Patterns.PATTERN100:return(Math.floor(l/2)+Math.floor(f/3))%2==0;case t.Patterns.PATTERN101:return l*f%2+l*f%3==0;case t.Patterns.PATTERN110:return(l*f%2+l*f%3)%2==0;case t.Patterns.PATTERN111:return(l*f%3+(l+f)%2)%2==0;default:throw new Error("bad maskPattern:"+o)}}t.isValid=function(o){return o!=null&&o!==""&&!isNaN(o)&&o>=0&&o<=7},t.from=function(o){return t.isValid(o)?parseInt(o,10):void 0},t.getPenaltyN1=function(o){const l=o.size;let f=0,a=0,u=0,c=null,d=null;for(let m=0;m<l;m++){a=u=0,c=d=null;for(let g=0;g<l;g++){let p=o.get(m,g);p===c?a++:(a>=5&&(f+=e+(a-5)),c=p,a=1),p=o.get(g,m),p===d?u++:(u>=5&&(f+=e+(u-5)),d=p,u=1)}a>=5&&(f+=e+(a-5)),u>=5&&(f+=e+(u-5))}return f},t.getPenaltyN2=function(o){const l=o.size;let f=0;for(let a=0;a<l-1;a++)for(let u=0;u<l-1;u++){const c=o.get(a,u)+o.get(a,u+1)+o.get(a+1,u)+o.get(a+1,u+1);c!==4&&c!==0||f++}return f*r},t.getPenaltyN3=function(o){const l=o.size;let f=0,a=0,u=0;for(let c=0;c<l;c++){a=u=0;for(let d=0;d<l;d++)a=a<<1&2047|o.get(c,d),d>=10&&(a===1488||a===93)&&f++,u=u<<1&2047|o.get(d,c),d>=10&&(u===1488||u===93)&&f++}return f*n},t.getPenaltyN4=function(o){let l=0;const f=o.data.length;for(let a=0;a<f;a++)l+=o.data[a];return Math.abs(Math.ceil(100*l/f/5)-10)*i},t.applyMask=function(o,l){const f=l.size;for(let a=0;a<f;a++)for(let u=0;u<f;u++)l.isReserved(u,a)||l.xor(u,a,s(o,u,a))},t.getBestMask=function(o,l){const f=Object.keys(t.Patterns).length;let a=0,u=1/0;for(let c=0;c<f;c++){l(c),t.applyMask(c,o);const d=t.getPenaltyN1(o)+t.getPenaltyN2(o)+t.getPenaltyN3(o)+t.getPenaltyN4(o);t.applyMask(c,o),d<u&&(u=d,a=c)}return a}})(zt);var tt={};const z=G,et=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],nt=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];tt.getBlocksCount=function(t,e){switch(e){case z.L:return et[4*(t-1)+0];case z.M:return et[4*(t-1)+1];case z.Q:return et[4*(t-1)+2];case z.H:return et[4*(t-1)+3];default:return}},tt.getTotalCodewordsCount=function(t,e){switch(e){case z.L:return nt[4*(t-1)+0];case z.M:return nt[4*(t-1)+1];case z.Q:return nt[4*(t-1)+2];case z.H:return nt[4*(t-1)+3];default:return}};var Dt={},rt={};const q=new Uint8Array(512),ot=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)q[e]=t,ot[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)q[e]=q[e-255]})(),rt.log=function(t){if(t<1)throw new Error("log("+t+")");return ot[t]},rt.exp=function(t){return q[t]},rt.mul=function(t,e){return t===0||e===0?0:q[ot[t]+ot[e]]},function(t){const e=rt;t.mul=function(r,n){const i=new Uint8Array(r.length+n.length-1);for(let s=0;s<r.length;s++)for(let o=0;o<n.length;o++)i[s+o]^=e.mul(r[s],n[o]);return i},t.mod=function(r,n){let i=new Uint8Array(r);for(;i.length-n.length>=0;){const s=i[0];for(let l=0;l<n.length;l++)i[l]^=e.mul(n[l],s);let o=0;for(;o<i.length&&i[o]===0;)o++;i=i.slice(o)}return i},t.generateECPolynomial=function(r){let n=new Uint8Array([1]);for(let i=0;i<r;i++)n=t.mul(n,new Uint8Array([1,e.exp(i)]));return n}}(Dt);const Ft=Dt;function lt(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}lt.prototype.initialize=function(t){this.degree=t,this.genPoly=Ft.generateECPolynomial(this.degree)},lt.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const r=Ft.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){const i=new Uint8Array(this.degree);return i.set(r,n),i}return r};var Ie=lt,Ht={},D={},Kt={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},k={};const Yt="[0-9]+";let $="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";$=$.replace(/u/g,"\\u");const Te="(?:(?![A-Z0-9 $%*+\\-./:]|"+$+`)(?:.|[\r
]))+`;k.KANJI=new RegExp($,"g"),k.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),k.BYTE=new RegExp(Te,"g"),k.NUMERIC=new RegExp(Yt,"g"),k.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const Me=new RegExp("^"+$+"$"),Pe=new RegExp("^"+Yt+"$"),Ne=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");k.testKanji=function(t){return Me.test(t)},k.testNumeric=function(t){return Pe.test(t)},k.testAlphanumeric=function(t){return Ne.test(t)},function(t){const e=Kt,r=k;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(n,i){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!e.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?n.ccBits[0]:i<27?n.ccBits[1]:n.ccBits[2]},t.getBestModeForData=function(n){return r.testNumeric(n)?t.NUMERIC:r.testAlphanumeric(n)?t.ALPHANUMERIC:r.testKanji(n)?t.KANJI:t.BYTE},t.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},t.isValid=function(n){return n&&n.bit&&n.ccBits},t.from=function(n,i){if(t.isValid(n))return n;try{return function(s){if(typeof s!="string")throw new Error("Param is not a string");switch(s.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+s)}}(n)}catch{return i}}}(D),function(t){const e=L,r=tt,n=G,i=D,s=Kt,o=e.getBCHDigit(7973);function l(a,u){return i.getCharCountIndicator(a,u)+4}function f(a,u){let c=0;return a.forEach(function(d){const m=l(d.mode,u);c+=m+d.getBitsLength()}),c}t.from=function(a,u){return s.isValid(a)?parseInt(a,10):u},t.getCapacity=function(a,u,c){if(!s.isValid(a))throw new Error("Invalid QR Code version");c===void 0&&(c=i.BYTE);const d=8*(e.getSymbolTotalCodewords(a)-r.getTotalCodewordsCount(a,u));if(c===i.MIXED)return d;const m=d-l(c,a);switch(c){case i.NUMERIC:return Math.floor(m/10*3);case i.ALPHANUMERIC:return Math.floor(m/11*2);case i.KANJI:return Math.floor(m/13);case i.BYTE:default:return Math.floor(m/8)}},t.getBestVersionForData=function(a,u){let c;const d=n.from(u,n.M);if(Array.isArray(a)){if(a.length>1)return function(m,g){for(let p=1;p<=40;p++)if(f(m,p)<=t.getCapacity(p,g,i.MIXED))return p}(a,d);if(a.length===0)return 1;c=a[0]}else c=a;return function(m,g,p){for(let h=1;h<=40;h++)if(g<=t.getCapacity(h,p,m))return h}(c.mode,c.getLength(),d)},t.getEncodedBits=function(a){if(!s.isValid(a)||a<7)throw new Error("Invalid QR Code version");let u=a<<12;for(;e.getBCHDigit(u)-o>=0;)u^=7973<<e.getBCHDigit(u)-o;return a<<12|u}}(Ht);var Jt={};const ft=L,Ot=ft.getBCHDigit(1335);Jt.getEncodedBits=function(t,e){const r=t.bit<<3|e;let n=r<<10;for(;ft.getBCHDigit(n)-Ot>=0;)n^=1335<<ft.getBCHDigit(n)-Ot;return 21522^(r<<10|n)};var jt={};const Re=D;function K(t){this.mode=Re.NUMERIC,this.data=t.toString()}K.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},K.prototype.getLength=function(){return this.data.length},K.prototype.getBitsLength=function(){return K.getBitsLength(this.data.length)},K.prototype.write=function(t){let e,r,n;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),n=parseInt(r,10),t.put(n,10);const i=this.data.length-e;i>0&&(r=this.data.substr(e),n=parseInt(r,10),t.put(n,3*i+1))};var xe=K;const Le=D,dt=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Y(t){this.mode=Le.ALPHANUMERIC,this.data=t}Y.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},Y.prototype.getLength=function(){return this.data.length},Y.prototype.getBitsLength=function(){return Y.getBitsLength(this.data.length)},Y.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*dt.indexOf(this.data[e]);r+=dt.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(dt.indexOf(this.data[e]),6)};var Se=Y;const Ue=D;function J(t){this.mode=Ue.BYTE,this.data=typeof t=="string"?new TextEncoder().encode(t):new Uint8Array(t)}J.getBitsLength=function(t){return 8*t},J.prototype.getLength=function(){return this.data.length},J.prototype.getBitsLength=function(){return J.getBitsLength(this.data.length)},J.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)};var ke=J;const _e=D,ze=L;function O(t){this.mode=_e.KANJI,this.data=t}O.getBitsLength=function(t){return 13*t},O.prototype.getLength=function(){return this.data.length},O.prototype.getBitsLength=function(){return O.getBitsLength(this.data.length)},O.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=ze.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13)}};var De=O,Qt={exports:{}};(function(t){var e={single_source_shortest_paths:function(r,n,i){var s={},o={};o[n]=0;var l,f,a,u,c,d,m,g=e.PriorityQueue.make();for(g.push(n,0);!g.empty();)for(a in f=(l=g.pop()).value,u=l.cost,c=r[f]||{})c.hasOwnProperty(a)&&(d=u+c[a],m=o[a],(o[a]===void 0||m>d)&&(o[a]=d,g.push(a,d),s[a]=f));if(i!==void 0&&o[i]===void 0){var p=["Could not find a path from ",n," to ",i,"."].join("");throw new Error(p)}return s},extract_shortest_path_from_predecessor_list:function(r,n){for(var i=[],s=n;s;)i.push(s),r[s],s=r[s];return i.reverse(),i},find_path:function(r,n,i){var s=e.single_source_shortest_paths(r,n,i);return e.extract_shortest_path_from_predecessor_list(s,i)},PriorityQueue:{make:function(r){var n,i=e.PriorityQueue,s={};for(n in r=r||{},i)i.hasOwnProperty(n)&&(s[n]=i[n]);return s.queue=[],s.sorter=r.sorter||i.default_sorter,s},default_sorter:function(r,n){return r.cost-n.cost},push:function(r,n){var i={value:r,cost:n};this.queue.push(i),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};t.exports=e})(Qt);var Fe=Qt.exports;(function(t){const e=D,r=xe,n=Se,i=ke,s=De,o=k,l=L,f=Fe;function a(g){return unescape(encodeURIComponent(g)).length}function u(g,p,h){const v=[];let w;for(;(w=g.exec(h))!==null;)v.push({data:w[0],index:w.index,mode:p,length:w[0].length});return v}function c(g){const p=u(o.NUMERIC,e.NUMERIC,g),h=u(o.ALPHANUMERIC,e.ALPHANUMERIC,g);let v,w;return l.isKanjiModeEnabled()?(v=u(o.BYTE,e.BYTE,g),w=u(o.KANJI,e.KANJI,g)):(v=u(o.BYTE_KANJI,e.BYTE,g),w=[]),p.concat(h,v,w).sort(function(E,y){return E.index-y.index}).map(function(E){return{data:E.data,mode:E.mode,length:E.length}})}function d(g,p){switch(p){case e.NUMERIC:return r.getBitsLength(g);case e.ALPHANUMERIC:return n.getBitsLength(g);case e.KANJI:return s.getBitsLength(g);case e.BYTE:return i.getBitsLength(g)}}function m(g,p){let h;const v=e.getBestModeForData(g);if(h=e.from(p,v),h!==e.BYTE&&h.bit<v.bit)throw new Error('"'+g+'" cannot be encoded with mode '+e.toString(h)+`.
 Suggested mode is: `+e.toString(v));switch(h!==e.KANJI||l.isKanjiModeEnabled()||(h=e.BYTE),h){case e.NUMERIC:return new r(g);case e.ALPHANUMERIC:return new n(g);case e.KANJI:return new s(g);case e.BYTE:return new i(g)}}t.fromArray=function(g){return g.reduce(function(p,h){return typeof h=="string"?p.push(m(h,null)):h.data&&p.push(m(h.data,h.mode)),p},[])},t.fromString=function(g,p){const h=function(y){const A=[];for(let b=0;b<y.length;b++){const C=y[b];switch(C.mode){case e.NUMERIC:A.push([C,{data:C.data,mode:e.ALPHANUMERIC,length:C.length},{data:C.data,mode:e.BYTE,length:C.length}]);break;case e.ALPHANUMERIC:A.push([C,{data:C.data,mode:e.BYTE,length:C.length}]);break;case e.KANJI:A.push([C,{data:C.data,mode:e.BYTE,length:a(C.data)}]);break;case e.BYTE:A.push([{data:C.data,mode:e.BYTE,length:a(C.data)}])}}return A}(c(g,l.isKanjiModeEnabled())),v=function(y,A){const b={},C={start:{}};let R=["start"];for(let P=0;P<y.length;P++){const T=y[P],N=[];for(let M=0;M<T.length;M++){const B=T[M],S=""+P+M;N.push(S),b[S]={node:B,lastCount:0},C[S]={};for(let F=0;F<R.length;F++){const I=R[F];b[I]&&b[I].node.mode===B.mode?(C[I][S]=d(b[I].lastCount+B.length,B.mode)-d(b[I].lastCount,B.mode),b[I].lastCount+=B.length):(b[I]&&(b[I].lastCount=B.length),C[I][S]=d(B.length,B.mode)+4+e.getCharCountIndicator(B.mode,A))}}R=N}for(let P=0;P<R.length;P++)C[R[P]].end=0;return{map:C,table:b}}(h,p),w=f.find_path(v.map,"start","end"),E=[];for(let y=1;y<w.length-1;y++)E.push(v.table[w[y]].node);return t.fromArray(function(y){return y.reduce(function(A,b){const C=A.length-1>=0?A[A.length-1]:null;return C&&C.mode===b.mode?(A[A.length-1].data+=b.data,A):(A.push(b),A)},[])}(E))},t.rawSplit=function(g){return t.fromArray(c(g,l.isKanjiModeEnabled()))}})(jt);const it=L,gt=G,He=Ae,Ke=be,Ye=kt,Je=_t,ht=zt,pt=tt,Oe=Ie,at=Ht,je=Jt,Qe=D,mt=jt;function wt(t,e,r){const n=t.size,i=je.getEncodedBits(e,r);let s,o;for(s=0;s<15;s++)o=(i>>s&1)==1,s<6?t.set(s,8,o,!0):s<8?t.set(s+1,8,o,!0):t.set(n-15+s,8,o,!0),s<8?t.set(8,n-s-1,o,!0):s<9?t.set(8,15-s-1+1,o,!0):t.set(8,15-s-1,o,!0);t.set(n-8,8,1,!0)}function Ve(t,e,r){const n=new He;r.forEach(function(o){n.put(o.mode.bit,4),n.put(o.getLength(),Qe.getCharCountIndicator(o.mode,t)),o.write(n)});const i=8*(it.getSymbolTotalCodewords(t)-pt.getTotalCodewordsCount(t,e));for(n.getLengthInBits()+4<=i&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);const s=(i-n.getLengthInBits())/8;for(let o=0;o<s;o++)n.put(o%2?17:236,8);return function(o,l,f){const a=it.getSymbolTotalCodewords(l),u=pt.getTotalCodewordsCount(l,f),c=a-u,d=pt.getBlocksCount(l,f),m=a%d,g=d-m,p=Math.floor(a/d),h=Math.floor(c/d),v=h+1,w=p-h,E=new Oe(w);let y=0;const A=new Array(d),b=new Array(d);let C=0;const R=new Uint8Array(o.buffer);for(let B=0;B<d;B++){const S=B<g?h:v;A[B]=R.slice(y,y+S),b[B]=E.encode(A[B]),y+=S,C=Math.max(C,S)}const P=new Uint8Array(a);let T,N,M=0;for(T=0;T<C;T++)for(N=0;N<d;N++)T<A[N].length&&(P[M++]=A[N][T]);for(T=0;T<w;T++)for(N=0;N<d;N++)P[M++]=b[N][T];return P}(n,t,e)}function qe(t,e,r,n){let i;if(Array.isArray(t))i=mt.fromArray(t);else{if(typeof t!="string")throw new Error("Invalid data");{let a=e;if(!a){const u=mt.rawSplit(t);a=at.getBestVersionForData(u,r)}i=mt.fromString(t,a||40)}}const s=at.getBestVersionForData(i,r);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<s)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+s+`.
`)}else e=s;const o=Ve(e,r,i),l=it.getSymbolSize(e),f=new Ke(l);return function(a,u){const c=a.size,d=Je.getPositions(u);for(let m=0;m<d.length;m++){const g=d[m][0],p=d[m][1];for(let h=-1;h<=7;h++)if(!(g+h<=-1||c<=g+h))for(let v=-1;v<=7;v++)p+v<=-1||c<=p+v||(h>=0&&h<=6&&(v===0||v===6)||v>=0&&v<=6&&(h===0||h===6)||h>=2&&h<=4&&v>=2&&v<=4?a.set(g+h,p+v,!0,!0):a.set(g+h,p+v,!1,!0))}}(f,e),function(a){const u=a.size;for(let c=8;c<u-8;c++){const d=c%2==0;a.set(c,6,d,!0),a.set(6,c,d,!0)}}(f),function(a,u){const c=Ye.getPositions(u);for(let d=0;d<c.length;d++){const m=c[d][0],g=c[d][1];for(let p=-2;p<=2;p++)for(let h=-2;h<=2;h++)p===-2||p===2||h===-2||h===2||p===0&&h===0?a.set(m+p,g+h,!0,!0):a.set(m+p,g+h,!1,!0)}}(f,e),wt(f,r,0),e>=7&&function(a,u){const c=a.size,d=at.getEncodedBits(u);let m,g,p;for(let h=0;h<18;h++)m=Math.floor(h/3),g=h%3+c-8-3,p=(d>>h&1)==1,a.set(m,g,p,!0),a.set(g,m,p,!0)}(f,e),function(a,u){const c=a.size;let d=-1,m=c-1,g=7,p=0;for(let h=c-1;h>0;h-=2)for(h===6&&h--;;){for(let v=0;v<2;v++)if(!a.isReserved(m,h-v)){let w=!1;p<u.length&&(w=(u[p]>>>g&1)==1),a.set(m,h-v,w),g--,g===-1&&(p++,g=7)}if(m+=d,m<0||c<=m){m-=d,d=-d;break}}}(f,o),isNaN(n)&&(n=ht.getBestMask(f,wt.bind(null,f,r))),ht.applyMask(n,f),wt(f,r,n),{modules:f,version:e,errorCorrectionLevel:r,maskPattern:n,segments:i}}St.create=function(t,e){if(t===void 0||t==="")throw new Error("No input text");let r,n,i=gt.M;return e!==void 0&&(i=gt.from(e.errorCorrectionLevel,gt.M),r=at.from(e.version),n=ht.from(e.maskPattern),e.toSJISFunc&&it.setToSJISFunction(e.toSJISFunc)),qe(t,r,i,n)};var Vt={},yt={};(function(t){function e(r){if(typeof r=="number"&&(r=r.toString()),typeof r!="string")throw new Error("Color should be defined as hex string");let n=r.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+r);n.length!==3&&n.length!==4||(n=Array.prototype.concat.apply([],n.map(function(s){return[s,s]}))),n.length===6&&n.push("F","F");const i=parseInt(n.join(""),16);return{r:i>>24&255,g:i>>16&255,b:i>>8&255,a:255&i,hex:"#"+n.slice(0,6).join("")}}t.getOptions=function(r){r||(r={}),r.color||(r.color={});const n=r.margin===void 0||r.margin===null||r.margin<0?4:r.margin,i=r.width&&r.width>=21?r.width:void 0,s=r.scale||4;return{width:i,scale:i?4:s,margin:n,color:{dark:e(r.color.dark||"#000000ff"),light:e(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},t.getScale=function(r,n){return n.width&&n.width>=r+2*n.margin?n.width/(r+2*n.margin):n.scale},t.getImageWidth=function(r,n){const i=t.getScale(r,n);return Math.floor((r+2*n.margin)*i)},t.qrToImageData=function(r,n,i){const s=n.modules.size,o=n.modules.data,l=t.getScale(s,i),f=Math.floor((s+2*i.margin)*l),a=i.margin*l,u=[i.color.light,i.color.dark];for(let c=0;c<f;c++)for(let d=0;d<f;d++){let m=4*(c*f+d),g=i.color.light;c>=a&&d>=a&&c<f-a&&d<f-a&&(g=u[o[Math.floor((c-a)/l)*s+Math.floor((d-a)/l)]?1:0]),r[m++]=g.r,r[m++]=g.g,r[m++]=g.b,r[m]=g.a}}})(yt),function(t){const e=yt;t.render=function(r,n,i){let s=i,o=n;s!==void 0||n&&n.getContext||(s=n,n=void 0),n||(o=function(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}()),s=e.getOptions(s);const l=e.getImageWidth(r.modules.size,s),f=o.getContext("2d"),a=f.createImageData(l,l);return e.qrToImageData(a.data,r,s),function(u,c,d){u.clearRect(0,0,c.width,c.height),c.style||(c.style={}),c.height=d,c.width=d,c.style.height=d+"px",c.style.width=d+"px"}(f,o,l),f.putImageData(a,0,0),o},t.renderToDataURL=function(r,n,i){let s=i;s!==void 0||n&&n.getContext||(s=n,n=void 0),s||(s={});const o=t.render(r,n,s),l=s.type||"image/png",f=s.rendererOpts||{};return o.toDataURL(l,f.quality)}}(Vt);var qt={};const $e=yt;function $t(t,e){const r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function Et(t,e,r){let n=t+e;return r!==void 0&&(n+=" "+r),n}qt.render=function(t,e,r){const n=$e.getOptions(e),i=t.modules.size,s=t.modules.data,o=i+2*n.margin,l=n.color.light.a?"<path "+$t(n.color.light,"fill")+' d="M0 0h'+o+"v"+o+'H0z"/>':"",f="<path "+$t(n.color.dark,"stroke")+' d="'+function(c,d,m){let g="",p=0,h=!1,v=0;for(let w=0;w<c.length;w++){const E=Math.floor(w%d),y=Math.floor(w/d);E||h||(h=!0),c[w]?(v++,w>0&&E>0&&c[w-1]||(g+=h?Et("M",E+m,.5+y+m):Et("m",p,0),p=0,h=!1),E+1<d&&c[w+1]||(g+=Et("h",v),v=0)):p++}return g}(s,i,n.margin)+'"/>',a='viewBox="0 0 '+o+" "+o+'"',u='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+a+' shape-rendering="crispEdges">'+l+f+`</svg>
`;return typeof r=="function"&&r(null,u),u};const Ze=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},vt=St,Zt=Vt,Xe=qt;function Ct(t,e,r,n,i){const s=[].slice.call(arguments,1),o=s.length,l=typeof s[o-1]=="function";if(!l&&!Ze())throw new Error("Callback required as last argument");if(!l){if(o<1)throw new Error("Too few arguments provided");return o===1?(r=e,e=n=void 0):o!==2||e.getContext||(n=r,r=e,e=void 0),new Promise(function(f,a){try{const u=vt.create(r,n);f(t(u,e,n))}catch(u){a(u)}})}if(o<2)throw new Error("Too few arguments provided");o===2?(i=r,r=e,e=n=void 0):o===3&&(e.getContext&&i===void 0?(i=n,n=void 0):(i=n,n=r,r=e,e=void 0));try{const f=vt.create(r,n);i(null,t(f,e,n))}catch(f){i(f)}}Q.create=vt.create,Q.toCanvas=Ct.bind(null,Zt.render),Q.toDataURL=Ct.bind(null,Zt.renderToDataURL),Q.toString=Ct.bind(null,function(t,e,r){return Xe.render(t,r)});const We={class:"absolute left-[50%] top-[50%] font-bold"},Ge=ue(It({name:"Qrcode",__name:"Qrcode",props:{tag:X.string.validate(t=>["canvas","img"].includes(t)).def("canvas"),text:{type:[String,Array],default:null},options:{type:Object,default:()=>({})},width:X.number.def(200),logo:{type:[String,Object],default:""},disabled:X.bool.def(!1),disabledText:X.string.def("")},emits:["done","click","disabled-click"],setup(t,{emit:e}){const r=t,n=e,{getPrefixCls:i}=re(),s=i("qrcode"),{toCanvas:o,toDataURL:l}=Q,f=Tt(!0),a=Tt(null),u=st(()=>String(r.text)),c=st(()=>({width:r.width+"px",height:r.width+"px"}));Gt(()=>u.value,w=>{w&&(async()=>{await ae();const E=se(r.options||{});if(r.tag==="canvas"){E.errorCorrectionLevel=E.errorCorrectionLevel||g(x(u));const y=await m(x(u),E);E.scale=r.width===0?void 0:r.width/y*4;const A=await o(x(a),x(u),E);if(r.logo){const b=await d(A);n("done",b),f.value=!1}else n("done",A.toDataURL()),f.value=!1}else{const y=await l(u.value,{errorCorrectionLevel:"H",width:r.width,...E});x(a).src=y,n("done",y),f.value=!1}})()},{deep:!0,immediate:!0});const d=w=>{const E=w.width,y=Object.assign({logoSize:.15,bgColor:"#ffffff",borderSize:.05,crossOrigin:"anonymous",borderRadius:8,logoRadius:0},Lt(r.logo)?{}:r.logo),{logoSize:A=.15,bgColor:b="#ffffff",borderSize:C=.05,crossOrigin:R="anonymous",borderRadius:P=8,logoRadius:T=0}=y,N=Lt(r.logo)?r.logo:r.logo.src,M=E*A,B=E*(1-A)/2,S=E*(A+C),F=E*(1-A-C)/2,I=w.getContext("2d");if(!I)return;p(I)(F,F,S,S,P),I.fillStyle=b,I.fill();const j=new Image;return(R||T)&&j.setAttribute("crossOrigin",R),j.src=N,new Promise(Xt=>{j.onload=()=>{var At;T?(Wt=>{const Z=document.createElement("canvas");Z.width=B+M,Z.height=B+M;const bt=Z.getContext("2d");if(!bt||!I||(bt.drawImage(Wt,B,B,M,M),p(I)(B,B,M,M,T),!I))return;const Bt=I.createPattern(Z,"no-repeat");Bt&&(I.fillStyle=Bt,I.fill())})(j):(At=j,I.drawImage(At,B,B,M,M)),Xt(w.toDataURL())}})},m=async(w,E)=>{const y=document.createElement("canvas");return await o(y,w,E),y.width},g=w=>w.length>36?"M":w.length>16?"Q":"H",p=w=>(E,y,A,b,C)=>{const R=Math.min(A,b);return C>R/2&&(C=R/2),w.beginPath(),w.moveTo(E+C,y),w.arcTo(E+A,y,E+A,y+b,C),w.arcTo(E+A,y+b,E,y+b,C),w.arcTo(E,y+b,E,y,C),w.arcTo(E,y,E+A,y,C),w.closePath(),w},h=()=>{n("click")},v=()=>{n("disabled-click")};return(w,E)=>{const y=ie,A=oe;return Mt((W(),Pt("div",{class:Rt([x(s),"relative inline-block"]),style:ne(c.value)},[(W(),Nt(te(t.tag),{ref_key:"wrapRef",ref:a,onClick:h},null,512)),t.disabled?(W(),Pt("div",{key:0,class:Rt([`${x(s)}--disabled`,"absolute left-0 top-0 h-full w-full flex items-center justify-center"]),onClick:v},[ut("div",We,[_(y,{size:30,color:"var(--el-color-primary)",icon:"ep:refresh-right"}),ut("div",null,xt(t.disabledText),1)])],2)):ee("",!0)],6)),[[A,f.value]])}}}),[["__scopeId","data-v-8fc6cf2d"]]),tn={class:"mt-15px w-[100%]"},en=It({name:"QrCodeForm",__name:"QrCodeForm",setup(t){const{t:e}=fe(),{handleBackLogin:r,getLoginState:n}=ye(),i=st(()=>x(n)===ve.QR_CODE);return(s,o)=>{const l=de,f=Ge,a=me,u=ge,c=pe,d=he;return Mt((W(),Nt(d,{class:"login-form",style:{"margin-right":"-10px","margin-left":"-10px"}},{default:H(()=>[_(l,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:H(()=>[_(Ee,{style:{width:"100%"}})]),_:1}),_(l,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:H(()=>[_(a,{class:"mb-10px text-center",shadow:"hover"},{default:H(()=>[_(f,{logo:x(we)},null,8,["logo"])]),_:1})]),_:1}),_(u,{class:"enter-x"},{default:H(()=>[le(xt(x(e)("login.qrcode")),1)]),_:1}),_(l,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:H(()=>[ut("div",tn,[_(c,{title:x(e)("login.backLogin"),class:"w-[100%]",onClick:o[0]||(o[0]=m=>x(r)())},null,8,["title"])])]),_:1})]),_:1},512)),[[ce,x(i)]])}}});export{en as _};
