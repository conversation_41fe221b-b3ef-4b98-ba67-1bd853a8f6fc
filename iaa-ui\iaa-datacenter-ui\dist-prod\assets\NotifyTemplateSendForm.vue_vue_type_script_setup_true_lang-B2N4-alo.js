import{d as D,y as E,j as i,r as H,o as d,h as c,w as u,k as s,u as l,v,x as y,c as V,F as _,g as b,N as M,D as R,t as z,Z as T,m as B,aH as L,S as Y,B as Z,ap as G,aq as J,z as K,A as O,I as Q,H as W,L as X}from"./index-C8b06LRn.js";import{_ as $}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{g as ee,s as ae}from"./index-D1CnAzxI.js";const le=D({name:"SystemNotifyTemplateSendForm",__name:"NotifyTemplateSendForm",setup(te,{expose:w}){const P=E(),p=i(!1),n=i(!1),t=i({content:"",params:{},userId:void 0,userType:1,templateCode:"",templateParams:new Map}),g=H({userId:[{required:!0,message:"\u7528\u6237\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],templateCode:[{required:!0,message:"\u6A21\u7248\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],templateParams:{}}),f=i(),h=i([]);w({open:async m=>{p.value=!0,U(),n.value=!0;try{const e=await ee(m);t.value.content=e.content,t.value.params=e.params,t.value.templateCode=e.code,t.value.templateParams=e.params.reduce((o,r)=>(o[r]="",o),{}),g.templateParams=e.params.reduce((o,r)=>(o[r]={required:!0,message:"\u53C2\u6570 "+r+" \u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},o),{})}finally{n.value=!1}h.value=await L()}});const k=async()=>{if(f&&await f.value.validate()){n.value=!0;try{const m=t.value,e=await ae(m);e&&P.success("\u63D0\u4EA4\u53D1\u9001\u6210\u529F\uFF01\u53D1\u9001\u7ED3\u679C\uFF0C\u89C1\u53D1\u9001\u65E5\u5FD7\u7F16\u53F7\uFF1A"+e),p.value=!1}finally{n.value=!1}}},U=()=>{var m;t.value={content:"",params:{},mobile:"",templateCode:"",templateParams:new Map,userType:1},(m=f.value)==null||m.resetFields()};return(m,e)=>{const o=Y,r=Z,x=G,C=J,S=K,q=O,F=Q,I=W,N=$,j=X;return d(),c(N,{modelValue:l(p),"onUpdate:modelValue":e[5]||(e[5]=a=>B(p)?p.value=a:null),title:"\u6D4B\u8BD5\u53D1\u9001","max-height":500},{footer:u(()=>[s(I,{disabled:l(n),type:"primary",onClick:k},{default:u(()=>e[6]||(e[6]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),s(I,{onClick:e[4]||(e[4]=a=>p.value=!1)},{default:u(()=>e[7]||(e[7]=[v("\u53D6 \u6D88")])),_:1})]),default:u(()=>[y((d(),c(F,{ref_key:"formRef",ref:f,model:l(t),rules:l(g),"label-width":"140px"},{default:u(()=>[s(r,{label:"\u6A21\u677F\u5185\u5BB9",prop:"content"},{default:u(()=>[s(o,{modelValue:l(t).content,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).content=a),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u5185\u5BB9",readonly:"",type:"textarea"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:u(()=>[s(C,{modelValue:l(t).userType,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).userType=a)},{default:u(()=>[(d(!0),V(_,null,b(l(M)(l(R).USER_TYPE),a=>(d(),c(x,{key:a.value,value:a.value},{default:u(()=>[v(z(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),y(s(r,{label:"\u63A5\u6536\u4EBAID",prop:"userId"},{default:u(()=>[s(o,{modelValue:l(t).userId,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).userId=a),style:{width:"160px"}},null,8,["modelValue"])]),_:1},512),[[T,l(t).userType===1]]),y(s(r,{label:"\u63A5\u6536\u4EBA",prop:"userId"},{default:u(()=>[s(q,{modelValue:l(t).userId,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).userId=a),placeholder:"\u8BF7\u9009\u62E9\u63A5\u6536\u4EBA"},{default:u(()=>[(d(!0),V(_,null,b(l(h),a=>(d(),c(S,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},512),[[T,l(t).userType===2]]),(d(!0),V(_,null,b(l(t).params,a=>(d(),c(r,{key:a,label:"\u53C2\u6570 {"+a+"}",prop:"templateParams."+a},{default:u(()=>[s(o,{modelValue:l(t).templateParams[a],"onUpdate:modelValue":A=>l(t).templateParams[a]=A,placeholder:"\u8BF7\u8F93\u5165 "+a+" \u53C2\u6570"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])),[[j,l(n)]])]),_:1},8,["modelValue"])}}});export{le as _};
