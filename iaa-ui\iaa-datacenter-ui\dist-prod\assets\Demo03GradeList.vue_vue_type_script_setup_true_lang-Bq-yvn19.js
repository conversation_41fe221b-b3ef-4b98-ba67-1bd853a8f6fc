import{d as U,p as j,y as D,j as d,r as F,au as H,T as J,o as i,c as K,k as a,w as r,x as u,h as m,v as k,u as o,F as R,G as q,H as A,J as B,K as E,L as M}from"./index-C8b06LRn.js";import{_ as O}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as Q}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as V}from"./formatTime-COZ9Bl52.js";import{k as W,l as X}from"./index-D7MUd3Bn.js";import{_ as Y}from"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-mXApwxlE.js";const Z=U({__name:"Demo03GradeList",props:{studentId:{}},setup(I){const{t:S}=j(),c=D(),f=I,g=d(!1),w=d([]),v=d(0),l=F({pageNo:1,pageSize:10,studentId:void 0});H(()=>f.studentId,t=>{t&&(l.studentId=t,b())},{immediate:!0,deep:!0});const p=async()=>{g.value=!0;try{const t=await W(l);w.value=t.list,v.value=t.total}finally{g.value=!1}},b=()=>{l.pageNo=1,p()},h=d(),C=(t,e)=>{f.studentId?h.value.open(t,e,f.studentId):c.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const x=q,_=A,n=B,z=E,N=Q,P=O,y=J("hasPermi"),G=M;return i(),K(R,null,[a(P,null,{default:r(()=>[u((i(),m(_,{plain:"",type:"primary",onClick:e[0]||(e[0]=s=>C("create"))},{default:r(()=>[a(x,{class:"mr-5px",icon:"ep:plus"}),e[3]||(e[3]=k(" \u65B0\u589E "))]),_:1})),[[y,["infra:demo03-student:create"]]]),u((i(),m(z,{data:o(w),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[a(n,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(n,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(n,{align:"center",label:"\u73ED\u4E3B\u4EFB",prop:"teacher"}),a(n,{formatter:o(V),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(n,{align:"center",label:"\u64CD\u4F5C"},{default:r(s=>[u((i(),m(_,{link:"",type:"primary",onClick:L=>C("update",s.row.id)},{default:r(()=>e[4]||(e[4]=[k(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:update"]]]),u((i(),m(_,{link:"",type:"danger",onClick:L=>(async T=>{try{await c.delConfirm(),await X(T),c.success(S("common.delSuccess")),await p()}catch{}})(s.row.id)},{default:r(()=>e[5]||(e[5]=[k(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[G,o(g)]]),a(N,{limit:o(l).pageSize,"onUpdate:limit":e[1]||(e[1]=s=>o(l).pageSize=s),page:o(l).pageNo,"onUpdate:page":e[2]||(e[2]=s=>o(l).pageNo=s),total:o(v),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(Y,{ref_key:"formRef",ref:h,onSuccess:p},null,512)],64)}}});export{Z as _};
