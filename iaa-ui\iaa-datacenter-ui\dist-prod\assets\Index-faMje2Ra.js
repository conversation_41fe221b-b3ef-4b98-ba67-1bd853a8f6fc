import{_ as y}from"./welcome-card.vue_vue_type_script_setup_true_lang-Dv32opBN.js";import _ from"./shortcut-card-4fLxUVtx.js";import{d as b,b as N,e as v,r as p,f as x,o as i,c as m,F as C,g as w,h as L,i as E,n as I,u as S,_ as F}from"./index-C8b06LRn.js";import"./el-avatar-BVm8aVjJ.js";import"./avatar-CPqUN878.js";import"./CardTitle-00NfZwLk.js";const R=F(b({__name:"Index",setup(k){const u=N(),{mobile:f}=v(),h=p(["Redirect","Home","UserInfo","dict","CodegenEdit","JobL","Login","SSOLogin","SocialLogin","NoAccess","NoFound","Error","bpm","/system","/infra","/report"]);function d(s,a="",t=0,r=0,e=[]){return s.forEach(o=>{var n;o.children&&o.children.length>0?d(o.children,(n=o==null?void 0:o.meta)==null?void 0:n.title,t+1,r,e):t>=r&&(o.meta.parentName=a,e.push(o))}),e}const g={welcomeCard:y,shortcutCard:_},c=p([{x:9,y:0,w:3,h:3,i:"0",componentName:"welcomeCard",data:void 0}]);return x(()=>{var r;let s=u.getRouters.filter(e=>!h.includes(e.name)&&e.name);const a=(r=d(s))==null?void 0:r.reduce((e,o)=>{var l;const n=((l=o.meta)==null?void 0:l.parentName)||"\u672A\u5206\u7EC4";return e[n]||(e[n]=[]),e[n].push(o),e},{});let t=0;for(let e in a)c.push({x:0,y:5*t++,w:9,h:5,i:t+"",componentName:"shortcutCard",static:!0,data:a[e],name:e})}),(s,a)=>(i(),m("div",{class:I(S(f)?"grid-layout-mobile":"grid-layout")},[(i(!0),m(C,null,w(c,t=>(i(),m("div",{key:t.i,class:"bg-#fff p-10px layout-item"},[(i(),L(E(g[t.componentName]),{data:t==null?void 0:t.data,name:t.name},null,8,["data","name"]))]))),128))],2))}}),[["__scopeId","data-v-8d138502"]]);export{R as default};
