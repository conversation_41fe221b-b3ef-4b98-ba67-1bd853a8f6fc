import{bb as Z,au as H,aO as E,cp as L,b0 as G,A as _,bF as V,Y as x,d as q,z as J,aE as ee,bV as te,cq as R,b_ as le,bY as ae,cr as se,cs as oe,av as de,ct as ce,bs as re,j as P,r as Y,f as ne,bv as B,bg as ue,bh as ie}from"./index-C8b06LRn.js";const he=q({extends:J,setup(a,n){const u=J.setup(a,n);delete u.selectOptionClick;const v=ee().proxy;return E(()=>{u.select.states.cachedOptions.get(v.value)||u.select.onOptionCreate(v)}),H(()=>n.attrs.visible,r=>{u.states.visible=r},{immediate:!0}),u},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function F(a){return a||a===0}function I(a){return Array.isArray(a)&&a.length}function O(a){return Array.isArray(a)?a:F(a)?[a]:[]}function z(a,n,u,v,r){for(let i=0;i<a.length;i++){const f=a[i];if(n(f,i,a,r))return v?v(f,i,a,r):f;{const A=u(f);if(I(A)){const M=z(A,n,u,v,f);if(M)return M}}}}function D(a,n,u,v){for(let r=0;r<a.length;r++){const i=a[r];n(i,r,a,v);const f=u(i);I(f)&&D(f,n,u,i)}}var pe=q({props:{data:{type:Array,default:()=>[]}},setup(a){const n=de(ce);return H(()=>a.data,()=>{var u;a.data.forEach(r=>{n.states.cachedOptions.has(r.value)||n.states.cachedOptions.set(r.value,r)});const v=((u=n.selectRef)==null?void 0:u.querySelectorAll("input"))||[];re&&!Array.from(v).includes(document.activeElement)&&n.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const ve=q({name:"ElTreeSelect",inheritAttrs:!1,props:{..._.props,...R.props,cacheData:{type:Array,default:()=>[]}},setup(a,n){const{slots:u,expose:v}=n,r=P(),i=P(),f=x(()=>a.nodeKey||a.valueKey||"value"),A=((e,{attrs:C,emit:S},{select:g,tree:y,key:h})=>{const j=Z("tree-select");return H(()=>e.data,()=>{e.filterable&&E(()=>{var p,s;(s=y.value)==null||s.filter((p=g.value)==null?void 0:p.states.inputValue)})},{flush:"post"}),{...L(G(e),Object.keys(_.props)),...C,"onUpdate:modelValue":p=>S(V,p),valueKey:h,popperClass:x(()=>{const p=[j.e("popper")];return e.popperClass&&p.push(e.popperClass),p.join(" ")}),filterMethod:(p="")=>{var s;e.filterMethod?e.filterMethod(p):e.remoteMethod?e.remoteMethod(p):(s=y.value)==null||s.filter(p)}}})(a,n,{select:r,tree:i,key:f}),{cacheOptions:M,...Q}=((e,{attrs:C,slots:S,emit:g},{select:y,tree:h,key:j})=>{H(()=>e.modelValue,()=>{e.showCheckbox&&E(()=>{const t=h.value;t&&!te(t.getCheckedKeys(),O(e.modelValue))&&t.setCheckedKeys(O(e.modelValue))})},{immediate:!0,deep:!0});const p=x(()=>({value:j.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props})),s=(t,l)=>{var o;const c=p.value[t];return ae(c)?c(l,(o=h.value)==null?void 0:o.getNode(s("value",l))):l[c]},U=O(e.modelValue).map(t=>z(e.data||[],l=>s("value",l)===t,l=>s("children",l),(l,o,c,k)=>k&&s("value",k))).filter(t=>F(t)),W=x(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const t=[];return D(e.data.concat(e.cacheData),l=>{const o=s("value",l);t.push({value:o,currentLabel:s("label",l),isDisabled:s("disabled",l)})},l=>s("children",l)),t}),$=()=>{var t;return(t=h.value)==null?void 0:t.getCheckedKeys().filter(l=>{var o;const c=(o=h.value)==null?void 0:o.getNode(l);return!se(c)&&oe(c.childNodes)})};return{...L(G(e),Object.keys(R.props)),...C,nodeKey:j,expandOnClickNode:x(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:x(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(U):U),renderContent:(t,{node:l,data:o,store:c})=>t(he,{value:s("value",o),label:s("label",o),disabled:s("disabled",o),visible:l.visible},e.renderContent?()=>e.renderContent(t,{node:l,data:o,store:c}):S.default?()=>S.default({node:l,data:o,store:c}):void 0),filterNodeMethod:(t,l,o)=>e.filterNodeMethod?e.filterNodeMethod(t,l,o):!t||new RegExp(le(t),"i").test(s("label",l)||""),onNodeClick:(t,l,o)=>{var c,k,m,N;if((c=C.onNodeClick)==null||c.call(C,t,l,o),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!l.isLeaf)e.expandOnClickNode&&o.proxy.handleExpandIconClick();else if(!s("disabled",t)){const K=(k=y.value)==null?void 0:k.states.options.get(s("value",t));(m=y.value)==null||m.handleOptionSelect(K)}(N=y.value)==null||N.focus()}},onCheck:(t,l)=>{var o;if(!e.showCheckbox)return;const c=s("value",t),k={};D([h.value.store.root],d=>k[d.key]=d,d=>d.childNodes);const m=l.checkedKeys,N=e.multiple?O(e.modelValue).filter(d=>!(d in k)&&!m.includes(d)):[],K=N.concat(m);if(e.checkStrictly)g(V,e.multiple?K:K.includes(c)?c:void 0);else if(e.multiple){const d=$();g(V,N.concat(d))}else{const d=z([t],b=>!I(s("children",b))&&!s("disabled",b),b=>s("children",b)),w=d?s("value",d):void 0,X=F(e.modelValue)&&!!z([t],b=>s("value",b)===e.modelValue,b=>s("children",b));g(V,w===e.modelValue||X?void 0:w)}E(()=>{var d;const w=O(e.modelValue);h.value.setCheckedKeys(w),(d=C.onCheck)==null||d.call(C,t,{checkedKeys:h.value.getCheckedKeys(),checkedNodes:h.value.getCheckedNodes(),halfCheckedKeys:h.value.getHalfCheckedKeys(),halfCheckedNodes:h.value.getHalfCheckedNodes()})}),(o=y.value)==null||o.focus()},onNodeExpand:(t,l,o)=>{var c;(c=C.onNodeExpand)==null||c.call(C,t,l,o),E(()=>{if(!e.checkStrictly&&e.lazy&&e.multiple&&l.checked){const k={},m=h.value.getCheckedKeys();D([h.value.store.root],d=>k[d.key]=d,d=>d.childNodes);const N=O(e.modelValue).filter(d=>!(d in k)&&!m.includes(d)),K=$();g(V,N.concat(K))}})},cacheOptions:W}})(a,n,{select:r,tree:i,key:f}),T=Y({});return v(T),ne(()=>{Object.assign(T,{...L(i.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...L(r.value,["focus","blur"])})}),()=>B(_,Y({...A,ref:e=>r.value=e}),{...u,default:()=>[B(pe,{data:M.value}),B(R,Y({...Q,ref:e=>i.value=e}))]})}}),fe=ie(ue(ve,[["__file","tree-select.vue"]]));export{fe as E};
