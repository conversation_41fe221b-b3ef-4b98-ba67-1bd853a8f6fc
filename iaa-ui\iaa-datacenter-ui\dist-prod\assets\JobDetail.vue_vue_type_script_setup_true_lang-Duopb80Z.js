import{_ as D}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as E,j as n,o as f,h as v,w as a,k as l,v as u,t as r,u as e,D as I,c as J,F as g,g as C,m as V}from"./index-C8b06LRn.js";import{E as j,a as k}from"./el-descriptions-item-Ctb8GMnZ.js";import{E as A,a as F}from"./el-timeline-item-BqzIH3Db.js";import{_ as N}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as S}from"./formatTime-COZ9Bl52.js";import{g as U,a as B}from"./index-DOCM6L63.js";const O=E({name:"InfraJobDetail",__name:"JobDetail",setup(P,{expose:y}){const s=n(!1),i=n(!1),t=n({}),_=n([]);return y({open:async m=>{if(s.value=!0,m){i.value=!0;try{t.value=await U(m),_.value=await B(m)}finally{i.value=!1}}}}),(m,p)=>{const o=j,c=N,h=A,w=F,T=k,x=D;return f(),v(x,{modelValue:e(s),"onUpdate:modelValue":p[0]||(p[0]=d=>V(s)?s.value=d:null),title:"\u4EFB\u52A1\u8BE6\u7EC6",width:"700px"},{default:a(()=>[l(T,{column:1,border:""},{default:a(()=>[l(o,{label:"\u4EFB\u52A1\u7F16\u53F7","min-width":"60"},{default:a(()=>[u(r(e(t).id),1)]),_:1}),l(o,{label:"\u4EFB\u52A1\u540D\u79F0"},{default:a(()=>[u(r(e(t).name),1)]),_:1}),l(o,{label:"\u4EFB\u52A1\u540D\u79F0"},{default:a(()=>[l(c,{type:e(I).INFRA_JOB_STATUS,value:e(t).status},null,8,["type","value"])]),_:1}),l(o,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57"},{default:a(()=>[u(r(e(t).handlerName),1)]),_:1}),l(o,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570"},{default:a(()=>[u(r(e(t).handlerParam),1)]),_:1}),l(o,{label:"Cron \u8868\u8FBE\u5F0F"},{default:a(()=>[u(r(e(t).cronExpression),1)]),_:1}),l(o,{label:"\u91CD\u8BD5\u6B21\u6570"},{default:a(()=>[u(r(e(t).retryCount),1)]),_:1}),l(o,{label:"\u91CD\u8BD5\u95F4\u9694"},{default:a(()=>[u(r(e(t).retryInterval+" \u6BEB\u79D2"),1)]),_:1}),l(o,{label:"\u76D1\u63A7\u8D85\u65F6\u65F6\u95F4"},{default:a(()=>[u(r(e(t).monitorTimeout>0?e(t).monitorTimeout+" \u6BEB\u79D2":"\u672A\u5F00\u542F"),1)]),_:1}),l(o,{label:"\u540E\u7EED\u6267\u884C\u65F6\u95F4"},{default:a(()=>[l(w,null,{default:a(()=>[(f(!0),J(g,null,C(e(_),(d,b)=>(f(),v(h,{key:b,timestamp:e(S)(d)},{default:a(()=>[u(" \u7B2C "+r(b+1)+" \u6B21 ",1)]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{O as _};
