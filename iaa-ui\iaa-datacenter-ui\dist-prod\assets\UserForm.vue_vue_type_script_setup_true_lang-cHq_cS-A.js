import{d as M,p as T,y as X,j as m,r as Y,o as p,h as f,w as a,k as e,u as l,v as x,x as $,aA as G,$ as E,c as S,F as q,g as A,N as K,D as O,m as P,ep as Q,aJ as W,aI as Z,eq as ee,er as le,S as ae,B as se,a8 as de,ab as ue,z as oe,A as te,I as re,H as me,L as pe}from"./index-C8b06LRn.js";import{_ as ne}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as ie}from"./el-tree-select-E9FCZb0j.js";import{C as F}from"./constants-D3f7Z3TX.js";import{g as ce}from"./index-Dz9lR_me.js";const fe=M({name:"SystemUserForm",__name:"UserForm",emits:["success"],setup(_e,{expose:N,emit:B}){const{t:V}=T(),b=X(),n=m(!1),k=m(""),i=m(!1),y=m(""),u=m({nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:F.ENABLE,roleIds:[]}),C=Y({username:[{required:!0,message:"\u7528\u6237\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],nickname:[{required:!0,message:"\u7528\u6237\u6635\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],password:[{required:!0,message:"\u7528\u6237\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],mobile:[{pattern:/^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}]}),v=m(),g=m([]),w=m([]);N({open:async(o,s)=>{if(n.value=!0,k.value=V("action."+o),y.value=o,D(),s){i.value=!0;try{u.value=await Q(s)}finally{i.value=!1}}g.value=W(await Z()),w.value=await ce()}});const L=B,R=async()=>{if(v&&await v.value.validate()){i.value=!0;try{const o=u.value;y.value==="create"?(await ee(o),b.success(V("common.createSuccess"))):(await le(o),b.success(V("common.updateSuccess"))),n.value=!1,L("success")}finally{i.value=!1}}},D=()=>{var o;u.value={nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:F.ENABLE,roleIds:[]},(o=v.value)==null||o.resetFields()};return(o,s)=>{const c=ae,t=se,r=de,j=ie,_=ue,h=oe,I=te,z=re,U=me,H=ne,J=pe;return p(),f(H,{modelValue:l(n),"onUpdate:modelValue":s[10]||(s[10]=d=>P(n)?n.value=d:null),title:l(k)},{footer:a(()=>[e(U,{disabled:l(i),type:"primary",onClick:R},{default:a(()=>s[11]||(s[11]=[x("\u786E \u5B9A")])),_:1},8,["disabled"]),e(U,{onClick:s[9]||(s[9]=d=>n.value=!1)},{default:a(()=>s[12]||(s[12]=[x("\u53D6 \u6D88")])),_:1})]),default:a(()=>[$((p(),f(z,{ref_key:"formRef",ref:v,model:l(u),rules:l(C),"label-width":"80px"},{default:a(()=>[e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:a(()=>[e(c,{modelValue:l(u).nickname,"onUpdate:modelValue":s[0]||(s[0]=d=>l(u).nickname=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:a(()=>[e(j,{modelValue:l(u).deptId,"onUpdate:modelValue":s[1]||(s[1]=d=>l(u).deptId=d),data:l(g),props:l(G),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:a(()=>[e(c,{modelValue:l(u).mobile,"onUpdate:modelValue":s[2]||(s[2]=d=>l(u).mobile=d),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u90AE\u7BB1",prop:"email"},{default:a(()=>[e(c,{modelValue:l(u).email,"onUpdate:modelValue":s[3]||(s[3]=d=>l(u).email=d),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),f(t,{key:0,label:"\u7528\u6237\u540D\u79F0",prop:"username"},{default:a(()=>[e(c,{modelValue:l(u).username,"onUpdate:modelValue":s[4]||(s[4]=d=>l(u).username=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0"},null,8,["modelValue"])]),_:1})):E("",!0)]),_:1}),e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),f(t,{key:0,label:"\u7528\u6237\u5BC6\u7801",prop:"password"},{default:a(()=>[e(c,{modelValue:l(u).password,"onUpdate:modelValue":s[5]||(s[5]=d=>l(u).password=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u5BC6\u7801","show-password":"",type:"password"},null,8,["modelValue"])]),_:1})):E("",!0)]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6027\u522B"},{default:a(()=>[e(I,{modelValue:l(u).sex,"onUpdate:modelValue":s[6]||(s[6]=d=>l(u).sex=d),placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),S(q,null,A(l(K)(l(O).SYSTEM_USER_SEX),d=>(p(),f(h,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5C97\u4F4D"},{default:a(()=>[e(I,{modelValue:l(u).postIds,"onUpdate:modelValue":s[7]||(s[7]=d=>l(u).postIds=d),multiple:"",placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),S(q,null,A(l(w),d=>(p(),f(h,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:24},{default:a(()=>[e(t,{label:"\u5907\u6CE8"},{default:a(()=>[e(c,{modelValue:l(u).remark,"onUpdate:modelValue":s[8]||(s[8]=d=>l(u).remark=d),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[J,l(i)]])]),_:1},8,["modelValue","title"])}}});export{fe as _};
