import{b6 as i,ba as n,d as l,bb as c,o,c as r,n as t,u as s,bf as d,v as y,t as f,$ as b,l as h,aK as v,bg as u,bh as p}from"./index-C8b06LRn.js";const S=i({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:n([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),$=l({name:"ElCard"}),g=p(u(l({...$,props:S,setup(w){const e=c("card");return(a,m)=>(o(),r("div",{class:t([s(e).b(),s(e).is(`${a.shadow}-shadow`)])},[a.$slots.header||a.header?(o(),r("div",{key:0,class:t(s(e).e("header"))},[d(a.$slots,"header",{},()=>[y(f(a.header),1)])],2)):b("v-if",!0),h("div",{class:t([s(e).e("body"),a.bodyClass]),style:v(a.bodyStyle)},[d(a.$slots,"default")],6),a.$slots.footer||a.footer?(o(),r("div",{key:1,class:t(s(e).e("footer"))},[d(a.$slots,"footer",{},()=>[y(f(a.footer),1)])],2)):b("v-if",!0)],2))}}),[["__file","card.vue"]]));export{g as E};
