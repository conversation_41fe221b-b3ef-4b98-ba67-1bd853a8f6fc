import{aG as t}from"./index-C8b06LRn.js";const s=async a=>await t.post({url:"/bpm/form/create",data:a}),r=async a=>await t.put({url:"/bpm/form/update",data:a}),e=async a=>await t.delete({url:"/bpm/form/delete?id="+a}),m=async a=>await t.get({url:"/bpm/form/get?id="+a}),p=async a=>await t.get({url:"/bpm/form/page",params:a}),i=async()=>await t.get({url:"/bpm/form/simple-list"});export{i as a,p as b,s as c,e as d,m as g,r as u};
