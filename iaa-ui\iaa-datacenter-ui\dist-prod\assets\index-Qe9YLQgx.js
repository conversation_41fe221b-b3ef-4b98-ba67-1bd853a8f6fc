import{d as le,O as te,a as se,y as oe,j as r,Y as re,f as ie,aF as ue,o as i,c as _,k as o,u as a,x as me,h as y,w as l,m as D,F as g,g as w,l as F,v as P,t as G,aH as ne,dq as pe,a8 as de,ab as fe,E as ce,q as ve,G as ye,H as be,z as _e,A as ge,B as he,I as ke,L as xe}from"./index-C8b06LRn.js";import{_ as we}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{E as Ve}from"./el-card-CaOo8U9P.js";import{E as Ce}from"./el-image-Dy1AcCSg.js";import{_ as Ue}from"./index-CkzUfjB7.js";import{b as Se,a as Ie}from"./index-DDzAA47d.js";import{g as qe,a as Fe}from"./index-czoBMNFJ.js";import{b as Pe}from"./formCreate-CdPDb26P.js";import{_ as ze}from"./ProcessInstanceBpmnViewer.vue_vue_type_style_index_0_lang-CysqzuVc.js";import{C as Ae}from"./index-uSeXqrUH.js";import{u as Be}from"./tagsView-D-HCnpxr.js";import"./bpmn-embedded-DWy7HXvQ.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./XTextButton-BSf0iZhI.js";import"./XButton-BOgar_Ex.js";import"./el-collapse-item-JANV_ocl.js";import"./index-CBsqkafF.js";import"./el-tree-select-E9FCZb0j.js";import"./index-C0LhU1J1.js";import"./index-Dz9lR_me.js";import"./index-Cgv48ZKs.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./constants-D3f7Z3TX.js";import"./index-SMELiJYy.js";import"./el-drawer-C5TFtzfV.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./index-B9cOKMOY.js";import"./formatTime-COZ9Bl52.js";const Ee={class:"flex"},He={class:"clearfix"},Le={class:"el-icon-document"},De=le({name:"BpmProcessInstanceCreate",__name:"index",setup(Ge){const R=te(),{push:z,currentRoute:T}=se(),V=oe(),{delView:X}=Be(),h=R.query.processInstanceId,C=r(!0),k=r([]),b=r(""),U=r([]),Y=re(()=>U.value.filter(t=>t.category==b.value)),n=r(),p=r({rule:[],option:{},value:{}}),d=r(),A=r(null),f=r([]),c=r({}),B=r(),S=r({}),E=r([]),H=async(t,e)=>{var v;if(d.value=t,f.value=[],c.value={},S.value={},t.formType==10){Pe(p,t.formConf,t.formFields,e);const m=await Ie(t.id);if(m&&(A.value=m.bpmnXml,f.value=m.startUserSelectTasks,((v=f.value)==null?void 0:v.length)>0)){p.value.rule.push({type:"startUserSelect",props:{title:"\u6307\u5B9A\u5BA1\u6279\u4EBA"}});for(const x of f.value)c.value[x.id]=[],S.value[x.id]=[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA",trigger:"blur"}];E.value=await ne()}}else t.formCustomCreatePath&&await z({path:t.formCustomCreatePath})},Z=async t=>{var e;if(n.value&&d.value){((e=f.value)==null?void 0:e.length)>0&&await B.value.validate(),n.value.btn.loading(!0);try{await Fe({processDefinitionId:d.value.id,variables:t,startUserSelectAssignees:c.value}),V.success("\u53D1\u8D77\u6D41\u7A0B\u6210\u529F"),X(a(T)),await z({name:"BpmProcessInstanceMy"})}finally{n.value.btn.loading(!1)}}};return ie(()=>{(async()=>{C.value=!0;try{if(k.value=await Ae.getCategorySimpleList(),k.value.length>0&&(b.value=k.value[0].code),U.value=await Se({suspensionState:1}),(h==null?void 0:h.length)>0){const t=await qe(h);if(!t)return void V.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9E\u4F8B\u4E0D\u5B58\u5728");const e=U.value.find(v=>{var m;return v.key==((m=t.processDefinition)==null?void 0:m.key)});if(!e)return void V.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9A\u4E49\u4E0D\u5B58\u5728");await H(e,t.formVariables)}}finally{C.value=!1}})()}),(t,e)=>{const v=Ue,m=Ce,x=pe,I=Ve,q=de,$=fe,j=ce,J=ve,L=we,K=ye,M=be,N=_e,O=ge,Q=he,W=ke,ee=ue("form-create"),ae=xe;return i(),_(g,null,[o(v,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(d)?(i(),y(L,{key:1},{default:l(()=>[o(I,{class:"box-card"},{default:l(()=>[F("div",He,[F("span",Le,"\u7533\u8BF7\u4FE1\u606F\u3010"+G(a(d).name)+"\u3011",1),o(M,{style:{float:"right"},type:"primary",onClick:e[1]||(e[1]=s=>d.value=void 0)},{default:l(()=>[o(K,{icon:"ep:delete"}),e[4]||(e[4]=P(" \u9009\u62E9\u5176\u5B83\u6D41\u7A0B "))]),_:1})]),o(q,{span:16,offset:6,style:{"margin-top":"20px"}},{default:l(()=>[o(ee,{rule:a(p).rule,api:a(n),"onUpdate:api":e[2]||(e[2]=s=>D(n)?n.value=s:null),modelValue:a(p).value,"onUpdate:modelValue":e[3]||(e[3]=s=>a(p).value=s),option:a(p).option,onSubmit:Z},{"type-startUserSelect":l(()=>[o(q,{span:24},{default:l(()=>[o(I,{class:"mb-10px"},{header:l(()=>e[5]||(e[5]=[P("\u6307\u5B9A\u5BA1\u6279\u4EBA")])),default:l(()=>[o(W,{model:a(c),rules:a(S),ref_key:"startUserSelectAssigneesFormRef",ref:B},{default:l(()=>[(i(!0),_(g,null,w(a(f),s=>(i(),y(Q,{key:s.id,label:`\u4EFB\u52A1\u3010${s.name}\u3011`,prop:s.id},{default:l(()=>[o(O,{modelValue:a(c)[s.id],"onUpdate:modelValue":u=>a(c)[s.id]=u,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA"},{default:l(()=>[(i(!0),_(g,null,w(a(E),u=>(i(),y(N,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["rule","api","modelValue","option"])]),_:1})]),_:1}),o(ze,{"bpmn-xml":a(A)},null,8,["bpmn-xml"])]),_:1})):me((i(),y(L,{key:0},{default:l(()=>[o(J,{"tab-position":"left",modelValue:a(b),"onUpdate:modelValue":e[0]||(e[0]=s=>D(b)?b.value=s:null)},{default:l(()=>[(i(!0),_(g,null,w(a(k),s=>(i(),y(j,{label:s.name,name:s.code,key:s.code},{default:l(()=>[o($,{gutter:20},{default:l(()=>[(i(!0),_(g,null,w(a(Y),u=>(i(),y(q,{lg:6,sm:12,xs:24,key:u.id},{default:l(()=>[o(I,{shadow:"hover",class:"mb-20px cursor-pointer",onClick:Re=>H(u)},{default:l(()=>[F("div",Ee,[o(m,{src:u.icon,class:"w-32px h-32px"},null,8,["src"]),o(x,{class:"!ml-10px",size:"large"},{default:l(()=>[P(G(u.name),1)]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[ae,a(C)]])],64)}}});export{De as default};
