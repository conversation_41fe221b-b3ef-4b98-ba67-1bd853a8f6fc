import{A as Nt,C as jt,a as qt,b as Ht,S as Et,M as Gt,P as Ut,c as Vt,d as $t,e as Jt,f as Kt,g as Zt,h as Qt,i as te,j as ee,k as ae,l as Ot,m as ne,n as re,o as zt,p as oe,q as ie,r as se,s as le,t as de,u as fe,v as ue,w as ce,x as he,y as ge,z as me,B as Dt,D as we,E as pe,F as Lt,G as ve,H as ye,I as xe,J as Me,K as Se,L as Rt,N as be,O as Ie,Q as Ce,R as Te,T as ke,U as Ee,V as Oe,W as ze,X as De,Y as wt,Z as Le,_ as Re,$ as Fe,a0 as Ae,a1 as Pe,a2 as Be,a3 as We,a4 as _e,a5 as Ye,a6 as Xe,a7 as Ne,a8 as je,a9 as qe,aa as He,ab as Ge,ac as Ue,ad as Ve,ae as $e,af as Ft,ag as Je,ah as Ke,ai as Ze,aj as Qe,ak as ta,al as ea,am as aa,an as na,ao as bt,ap as ra,aq as oa,ar as At,as as ia,at as sa,au as la}from"./echarts-cfVEL83D.js";import{d as da,aj as Pt,e as fa,Y as pt,u as H,j as Bt,aZ as Wt,au as ua,bU as ca,f as ha,co as ga,bu as ma,o as wa,c as pa,n as va,aK as ya,X as xa}from"./index-C8b06LRn.js";const Ma=Object.freeze(Object.defineProperty({__proto__:null,Axis:Nt,ChartView:jt,ComponentModel:qt,ComponentView:Ht,List:Et,Model:Gt,PRIORITY:Ut,SeriesModel:Vt,color:$t,connect:Jt,dataTool:Kt,dependencies:Zt,disConnect:Qt,disconnect:te,dispose:ee,env:ae,extendChartView:Ot,extendComponentModel:ne,extendComponentView:re,extendSeriesModel:zt,format:oe,getCoordinateSystemDimensions:ie,getInstanceByDom:se,getInstanceById:le,getMap:de,graphic:fe,helper:ue,init:ce,innerDrawElementOnCanvas:he,matrix:ge,number:me,parseGeoJSON:Dt,parseGeoJson:Dt,registerAction:we,registerCoordinateSystem:pe,registerLayout:Lt,registerLoading:ve,registerLocale:ye,registerMap:xe,registerPostInit:Me,registerPostUpdate:Se,registerPreprocessor:Rt,registerProcessor:be,registerTheme:Ie,registerTransform:Ce,registerUpdateLifecycle:Te,registerVisual:ke,setCanvasCreator:Ee,setPlatformAPI:Oe,throttle:ze,time:De,use:wt,util:Le,vector:Re,version:Fe,zrUtil:Ae,zrender:Pe},Symbol.toStringTag,{value:"Module"}));wt([Be,We,_e,Ye,Xe,Ne,je,qe,He,Ge,Ue,Ve,$e,Ft,Je,Ke,Ze,Qe]),wt([Ft,ta]),wt(ea),zt({type:"series.wordCloud",visualStyleAccessPath:"textStyle",visualStyleMapper:function(n){return{fill:n.get("color")}},visualDrawType:"fill",optionUpdated:function(){var n=this.option;n.gridSize=Math.max(Math.floor(n.gridSize),4)},getInitialData:function(n,l){var r=aa(n.data,{coordDimensions:["value"]}),t=new Et(r,this);return t.initData(n.data),t},defaultOption:{maskImage:null,shape:"circle",keepAspect:!1,left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,textStyle:{fontWeight:"normal"}}}),Ot({type:"wordCloud",render:function(n,l,r){var t=this.group;t.removeAll();var S=n.getData(),c=n.get("gridSize");n.layoutInstance.ondraw=function(z,g,i,y){var w=S.getItemModel(i),A=w.getModel("textStyle"),C=new na({style:bt(A),scaleX:1/y.info.mu,scaleY:1/y.info.mu,x:(y.gx+y.info.gw/2)*c,y:(y.gy+y.info.gh/2)*c,rotation:y.rot});C.setStyle({x:y.info.fillTextOffsetX,y:y.info.fillTextOffsetY+.5*g,text:z,verticalAlign:"middle",fill:S.getItemVisual(i,"style").fill,fontSize:g}),t.add(C),S.setItemGraphicEl(i,C),C.ensureState("emphasis").style=bt(w.getModel(["emphasis","textStyle"]),{state:"emphasis"}),C.ensureState("blur").style=bt(w.getModel(["blur","textStyle"]),{state:"blur"}),ra(C,w.get(["emphasis","focus"]),w.get(["emphasis","blurScope"])),C.stateTransition={duration:n.get("animation")?n.get(["stateAnimation","duration"]):0,easing:n.get(["stateAnimation","easing"])},C.__highDownDispatcher=!0},this._model=n},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}}),window.setImmediate||(window.setImmediate=window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var n=[void 0],l="zero-timeout-message";return window.addEventListener("message",function(r){if(typeof r.data=="string"&&r.data.substr(0,20)===l){r.stopImmediatePropagation();var t=parseInt(r.data.substr(20),36);n[t]&&(n[t](),n[t]=void 0)}},!0),window.clearImmediate=function(r){n[r]&&(n[r]=void 0)},function(r){var t=n.length;return n.push(r),window.postMessage(l+t.toString(36),"*"),t}}()||function(n){window.setTimeout(n,0)}),window.clearImmediate||(window.clearImmediate=window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(n){window.clearTimeout(n)});var It=function(){var n=document.createElement("canvas");if(!n||!n.getContext)return!1;var l=n.getContext("2d");return!!l&&!!l.getImageData&&!!l.fillText&&!!Array.prototype.some&&!!Array.prototype.push}(),Ct=function(){if(It){for(var n,l,r=document.createElement("canvas").getContext("2d"),t=20;t;){if(r.font=t.toString(10)+"px sans-serif",r.measureText("\uFF37").width===n&&r.measureText("m").width===l)return t+1;n=r.measureText("\uFF37").width,l=r.measureText("m").width,t--}return 0}}(),Sa=function(n){for(var l,r,t=n.length;t;)l=Math.floor(Math.random()*t),r=n[--t],n[t]=n[l],n[l]=r;return n},ut={},vt=function(n,l){if(It){var r=Math.floor(Math.random()*Date.now());Array.isArray(n)||(n=[n]),n.forEach(function(e,a){if(typeof e=="string"){if(n[a]=document.getElementById(e),!n[a])throw new Error("The element id specified is not found.")}else if(!e.tagName&&!e.appendChild)throw new Error("You must pass valid HTML elements, or ID of the element.")});var t={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "\u5FAE\u8EDF\u6B63\u9ED1\u9AD4", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,layoutAnimation:!0,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(l)for(var S in l)S in t&&(t[S]=l[S]);if(typeof t.weightFactor!="function"){var c=t.weightFactor;t.weightFactor=function(e){return e*c}}if(typeof t.shape!="function")switch(t.shape){case"circle":default:t.shape="circle";break;case"cardioid":t.shape=function(e){return 1-Math.sin(e)};break;case"diamond":t.shape=function(e){var a=e%(2*Math.PI/4);return 1/(Math.cos(a)+Math.sin(a))};break;case"square":t.shape=function(e){return Math.min(1/Math.abs(Math.cos(e)),1/Math.abs(Math.sin(e)))};break;case"triangle-forward":t.shape=function(e){var a=e%(2*Math.PI/3);return 1/(Math.cos(a)+Math.sqrt(3)*Math.sin(a))};break;case"triangle":case"triangle-upright":t.shape=function(e){var a=(e+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(a)+Math.sqrt(3)*Math.sin(a))};break;case"pentagon":t.shape=function(e){var a=(e+.955)%(2*Math.PI/5);return 1/(Math.cos(a)+.726543*Math.sin(a))};break;case"star":t.shape=function(e){var a=(e+.955)%(2*Math.PI/10);return(e+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-a)+3.07768*Math.sin(2*Math.PI/10-a)):1/(Math.cos(a)+3.07768*Math.sin(a))}}t.gridSize=Math.max(Math.floor(t.gridSize),4);var z,g,i,y,w,A,C,P,o=t.gridSize,q=o-t.maskGapWidth,h=Math.abs(t.maxRotation-t.minRotation),p=Math.min(t.maxRotation,t.minRotation),x=t.rotationStep;switch(t.color){case"random-dark":C=function(){return Tt(10,50)};break;case"random-light":C=function(){return Tt(50,90)};break;default:typeof t.color=="function"&&(C=t.color)}typeof t.fontWeight=="function"&&(P=t.fontWeight);var T=null;typeof t.classes=="function"&&(T=t.classes);var ot,it=!1,m=[],gt=function(e){var a,s,f=e.currentTarget,u=f.getBoundingClientRect();e.touches?(a=e.touches[0].clientX,s=e.touches[0].clientY):(a=e.clientX,s=e.clientY);var G=a-u.left,_=s-u.top,U=Math.floor(G*(f.width/u.width||1)/o),E=Math.floor(_*(f.height/u.height||1)/o);return m[U]?m[U][E]:null},ct=function(e){var a=gt(e);ot!==a&&(ot=a,a?t.hover(a.item,a.dimension,e):t.hover(void 0,void 0,e))},yt=function(e){var a=gt(e);a&&(t.click(a.item,a.dimension,e),e.preventDefault())},xt=[],_t=function(e){if(xt[e])return xt[e];var a=8*e,s=a,f=[];for(e===0&&f.push([y[0],y[1],0]);s--;){var u=1;t.shape!=="circle"&&(u=t.shape(s/a*2*Math.PI)),f.push([y[0]+e*u*Math.cos(-s/a*2*Math.PI),y[1]+e*u*Math.sin(-s/a*2*Math.PI)*t.ellipticity,s/a*2*Math.PI])}return xt[e]=f,f},Mt=function(){return t.abortThreshold>0&&new Date().getTime()-A>t.abortThreshold},Yt=function(e,a,s,f,u){e>=g||a>=i||e<0||a<0||(z[e][a]=!1,s&&n[0].getContext("2d").fillRect(e*o,a*o,q,q),it&&(m[e][a]={item:u,dimension:f}))},Xt=function e(a,s){if(s>20)return null;var f,u,G;Array.isArray(a)?(f=a[0],u=a[1]):(f=a.word,u=a.weight,G=a.attributes);var _=t.rotateRatio===0||Math.random()>t.rotateRatio?0:h===0?p:p+Math.round(Math.random()*h/x)*x,U=function(d){if(Array.isArray(d)){var b=d.slice();return b.splice(0,2),b}return[]}(a),E=function(d,b,O,$){var k=t.weightFactor(b);if(k<=t.minSize)return!1;var v,M=1;k<Ct&&(M=function(){for(var St=2;St*k<Ct;)St+=2;return St}()),v=P?P(d,b,k,$):t.fontWeight;var R=document.createElement("canvas"),I=R.getContext("2d",{willReadFrequently:!0});I.font=v+" "+(k*M).toString(10)+"px "+t.fontFamily;var B=I.measureText(d).width/M,D=Math.max(k*M,I.measureText("m").width,I.measureText("\uFF37").width)/M,Y=B+2*D,X=3*D,W=Math.ceil(Y/o),J=Math.ceil(X/o);Y=W*o,X=J*o;var V=-B/2,F=.4*-D,K=Math.ceil((Y*Math.abs(Math.sin(O))+X*Math.abs(Math.cos(O)))/o),L=Math.ceil((Y*Math.abs(Math.cos(O))+X*Math.abs(Math.sin(O)))/o),N=L*o,Z=K*o;R.setAttribute("width",N),R.setAttribute("height",Z),I.scale(1/M,1/M),I.translate(N*M/2,Z*M/2),I.rotate(-O),I.font=v+" "+(k*M).toString(10)+"px "+t.fontFamily,I.fillStyle="#000",I.textBaseline="middle",I.fillText(d,V*M,(F+.5*k)*M);var nt=I.getImageData(0,0,N,Z).data;if(Mt())return!1;for(var j,dt,ft,kt=[],rt=L,Q=[K/2,L/2,K/2,L/2];rt--;)for(j=K;j--;){ft=o;t:for(;ft--;)for(dt=o;dt--;)if(nt[4*((j*o+ft)*N+(rt*o+dt))+3]){kt.push([rt,j]),rt<Q[3]&&(Q[3]=rt),rt>Q[1]&&(Q[1]=rt),j<Q[0]&&(Q[0]=j),j>Q[2]&&(Q[2]=j);break t}}return{mu:M,occupied:kt,bounds:Q,gw:L,gh:K,fillTextOffsetX:V,fillTextOffsetY:F,fillTextWidth:B,fillTextHeight:D,fontSize:k}}(f,u,_,U);if(!E||Mt())return!1;if(!t.drawOutOfBound&&!t.shrinkToFit){var tt=E.bounds;if(tt[1]-tt[3]+1>g||tt[2]-tt[0]+1>i)return!1}for(var st=w+1,mt=function(d){var b=Math.floor(d[0]-E.gw/2),O=Math.floor(d[1]-E.gh/2);return E.gw,E.gh,!!function($,k,v,M,R){for(var I=R.length;I--;){var B=$+R[I][0],D=k+R[I][1];if(B>=g||D>=i||B<0||D<0){if(!t.drawOutOfBound)return!1}else if(!z[B][D])return!1}return!0}(b,O,0,0,E.occupied)&&(function($,k,v,M,R,I,B,D,Y,X){var W,J,V,F=v.fontSize;W=C?C(M,R,F,I,B,X):t.color,J=P?P(M,R,F,X):t.fontWeight,V=T?T(M,R,F,X):t.classes,n.forEach(function(K){if(K.getContext){var L=K.getContext("2d"),N=v.mu;L.save(),L.scale(1/N,1/N),L.font=J+" "+(F*N).toString(10)+"px "+t.fontFamily,L.fillStyle=W,L.translate(($+v.gw/2)*o*N,(k+v.gh/2)*o*N),D!==0&&L.rotate(-D),L.textBaseline="middle",L.fillText(M,v.fillTextOffsetX*N,(v.fillTextOffsetY+.5*F)*N),L.restore()}else{var Z=document.createElement("span"),nt="";nt="rotate("+-D/Math.PI*180+"deg) ",v.mu!==1&&(nt+="translateX(-"+v.fillTextWidth/4+"px) scale("+1/v.mu+")");var j={position:"absolute",display:"block",font:J+" "+F*v.mu+"px "+t.fontFamily,left:($+v.gw/2)*o+v.fillTextOffsetX+"px",top:(k+v.gh/2)*o+v.fillTextOffsetY+"px",width:v.fillTextWidth+"px",height:v.fillTextHeight+"px",lineHeight:F+"px",whiteSpace:"nowrap",transform:nt,webkitTransform:nt,msTransform:nt,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};for(var dt in W&&(j.color=W),Z.textContent=M,j)Z.style[dt]=j[dt];if(Y)for(var ft in Y)Z.setAttribute(ft,Y[ft]);V&&(Z.className+=V),K.appendChild(Z)}})}(b,O,E,f,u,w-st,d[2],_,G,U),function($,k,v,M,R,I){var B,D,Y=R.occupied,X=t.drawMask;if(X&&((B=n[0].getContext("2d")).save(),B.fillStyle=t.maskColor),it){var W=R.bounds;D={x:($+W[3])*o,y:(k+W[0])*o,w:(W[1]-W[3]+1)*o,h:(W[2]-W[0]+1)*o}}for(var J=Y.length;J--;){var V=$+Y[J][0],F=k+Y[J][1];V>=g||F>=i||V<0||F<0||Yt(V,F,X,D,I)}X&&B.restore()}(b,O,0,0,E,a),{gx:b,gy:O,rot:_,info:E})};st--;){var et=_t(w-st);t.shuffle&&(et=[].concat(et),Sa(et));for(var at=0;at<et.length;at++){var lt=mt(et[at]);if(lt)return lt}}return t.shrinkToFit?(Array.isArray(a)?a[1]=3*a[1]/4:a.weight=3*a.weight/4,e(a,s+1)):null},ht=function(e,a,s){if(a)return!n.some(function(f){var u=new CustomEvent(e,{detail:s||{}});return!f.dispatchEvent(u)},this);n.forEach(function(f){var u=new CustomEvent(e,{detail:s||{}});f.dispatchEvent(u)},this)};(function(){var e=n[0];if(e.getContext)g=Math.ceil(e.width/o),i=Math.ceil(e.height/o);else{var a=e.getBoundingClientRect();g=Math.ceil(a.width/o),i=Math.ceil(a.height/o)}if(ht("wordcloudstart",!0)){var s,f,u,G,_;if(y=t.origin?[t.origin[0]/o,t.origin[1]/o]:[g/2,i/2],w=Math.floor(Math.sqrt(g*g+i*i)),z=[],!e.getContext||t.clearCanvas)for(n.forEach(function(d){if(d.getContext){var b=d.getContext("2d");b.fillStyle=t.backgroundColor,b.clearRect(0,0,g*(o+1),i*(o+1)),b.fillRect(0,0,g*(o+1),i*(o+1))}else d.textContent="",d.style.backgroundColor=t.backgroundColor,d.style.position="relative"}),s=g;s--;)for(z[s]=[],f=i;f--;)z[s][f]=!0;else{var U=document.createElement("canvas").getContext("2d");U.fillStyle=t.backgroundColor,U.fillRect(0,0,1,1);var E,tt,st=U.getImageData(0,0,1,1).data,mt=e.getContext("2d").getImageData(0,0,g*o,i*o).data;for(s=g;s--;)for(z[s]=[],f=i;f--;){tt=o;t:for(;tt--;)for(E=o;E--;)for(u=4;u--;)if(mt[4*((f*o+tt)*g*o+(s*o+E))+u]!==st[u]){z[s][f]=!1;break t}z[s][f]!==!1&&(z[s][f]=!0)}mt=U=st=void 0}if(t.hover||t.click){for(it=!0,s=g+1;s--;)m[s]=[];t.hover&&e.addEventListener("mousemove",ct),t.click&&(e.addEventListener("click",yt),e.addEventListener("touchstart",yt),e.addEventListener("touchend",function(d){d.preventDefault()}),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",function d(){e.removeEventListener("wordcloudstart",d),e.removeEventListener("mousemove",ct),e.removeEventListener("click",yt),ot=void 0})}u=0;var et=!0;t.layoutAnimation?t.wait!==0?(G=window.setTimeout,_=window.clearTimeout):(G=window.setImmediate,_=window.clearImmediate):(G=function(d){d()},_=function(){et=!1});var at=function(d,b){n.forEach(function(O){O.removeEventListener(d,b)},this)},lt=function d(){at("wordcloudstart",d),_(ut[r])};(function(d,b){n.forEach(function(O){O.addEventListener(d,b)},this)})("wordcloudstart",lt),ut[r]=(t.layoutAnimation?G:setTimeout)(function d(){if(et){if(u>=t.list.length)return _(ut[r]),ht("wordcloudstop",!1),at("wordcloudstart",lt),void delete ut[r];A=new Date().getTime();var b=Xt(t.list[u],0),O=!ht("wordclouddrawn",!0,{item:t.list[u],drawn:b});if(Mt()||O)return _(ut[r]),t.abort(),ht("wordcloudabort",!1),ht("wordcloudstop",!1),void at("wordcloudstart",lt);u++,ut[r]=G(d,t.wait)}},t.wait)}})()}function Tt(e,a){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(a-e)+e).toFixed()+"%)"}};if(vt.isSupported=It,vt.minFontSize=Ct,!vt.isSupported)throw new Error("Sorry your browser not support wordCloud");Lt(function(n,l){n.eachSeriesByType("wordCloud",function(r){var t=sa(r.getBoxLayoutParams(),{width:l.getWidth(),height:l.getHeight()}),S=r.get("keepAspect"),c=r.get("maskImage"),z=c?c.width/c.height:1;S&&function(h,p){var x=h.width,T=h.height;x>T*p?(h.x+=(x-T*p)/2,h.width=T*p):(h.y+=(T-x/p)/2,h.height=x/p)}(t,z);var g=r.getData(),i=document.createElement("canvas");i.width=t.width,i.height=t.height;var y=i.getContext("2d");if(c)try{y.drawImage(c,0,0,i.width,i.height),function(h){for(var p=h.getContext("2d"),x=p.getImageData(0,0,h.width,h.height),T=p.createImageData(x),ot=0,it=0,m=0;m<x.data.length;m+=4)x.data[m+3]>128&&(ot+=ct=x.data[m]+x.data[m+1]+x.data[m+2],++it);var gt=ot/it;for(m=0;m<x.data.length;m+=4){var ct=x.data[m]+x.data[m+1]+x.data[m+2];x.data[m+3]<128||ct>gt?(T.data[m]=0,T.data[m+1]=0,T.data[m+2]=0,T.data[m+3]=0):(T.data[m]=255,T.data[m+1]=255,T.data[m+2]=255,T.data[m+3]=255)}p.putImageData(T,0,0)}(i)}catch{}var w=r.get("sizeRange"),A=r.get("rotationRange"),C=g.getDataExtent("value"),P=Math.PI/180,o=r.get("gridSize");function q(h){var p=h.detail.item;h.detail.drawn&&r.layoutInstance.ondraw&&(h.detail.drawn.gx+=t.x/o,h.detail.drawn.gy+=t.y/o,r.layoutInstance.ondraw(p[0],p[1],p[2],h.detail.drawn))}vt(i,{list:g.mapArray("value",function(h,p){var x=g.getItemModel(p);return[g.getName(p),x.get("textStyle.fontSize",!0)||la(h,C,w),p]}).sort(function(h,p){return p[1]-h[1]}),fontFamily:r.get("textStyle.fontFamily")||r.get("emphasis.textStyle.fontFamily")||n.get("textStyle.fontFamily"),fontWeight:r.get("textStyle.fontWeight")||r.get("emphasis.textStyle.fontWeight")||n.get("textStyle.fontWeight"),gridSize:o,ellipticity:t.height/t.width,minRotation:A[0]*P,maxRotation:A[1]*P,clearCanvas:!c,rotateRatio:1,rotationStep:r.get("rotationStep")*P,drawOutOfBound:r.get("drawOutOfBound"),shrinkToFit:r.get("shrinkToFit"),layoutAnimation:r.get("layoutAnimation"),shuffle:!1,shape:r.get("shape")}),i.addEventListener("wordclouddrawn",q),r.layoutInstance&&r.layoutInstance.dispose(),r.layoutInstance={ondraw:null,dispose:function(){i.removeEventListener("wordclouddrawn",q),i.addEventListener("wordclouddrawn",function(h){h.preventDefault()})}}})}),Rt(function(n){var l=(n||{}).series;!oa(l)&&(l=l?[l]:[]);var r=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function t(S){S&&At(r,function(c){S.hasOwnProperty(c)&&(S["text"+ia(c)]=S[c])})}At(l,function(S){if(S&&S.type==="wordCloud"){var c=S.textStyle||{};t(c.normal),t(c.emphasis)}})});const ba=da({name:"EChart",__name:"Echart",props:{options:{type:Object,required:!0},width:Pt.oneOfType([Number,String]).def(""),height:Pt.oneOfType([Number,String]).def("500px")},setup(n){const{getPrefixCls:l,variables:r}=xa(),t=l("echart"),S=fa(),c=n,z=pt(()=>S.getIsDark),g=pt(()=>!!H(z)||"auto"),i=pt(()=>Object.assign(c.options,{darkMode:H(g)})),y=Bt();let w=null;const A=Bt(),C=pt(()=>({width:Wt(c.width)?c.width:`${c.width}px`,height:Wt(c.height)?c.height:`${c.height}px`}));ua(()=>i.value,q=>{w&&(w==null||w.setOption(q))},{deep:!0});const P=ca(()=>{w&&w.resize()},100),o=async q=>{q.propertyName==="width"&&P()};return ha(()=>{H(y)&&c.options&&(w=Ma.init(H(y)),w==null||w.setOption(H(i))),window.addEventListener("resize",P),A.value=document.getElementsByClassName(`${r.namespace}-layout-content`)[0],H(A)&&H(A).addEventListener("transitionend",o)}),ga(()=>{window.removeEventListener("resize",P),H(A)&&H(A).removeEventListener("transitionend",o)}),ma(()=>{w&&w.resize()}),(q,h)=>(wa(),pa("div",{ref_key:"elRef",ref:y,class:va([q.$attrs.class,H(t)]),style:ya(H(C))},null,6))}});export{ba as _};
