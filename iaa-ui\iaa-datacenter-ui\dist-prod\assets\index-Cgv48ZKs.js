import{aG as s}from"./index-C8b06LRn.js";const r=async a=>await s.post({url:"/bpm/user-group/create",data:a}),e=async a=>await s.put({url:"/bpm/user-group/update",data:a}),t=async a=>await s.delete({url:"/bpm/user-group/delete?id="+a}),u=async a=>await s.get({url:"/bpm/user-group/get?id="+a}),p=async a=>await s.get({url:"/bpm/user-group/page",params:a}),g=async()=>await s.get({url:"/bpm/user-group/simple-list"});export{u as a,p as b,r as c,t as d,g,e as u};
