import{aG as v,d as N,p as P,y as R,j as p,r as B,o,h as m,w as s,k as u,u as a,v as y,x as G,c as T,F as S,g as I,N as w,D as U,t as k,$ as H,m as K,S as $,B as z,ap as J,aq as Q,I as W,H as X,L as Z}from"./index-C8b06LRn.js";import{_ as ee}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const le=async g=>await v.get({url:"/system/social-client/page",params:g}),ae=async g=>await v.delete({url:"/system/social-client/delete?id="+g}),te=N({__name:"SocialClientForm",emits:["success"],setup(g,{expose:x,emit:F}){const{t:_}=P(),q=R(),r=p(!1),h=p(""),c=p(!1),C=p(""),t=p({id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0}),M=B({name:[{required:!0,message:"\u5E94\u7528\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],socialType:[{required:!0,message:"\u793E\u4EA4\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userType:[{required:!0,message:"\u7528\u6237\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientId:[{required:!0,message:"\u5BA2\u6237\u7AEF\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientSecret:[{required:!0,message:"\u5BA2\u6237\u7AEF\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=p();x({open:async(d,e)=>{if(r.value=!0,h.value=_("action."+d),C.value=d,j(),e){c.value=!0;try{t.value=await(async n=>await v.get({url:"/system/social-client/get?id="+n}))(e)}finally{c.value=!1}}}});const O=F,Y=async()=>{if(f&&await f.value.validate()){c.value=!0;try{const d=t.value;C.value==="create"?(await(async e=>await v.post({url:"/system/social-client/create",data:e}))(d),q.success(_("common.createSuccess"))):(await(async e=>await v.put({url:"/system/social-client/update",data:e}))(d),q.success(_("common.updateSuccess"))),r.value=!1,O("success")}finally{c.value=!1}}},j=()=>{var d;t.value={id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0},(d=f.value)==null||d.resetFields()};return(d,e)=>{const n=$,i=z,V=J,b=Q,A=W,E=X,D=ee,L=Z;return o(),m(D,{modelValue:a(r),"onUpdate:modelValue":e[8]||(e[8]=l=>K(r)?r.value=l:null),title:a(h)},{footer:s(()=>[u(E,{disabled:a(c),type:"primary",onClick:Y},{default:s(()=>e[9]||(e[9]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),u(E,{onClick:e[7]||(e[7]=l=>r.value=!1)},{default:s(()=>e[10]||(e[10]=[y("\u53D6 \u6D88")])),_:1})]),default:s(()=>[G((o(),m(A,{ref_key:"formRef",ref:f,model:a(t),rules:a(M),"label-width":"120px"},{default:s(()=>[u(i,{label:"\u5E94\u7528\u540D",prop:"name"},{default:s(()=>[u(n,{modelValue:a(t).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D"},null,8,["modelValue"])]),_:1}),u(i,{label:"\u793E\u4EA4\u5E73\u53F0",prop:"socialType"},{default:s(()=>[u(b,{modelValue:a(t).socialType,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).socialType=l)},{default:s(()=>[(o(!0),T(S,null,I(a(w)(a(U).SYSTEM_SOCIAL_TYPE),l=>(o(),m(V,{key:l.value,value:l.value},{default:s(()=>[y(k(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(i,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:s(()=>[u(b,{modelValue:a(t).userType,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).userType=l)},{default:s(()=>[(o(!0),T(S,null,I(a(w)(a(U).USER_TYPE),l=>(o(),m(V,{key:l.value,value:l.value},{default:s(()=>[y(k(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(i,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",prop:"clientId"},{default:s(()=>[u(n,{modelValue:a(t).clientId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).clientId=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u7F16\u53F7,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appKey"},null,8,["modelValue"])]),_:1}),u(i,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",prop:"clientSecret"},{default:s(()=>[u(n,{modelValue:a(t).clientSecret,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).clientSecret=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u5BC6\u94A5,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appSecret"},null,8,["modelValue"])]),_:1}),a(t).socialType===30?(o(),m(i,{key:0,label:"agentId",prop:"agentId"},{default:s(()=>[u(n,{modelValue:a(t).agentId,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).agentId=l),placeholder:"\u6388\u6743\u65B9\u7684\u7F51\u9875\u5E94\u7528 ID\uFF0C\u6709\u5219\u586B"},null,8,["modelValue"])]),_:1})):H("",!0),u(i,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[u(b,{modelValue:a(t).status,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).status=l)},{default:s(()=>[(o(!0),T(S,null,I(a(w)(a(U).COMMON_STATUS),l=>(o(),m(V,{key:l.value,value:l.value},{default:s(()=>[y(k(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[L,a(c)]])]),_:1},8,["modelValue","title"])}}});export{te as _,ae as d,le as g};
