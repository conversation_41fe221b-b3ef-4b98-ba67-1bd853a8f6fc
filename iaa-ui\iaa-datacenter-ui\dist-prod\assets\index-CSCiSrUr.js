import{aG as t}from"./index-C8b06LRn.js";const a={getCustomers:async e=>await t.get({url:`/collection/reconciliation/getCustomers?customersName=${e}`}),getOrderReceivablePage:async e=>await t.post({url:"/collection/reconciliation/getOrderReceivablePage",data:e}),getExpenseReceivablePage:async e=>await t.post({url:"/collection/reconciliation/getExpenseReceivablePage",data:e}),getTotalAmount:async e=>await t.post({url:"/collection/reconciliation/getTotalAmount",data:e})};export{a as B};
