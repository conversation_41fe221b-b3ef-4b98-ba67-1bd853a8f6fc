import{_ as D}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as g}from"./Descriptions-iRMIdIt1.js";import{aG as c,r as Y,D as b,d as M,j as t,T as x,o as P,h as v,w as f,k as C,u as s,x as H,l as E,m as F,__tla as I}from"./index-C8b06LRn.js";import{d as h}from"./formatTime-COZ9Bl52.js";import{e as k,u as A}from"./useCrudSchemas-HzMMRa-v.js";let u,i,T,L=Promise.all([(()=>{try{return I}catch{}})()]).then(async()=>{let o,m;T=async d=>await c.get({url:"/system/mail-log/page",params:d}),o=await k(),m=Y([{label:"\u7F16\u53F7",field:"id"},{label:"\u53D1\u9001\u65F6\u95F4",field:"sendTime",formatter:h,search:{show:!0,component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss",type:"daterange",defaultTime:[new Date("1 00:00:00"),new Date("1 23:59:59")],style:{width:"240px"}}},detail:{dateFormat:"YYYY-MM-DD HH:mm:ss"}},{label:"\u63A5\u6536\u90AE\u7BB1",field:"toMail"},{label:"\u7528\u6237\u7F16\u53F7",field:"userId",isSearch:!0,isTable:!1,search:{componentProps:{style:{width:"240px"}}}},{label:"\u7528\u6237\u7C7B\u578B",field:"userType",dictType:b.USER_TYPE,dictClass:"number",isSearch:!0,isTable:!1,search:{componentProps:{style:{width:"240px"}}}},{label:"\u90AE\u4EF6\u6807\u9898",field:"templateTitle"},{label:"\u90AE\u4EF6\u5185\u5BB9",field:"templateContent",isTable:!1},{label:"\u90AE\u7BB1\u53C2\u6570",field:"templateParams",isTable:!1},{label:"\u53D1\u9001\u72B6\u6001",field:"sendStatus",dictType:b.SYSTEM_MAIL_SEND_STATUS,dictClass:"string",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u90AE\u7BB1\u8D26\u53F7",field:"accountId",isTable:!1,search:{show:!0,component:"Select",api:()=>o,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"},style:{width:"240px"}}}},{label:"\u53D1\u9001\u90AE\u7BB1\u5730\u5740",field:"fromMail",table:{label:"\u90AE\u7BB1\u8D26\u53F7"}},{label:"\u6A21\u677F\u7F16\u53F7",field:"templateId",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u6A21\u677F\u7F16\u7801",field:"templateCode",isTable:!1},{label:"\u6A21\u7248\u53D1\u9001\u4EBA\u540D\u79F0",field:"templateNickname",isTable:!1},{label:"\u53D1\u9001\u8FD4\u56DE\u7684\u6D88\u606F\u7F16\u53F7",field:"sendMessageId",isTable:!1},{label:"\u53D1\u9001\u5F02\u5E38",field:"sendException",isTable:!1},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isTable:!1,formatter:h,detail:{dateFormat:"YYYY-MM-DD HH:mm:ss"}},{label:"\u64CD\u4F5C",field:"action",isDetail:!1}]),{allSchemas:i}=A(m),u=M({name:"SystemMailLogDetail",__name:"MailLogDetail",setup(d,{expose:y}){const e=t(!1),r=t(!1),n=t();return y({open:async p=>{e.value=!0,r.value=!0;try{n.value=await(async a=>await c.get({url:"/system/mail-log/get?id="+a}))(p)}finally{r.value=!1}}}),(p,a)=>{const _=g,w=D,S=x("dompurify-html");return P(),v(w,{modelValue:s(e),"onUpdate:modelValue":a[0]||(a[0]=l=>F(e)?e.value=l:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5"},{default:f(()=>[C(_,{data:s(n),schema:s(i).detailSchema},{templateContent:f(({row:l})=>[H(E("div",null,null,512),[[S,l.templateContent]])]),_:1},8,["data","schema"])]),_:1},8,["modelValue"])}}})});export{u as _,L as __tla,i as a,T as g};
