import{d as K,aj as C,j as _,Y as L,au as A,o as u,c as d,l as s,k as p,w as f,v as j,t as h,F as $,g as z,h as N,$ as q,d1 as v,H,S as J,z as M,A as Y,J as G,K as Q,_ as W}from"./index-C8b06LRn.js";const X={class:"paste-parser"},Z={class:"flex justify-between items-center mb-4"},ee={class:"flex gap-2"},ae={class:"mb-4"},le={class:"text-xs text-gray-500 mt-1"},te={key:0,class:"mb-4"},se={class:"grid grid-cols-6 md:grid-cols-6 lg:grid-cols-6 gap-4 p-4 bg-gray-50 rounded"},re={class:"text-xs text-gray-600 mb-1"},ie=["title"],ue={key:1,class:"mb-4"},ne={class:"flex justify-between items-center mb-2"},oe={class:"flex gap-2"},de={class:"max-h-96 overflow-auto border rounded"},me={key:2,class:"mb-4"},ce={class:"bg-red-50 border border-red-200 rounded p-3"},ve={class:"text-sm text-red-600"},ge=W(K({__name:"PasteParser",props:{targetFields:C.oneOfType([]).isRequired,mappings:C.oneOfType([]).isRequired},emits:["data-parsed"],setup(O,{emit:T}){const F=T,V=O,b=_(""),i=_([]),n=_([]),y=_([]),g=L(()=>b.value.trim()?b.value.trim().split(`
`).filter(l=>l.trim()):[]);A(g,l=>{l.length>0&&S(l[0])},{immediate:!0});const S=l=>{const e=l.split("	");y.value=e.map((r,a)=>({target:V.mappings[a]||"",sample:r.trim().substring(0,20)+(r.trim().length>20?"...":"")}))},U=()=>{setTimeout(()=>{g.value.length>0&&v.success(`\u68C0\u6D4B\u5230 ${g.value.length} \u884C\u6570\u636E`)},100)},D=()=>{i.value=[],n.value=[]},E=()=>{if(n.value=[],i.value=[],g.value.length===0)return void v.warning("\u8BF7\u5148\u8F93\u5165\u6570\u636E");const l=[];g.value.forEach((e,r)=>{try{const a=e.split("	"),o={};if(y.value.forEach((c,w)=>{if(c.target&&a[w]!==void 0){const m=a[w].trim();if(c.target==="quantity"){const t=parseFloat(m);isNaN(t)||(o[c.target]=t)}else c.target==="requireDate"?m&&m!==""&&(o[c.target]=I(m)):m&&m!==""&&(o[c.target]=m)}}),!o.itemCode)return void n.value.push({row:r+1,message:"\u54C1\u53F7\u4E0D\u80FD\u4E3A\u7A7A"});if(!o.itemName)return void n.value.push({row:r+1,message:"\u54C1\u540D\u4E0D\u80FD\u4E3A\u7A7A"});l.push(o)}catch(a){n.value.push({row:r+1,message:`\u89E3\u6790\u5931\u8D25: ${a}`})}}),i.value=l,l.length>0&&v.success(`\u6210\u529F\u89E3\u6790 ${l.length} \u6761\u6570\u636E`),n.value.length>0&&v.warning(`\u6709 ${n.value.length} \u6761\u6570\u636E\u89E3\u6790\u5931\u8D25`)},I=l=>{const e=new Date(l);return isNaN(e.getTime())?l:e.toISOString().split("T")[0]},P=()=>{b.value="",i.value=[],n.value=[],y.value=[]},R=()=>{if(i.value.length===0)return void v.warning("\u6CA1\u6709\u53EF\u5BFC\u51FA\u7684\u6570\u636E");const l=[["\u54C1\u53F7","\u54C1\u540D","\u89C4\u683C","\u7C7B\u522B","\u5C5E\u6027","\u6570\u91CF","\u5355\u4F4D","\u9700\u6C42\u65E5\u671F","\u5907\u6CE8"].join(","),...i.value.map(a=>[a.itemCode||"",a.itemName||"",a.spec||"",a.category||"",a.attribute||"",a.quantity||"",a.unit||"",a.requireDate||"",a.remark||""].map(o=>`"${o}"`).join(","))].join(`
`),e=new Blob([l],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");r.href=URL.createObjectURL(e),r.download=`BOM\u6570\u636E_${new Date().toISOString().split("T")[0]}.csv`,r.click(),v.success("\u6570\u636E\u5BFC\u51FA\u6210\u529F")},B=()=>{i.value.length!==0?(F("data-parsed",i.value),v.success(`\u5DF2\u786E\u8BA4\u5BFC\u5165 ${i.value.length} \u6761\u6570\u636E`)):v.warning("\u6CA1\u6709\u53EF\u786E\u8BA4\u7684\u6570\u636E")};return(l,e)=>{const r=H,a=J,o=M,c=Y,w=G,m=Q;return u(),d("div",X,[s("div",Z,[s("div",ee,[p(r,{size:"small",onClick:P},{default:f(()=>e[1]||(e[1]=[j("\u6E05\u7A7A")])),_:1}),p(r,{type:"primary",size:"small",onClick:E,disabled:!b.value.trim()},{default:f(()=>e[2]||(e[2]=[j(" \u89E3\u6790\u6570\u636E ")])),_:1},8,["disabled"])])]),s("div",ae,[e[3]||(e[3]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," \u7C98\u8D34\u6570\u636E (\u652F\u6301\u4ECEExcel/\u8868\u683C\u76F4\u63A5\u590D\u5236\u7C98\u8D34): ",-1)),p(a,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=t=>b.value=t),type:"textarea",rows:4,placeholder:"\u8BF7\u7C98\u8D34\u8868\u683C\u6570\u636E\uFF0C\u652F\u6301\u5236\u8868\u7B26\u5206\u9694\u7684\u6570\u636E\u683C\u5F0F...",onPaste:U,onInput:D},null,8,["modelValue"]),s("div",le," \u68C0\u6D4B\u5230 "+h(g.value.length)+" \u884C\u6570\u636E ",1)]),g.value.length>0?(u(),d("div",te,[e[4]||(e[4]=s("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"\u5B57\u6BB5\u6620\u5C04\u914D\u7F6E:",-1)),s("div",se,[(u(!0),d($,null,z(y.value,(t,k)=>(u(),d("div",{key:k,class:"flex flex-col"},[s("label",re,"\u5217 "+h(k+1),1),p(c,{modelValue:t.target,"onUpdate:modelValue":x=>t.target=x,size:"small",clearable:""},{default:f(()=>[(u(!0),d($,null,z(V.targetFields,x=>(u(),N(o,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),s("div",{class:"text-xs text-gray-400 mt-1 truncate",title:t.sample}," \u793A\u4F8B: "+h(t.sample),9,ie)]))),128))])])):q("",!0),i.value.length>0?(u(),d("div",ue,[s("div",ne,[e[6]||(e[6]=s("label",{class:"text-sm font-medium text-gray-700"},"\u89E3\u6790\u7ED3\u679C\u9884\u89C8:",-1)),s("div",oe,[p(r,{size:"small",onClick:R},{default:f(()=>e[5]||(e[5]=[j("\u5BFC\u51FA\u6570\u636E")])),_:1}),p(r,{type:"success",size:"small",onClick:B},{default:f(()=>[j(" \u786E\u8BA4\u5BFC\u5165 ("+h(i.value.length)+" \u6761) ",1)]),_:1})])]),s("div",de,[p(m,{data:i.value,size:"small",stripe:""},{default:f(()=>[(u(!0),d($,null,z(V.targetFields,t=>(u(),N(w,{key:t.value,prop:t.value,label:t.label,"show-overflow-tooltip":""},null,8,["prop","label"]))),128))]),_:1},8,["data"])])])):q("",!0),n.value.length>0?(u(),d("div",me,[e[7]||(e[7]=s("label",{class:"text-sm font-medium text-red-600 mb-2 block"},"\u89E3\u6790\u9519\u8BEF:",-1)),s("div",ce,[s("ul",ve,[(u(!0),d($,null,z(n.value,(t,k)=>(u(),d("li",{key:k,class:"mb-1"}," \u7B2C "+h(t.row)+" \u884C: "+h(t.message),1))),128))])])])):q("",!0)])}}}),[["__scopeId","data-v-73b1c598"]]);export{ge as default};
