import{d as c,j as o,f as d,o as n,c as u,k as e,w as f,u as t,x as y,h as _,$ as v,F as b,L as x}from"./index-C8b06LRn.js";import{_ as h}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as j}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as g}from"./index-CkzUfjB7.js";import{b as k}from"./index-C78nCjiS.js";import"./el-card-CaOo8U9P.js";const w=c({name:"InfraAdminServer",__name:"index",setup(S){const s=o(!0),r=o("https://sj.iaa360.cn:13141/admin/applications");return d(async()=>{try{const a=await k("url.spring-boot-admin");a&&a.length>0&&(r.value=a)}finally{s.value=!1}}),(a,A)=>{const i=g,m=j,l=h,p=x;return n(),u(b,null,[e(i,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(l,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[t(s)?v("",!0):y((n(),_(m,{key:0,src:t(r)},null,8,["src"])),[[p,t(s)]])]),_:1})],64)}}});export{w as default};
