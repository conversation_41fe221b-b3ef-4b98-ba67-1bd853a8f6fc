import{aG as a}from"./index-C8b06LRn.js";const e={getDeptIndicatorPage:async t=>await a.get({url:"/hr/dept-indicator/page",params:t}),getDeptIndicatorList:async t=>await a.get({url:"/hr/dept-indicator/list",params:t}),getDeptIndicator:async t=>await a.get({url:"/hr/dept-indicator/get",params:t}),saveDeptIndicator:async t=>await a.post({url:"/hr/dept-indicator/save",data:t}),deleteDeptIndicator:async t=>await a.delete({url:"/hr/dept-indicator/delete",params:t}),exportDeptIndicator:async t=>await a.download({url:"/hr/dept-indicator/export-excel",params:t}),getInformationList:t=>a.get({url:"/hr/dept-indicator/list-information",params:t}),updateInformation:t=>a.post({url:"/hr/dept-indicator/update-information",data:t}),updateDeptLeader:t=>a.post({url:"/hr/dept-indicator/update-dept-leader",data:t})};export{e as D};
