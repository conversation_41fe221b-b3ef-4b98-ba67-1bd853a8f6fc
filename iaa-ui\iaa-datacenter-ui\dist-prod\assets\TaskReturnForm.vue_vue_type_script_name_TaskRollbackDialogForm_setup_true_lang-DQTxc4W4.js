import{d as U,j as o,o as f,h as c,w as t,k as n,v as g,u as l,x as q,c as A,g as C,F as j,m as z,y as B,z as H,A as I,B as L,S,I as E,H as G,L as J}from"./index-C8b06LRn.js";import{_ as M}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{b as N,c as O}from"./index-BwETMpJ2.js";const P=U({__name:"TaskReturnForm",emits:["success"],setup(Q,{expose:k,emit:_}){const v=B(),i=o(!1),u=o(!1),s=o({id:"",targetTaskDefinitionKey:void 0,reason:""}),b=o({targetTaskDefinitionKey:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u56DE\u9000\u8282\u70B9",trigger:"change"}],reason:[{required:!0,message:"\u56DE\u9000\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=o(),m=o([]);k({open:async r=>{if(m.value=await N(r),m.value.length===0)return v.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u56DE\u9000\u7684\u8282\u70B9"),!1;i.value=!0,K(),s.value.id=r}});const V=_,w=async()=>{if(d&&await d.value.validate()){u.value=!0;try{await O(s.value),v.success("\u56DE\u9000\u6210\u529F"),i.value=!1,V("success")}finally{u.value=!1}}},K=()=>{var r;s.value={id:"",targetTaskDefinitionKey:void 0,reason:""},(r=d.value)==null||r.resetFields()};return(r,e)=>{const D=H,h=I,p=L,T=S,x=E,y=G,F=M,R=J;return f(),c(F,{modelValue:l(i),"onUpdate:modelValue":e[3]||(e[3]=a=>z(i)?i.value=a:null),title:"\u56DE\u9000\u4EFB\u52A1",width:"500"},{footer:t(()=>[n(y,{disabled:l(u),type:"primary",onClick:w},{default:t(()=>e[4]||(e[4]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),n(y,{onClick:e[2]||(e[2]=a=>i.value=!1)},{default:t(()=>e[5]||(e[5]=[g("\u53D6 \u6D88")])),_:1})]),default:t(()=>[q((f(),c(x,{ref_key:"formRef",ref:d,model:l(s),rules:l(b),"label-width":"110px"},{default:t(()=>[n(p,{label:"\u9000\u56DE\u8282\u70B9",prop:"targetTaskDefinitionKey"},{default:t(()=>[n(h,{modelValue:l(s).targetTaskDefinitionKey,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).targetTaskDefinitionKey=a),clearable:"",style:{width:"100%"}},{default:t(()=>[(f(!0),A(j,null,C(l(m),a=>(f(),c(D,{key:a.taskDefinitionKey,label:a.name,value:a.taskDefinitionKey},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(p,{label:"\u56DE\u9000\u7406\u7531",prop:"reason"},{default:t(()=>[n(T,{modelValue:l(s).reason,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).reason=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u56DE\u9000\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,l(u)]])]),_:1},8,["modelValue"])}}});export{P as _};
