import{d as J,y as L,j as s,r as M,o as f,h as w,w as l,k as a,u as o,v,c as N,F as Q,g as D,M as V,x as E,K as O,m as P,z as W,A as X,B as Y,S as Z,G as $,H as ee,I as ae,J as le,ab as oe,L as te}from"./index-C8b06LRn.js";import{_ as de}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{b as se,c as ne}from"./index-CAhgYtFg.js";import{g as ue}from"./index-BqFsG9Sv.js";const me=J({name:"InfraCodegenImportTable",__name:"ImportTable",emits:["success"],setup(re,{expose:x,emit:S}){const k=L(),n=s(!1),_=s(!0),g=s([]),t=M({name:void 0,comment:void 0,dataSourceConfigId:0}),R=s(),m=s([]),u=async()=>{_.value=!0;try{g.value=await se(t)}finally{_.value=!1}},U=async()=>{t.name=void 0,t.comment=void 0,t.dataSourceConfigId=m.value[0].id,await u()};x({open:async()=>{m.value=await ue(),t.dataSourceConfigId=m.value[0].id,n.value=!0,await u()}});const y=()=>{n.value=!1,r.value=[]},h=s(),r=s([]),K=c=>{var e;(e=o(h))==null||e.toggleRowSelection(c)},T=c=>{r.value=c.map(e=>e.name)},j=async()=>{await ne({dataSourceConfigId:t.dataSourceConfigId,tableNames:r.value}),k.success("\u5BFC\u5165\u6210\u529F"),F("success"),y()},F=S;return(c,e)=>{const q=W,z=X,i=Y,C=Z,I=$,p=ee,A=ae,b=le,B=oe,G=de,H=te;return f(),w(G,{modelValue:o(n),"onUpdate:modelValue":e[3]||(e[3]=d=>P(n)?n.value=d:null),title:"\u5BFC\u5165\u8868",width:"800px"},{footer:l(()=>[a(p,{disabled:o(r).length===0,type:"primary",onClick:j},{default:l(()=>e[6]||(e[6]=[v(" \u5BFC\u5165 ")])),_:1},8,["disabled"]),a(p,{onClick:y},{default:l(()=>e[7]||(e[7]=[v("\u5173\u95ED")])),_:1})]),default:l(()=>[a(A,{ref_key:"queryFormRef",ref:R,inline:!0,model:o(t),"label-width":"68px"},{default:l(()=>[a(i,{label:"\u6570\u636E\u6E90",prop:"dataSourceConfigId"},{default:l(()=>[a(z,{modelValue:o(t).dataSourceConfigId,"onUpdate:modelValue":e[0]||(e[0]=d=>o(t).dataSourceConfigId=d),class:"!w-240px",placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u6E90"},{default:l(()=>[(f(!0),N(Q,null,D(o(m),d=>(f(),w(q,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u8868\u540D\u79F0",prop:"name"},{default:l(()=>[a(C,{modelValue:o(t).name,"onUpdate:modelValue":e[1]||(e[1]=d=>o(t).name=d),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u540D\u79F0",onKeyup:V(u,["enter"])},null,8,["modelValue"])]),_:1}),a(i,{label:"\u8868\u63CF\u8FF0",prop:"comment"},{default:l(()=>[a(C,{modelValue:o(t).comment,"onUpdate:modelValue":e[2]||(e[2]=d=>o(t).comment=d),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u63CF\u8FF0",onKeyup:V(u,["enter"])},null,8,["modelValue"])]),_:1}),a(i,null,{default:l(()=>[a(p,{onClick:u},{default:l(()=>[a(I,{class:"mr-5px",icon:"ep:search"}),e[4]||(e[4]=v(" \u641C\u7D22 "))]),_:1}),a(p,{onClick:U},{default:l(()=>[a(I,{class:"mr-5px",icon:"ep:refresh"}),e[5]||(e[5]=v(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),a(B,null,{default:l(()=>[E((f(),w(o(O),{ref_key:"tableRef",ref:h,data:o(g),height:"260px",onRowClick:K,onSelectionChange:T},{default:l(()=>[a(b,{type:"selection",width:"55"}),a(b,{"show-overflow-tooltip":!0,label:"\u8868\u540D\u79F0",prop:"name"}),a(b,{"show-overflow-tooltip":!0,label:"\u8868\u63CF\u8FF0",prop:"comment"})]),_:1},8,["data"])),[[H,o(_)]])]),_:1})]),_:1},8,["modelValue"])}}});export{me as _};
