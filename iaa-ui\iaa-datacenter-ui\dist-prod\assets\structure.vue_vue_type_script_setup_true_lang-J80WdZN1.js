import{_ as J}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as K,j as h,y as L,f as Q,aO as W,u as a,aF as T,T as X,o as p,c as V,k as o,w as d,x as Z,h as b,v as z,F as C,g as w,N as Y,l as r,di as ee,t as le,dk as ae,dl as _,d2 as te,ax as oe,H as de,ap as ie,aq as ue,C as ne,z as se,A as re,aC as me,S as ce}from"./index-C8b06LRn.js";import{C as A}from"./index-B2m4kQ_G.js";import pe from"./DetailForm-BVwfkXTG.js";import ve from"./CustomForm-BmRVAS9-.js";import{c as fe}from"./vxeCustom-D2Re1O-c.js";import{d as ge}from"./download-D5Lb_h0f.js";import{c as he}from"./permission-DVzqLl93.js";const De={class:"h-[calc(100vh-260px)]"},Ve=K({__name:"structure",setup(be){const N=h(),x=h(),R=h(),S=h(),t=h({pageNo:1,pageSize:100,type:"structure",seller:void 0,businessDate:void 0,customerName:void 0,description:void 0,custom:void 0,itemCode:void 0,itemName:void 0,spec:void 0,planReceiptDate:void 0,actualReceiptDate:void 0,planDesignDate:void 0,actualDesignDate:void 0,planTestingDate:void 0,actualTestingDate:void 0,planAdmitDate:void 0,actualAdmitDate:void 0,planChangeDate:void 0,actualChangeDate:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,taskStatus:void 0,remark:void 0,showAll:!0,overdue:void 0,overdueType:1,receipt:void 0,design:void 0,test:void 0,complete:void 0,admit:void 0}),F=h([]),B=h(0),k=h(!1),U=async()=>{k.value=!0;try{const u=await A.pageDetail(t.value);F.value=u.list,B.value=u.total}finally{k.value=!1}},i=()=>{t.value.pageNo=1,U()},P=({row:u,column:e})=>{let m,f,g;if(e.field.includes("plan")){if(!u[e.field]&&!u[e.field.replace("plan","actual")])return{};m=_(u[e.field]),f=u[e.field.replace("plan","actual")]?_(u[e.field.replace("plan","actual")]):_(),g=u[e.field.replace("plan","actual")]}else{if(!e.field.includes("actual"))return{};if(!u[u[e.field.replace("actual","plan")]]&&!u[e.field])return{};m=_(u[e.field.replace("actual","plan")]),f=u[e.field]?_(u[e.field]):_(),g=u[e.field]}const c=m.startOf("day"),n=f.startOf("day"),v=c.diff(n,"days");return g?v>=0?{backgroundColor:"var(--el-color-success-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"var(--el-color-error-light-3)",color:"#ffffff",cursor:"pointer"}:v>=2?{cursor:"pointer"}:v>=0?{backgroundColor:"var(--el-color-warning-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"var(--el-color-error-light-3)",color:"#ffffff",cursor:"pointer"}},$=()=>{t.value={pageNo:1,pageSize:100,type:"structure",seller:void 0,businessDate:void 0,customerName:void 0,description:void 0,custom:void 0,itemCode:void 0,itemName:void 0,spec:void 0,planReceiptDate:void 0,actualReceiptDate:void 0,planDesignDate:void 0,actualDesignDate:void 0,planTestingDate:void 0,actualTestingDate:void 0,planAdmitDate:void 0,actualAdmitDate:void 0,planChangeDate:void 0,actualChangeDate:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,taskStatus:void 0,remark:void 0,showAll:!0,overdue:void 0,overdueType:1,receipt:void 0,design:void 0,test:void 0,complete:void 0,admit:void 0},i()},O=async({options:u})=>{const e=await A.exportDetail({...t.value,exportColumns:u.columns.map(m=>m.field)});ge.excel(e,u.filename+".xlsx")},M=L(),H=async({menu:u,row:e,column:m})=>{var c;const f=(c=a(x))==null?void 0:c.getCheckboxRecords();if(f.length===0)return void M.error("\u8BF7\u9009\u62E9\u8981\u6279\u91CF\u8BBE\u7F6E\u7684\u884C\uFF01");if(!m.field.includes("plan")&&!m.field.includes("actual"))return void M.error("\u5F53\u524D\u5217\u4E0D\u80FD\u4FEE\u6539");let g="";if(f.filter(n=>n.id!==e.id).some(n=>n[m.field])){const{value:n}=await te.prompt("\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0","\u4FEE\u6539\u539F\u56E0",{confirmButtonText:"\u786E\u8BA4",cancelButtonText:"\u53D6\u6D88",inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});g=n}if(u.code==="batchBottom"){for(const n of f){if(n.id===e.id)continue;const v=oe(n[m.field]);n[m.field]=e[m.field],await A.saveDetail({...n,modifyInfo:g?`${m.title}\u4ECE${v}\u4FEE\u6539\u4E3A${e[m.field]}`:"",reason:g})}M.success("\u6279\u91CF\u8BBE\u7F6E\u6210\u529F")}};return Q(()=>{i(),W(()=>{var u;(u=a(x))==null||u.connect(a(N))})}),(u,e)=>{const m=de,f=ie,g=ue,c=ne,n=se,v=re,j=me,q=T("vxe-toolbar"),s=T("vxe-column"),D=ce,E=T("vxe-table"),I=J,G=X("hasPermi");return p(),V(C,null,[o(q,{ref_key:"toolbarRef",ref:N,custom:"",size:"mini",export:""},{buttons:d(()=>[Z((p(),b(m,{type:"danger",size:"small",plain:"",onClick:e[0]||(e[0]=l=>{var y;return(y=a(S))==null?void 0:y.openForm()})},{default:d(()=>e[32]||(e[32]=[z("\u81EA\u5B9A\u4E49\u4EFB\u52A1")])),_:1})),[[G,["order-bom:task:save-custom-structure"]]]),o(m,{type:"primary",size:"small",plain:"",onClick:i},{default:d(()=>e[33]||(e[33]=[z("\u67E5\u8BE2")])),_:1}),o(m,{type:"warning",size:"small",plain:"",onClick:$},{default:d(()=>e[34]||(e[34]=[z("\u6E05\u7A7A\u7B5B\u9009")])),_:1}),o(g,{size:"small",class:"ml-10px",modelValue:a(t).overdueType,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).overdueType=l)},{default:d(()=>[o(f,{label:"\u81F3\u4ECA",value:1}),o(f,{label:"\u5F53\u5929",value:2})]),_:1},8,["modelValue"]),o(c,{modelValue:a(t).overdue,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).overdue=l),class:"ml-10px !w-100px",size:"small",placeholder:"\u672A\u5B8C\u6210\u6570\u636E",onChange:i,"value-format":"YYYY-MM-DD"},null,8,["modelValue"]),o(v,{placeholder:"\u63A5\u6536\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).receipt,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).receipt=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),V(C,null,w(a(Y)("eng_bom_status"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u8BBE\u8BA1\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).design,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).design=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),V(C,null,w(a(Y)("eng_bom_status"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u6253\u6837\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).test,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).test=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),V(C,null,w(a(Y)("eng_bom_status"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u627F\u8BA4\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).admit,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).admit=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),V(C,null,w(a(Y)("eng_bom_status"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(v,{placeholder:"\u5B8C\u6210\u65E5\u671F",class:"ml-10px !w-100px",size:"small",modelValue:a(t).complete,"onUpdate:modelValue":e[7]||(e[7]=l=>a(t).complete=l),clearable:"",onChange:i},{default:d(()=>[(p(!0),V(C,null,w(a(Y)("eng_bom_status"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),tools:d(()=>[o(j,{"active-text":"\u9690\u85CF\u5DF2\u5B8C\u6210","inactive-text":"\u663E\u793A\u6240\u6709",size:"small",modelValue:a(t).showAll,"onUpdate:modelValue":e[8]||(e[8]=l=>a(t).showAll=l),onChange:i},null,8,["modelValue"])]),_:1},512),r("div",De,[o(E,{"row-config":{height:34,isCurrent:!0,isHover:!0},"row-style":{cursor:"pointer"},id:"structureTable","custom-config":a(fe),"header-cell-style":{padding:0,height:"30px"},"column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},align:"center",border:"","show-overflow":"",height:"100%",data:a(F),ref_key:"tableRef",ref:x,size:"small","cell-style":P,onCellClick:e[29]||(e[29]=l=>{var y;return(y=a(R))==null?void 0:y.openForm(l.row)}),loading:a(k),"export-config":{remote:!0,exportMethod:O},"checkbox-config":{labelField:"check",range:!0},"menu-config":{enabled:a(he)(["order-bom:task:structure-edit"]),body:{options:[[{code:"batchBottom",name:"\u6279\u91CF\u8BBE\u7F6E"}]]}},onMenuClick:H},{default:d(()=>[o(s,{type:"checkbox",width:"50",field:"check",align:"center",fixed:"left"}),o(s,{field:"businessDate",width:"200",title:"\u65E5\u671F"},{header:d(()=>[e[35]||(e[35]=r("div",null,"\u65E5\u671F",-1)),o(c,{modelValue:a(t).businessDate,"onUpdate:modelValue":e[9]||(e[9]=l=>a(t).businessDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"},{header:d(()=>[e[36]||(e[36]=r("div",null,"\u4E1A\u52A1\u5458",-1)),o(D,{modelValue:a(t).seller,"onUpdate:modelValue":e[10]||(e[10]=l=>a(t).seller=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"customerName",width:"120",title:"\u5BA2\u6237"},{header:d(()=>[e[37]||(e[37]=r("div",null,"\u5BA2\u6237",-1)),o(D,{modelValue:a(t).customerName,"onUpdate:modelValue":e[11]||(e[11]=l=>a(t).customerName=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"itemCode",width:"120",title:"\u54C1\u53F7"},{header:d(()=>[e[38]||(e[38]=r("div",null,"\u54C1\u53F7",-1)),o(D,{modelValue:a(t).itemCode,"onUpdate:modelValue":e[12]||(e[12]=l=>a(t).itemCode=l),clearable:"",placeholder:"\u5DE6\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"itemName",width:"140",title:"\u54C1\u540D"},{header:d(()=>[e[39]||(e[39]=r("div",null,"\u54C1\u540D",-1)),o(D,{modelValue:a(t).itemName,"onUpdate:modelValue":e[13]||(e[13]=l=>a(t).itemName=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"spec","min-width":"200",title:"\u89C4\u683C"},{header:d(()=>[e[40]||(e[40]=r("div",null,"\u89C4\u683C",-1)),o(D,{modelValue:a(t).spec,"onUpdate:modelValue":e[14]||(e[14]=l=>a(t).spec=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{header:d(()=>[e[41]||(e[41]=r("div",null,"\u5B9A\u5236\u5185\u5BB9",-1)),o(v,{size:"small",modelValue:a(t).custom,"onUpdate:modelValue":e[15]||(e[15]=l=>a(t).custom=l),clearable:"",multiple:"","collapse-tags":""},{default:d(()=>[(p(!0),V(C,null,w(a(ee)("eng_structure_dict"),l=>(p(),b(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:d(({row:l})=>[z(le(a(ae)("eng_structure_dict",l.custom)),1)]),_:1}),o(s,{field:"description",width:"200",title:"\u4EFB\u52A1"},{header:d(()=>[e[42]||(e[42]=r("div",null,"\u4EFB\u52A1",-1)),o(D,{modelValue:a(t).description,"onUpdate:modelValue":e[16]||(e[16]=l=>a(t).description=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"},{header:d(()=>[e[43]||(e[43]=r("div",null,"\u8BA1\u5212\u63A5\u6536\u65E5\u671F",-1)),o(c,{modelValue:a(t).planReceiptDate,"onUpdate:modelValue":e[17]||(e[17]=l=>a(t).planReceiptDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"},{header:d(()=>[e[44]||(e[44]=r("div",null,"\u5B9E\u9645\u63A5\u6536\u65E5\u671F",-1)),o(c,{modelValue:a(t).actualReceiptDate,"onUpdate:modelValue":e[18]||(e[18]=l=>a(t).actualReceiptDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"},{header:d(()=>[e[45]||(e[45]=r("div",null,"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F",-1)),o(c,{modelValue:a(t).planDesignDate,"onUpdate:modelValue":e[19]||(e[19]=l=>a(t).planDesignDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"},{header:d(()=>[e[46]||(e[46]=r("div",null,"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F",-1)),o(c,{modelValue:a(t).actualDesignDate,"onUpdate:modelValue":e[20]||(e[20]=l=>a(t).actualDesignDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"},{header:d(()=>[e[47]||(e[47]=r("div",null,"\u8BA1\u5212\u6253\u6837\u65E5\u671F",-1)),o(c,{modelValue:a(t).planTestingDate,"onUpdate:modelValue":e[21]||(e[21]=l=>a(t).planTestingDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"},{header:d(()=>[e[48]||(e[48]=r("div",null,"\u5B9E\u9645\u6253\u6837\u65E5\u671F",-1)),o(c,{modelValue:a(t).actualTestingDate,"onUpdate:modelValue":e[22]||(e[22]=l=>a(t).actualTestingDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u627F\u8BA4\u65E5\u671F"},{header:d(()=>[e[49]||(e[49]=r("div",null,"\u8BA1\u5212\u627F\u8BA4\u65E5\u671F",-1)),o(c,{modelValue:a(t).planAdmitDate,"onUpdate:modelValue":e[23]||(e[23]=l=>a(t).planAdmitDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u627F\u8BA4\u65E5\u671F"},{header:d(()=>[e[50]||(e[50]=r("div",null,"\u5B9E\u9645\u627F\u8BA4\u65E5\u671F",-1)),o(c,{modelValue:a(t).actualAdmitDate,"onUpdate:modelValue":e[24]||(e[24]=l=>a(t).actualAdmitDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"},{header:d(()=>[e[51]||(e[51]=r("div",null,"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F",-1)),o(c,{modelValue:a(t).planCompleteDate,"onUpdate:modelValue":e[25]||(e[25]=l=>a(t).planCompleteDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"},{header:d(()=>[e[52]||(e[52]=r("div",null,"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F",-1)),o(c,{modelValue:a(t).actualCompleteDate,"onUpdate:modelValue":e[26]||(e[26]=l=>a(t).actualCompleteDate=l),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:i},null,8,["modelValue"])]),_:1}),o(s,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[e[53]||(e[53]=r("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),o(v,{modelValue:a(t).taskStatus,"onUpdate:modelValue":e[27]||(e[27]=l=>a(t).taskStatus=l),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:U},{default:d(()=>[o(n,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),o(n,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),o(n,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),o(n,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),o(s,{field:"remark",width:"200",title:"\u5907\u6CE8"},{header:d(()=>[e[54]||(e[54]=r("div",null,"\u5907\u6CE8",-1)),o(D,{modelValue:a(t).remark,"onUpdate:modelValue":e[28]||(e[28]=l=>a(t).remark=l),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:i},null,8,["modelValue"])]),_:1})]),_:1},8,["custom-config","data","loading","export-config","menu-config"])]),o(I,{limit:a(t).pageSize,"onUpdate:limit":e[30]||(e[30]=l=>a(t).pageSize=l),page:a(t).pageNo,"onUpdate:page":e[31]||(e[31]=l=>a(t).pageNo=l),total:a(B),onPagination:U,size:"small"},null,8,["limit","page","total"]),o(pe,{ref_key:"detailFormRef",ref:R,onSuccess:i},null,512),o(ve,{ref_key:"customFormRef",ref:S,type:"structure",onSuccess:i},null,512)],64)}}});export{Ve as _};
