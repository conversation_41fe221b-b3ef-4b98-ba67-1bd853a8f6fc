import{_ as We}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as Ie}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as Ae}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as Te}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{aG as W,d as De,j as c,r as H,y as Re,f as ze,aO as Fe,e6 as Me,aF as S,T as Ee,o as z,c as Se,k as r,w as s,x as N,h as j,v,u as d,l as K,b4 as Ne,t as F,N as je,D as Be,m as Z,F as Le,aH as Pe,aw as ee,dV as L,d2 as ae,H as Oe,_ as $e}from"./index-C8b06LRn.js";import{f as te}from"./dateUtil-D9m5ek6U.js";import{d as He}from"./download-D5Lb_h0f.js";import Ke from"./PasteParser-CXOSTR0p.js";import{t as I,s as qe,u as le}from"./Filter-Dzz2caxb.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";const Ge=async p=>await W.post({url:"/report/standard/page",data:p}),Ye=async p=>await W.post({url:"/report/standard/create",data:p}),Je=async p=>await W.post({url:"/report/standard/batch-create",data:p}),Qe=async p=>await W.post({url:"/report/standard/update-batch",data:p}),re=async p=>await W.post({url:"/report/standard/delete",data:p}),Xe=async p=>await W.download({url:"/report/standard/export-excel",params:p}),Ze=async()=>await W.get({url:"/report/standard/getAllModels"}),ea={class:"h-[calc(100vh-260px)]"},aa=$e(De({__name:"index",setup(p){const ie=c([{data:""}]),oe=c([{data:""}]),ne=c([{data:""}]),de=c([{data:""}]),se=c([{data:""}]),ue=c([{data:""}]),ce=c([{data:""}]),fe=c([{data:{}}]),me=c([{data:{}}]),q=c([]),B=c([]),P=c(!1),pe=H({name:"$select",props:{filterable:!0,clearable:!0,options:B.value}}),G=async()=>{if(!(B.value.length>0))try{P.value=!0;const t=await Ze();B.value=t.map(e=>({value:e,label:e}))}catch{m.error("\u52A0\u8F7D\u578B\u53F7\u9009\u9879\u5931\u8D25")}finally{P.value=!1}},m=Re(),_=c(!0),h=c([]),Y=c(0),k=H({pageNo:1,pageSize:30,model:void 0,classification:void 0,assembledWork:void 0,packagingWork:void 0,standardWork:void 0,throughMembrane:void 0,capsule:void 0,remark:void 0,createUser:void 0,updateUser:void 0,createTime:[],updateTime:[]}),J=t=>t&&t.length!==0?q.value.filter(e=>t.includes(e.id)).map(e=>e.nickname).join(","):"";c();const O=c(!1),ge=t=>{const e=["classification"],o={};t.filterList.forEach(i=>{const{field:l,values:n,datas:f}=i;e.includes(l)&&n.length>0?o[l]=n:f.length>0&&(o[l]=f[0])}),Object.keys(k).forEach(i=>{["pageNo","pageSize"].includes(i)||t.filterList.some(l=>l.field===i)||(k[i]=void 0)}),Object.assign(k,o),b()},we=[{label:"\u578B\u53F7",value:"model"},{label:"\u5206\u7C7B",value:"classification"},{label:"\u7EC4\u88C5\u6807\u51C6\u5DE5\u65F6(\u5206)",value:"assembledWork"},{label:"\u5305\u88C5\u6807\u51C6\u5DE5\u65F6\uFF08\u5206\u949F/\u53F0\uFF09",value:"packagingWork"},{label:"\u6807\u51C6\u5DE5\u65F6\uFF08\u7EC4\u88C5+\u5305\u88C5\uFF09",value:"standardWork"},{label:"\u8FC7\u819C",value:"throughMembrane"},{label:"\u7EC4\u5305\u819C",value:"capsule"},{label:"\u5907\u6CE8",value:"remark"}],ve=["model","classification","assembledWork","packagingWork","standardWork","throughMembrane","capsule","remark"];c(!0);const x=t=>{const e=w.value;if(e)return e.updateStatus(t)},he=async()=>{const t=w.value;if(t){const{row:e}=await t.insert();t.setEditRow(e),await G()}},Q=async()=>{const t=w.value;if(t){const e=t.getUpdateRecords(),o=t.getInsertRecords(),i=e.map(n=>ee(n)),l=o.map(n=>{const f=ee(n),{id:g,...U}=f;return U});if(l.length>0&&l.find(n=>!n.model||n.model.trim()===""))return void m.error("\u578B\u53F7\u548C\u5206\u7C7B\u4E3A\u5FC5\u586B\u9879\uFF0C\u8BF7\u586B\u5199\u5B8C\u6574");if(i.length>0&&i.find(n=>!n.model||n.model.trim()===""))return void m.error("\u578B\u53F7\u4E3A\u5FC5\u586B\u9879\uFF0C\u8BF7\u586B\u5199\u5B8C\u6574");if(i.length>0||l.length>0)try{_.value=!0;const n=[];if(i.length>0){if(!L(["standard:work:update"]))return m.error("\u60A8\u6CA1\u6709\u4FEE\u6539\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01"),void await b();n.push(Qe(i))}if(l.length>0){if(!L(["standard:work:create"]))return m.error("\u60A8\u6CA1\u6709\u65B0\u589E\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01"),void await b();n.push(Ye(l))}await Promise.all(n),_.value=!1,await b(),m.success("\u4FDD\u5B58\u6210\u529F")}finally{_.value=!1}else m.info("\u6CA1\u6709\u66F4\u65B0\u6570\u636E")}},ke=t=>t.id&&typeof t.id=="number",be=async t=>{const e=await Je(t);if(Array.isArray(e)){const o=e.map(i=>i.replace(/;\s*$/,"")).join("<br>");ae.alert(o,"\u63D0\u793A",{dangerouslyUseHTMLString:!0})}A.value=!1,b()},A=c(!1),w=c(),b=async()=>{_.value=!0;try{const t=await Ge(k);h.value=t.list,Y.value=t.total}finally{_.value=!1}},ye=H({body:{options:[[{code:"batchDelete",name:"\u6279\u91CF\u5220\u9664"}]]}}),T=c(!1),M=c([]),_e=({menu:t})=>{const e=w.value;if(e&&t.code==="batchDelete"){const o=e.getCheckboxRecords();if(o.length===0)return m.alertError("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6570\u636E"),void(M.value=[]);M.value=o,T.value=!0}},Ve=async()=>{try{await m.exportConfirm(),O.value=!0;const t=await Xe(k);He.excel(t,"\u6807\u51C6\u5DE5\u65F6\u5F55\u5165.xls")}catch{}finally{O.value=!1}},V=["model","classification","assembledWork","packagingWork","standardWork","throughMembrane","capsule","remark"],xe=t=>{const e=w.value;if(!e)return;const o=e.getActiveRecord();if(!o)return;const{row:i,column:l}=o,n=h.value.findIndex(f=>f.id===i.id);if(["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Enter","Tab"].includes(t.key))switch(t.key){case"ArrowLeft":D(e,i,l,"left"),t.preventDefault();break;case"Tab":t.shiftKey?D(e,i,l,"left"):D(e,i,l,"right"),t.preventDefault();break;case"ArrowRight":D(e,i,l,"right"),t.preventDefault();break;case"ArrowUp":D(e,i,l,"up",n),t.preventDefault();break;case"ArrowDown":case"Enter":D(e,i,l,"down",n),t.preventDefault()}},D=(t,e,o,i,l=-1)=>{try{let n=e,f=o;const g=V.indexOf(o.field);switch(i){case"left":if(g>0){const U=V[g-1];f=t.getColumnByField(U)}else{if(!(l>0))return;n=h.value[l-1],f=t.getColumnByField(V[V.length-1])}break;case"right":if(g<V.length-1){const U=V[g+1];f=t.getColumnByField(U)}else{if(!(l<h.value.length-1))return;n=h.value[l+1],f=t.getColumnByField(V[0])}break;case"up":if(!(l>0))return;n=h.value[l-1];break;case"down":if(!(l<h.value.length-1))return;n=h.value[l+1]}n&&f&&V.includes(f.field)&&t.setEditCell(n,f.field)}catch{}};return ze(()=>{(async()=>q.value=await Pe())(),b(),Fe(()=>{w.value&&w.value.$el.setAttribute("tabindex","0")})}),Me(async(t,e,o)=>{if((()=>{if(!L(["standard:work:update"]))return!1;const i=w.value;return i?i.getUpdateRecords().length>0:!1})())try{await ae.confirm("\u60A8\u6709\u672A\u4FDD\u5B58\u7684\u4FEE\u6539\uFF0C\u662F\u5426\u4FDD\u5B58\u540E\u518D\u79BB\u5F00\uFF1F","\u63D0\u793A",{confirmButtonText:"\u4FDD\u5B58\u5E76\u79BB\u5F00",cancelButtonText:"\u4E0D\u4FDD\u5B58\u79BB\u5F00",type:"warning",distinguishCancelAndClose:!0}),await Q(),o()}catch(i){i==="cancel"?o():o(!1)}else o()}),(t,e)=>{const o=Oe,i=S("vxe-toolbar"),l=S("vxe-column"),n=S("vxe-select"),f=Te,g=S("vxe-input"),U=S("vxe-table"),Ue=Ae,Ce=Ie,X=We,E=Ee("hasPermi");return z(),Se(Le,null,[r(Ce,null,{default:s(()=>[r(i,{custom:"",ref:"toolbarRef",size:"mini"},{buttons:s(()=>[N((z(),j(o,{type:"primary",plain:"",size:"small",onClick:he},{default:s(()=>e[7]||(e[7]=[v(" \u65B0\u589E ")])),_:1})),[[E,["standard:work:create"]]]),N((z(),j(o,{type:"success",plain:"",size:"small",onClick:Q},{default:s(()=>e[8]||(e[8]=[v(" \u4FDD\u5B58 ")])),_:1})),[[E,["standard:work:update","standard:work:create"]]]),N((z(),j(o,{type:"info",plain:"",size:"small",onClick:e[0]||(e[0]=a=>A.value=!0)},{default:s(()=>e[9]||(e[9]=[v(" \u6279\u91CF\u7C98\u8D34\u89E3\u6790 ")])),_:1})),[[E,["standard:work:create"]]]),N((z(),j(o,{onClick:Ve,type:"warning",plain:"",loading:d(O),style:{"margin-left":"30px"},size:"small"},{default:s(()=>e[10]||(e[10]=[v("\u5BFC\u51FA")])),_:1},8,["loading"])),[[E,["standard:work:export"]]])]),_:1},512),K("div",ea,[r(U,{"row-config":{height:25,keyField:"id"},ref_key:"tableRef",ref:w,data:d(h),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:d(_),"menu-config":d(ye),onMenuClick:_e,"checkbox-config":{reserve:!0,highlight:!0,range:!0},"edit-config":{trigger:"click",mode:"cell",showStatus:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},onFilterChange:ge,onKeydown:Ne(xe,["stop"]),tabindex:"0",size:"mini"},{default:s(()=>[r(l,{type:"checkbox","min-width":"40",field:"id",fixed:"left"}),r(l,{field:"model",title:"\u578B\u53F7",width:"120",align:"left","edit-render":d(pe),filters:d(ie),"filter-render":I,fixed:"left"},{edit:s(({row:a})=>[r(n,{modelValue:a.model,"onUpdate:modelValue":u=>a.model=u,options:d(B),filterable:"",clearable:"",onChange:u=>x({row:a}),disabled:ke(a),loading:d(P),onFocus:G},null,8,["modelValue","onUpdate:modelValue","options","onChange","disabled","loading"])]),default:s(({row:a})=>[K("span",null,F(a.model),1)]),_:1},8,["edit-render","filters","filter-render"]),r(l,{title:"\u5206\u7C7B",field:"classification","min-width":"100",filters:qe,"edit-render":{name:"$select",options:d(je)(d(Be).STANDARD_TYPE),props:{value:"value",label:"label"}}},{default:s(({row:a})=>[r(f,{type:"standard_type",value:a.classification},null,8,["value"])]),_:1},8,["filters","edit-render"]),r(l,{title:"\u7EC4\u88C5",field:"assembledWork","min-width":"100","edit-render":{name:"input"},filters:d(oe),"filter-render":I},{edit:s(a=>[r(g,{type:"number",modelValue:a.row.assembledWork,"onUpdate:modelValue":u=>a.row.assembledWork=u,min:"0",onInput:u=>x(a),controls:!1},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u5305\u88C5",field:"packagingWork","min-width":"100","edit-render":{name:"input"},filters:d(ne),"filter-render":I},{edit:s(a=>[r(g,{type:"number",modelValue:a.row.packagingWork,"onUpdate:modelValue":u=>a.row.packagingWork=u,min:"0",onInput:u=>x(a),controls:!1},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u7EC4\u5305",field:"standardWork","min-width":"100","edit-render":{name:"input"},filters:d(de),"filter-render":I},{edit:s(a=>[r(g,{type:"number",modelValue:a.row.standardWork,"onUpdate:modelValue":u=>a.row.standardWork=u,min:"0",onInput:u=>x(a),controls:!1},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u5305\u819C",field:"throughMembrane","min-width":"100","edit-render":{name:"input"},filters:d(se),"filter-render":I},{edit:s(a=>[r(g,{type:"number",modelValue:a.row.throughMembrane,"onUpdate:modelValue":u=>a.row.throughMembrane=u,min:"0",onInput:u=>x(a),controls:!1},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u7EC4\u5305\u819C",field:"capsule","min-width":"100","edit-render":{name:"input"},filters:d(ue),"filter-render":I},{edit:s(a=>[r(g,{type:"number",modelValue:a.row.capsule,"onUpdate:modelValue":u=>a.row.capsule=u,onInput:u=>x(a),min:"0",controls:!1},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u5907\u6CE8",field:"remark","min-width":"100","edit-render":{name:"input"},filters:d(ce),"filter-render":I},{edit:s(a=>[r(g,{type:"text",modelValue:a.row.remark,"onUpdate:modelValue":u=>a.row.remark=u,onInput:u=>x(a)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1},8,["filters","filter-render"]),r(l,{title:"\u521B\u5EFA\u4EBA","min-width":"100",field:"creator",filters:d(fe),"filter-render":le},{default:s(({row:a})=>[v(F(J(a.creator)),1)]),_:1},8,["filters","filter-render"]),r(l,{field:"createTime","min-width":"140",title:"\u521B\u5EFA\u65F6\u95F4"},{default:s(({row:a})=>[v(F(a.createTime&&d(te)(a.createTime)),1)]),_:1}),r(l,{title:"\u4FEE\u6539\u4EBA","min-width":"100",field:"updater",filters:d(me),"filter-render":le},{default:s(({row:a})=>[v(F(J(a.updater)),1)]),_:1},8,["filters","filter-render"]),r(l,{field:"updateTime","min-width":"140",title:"\u4FEE\u6539\u65F6\u95F4"},{default:s(({row:a})=>[v(F(a.updateTime&&d(te)(a.updateTime)),1)]),_:1}),r(l,{title:"\u64CD\u4F5C","min-width":"80",fixed:"right"},{default:s(({row:a})=>[N((z(),j(o,{onClick:u=>(async C=>{try{await m.delConfirm();const R=w.value;if(typeof C=="string"){const y=R.getInsertRecords().find($=>$.id===C);return void(y&&(await R.remove(y),m.success("\u5220\u9664\u6210\u529F")))}await re([C]),m.success("\u5220\u9664\u6210\u529F"),await b()}catch{}})(a.id),link:"",type:"danger"},{default:s(()=>e[11]||(e[11]=[v("\u5220\u9664")])),_:2},1032,["onClick"])),[[E,["standard:work:delete"]]])]),_:1})]),_:1},8,["data","loading","menu-config"])]),r(Ue,{total:d(Y),page:d(k).pageNo,"onUpdate:page":e[1]||(e[1]=a=>d(k).pageNo=a),limit:d(k).pageSize,"onUpdate:limit":e[2]||(e[2]=a=>d(k).pageSize=a),onPagination:b},null,8,["total","page","limit"])]),_:1}),r(X,{title:"\u6279\u91CF\u5220\u9664\u786E\u8BA4",modelValue:d(T),"onUpdate:modelValue":e[5]||(e[5]=a=>Z(T)?T.value=a:null)},{footer:s(()=>[r(o,{type:"danger",onClick:e[3]||(e[3]=a=>(async()=>{_.value=!0;try{if(!L(["standard:work:delete"]))return void m.error("\u60A8\u6CA1\u6709\u5220\u9664\u6570\u636E\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\uFF01");{const u=w.value;if(!u)return;const C=[],R=[];if(M.value.forEach(y=>{typeof y.id=="string"?C.push(y.id):R.push(y.id)}),C.length>0){const y=u.getInsertRecords().filter($=>C.includes($.id));y.length>0&&await u.remove(y)}R.length>0&&await re(R),T.value=!1,M.value=[],await b(),m.success("\u6279\u91CF\u5220\u9664\u6210\u529F")}}catch{m.error("\u6279\u91CF\u5220\u9664\u5931\u8D25")}finally{_.value=!1}})())},{default:s(()=>e[12]||(e[12]=[v("\u786E\u8BA4\u5220\u9664")])),_:1}),r(o,{onClick:e[4]||(e[4]=a=>T.value=!1)},{default:s(()=>e[13]||(e[13]=[v("\u53D6\u6D88")])),_:1})]),default:s(()=>[K("div",null,"\u5F53\u524D\u9009\u4E2D\uFF1A"+F(d(M).length)+" \u6761\u6570\u636E\uFF0C\u786E\u8BA4\u8981\u5220\u9664\u5417\uFF1F",1)]),_:1},8,["modelValue"]),r(X,{title:"\u6570\u636E\u6279\u91CF\u7C98\u8D34\u89E3\u6790",modelValue:d(A),"onUpdate:modelValue":e[6]||(e[6]=a=>Z(A)?A.value=a:null),width:"90%","before-close":()=>A.value=!1},{default:s(()=>[r(Ke,{"target-fields":we,mappings:ve,onDataParsed:be})]),_:1},8,["modelValue","before-close"])],64)}}}),[["__scopeId","data-v-fae6a457"]]);export{aa as default};
