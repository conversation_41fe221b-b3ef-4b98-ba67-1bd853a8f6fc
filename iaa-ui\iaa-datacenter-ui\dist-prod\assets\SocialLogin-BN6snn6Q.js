import{d as Z,e as A,j as h,b as H,Y as J,r as Q,f as ee,o as M,c as ae,l as n,n as U,u as e,t as x,U as I,k as a,w as l,V as te,x as le,Z as se,h as oe,$ as ne,M as ie,v as T,W as re,p as pe,O as de,X as me,a as ce,a0 as ue,a1 as xe,a2 as E,a3 as fe,a4 as ge,a5 as he,a6 as we,a7 as ye,B as _e,a8 as ve,S as be,a9 as Fe,aa as Ve,ab as ke,I as Ne,ac as je,ad as Le,_ as Pe}from"./index-C8b06LRn.js";import{_ as Se}from"./Verify-BrbFSGD0.js";import{_ as Me}from"./XButton-BOgar_Ex.js";import{_ as q}from"./logo-BB9UjfGS.js";import{_ as Ue,a as Ie}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-YKnCp4Oa.js";import{u as b}from"./useIcon-CwemBubV.js";import{T as Te}from"./ThemeSwitch-CpOMjpIf.js";import{u as Ee,_ as qe,L as Re,a as ze}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";import{r as F}from"./formRules-Upspu04w.js";const Ce={class:"relative mx-auto h-full flex"},Oe={class:"relative flex items-center text-white"},$e={class:"text-20px font-bold"},Be={class:"h-[calc(100%-60px)] flex items-center justify-center"},De={key:"2",class:"text-3xl text-white"},Ge={key:"3",class:"mt-5 text-14px font-normal text-white"},Ke={class:"relative flex-1 p-30px dark:bg-[var(--login-bg-color)] lt-sm:p-10px overflow-x-hidden overflow-y-auto"},We={class:"flex items-center justify-between text-white at-2xl:justify-end at-xl:justify-end"},Xe={class:"flex items-center at-2xl:hidden at-xl:hidden"},Ye={class:"text-20px font-bold"},Ze={class:"flex items-center justify-end space-x-10px h-48px"},Ae={class:"m-auto h-[calc(100%-60px)] w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"},He=Pe(Z({name:"SocialLogin",__name:"SocialLogin",setup(Je){const{t:m}=pe(),r=de(),V=A(),{getPrefixCls:R}=me(),k=R("login"),z=b({icon:"ep:house"}),C=b({icon:"ep:avatar"}),O=b({icon:"ep:lock"}),N=h(),{validForm:$}=ze(N),{getLoginState:B}=Ee(),{push:D}=ce(),G=H(),_=h(!1),j=h(),K=h("blockPuzzle"),W=J(()=>e(B)===Re.LOGIN),X={tenantName:[F],username:[F],password:[F]},s=Q({isShowPassword:!1,captchaEnable:!0,tenantEnable:!1,loginForm:{tenantName:"\u828B\u9053\u6E90\u7801",username:"",password:"",captchaVerification:"",rememberMe:!1}}),L=async()=>{s.captchaEnable?j.value.show():await S({})},P=h();function w(o){return new URL(decodeURIComponent(location.href)).searchParams.get(o)??""}const S=async o=>{var t,p;_.value=!0;try{if(await(async()=>{if(s.tenantEnable){const v=await je(s.loginForm.tenantName);Le(v)}})(),!await $())return;let i=w("redirect");const c=w("type"),f=(t=r==null?void 0:r.query)==null?void 0:t.code,g=(p=r==null?void 0:r.query)==null?void 0:p.state,u={...s.loginForm},y=await ge({username:u.username,password:u.password,captchaVerification:o.captchaVerification,socialCode:f,socialState:g,socialType:c});if(!y)return;P.value=he.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),u.rememberMe?we(u):ye(),E(y),i||(i="/"),i.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):D({path:i||G.addRouters[0].path})}finally{_.value=!1,P.value.close()}};return ee(()=>{(()=>{const o=ue();o&&(s.loginForm={...s.loginForm,username:o.username?o.username:s.loginForm.username,password:o.password?o.password:s.loginForm.password,rememberMe:!!o.rememberMe,tenantName:o.tenantName?o.tenantName:s.loginForm.tenantName})})(),(async()=>{var o,t;try{const p=w("type"),i=w("redirect"),c=(o=r==null?void 0:r.query)==null?void 0:o.code,f=(t=r==null?void 0:r.query)==null?void 0:t.state,g=await xe(p,c,f);E(g),fe.push({path:i||"/"})}catch{}})()}),(o,t)=>{const p=_e,i=ve,c=be,f=Fe,g=Ve,u=ke,y=Me,v=Se,Y=Ne;return M(),ae("div",{class:U([e(k),"relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"])},[n("div",Ce,[n("div",{class:U(`${e(k)}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden overflow-x-hidden overflow-y-auto`)},[n("div",Oe,[t[6]||(t[6]=n("img",{alt:"",class:"mr-10px h-48px w-48px",src:q},null,-1)),n("span",$e,x(e(I)(e(V).getTitle)),1)]),n("div",Be,[a(te,{appear:"","enter-active-class":"animate__animated animate__bounceInLeft",tag:"div"},{default:l(()=>[t[7]||(t[7]=n("img",{key:"1",alt:"",class:"w-350px",src:Ue},null,-1)),n("div",De,x(e(m)("login.welcome")),1),n("div",Ge,x(e(m)("login.message")),1)]),_:1})])],2),n("div",Ke,[n("div",We,[n("div",Xe,[t[8]||(t[8]=n("img",{alt:"",class:"mr-10px h-48px w-48px",src:q},null,-1)),n("span",Ye,x(e(I)(e(V).getTitle)),1)]),n("div",Ze,[a(e(Te)),a(e(Ie),{class:"dark:text-white lt-xl:text-white"})])]),a(re,{appear:"","enter-active-class":"animate__animated animate__bounceInRight"},{default:l(()=>[n("div",Ae,[le(a(Y,{ref_key:"formLogin",ref:N,model:e(s).loginForm,rules:X,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[a(u,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(qe,{style:{width:"100%"}})]),_:1})]),_:1}),a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[e(s).tenantEnable?(M(),oe(p,{key:0,prop:"tenantName"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.tenantName,"onUpdate:modelValue":t[0]||(t[0]=d=>e(s).loginForm.tenantName=d),placeholder:e(m)("login.tenantNamePlaceholder"),"prefix-icon":e(z),link:"",type:"primary"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):ne("",!0)]),_:1}),a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,{prop:"username"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.username,"onUpdate:modelValue":t[1]||(t[1]=d=>e(s).loginForm.username=d),placeholder:e(m)("login.usernamePlaceholder"),"prefix-icon":e(C)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,{prop:"password"},{default:l(()=>[a(c,{modelValue:e(s).loginForm.password,"onUpdate:modelValue":t[2]||(t[2]=d=>e(s).loginForm.password=d),placeholder:e(m)("login.passwordPlaceholder"),"prefix-icon":e(O),"show-password":"",type:"password",onKeyup:t[3]||(t[3]=ie(d=>L(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px","margin-top":"-20px","margin-bottom":"-20px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(u,{justify:"space-between",style:{width:"100%"}},{default:l(()=>[a(i,{span:6},{default:l(()=>[a(f,{modelValue:e(s).loginForm.rememberMe,"onUpdate:modelValue":t[4]||(t[4]=d=>e(s).loginForm.rememberMe=d)},{default:l(()=>[T(x(e(m)("login.remember")),1)]),_:1},8,["modelValue"])]),_:1}),a(i,{offset:6,span:12},{default:l(()=>[a(g,{style:{float:"right"},type:"primary"},{default:l(()=>[T(x(e(m)("login.forgetPassword")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),a(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(p,null,{default:l(()=>[a(y,{loading:e(_),title:e(m)("login.login"),class:"w-[100%]",type:"primary",onClick:t[5]||(t[5]=d=>L())},null,8,["loading","title"])]),_:1})]),_:1}),a(v,{ref_key:"verify",ref:j,captchaType:e(K),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:S},null,8,["captchaType"])]),_:1})]),_:1},8,["model"]),[[se,e(W)]])])]),_:1})])])],2)}}}),[["__scopeId","data-v-a144f9a4"]]);export{He as default};
