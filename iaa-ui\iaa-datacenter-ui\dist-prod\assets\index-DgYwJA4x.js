import{d as Q,dm as W,p as X,y as Y,j as i,r as Z,f as ee,T as ae,o as r,c as M,k as e,w as o,u as t,M as le,F as N,g as oe,N as te,D as se,h as f,v as u,x as v,$ as ne,aJ as re,aO as ue,dn as T,S as ie,B as de,z as pe,A as ce,G as me,H as fe,I as ve,J as ye,aC as we,K as _e,L as he}from"./index-C8b06LRn.js";import{_ as ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as be}from"./index-CkzUfjB7.js";import{b as Ce,d as xe,u as ge}from"./index-BwMxOnEu.js";import{_ as Ve}from"./MenuForm.vue_vue_type_script_setup_true_lang-DUkaCNfL.js";import{C as q}from"./constants-D3f7Z3TX.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./index-Cl43piKd.js";import"./el-tree-select-E9FCZb0j.js";const Se=Q({name:"SystemMenu",__name:"index",setup(Ue){const{wsCache:E}=W(),{t:F}=X(),k=Y(),b=i(!0),R=i([]),d=Z({name:void 0,status:void 0}),O=i(),C=i(!1),x=i(!0),w=async()=>{b.value=!0;try{const _=await Ce(d);R.value=re(_)}finally{b.value=!1}},g=()=>{w()},L=()=>{O.value.resetFields(),g()},A=i(),V=(_,a,h)=>{A.value.open(_,a,h)},D=()=>{x.value=!1,C.value=!C.value,ue(()=>{x.value=!0})},I=async()=>{try{await k.confirm("\u5373\u5C06\u66F4\u65B0\u7F13\u5B58\u5237\u65B0\u6D4F\u89C8\u5668\uFF01","\u5237\u65B0\u83DC\u5355\u7F13\u5B58"),E.delete(T.USER),E.delete(T.ROLE_ROUTERS),location.reload()}catch{}},S=i({});return ee(()=>{w()}),(_,a)=>{const h=be,J=ie,U=de,K=pe,j=ce,p=me,s=fe,z=ve,B=ke,n=ye,G=we,H=_e,y=ae("hasPermi"),P=he;return r(),M(N,null,[e(h,{title:"\u529F\u80FD\u6743\u9650",url:"https://doc.iocoder.cn/resource-permission"}),e(h,{title:"\u83DC\u5355\u8DEF\u7531",url:"https://doc.iocoder.cn/vue3/route/"}),e(B,null,{default:o(()=>[e(z,{ref_key:"queryFormRef",ref:O,inline:!0,model:t(d),class:"-mb-15px","label-width":"68px"},{default:o(()=>[e(U,{label:"\u83DC\u5355\u540D\u79F0",prop:"name"},{default:o(()=>[e(J,{modelValue:t(d).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(d).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u83DC\u5355\u540D\u79F0",onKeyup:le(g,["enter"])},null,8,["modelValue"])]),_:1}),e(U,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[e(j,{modelValue:t(d).status,"onUpdate:modelValue":a[1]||(a[1]=l=>t(d).status=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u83DC\u5355\u72B6\u6001"},{default:o(()=>[(r(!0),M(N,null,oe(t(te)(t(se).COMMON_STATUS),l=>(r(),f(K,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(U,null,{default:o(()=>[e(s,{onClick:g},{default:o(()=>[e(p,{class:"mr-5px",icon:"ep:search"}),a[3]||(a[3]=u(" \u641C\u7D22 "))]),_:1}),e(s,{onClick:L},{default:o(()=>[e(p,{class:"mr-5px",icon:"ep:refresh"}),a[4]||(a[4]=u(" \u91CD\u7F6E "))]),_:1}),v((r(),f(s,{plain:"",type:"primary",onClick:a[2]||(a[2]=l=>V("create"))},{default:o(()=>[e(p,{class:"mr-5px",icon:"ep:plus"}),a[5]||(a[5]=u(" \u65B0\u589E "))]),_:1})),[[y,["system:menu:create"]]]),e(s,{plain:"",type:"danger",onClick:D},{default:o(()=>[e(p,{class:"mr-5px",icon:"ep:sort"}),a[6]||(a[6]=u(" \u5C55\u5F00/\u6298\u53E0 "))]),_:1}),e(s,{plain:"",onClick:I},{default:o(()=>[e(p,{class:"mr-5px",icon:"ep:refresh"}),a[7]||(a[7]=u(" \u5237\u65B0\u83DC\u5355\u7F13\u5B58 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(B,null,{default:o(()=>[t(x)?v((r(),f(H,{key:0,data:t(R),"default-expand-all":t(C),"row-key":"id"},{default:o(()=>[e(n,{"show-overflow-tooltip":!0,label:"\u83DC\u5355\u540D\u79F0",prop:"name",width:"250"}),e(n,{align:"center",label:"\u56FE\u6807",prop:"icon",width:"100"},{default:o(l=>[e(p,{icon:l.row.icon},null,8,["icon"])]),_:1}),e(n,{label:"\u6392\u5E8F",prop:"sort",width:"60"}),e(n,{"show-overflow-tooltip":!0,label:"\u6743\u9650\u6807\u8BC6",prop:"permission"}),e(n,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u8DEF\u5F84",prop:"component"}),e(n,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u540D\u79F0",prop:"componentName"}),e(n,{label:"\u72B6\u6001",prop:"status"},{default:o(l=>[v(e(G,{class:"ml-4px",modelValue:l.row.status,"onUpdate:modelValue":c=>l.row.status=c,"active-value":t(q).ENABLE,"inactive-value":t(q).DISABLE,loading:t(S)[l.row.id],onChange:c=>(async(m,$)=>{S.value[m.id]=!0;try{m.status=$,await ge(m)}finally{S.value[m.id]=!1}})(l.row,c)},null,8,["modelValue","onUpdate:modelValue","active-value","inactive-value","loading","onChange"]),[[y,["system:menu:update"]]])]),_:1}),e(n,{align:"center",label:"\u64CD\u4F5C"},{default:o(l=>[v((r(),f(s,{link:"",type:"primary",onClick:c=>V("update",l.row.id)},{default:o(()=>a[8]||(a[8]=[u(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[y,["system:menu:update"]]]),v((r(),f(s,{link:"",type:"primary",onClick:c=>V("create",void 0,l.row.id)},{default:o(()=>a[9]||(a[9]=[u(" \u65B0\u589E ")])),_:2},1032,["onClick"])),[[y,["system:menu:create"]]]),v((r(),f(s,{link:"",type:"danger",onClick:c=>(async m=>{try{await k.delConfirm(),await xe(m),k.success(F("common.delSuccess")),await w()}catch{}})(l.row.id)},{default:o(()=>a[10]||(a[10]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["system:menu:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[P,t(b)]]):ne("",!0)]),_:1}),e(Ve,{ref_key:"formRef",ref:A,onSuccess:w},null,512)],64)}}});export{Se as default};
