import{d as q,p as B,y as J,j as o,r as L,o as w,h as C,w as l,k as s,u,v as c,x as z,t as V,m as h,aA as D,aJ as G,ej as O,ek as P,aM as Q,B as T,aC as W,cq as X,I as Y,H as Z,L as $}from"./index-C8b06LRn.js";import{_ as ee}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as ae}from"./el-card-CaOo8U9P.js";import{g as le}from"./index-BwMxOnEu.js";const se=q({name:"SystemRoleAssignMenuForm",__name:"RoleAssignMenuForm",emits:["success"],setup(te,{expose:b,emit:g}){const{t:I}=B(),M=J(),n=o(!1),i=o(!1),t=L({id:void 0,name:"",code:"",menuIds:[]}),p=o(),y=o([]),m=o(!1),d=o(),v=o(!1);b({open:async a=>{n.value=!0,A(),y.value=G(await le()),t.id=a.id,t.name=a.name,t.code=a.code,i.value=!0;try{t.value.menuIds=await O(a.id),t.value.menuIds.forEach(e=>{d.value.setChecked(e,!0,!1)})}finally{i.value=!1}}});const R=g,j=async()=>{if(p&&await p.value.validate()){i.value=!0;try{const a={roleId:t.id,menuIds:[...d.value.getCheckedKeys(!1),...d.value.getHalfCheckedKeys()]};await P(a),M.success(I("common.updateSuccess")),n.value=!1,R("success")}finally{i.value=!1}}},A=()=>{var a,e;v.value=!1,m.value=!1,t.value={id:void 0,name:"",code:"",menuIds:[]},(a=d.value)==null||a.setCheckedNodes([]),(e=p.value)==null||e.resetFields()},F=()=>{d.value.setCheckedNodes(v.value?y.value:[])},U=()=>{var e;const a=(e=d.value)==null?void 0:e.store.nodesMap;for(let f in a)a[f].expanded!==m.value&&(a[f].expanded=m.value)};return(a,e)=>{const f=Q,_=T,k=W,E=X,H=ae,K=Y,x=Z,N=ee,S=$;return w(),C(N,{modelValue:u(n),"onUpdate:modelValue":e[3]||(e[3]=r=>h(n)?n.value=r:null),title:"\u83DC\u5355\u6743\u9650"},{footer:l(()=>[s(x,{disabled:u(i),type:"primary",onClick:j},{default:l(()=>e[6]||(e[6]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),s(x,{onClick:e[2]||(e[2]=r=>n.value=!1)},{default:l(()=>e[7]||(e[7]=[c("\u53D6 \u6D88")])),_:1})]),default:l(()=>[z((w(),C(K,{ref_key:"formRef",ref:p,model:u(t),"label-width":"80px"},{default:l(()=>[s(_,{label:"\u89D2\u8272\u540D\u79F0"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(u(t).name),1)]),_:1})]),_:1}),s(_,{label:"\u89D2\u8272\u6807\u8BC6"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(u(t).code),1)]),_:1})]),_:1}),s(_,{label:"\u83DC\u5355\u6743\u9650"},{default:l(()=>[s(H,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:l(()=>[e[4]||(e[4]=c(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(k,{modelValue:u(v),"onUpdate:modelValue":e[0]||(e[0]=r=>h(v)?v.value=r:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:F},null,8,["modelValue"]),e[5]||(e[5]=c(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(k,{modelValue:u(m),"onUpdate:modelValue":e[1]||(e[1]=r=>h(m)?m.value=r:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:U},null,8,["modelValue"])]),default:l(()=>[s(E,{ref_key:"treeRef",ref:d,data:u(y),props:u(D),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1})]),_:1},8,["model"])),[[S,u(i)]])]),_:1},8,["modelValue"])}}});export{se as _};
