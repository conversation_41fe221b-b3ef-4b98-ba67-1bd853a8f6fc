import{aG as x,d as B,y as D,p as H,j as c,r as J,f as L,T as M,o as u,c as I,k as e,w as s,u as a,M as S,F as V,g as Q,N as W,D as U,h as y,v as _,x as C,S as X,B as Z,z as $,A as ee,G as le,H as ae,I as te,J as se,K as oe,L as re}from"./index-C8b06LRn.js";import{_ as ne}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ue}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ie}from"./index-CkzUfjB7.js";import{d as F}from"./formatTime-COZ9Bl52.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";const de=B({name:"SystemTokenClient",__name:"index",setup(ce){const g=D(),{t:N}=H(),m=c(!0),h=c(0),b=c([]),o=J({pageNo:1,pageSize:10,userId:null,userType:void 0,clientId:null}),k=c(),p=async()=>{m.value=!0;try{const l=await(n=o,x.get({url:"/system/oauth2-token/page",params:n}));b.value=l.list,h.value=l.total}finally{m.value=!1}var n},i=()=>{o.pageNo=1,p()},z=()=>{k.value.resetFields(),i()},E=async n=>{try{await g.confirm("\u662F\u5426\u8981\u5F3A\u5236\u9000\u51FA\u7528\u6237"),await(l=>x.delete({url:"/system/oauth2-token/delete?accessToken="+l}))(n),g.success(N("common.success")),await p()}catch{}};return L(()=>{p()}),(n,l)=>{const K=ie,T=X,d=Z,P=$,R=ee,w=le,f=ae,A=te,v=pe,r=se,G=ue,O=oe,Y=ne,j=M("hasPermi"),q=re;return u(),I(V,null,[e(K,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),e(v,null,{default:s(()=>[e(A,{class:"-mb-15px",model:a(o),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"90px"},{default:s(()=>[e(d,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:s(()=>[e(T,{modelValue:a(o).userId,"onUpdate:modelValue":l[0]||(l[0]=t=>a(o).userId=t),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:S(i,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:s(()=>[e(R,{modelValue:a(o).userType,"onUpdate:modelValue":l[1]||(l[1]=t=>a(o).userType=t),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:s(()=>[(u(!0),I(V,null,Q(a(W)(a(U).USER_TYPE),t=>(u(),y(P,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",prop:"clientId"},{default:s(()=>[e(T,{modelValue:a(o).clientId,"onUpdate:modelValue":l[2]||(l[2]=t=>a(o).clientId=t),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u7F16\u53F7",clearable:"",onKeyup:S(i,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,null,{default:s(()=>[e(f,{onClick:i},{default:s(()=>[e(w,{icon:"ep:search",class:"mr-5px"}),l[5]||(l[5]=_(" \u641C\u7D22"))]),_:1}),e(f,{onClick:z},{default:s(()=>[e(w,{icon:"ep:refresh",class:"mr-5px"}),l[6]||(l[6]=_(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:s(()=>[C((u(),y(O,{data:a(b)},{default:s(()=>[e(r,{label:"\u8BBF\u95EE\u4EE4\u724C",align:"center",prop:"accessToken",width:"300"}),e(r,{label:"\u5237\u65B0\u4EE4\u724C",align:"center",prop:"refreshToken",width:"300"}),e(r,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"userId"}),e(r,{label:"\u7528\u6237\u7C7B\u578B",align:"center",prop:"userType"},{default:s(t=>[e(G,{type:a(U).USER_TYPE,value:t.row.userType},null,8,["type","value"])]),_:1}),e(r,{label:"\u8FC7\u671F\u65F6\u95F4",align:"center",prop:"expiresTime",formatter:a(F),width:"180"},null,8,["formatter"]),e(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(F),width:"180"},null,8,["formatter"]),e(r,{label:"\u64CD\u4F5C",align:"center"},{default:s(t=>[C((u(),y(f,{link:"",type:"danger",onClick:me=>E(t.row.accessToken)},{default:s(()=>l[7]||(l[7]=[_(" \u5F3A\u9000 ")])),_:2},1032,["onClick"])),[[j,["system:oauth2-token:delete"]]])]),_:1})]),_:1},8,["data"])),[[q,a(m)]]),e(Y,{total:a(h),page:a(o).pageNo,"onUpdate:page":l[3]||(l[3]=t=>a(o).pageNo=t),limit:a(o).pageSize,"onUpdate:limit":l[4]||(l[4]=t=>a(o).pageSize=t),onPagination:p},null,8,["total","page","limit"])]),_:1})],64)}}});export{de as default};
