import{a$ as K,p as ee,b0 as te,j as t,r as L,Y as ne,au as ve,f as se,o as T,c as C,u as e,aK as w,l as s,x as ae,Z as ie,k as he,w as de,n as Y,t as M,$ as R,W as ge,aE as re,aO as oe,b1 as ce,b2 as ue,F as fe,g as ye,_ as me,b3 as be,v as xe,h as Se,i as we}from"./index-C8b06LRn.js";function G(a,d="XwKsGlMcdPMEhR1B"){const l=K.enc.Utf8.parse(d),o=K.enc.Utf8.parse(a);return K.AES.encrypt(o,l,{mode:K.mode.ECB,padding:K.pad.Pkcs7}).toString()}function pe(a){let d,l,o,f;const r=window,u=a.$el.parentNode.offsetWidth||r.offsetWidth,y=a.$el.parentNode.offsetHeight||r.offsetHeight;return d=a.imgSize.width.indexOf("%")!=-1?parseInt(a.imgSize.width)/100*u+"px":a.imgSize.width,l=a.imgSize.height.indexOf("%")!=-1?parseInt(a.imgSize.height)/100*y+"px":a.imgSize.height,o=a.barSize.width.indexOf("%")!=-1?parseInt(a.barSize.width)/100*u+"px":a.barSize.width,f=a.barSize.height.indexOf("%")!=-1?parseInt(a.barSize.height)/100*y+"px":a.barSize.height,{imgWidth:d,imgHeight:l,barWidth:o,barHeight:f}}const ze={style:{position:"relative"}},ke=["src"],We=["textContent"],Te=["textContent"],He=["src"],$e={style:{position:"relative"}},Ie={class:"verify-img-out"},Oe=["src"],Ce={class:"verify-msg"},Ne={key:0,class:"verifybox-top"},Be=me({name:"Vue3Verify",components:{VerifySlide:{__name:"VerifySlide",props:{captchaType:{type:String},type:{type:String,default:"1"},mode:{type:String,default:"fixed"},vSpace:{type:Number,default:5},explain:{type:String,default:""},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object,default:()=>({width:"50px",height:"50px"})},barSize:{type:Object,default:()=>({width:"310px",height:"30px"})}},setup(a){const d=a,{t:l}=ee(),{mode:o,captchaType:f,type:r,blockSize:u,explain:y}=te(d),{proxy:v}=re();let p=t(""),z=t(""),_=t(""),P=t(""),H=t(""),m=t(""),j=t(""),b=t(""),k=t(""),E=t(""),h=L({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),N=t(void 0),$=t(void 0),I=t(void 0),B=t("#ddd"),O=t(void 0),D=t("icon-right"),J=t(!1),g=t(!1),n=t(!0),x=t(""),S=t(""),X=t(0);const V=ne(()=>v.$el.querySelector(".verify-bar-area")),q=()=>{y.value===""?k.value=l("captcha.slide"):k.value=y.value,le(),oe(()=>{let{imgHeight:i,imgWidth:c,barHeight:W,barWidth:Q}=pe(v);h.imgHeight=i,h.imgWidth=c,h.barHeight=W,h.barWidth=Q,v.$parent.$emit("ready",v)}),window.removeEventListener("touchmove",function(i){F(i)}),window.removeEventListener("mousemove",function(i){F(i)}),window.removeEventListener("touchend",function(){U()}),window.removeEventListener("mouseup",function(){U()}),window.addEventListener("touchmove",function(i){F(i)}),window.addEventListener("mousemove",function(i){F(i)}),window.addEventListener("touchend",function(){U()}),window.addEventListener("mouseup",function(){U()})};ve(r,()=>{q()}),se(()=>{q(),v.$el.onselectstart=function(){return!1}});const A=i=>{if((i=i||window.event).touches)c=i.touches[0].pageX;else var c=i.clientX;X.value=Math.floor(c-V.value.getBoundingClientRect().left),m.value=+new Date,g.value==0&&(k.value="",I.value="#337ab7",B.value="#337AB7",O.value="#fff",i.stopPropagation(),J.value=!0)},F=i=>{if(i=i||window.event,J.value&&g.value==0){if(i.touches)c=i.touches[0].pageX;else var c=i.clientX;var W=c-V.value.getBoundingClientRect().left;W>=V.value.offsetWidth-parseInt(parseInt(u.value.width)/2)-2&&(W=V.value.offsetWidth-parseInt(parseInt(u.value.width)/2)-2),W<=0&&(W=parseInt(parseInt(u.value.width)/2)),N.value=W-X.value+"px",$.value=W-X.value+"px"}},U=()=>{if(j.value=+new Date,J.value&&g.value==0){var i=parseInt((N.value||"0").replace("px",""));i=310*i/parseInt(h.imgWidth);let c={captchaType:f.value,pointJson:p.value?G(JSON.stringify({x:i,y:5}),p.value):JSON.stringify({x:i,y:5}),token:H.value};ce(c).then(W=>{if(W.repCode=="0000"){I.value="#5cb85c",B.value="#5cb85c",O.value="#fff",D.value="icon-check",n.value=!1,g.value=!0,o.value=="pop"&&setTimeout(()=>{v.$parent.clickShow=!1,Z()},1500),z.value=!0,b.value=`${((j.value-m.value)/1e3).toFixed(2)}s
            ${l("captcha.success")}`;var Q=p.value?G(H.value+"---"+JSON.stringify({x:i,y:5}),p.value):H.value+"---"+JSON.stringify({x:i,y:5});setTimeout(()=>{b.value="",v.$parent.closeBox(),v.$parent.$emit("success",{captchaVerification:Q})},1e3)}else I.value="#d9534f",B.value="#d9534f",O.value="#fff",D.value="icon-close",z.value=!1,setTimeout(function(){Z()},1e3),v.$parent.$emit("error",v),b.value=l("captcha.fail"),setTimeout(()=>{b.value=""},1e3)}),J.value=!1}},Z=async()=>{n.value=!0,E.value="",x.value="left .3s",N.value=0,$.value=void 0,S.value="width .3s",B.value="#ddd",I.value="#fff",O.value="#000",D.value="icon-right",g.value=!1,await le(),setTimeout(()=>{S.value="",x.value="",k.value=y.value},300)},le=async()=>{let i={captchaType:f.value};const c=await ue(i);c.repCode=="0000"?(_.value=c.repData.originalImageBase64,P.value=c.repData.jigsawImageBase64,H.value=c.repData.token,p.value=c.repData.secretKey):b.value=c.repMsg};return(i,c)=>(T(),C("div",ze,[e(r)==="2"?(T(),C("div",{key:0,style:w({height:parseInt(e(h).imgHeight)+a.vSpace+"px"}),class:"verify-img-out"},[s("div",{style:w({width:e(h).imgWidth,height:e(h).imgHeight}),class:"verify-img-panel"},[s("img",{src:"data:image/png;base64,"+e(_),alt:"",style:{display:"block",width:"100%",height:"100%"}},null,8,ke),ae(s("div",{class:"verify-refresh",onClick:Z},c[0]||(c[0]=[s("i",{class:"iconfont icon-refresh"},null,-1)]),512),[[ie,e(n)]]),he(ge,{name:"tips"},{default:de(()=>[e(b)?(T(),C("span",{key:0,class:Y([e(z)?"suc-bg":"err-bg","verify-tips"])},M(e(b)),3)):R("",!0)]),_:1})],4)],4)):R("",!0),s("div",{style:w({width:e(h).imgWidth,height:a.barSize.height,"line-height":a.barSize.height}),class:"verify-bar-area"},[s("span",{class:"verify-msg",textContent:M(e(k))},null,8,We),s("div",{style:w({width:e($)!==void 0?e($):a.barSize.height,height:a.barSize.height,"border-color":e(B),transaction:e(S)}),class:"verify-left-bar"},[s("span",{class:"verify-msg",textContent:M(e(E))},null,8,Te),s("div",{style:w({width:a.barSize.height,height:a.barSize.height,"background-color":e(I),left:e(N),transition:e(x)}),class:"verify-move-block",onMousedown:A,onTouchstart:A},[s("i",{class:Y(["verify-icon iconfont",e(D)]),style:w({color:e(O)})},null,6),e(r)==="2"?(T(),C("div",{key:0,style:w({width:Math.floor(47*parseInt(e(h).imgWidth)/310)+"px",height:e(h).imgHeight,top:"-"+(parseInt(e(h).imgHeight)+a.vSpace)+"px","background-size":e(h).imgWidth+" "+e(h).imgHeight}),class:"verify-sub-block"},[s("img",{src:"data:image/png;base64,"+e(P),alt:"",style:{display:"block",width:"100%",height:"100%","-webkit-user-drag":"none"}},null,8,He)],4)):R("",!0)],36)],4)],4)]))}},VerifyPoints:{__name:"VerifyPoints",props:{mode:{type:String,default:"fixed"},captchaType:{type:String},vSpace:{type:Number,default:5},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},barSize:{type:Object,default:()=>({width:"310px",height:"40px"})}},setup(a){const d=a,{t:l}=ee(),{mode:o,captchaType:f}=te(d),{proxy:r}=re();let u=t(""),y=t(3),v=L([]),p=L([]),z=t(1),_=t(""),P=L([]),H=t(""),m=L({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),j=L([]),b=t(""),k=t(void 0),E=t(void 0),h=t(!0),N=t(!0);se(()=>{v.splice(0,v.length),p.splice(0,p.length),z.value=1,D(),oe(()=>{let{imgHeight:g,imgWidth:n,barHeight:x,barWidth:S}=pe(r);m.imgHeight=g,m.imgWidth=n,m.barHeight=x,m.barWidth=S,r.$parent.$emit("ready",r)}),r.$el.onselectstart=function(){return!1}});const $=t(null),I=function(g,n){return{x:n.offsetX,y:n.offsetY}},B=function(g){return j.push(Object.assign({},g)),z.value+1},O=async function(){j.splice(0,j.length),k.value="#000",E.value="#ddd",N.value=!0,v.splice(0,v.length),p.splice(0,p.length),z.value=1,await D(),h.value=!0},D=async()=>{let g={captchaType:f.value};const n=await ue(g);n.repCode=="0000"?(_.value=n.repData.originalImageBase64,H.value=n.repData.token,u.value=n.repData.secretKey,P.value=n.repData.wordList,b.value=l("captcha.point")+"\u3010"+P.value.join(",")+"\u3011"):b.value=n.repMsg},J=function(g,n){return g.map(x=>({x:Math.round(310*x.x/parseInt(n.imgWidth)),y:Math.round(155*x.y/parseInt(n.imgHeight))}))};return(g,n)=>(T(),C("div",$e,[s("div",Ie,[s("div",{style:w({width:e(m).imgWidth,height:e(m).imgHeight,"background-size":e(m).imgWidth+" "+e(m).imgHeight,"margin-bottom":a.vSpace+"px"}),class:"verify-img-panel"},[ae(s("div",{class:"verify-refresh",style:{"z-index":"3"},onClick:O},n[1]||(n[1]=[s("i",{class:"iconfont icon-refresh"},null,-1)]),512),[[ie,e(h)]]),s("img",{ref_key:"canvas",ref:$,src:"data:image/png;base64,"+e(_),alt:"",style:{display:"block",width:"100%",height:"100%"},onClick:n[0]||(n[0]=x=>e(N)?(S=>{if(p.push(I($,S)),z.value==y.value){z.value=B(I($,S));let X=J(p,m);p.length=0,p.push(...X),setTimeout(()=>{var V=u.value?G(H.value+"---"+JSON.stringify(p),u.value):H.value+"---"+JSON.stringify(p);let q={captchaType:f.value,pointJson:u.value?G(JSON.stringify(p),u.value):JSON.stringify(p),token:H.value};ce(q).then(A=>{A.repCode=="0000"?(k.value="#4cae4c",E.value="#5cb85c",b.value=l("captcha.success"),N.value=!1,o.value=="pop"&&setTimeout(()=>{r.$parent.clickShow=!1,O()},1500),r.$parent.$emit("success",{captchaVerification:V})):(r.$parent.$emit("error",r),k.value="#d9534f",E.value="#d9534f",b.value=l("captcha.fail"),setTimeout(()=>{O()},700))})},400)}z.value<y.value&&(z.value=B(I($,S)))})(x):void 0)},null,8,Oe),(T(!0),C(fe,null,ye(e(j),(x,S)=>(T(),C("div",{key:S,style:w({"background-color":"#1abd6c",color:"#fff","z-index":9999,width:"20px",height:"20px","text-align":"center","line-height":"20px","border-radius":"50%",position:"absolute",top:parseInt(x.y-10)+"px",left:parseInt(x.x-10)+"px"}),class:"point-area"},M(S+1),5))),128))],4)]),s("div",{style:w({width:e(m).imgWidth,color:e(k),"border-color":e(E),"line-height":a.barSize.height}),class:"verify-bar-area"},[s("span",Ce,M(e(b)),1)],4)]))}}},props:{captchaType:{type:String,required:!0},figure:{type:Number},arith:{type:Number},mode:{type:String,default:"pop"},vSpace:{type:Number},explain:{type:String},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object},barSize:{type:Object}},setup(a){const{t:d}=ee(),{captchaType:l,mode:o}=te(a),f=t(!1),r=t(void 0),u=t(void 0),y=t({}),v=ne(()=>o.value!="pop"||f.value);return be(()=>{switch(l.value){case"blockPuzzle":r.value="2",u.value="VerifySlide";break;case"clickWord":r.value="",u.value="VerifyPoints"}}),{t:d,clickShow:f,verifyType:r,componentType:u,instance:y,showBox:v,closeBox:()=>{f.value=!1,y.value.refresh&&y.value.refresh()},show:()=>{o.value=="pop"&&(f.value=!0)}}}},[["render",function(a,d,l,o,f,r){return ae((T(),C("div",{class:Y(l.mode=="pop"?"mask":"")},[s("div",{class:Y(l.mode=="pop"?"verifybox":""),style:w({"max-width":parseInt(l.imgSize.width)+20+"px"})},[l.mode=="pop"?(T(),C("div",Ne,[xe(M(o.t("captcha.verification"))+" ",1),s("span",{class:"verifybox-close",onClick:d[0]||(d[0]=(...u)=>o.closeBox&&o.closeBox(...u))},d[1]||(d[1]=[s("i",{class:"iconfont icon-close"},null,-1)]))])):R("",!0),s("div",{style:w({padding:l.mode=="pop"?"10px":"0"}),class:"verifybox-bottom"},[o.componentType?(T(),Se(we(o.componentType),{key:0,ref:"instance",arith:l.arith,barSize:l.barSize,blockSize:l.blockSize,captchaType:l.captchaType,explain:l.explain,figure:l.figure,imgSize:l.imgSize,mode:l.mode,type:o.verifyType,vSpace:l.vSpace},null,8,["arith","barSize","blockSize","captchaType","explain","figure","imgSize","mode","type","vSpace"])):R("",!0)],4)],6)],2)),[[ie,o.showBox]])}]]);export{Be as _};
