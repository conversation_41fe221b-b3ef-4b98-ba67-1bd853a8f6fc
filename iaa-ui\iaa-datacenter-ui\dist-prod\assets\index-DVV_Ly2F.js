import{d as j,y as B,p as F,j as p,f as G,T as H,o as s,c as J,k as a,w as l,x as n,h as i,v as f,u as m,F as K,G as L,H as P,B as R,I as q,J as z,K as A,L as E}from"./index-C8b06LRn.js";import{_ as M}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as N}from"./formatTime-COZ9Bl52.js";import{g as O,d as Q}from"./index-BqFsG9Sv.js";import{_ as U}from"./DataSourceConfigForm.vue_vue_type_script_setup_true_lang-ZIKBQzlK.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const V=j({name:"InfraDataSourceConfig",__name:"index",setup(W){const _=B(),{t:C}=F(),o=p(!0),g=p([]),c=async()=>{o.value=!0;try{g.value=await O()}finally{o.value=!1}},w=p(),y=(b,e)=>{w.value.open(b,e)};return G(()=>{c()}),(b,e)=>{const v=L,d=P,x=R,h=q,k=M,r=z,S=A,u=H("hasPermi"),D=E;return s(),J(K,null,[a(k,null,{default:l(()=>[a(h,{class:"-mb-15px",inline:!0},{default:l(()=>[a(x,null,{default:l(()=>[n((s(),i(d,{type:"primary",plain:"",onClick:e[0]||(e[0]=t=>y("create"))},{default:l(()=>[a(v,{icon:"ep:plus",class:"mr-5px"}),e[1]||(e[1]=f(" \u65B0\u589E "))]),_:1})),[[u,["infra:data-source-config:create"]]])]),_:1})]),_:1})]),_:1}),a(k,null,{default:l(()=>[n((s(),i(S,{data:m(g)},{default:l(()=>[a(r,{label:"\u4E3B\u952E\u7F16\u53F7",align:"center",prop:"id"}),a(r,{label:"\u6570\u636E\u6E90\u540D\u79F0",align:"center",prop:"name"}),a(r,{label:"\u6570\u636E\u6E90\u8FDE\u63A5",align:"center",prop:"url","show-overflow-tooltip":!0}),a(r,{label:"\u7528\u6237\u540D",align:"center",prop:"username"}),a(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:m(N)},null,8,["formatter"]),a(r,{label:"\u64CD\u4F5C",align:"center"},{default:l(t=>[n((s(),i(d,{link:"",type:"primary",onClick:I=>y("update",t.row.id),disabled:t.row.id===0},{default:l(()=>e[2]||(e[2]=[f(" \u7F16\u8F91 ")])),_:2},1032,["onClick","disabled"])),[[u,["infra:data-source-config:update"]]]),n((s(),i(d,{link:"",type:"danger",onClick:I=>(async T=>{try{await _.delConfirm(),await Q(T),_.success(C("common.delSuccess")),await c()}catch{}})(t.row.id),disabled:t.row.id===0},{default:l(()=>e[3]||(e[3]=[f(" \u5220\u9664 ")])),_:2},1032,["onClick","disabled"])),[[u,["infra:data-source-config:delete"]]])]),_:1})]),_:1},8,["data"])),[[D,m(o)]])]),_:1}),a(U,{ref_key:"formRef",ref:w,onSuccess:c},null,512)],64)}}});export{V as default};
