import{cZ as be,d as De,j as p,cj as w,dO as xe,r as Ye,y as _e,Y as Se,f as ze,aF as se,o as s,c as b,l as o,k as e,w as d,t as g,v,h,$ as D,F as ne,g as Ce,n as Ve,u as B,dk as E,d1 as Q,aD as Me,aq as Ne,C as Ue,H as We,J as Oe,aM as Re,K as Te,z as Ae,A as Be,cy as Fe,_ as $e}from"./index-C8b06LRn.js";import{_ as Pe}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{E as Le}from"./el-card-CaOo8U9P.js";import{_ as Ee}from"./Echart.vue_vue_type_script_setup_true_lang-Cn4V3zug.js";import{d as K}from"./download-D5Lb_h0f.js";import{C as V}from"./index-B2m4kQ_G.js";import j from"./DetailDate-DCB2Zfz0.js";import"./index-Cl43piKd.js";import"./echarts-cfVEL83D.js";import"./permission-DVzqLl93.js";var Z,je={exports:{}};const Qe=be(je.exports=(Z="day",function(re,F,M){var y=function(c){return c.add(4-c.isoWeekday(),Z)},r=F.prototype;r.isoWeekYear=function(){return y(this).year()},r.isoWeek=function(c){if(!this.$utils().u(c))return this.add(7*(c-this.isoWeek()),Z);var k,i,S,f=y(this),q=(k=this.isoWeekYear(),S=4-(i=(this.$u?M.utc:M)().year(k).startOf("year")).isoWeekday(),i.isoWeekday()>4&&(S+=7),i.add(S,Z));return f.diff(q,"week")+1},r.isoWeekday=function(c){return this.$utils().u(c)?this.day()||7:this.day(this.day()%7?c:c-7)};var m=r.startOf;r.startOf=function(c,k){var i=this.$utils(),S=!!i.u(k)||k;return i.p(c)==="isoweek"?S?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):m.bind(this)(c,k)}})),Ze={class:"interaction-rate-container"},qe={class:"delivery-rate-section mb-6"},Ge={class:"card-header"},He={class:"text-lg font-medium"},Ie={key:0},Je={key:1},Ke={class:"text-sm text-gray-500 ml-2"},Xe={class:"flex items-center gap-4"},et={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4"},tt=["onClick"],at={class:"text-center"},lt={class:"text-2xl font-bold text-blue-600 mb-2"},it={class:"text-sm text-gray-600 mb-1"},dt={class:"text-xs text-gray-400"},ot={class:"design-rate-section mb-6"},st={class:"card-header"},nt={class:"text-lg font-medium"},rt={key:0},ut={key:1},ct={class:"text-sm text-gray-500 ml-2"},mt={key:0},pt={key:1},ht={class:"overflow-x-auto"},ft={class:"trend-chart-section"},gt={class:"card-header"},vt={class:"text-sm text-gray-500 ml-2"},wt={class:"flex items-center gap-4"},yt={class:"chart-container",style:{height:"400px"}},kt={class:"flex justify-between items-center w-full"},bt={key:0,class:"h-[calc(100vh-260px)]"},Dt={key:1,class:"h-[calc(100vh-260px)]"},xt={key:2,class:"h-[calc(100vh-260px)]"},Yt={key:3,class:"h-[calc(100vh-260px)]"},_t={key:4,class:"h-[calc(100vh-260px)]"},St={key:5,class:"h-[calc(100vh-260px)]"},zt=$e(De({__name:"interactionRate",setup(re){const F=p(!1),M=p(null),y=p(!1),r=p();w.extend(Qe),w.extend(xe);const m=Ye({month:w().format("YYYY-MM"),year:w().format("YYYY"),week:w().format("YYYY-WW")}),c=p([]),k=p(0),i=p({pageNo:1,pageSize:30,planCompleteDate:[],taskStatus:"",showAll:!1,type:""}),S=async()=>{y.value=!0;try{const n=await V.pageTask(i.value);c.value=n.list,k.value=n.total,y.value=!1}finally{y.value=!1}},f=async()=>{y.value=!0;try{const n=await V.pageDetail(i.value);c.value=n.list,k.value=n.total,y.value=!1}finally{y.value=!1}},q=async()=>{var n;if(r.value==="main"){const l=await V.export(i.value);K.excel(l,`\u8BA2\u5355BOM\u8DDF\u8FDB\u6570\u636E${m.month}.xlsx`)}else{const l=await V.exportDetail(i.value);K.excel(l,((n=M.value)==null?void 0:n.name)+m.month+".xlsx")}},x=p("month"),G=p("year"),U=p();p();const $=p(),P=p(),ue=p(),ce=p(),me=_e(),H=p(!1),I=p({main:[32,88,92,89,91,94,87,90,93,88,91,89],program:[78,82,85,88,84,87,90,86,89,91,88,85],structure:[82,85,88,91,87,90,93,89,92,94,90,88],packing:[75,78,82,85,81,84,87,83,86,89,85,82],instruction:[88,91,94,92,95,93,96,94,97,95,93,91],logo:[80,83,86,89,85,88,91,87,90,93,89,86]}),X=p([{type:"main",name:"\u4E3B\u8868",total:0,completed:0,current:0,rate:0},{type:"program",name:"\u7A0B\u5E8F",total:0,completed:0,current:0,rate:0},{type:"structure",name:"\u7ED3\u6784",total:0,completed:0,current:0,rate:0},{type:"packing",name:"\u5305\u6750",total:0,completed:0,current:0,rate:0},{type:"instruction",name:"\u8BF4\u660E\u4E66",total:0,completed:0,current:0,rate:0},{type:"logo",name:"\u9762\u677F",total:0,completed:0,current:0,rate:0}]),ee=p([{type:"main",name:"\u4E3B\u8868",total:0,completed:0,delayed:0,unfinished:0,rate:0},{type:"program",name:"\u7A0B\u5E8F",total:0,completed:0,delayed:0,unfinished:0,rate:0},{type:"structure",name:"\u7ED3\u6784",total:0,completed:0,delayed:0,unfinished:0,rate:0},{type:"packing",name:"\u5305\u6750",total:0,completed:0,delayed:0,unfinished:0,rate:0},{type:"instruction",name:"\u8BF4\u660E\u4E66",total:0,completed:0,delayed:0,unfinished:0,rate:0},{type:"logo",name:"\u9762\u677F",total:0,completed:0,delayed:0,unfinished:0,rate:0}]),te=n=>n?n.split("-")[1]:"",pe=()=>{x.value==="week"?(U.value=w().startOf("isoWeek").format("YYYY-MM-DD"),m.week=w().format("YYYY-WW"),ae()):(m.month=w().format("YYYY-MM"),L())},ae=()=>{m.week=w(U.value).format("YYYY-WW"),$.value=w(U.value).startOf("isoWeek").format("YYYY-MM-DD"),P.value=w(U.value).endOf("isoWeek").format("YYYY-MM-DD"),L()},he=Se(()=>{let n=[],l={main:[],program:[],structure:[],packing:[],instruction:[],logo:[]};if(G.value==="year")n=["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],l=I.value;else{const N=w(ue.value),W=w(ce.value),Y=[];let z=N;for(;z.isSameOrBefore(W);)Y.push(z.format("YYYY-MM-DD")),z=z.add(1,"day");n=Y,l=I.value}return{title:{text:G.value==="year"?"\u5404\u677F\u5757\u6708\u5EA6\u51C6\u65F6\u4EA4\u4ED8\u7387\u8D8B\u52BF":"\u5404\u677F\u5757\u5468\u51C6\u65F6\u4EA4\u4ED8\u7387\u8D8B\u52BF",left:"center",textStyle:{fontSize:16,fontWeight:400}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:N=>{let W=`${N[0].axisValue}<br/>`;return N.forEach(Y=>{W+=`${Y.marker}${Y.seriesName}: ${Y.value}%<br/>`}),W}},legend:{data:["\u4E3B\u8868","\u7A0B\u5E8F","\u7ED3\u6784","\u5305\u6750","\u8BF4\u660E\u4E66","\u9762\u677F"],top:30,type:"scroll"},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:n,axisLine:{lineStyle:{color:"#e0e6ed"}},axisLabel:{color:"#606266"}},yAxis:{type:"value",name:"\u4EA4\u4ED8\u7387(%)",min:0,max:100,axisLine:{lineStyle:{color:"#e0e6ed"}},axisLabel:{color:"#606266",formatter:"{value}%"},splitLine:{lineStyle:{color:"#f5f7fa"}}},series:[{name:"\u4E3B\u8868",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#409EFF"},data:l.main},{name:"\u7A0B\u5E8F",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#67C23A"},data:l.program},{name:"\u7ED3\u6784",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#E6A23C"},data:l.structure},{name:"\u5305\u6750",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#F56C6C"},data:l.packing},{name:"\u8BF4\u660E\u4E66",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#909399"},data:l.instruction},{name:"\u9762\u677F",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2},itemStyle:{color:"#9C27B0"},data:l.logo}]}}),fe=n=>n>=90?"\u4F18\u79C0":n>=80?"\u826F\u597D":"\u9700\u6539\u8FDB",L=async()=>{try{await Promise.all([ge()]),Q.success("\u6570\u636E\u5237\u65B0\u6210\u529F")}catch{Q.error("\u6570\u636E\u67E5\u8BE2\u5931\u8D25")}},le=async()=>{try{await Promise.all([ie()]),Q.success("\u5E74\u5EA6\u4EA4\u4ED8\u7387\u8D8B\u52BF\u56FE\u5237\u65B0\u6210\u529F")}catch{Q.error("\u6570\u636E\u67E5\u8BE2\u5931\u8D25")}},ge=async()=>{try{let n;n=x.value==="week"?await V.getDeliveryRate({weekStart:$.value,weekEnd:P.value}):await V.getDeliveryRate({month:m.month}),X.value=n||[],ee.value=n||[]}catch{}},ie=async()=>{try{const n=await V.getTrendData({year:m.year});n&&(I.value=n)}catch{}};return ze(()=>{L(),ie()}),(n,l)=>{var de;const N=Me,W=Ne,Y=Ue,z=We,J=Le,C=Oe,O=Re,ve=Te,u=Ae,R=Be,t=se("vxe-column"),T=se("vxe-table"),A=Pe,we=Fe;return s(),b(ne,null,[o("div",Ze,[o("div",qe,[e(J,{shadow:"never"},{header:d(()=>[o("div",Ge,[o("span",He,[x.value==="week"?(s(),b("span",Ie,"\u7B2C"+g(te(m.week))+"\u5468BOM\u51C6\u65F6\u4EA4\u4ED8\u7387\u7EDF\u8BA1",1)):(s(),b("span",Je,"\u6708\u5EA6BOM\u51C6\u65F6\u4EA4\u4ED8\u7387\u7EDF\u8BA1"))]),o("span",Ke,[o("div",Xe,[e(W,{modelValue:x.value,"onUpdate:modelValue":l[0]||(l[0]=a=>x.value=a),size:"small",onChange:pe},{default:d(()=>[e(N,{label:"month"},{default:d(()=>l[24]||(l[24]=[v("\u6708")])),_:1}),e(N,{label:"week"},{default:d(()=>l[25]||(l[25]=[v("\u5468")])),_:1})]),_:1},8,["modelValue"]),x.value==="week"?(s(),h(Y,{key:0,modelValue:U.value,"onUpdate:modelValue":l[1]||(l[1]=a=>U.value=a),type:"week",format:$.value+" \u81F3 "+P.value+"\u7B2C WW \u5468",size:"small",onChange:ae,clearable:!1},null,8,["modelValue","format"])):D("",!0),x.value==="month"?(s(),h(Y,{key:1,modelValue:m.month,"onUpdate:modelValue":l[2]||(l[2]=a=>m.month=a),type:"month",placeholder:"\u9009\u62E9\u6708\u4EFD",format:"YYYY-MM","value-format":"YYYY-MM",size:"small",onChange:L,clearable:!1},null,8,["modelValue"])):D("",!0),e(z,{type:"primary",size:"small",onClick:L},{default:d(()=>l[26]||(l[26]=[v("\u67E5\u8BE2")])),_:1})])])])]),default:d(()=>[o("div",et,[(s(!0),b(ne,null,Ce(X.value,a=>(s(),b("div",{key:a.type,class:"stat-card p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer",onClick:_=>(oe=>{if(c.value=[],i.value.type="",i.value.taskStatus="",M.value=oe,x.value==="month"){const ye=w(m.month).startOf("month").format("YYYY-MM-DD"),ke=w(m.month).endOf("month").format("YYYY-MM-DD");i.value.planCompleteDate=[ye,ke]}else i.value.planCompleteDate=[$.value,P.value];switch(i.value.pageNo=1,i.value.pageSize=30,F.value=!0,oe.name){case"\u4E3B\u8868":r.value="main",S();break;case"\u9762\u677F":r.value="logo",i.value.type="logo",f();break;case"\u5305\u6750":r.value="packing",i.value.type="packing",f();break;case"\u8BF4\u660E\u4E66":r.value="instruction",i.value.type="instruction",f();break;case"\u7A0B\u5E8F":r.value="program",i.value.type="program",f();break;case"\u7ED3\u6784":r.value="structure",i.value.type="structure",f()}})(a)},[o("div",at,[o("div",lt,g(a.rate)+"% ",1),o("div",it,g(a.name),1),o("div",dt,g(a.completed)+"/"+g(a.current),1)])],8,tt))),128))])]),_:1})]),o("div",ot,[e(J,{shadow:"never"},{header:d(()=>[o("div",st,[o("span",nt,[x.value==="week"?(s(),b("span",rt,"\u7B2C"+g(te(m.week))+"\u5468\u8BBE\u8BA1\u4EA4\u4ED8\u7387\u7EDF\u8BA1",1)):(s(),b("span",ut,"\u6708\u5EA6\u8BBE\u8BA1\u4EA4\u4ED8\u7387\u7EDF\u8BA1"))]),o("span",ct,[x.value==="week"?(s(),b("span",mt,g($.value)+" \u5230 "+g(P.value),1)):(s(),b("span",pt,g(m.month||"\u5F53\u6708"),1))]),o("span",null,[e(z,{onClick:l[3]||(l[3]=a=>(async()=>{try{await me.exportConfirm(),H.value=!0;const _=await V.exportDay(m);K.excel(_,`\u6708\u5EA6\u8BBE\u8BA1\u4EA4\u4ED8\u7387\u7EDF\u8BA1_${m.month||w().format("YYYY-MM")}.xls`)}catch{}finally{H.value=!1}})()),type:"success",style:{"margin-left":"30px"},size:"small"},{default:d(()=>l[27]||(l[27]=[v("\u5BFC\u51FA")])),_:1})])])]),default:d(()=>[o("div",ht,[e(ve,{data:ee.value,border:"",size:"small",class:"w-full"},{default:d(()=>[e(C,{prop:"name",label:"\u677F\u5757","min-width":"120",align:"center"}),e(C,{prop:"total",label:x.value==="week"?"\u5468\u603B\u4EFB\u52A1\u6570":"\u6708\u5EA6\u603B\u4EFB\u52A1\u6570","min-width":"120",align:"center"},null,8,["label"]),e(C,{prop:"current",label:"\u5F53\u524D\u5E94\u5B8C\u6210\u6570","min-width":"120",align:"center"}),e(C,{prop:"completed",label:"\u5982\u671F\u5B8C\u6210\u6570","min-width":"120",align:"center"}),e(C,{prop:"rate",label:"\u4EA4\u4ED8\u7387","min-width":"100",align:"center"},{default:d(({row:a})=>{return[o("span",{class:Ve(["font-medium",(_=a.rate,_>=90?"text-green-600":_>=80?"text-yellow-600":"text-red-600")])},g(a.rate)+"% ",3)];var _}),_:1}),e(C,{prop:"delayed",label:"\u5EF6\u671F\u6570","min-width":"100",align:"center"}),e(C,{prop:"unfinished",label:"\u672A\u5230\u671F\u6570","min-width":"100",align:"center"}),e(C,{label:"\u72B6\u6001","min-width":"120",align:"center"},{default:d(({row:a})=>{return[e(O,{type:(_=a.rate,_>=90?"success":_>=80?"warning":"danger"),size:"small"},{default:d(()=>[v(g(fe(a.rate)),1)]),_:2},1032,["type"])];var _}),_:1})]),_:1},8,["data"])])]),_:1})]),o("div",ft,[e(J,{shadow:"never"},{header:d(()=>[o("div",gt,[l[29]||(l[29]=o("span",{class:"text-lg font-medium"},"\u5E74\u5EA6\u51C6\u65F6\u4EA4\u4ED8\u7387\u8D8B\u52BF\u56FE",-1)),o("span",vt,[o("div",wt,[G.value==="year"?(s(),h(Y,{key:0,modelValue:m.year,"onUpdate:modelValue":l[4]||(l[4]=a=>m.year=a),type:"year",placeholder:"\u9009\u62E9\u5E74\u4EFD",format:"YYYY","value-format":"YYYY",size:"small",onChange:le,clearable:!1},null,8,["modelValue"])):D("",!0),e(z,{type:"primary",size:"small",onClick:le},{default:d(()=>l[28]||(l[28]=[v(" \u67E5\u8BE2 ")])),_:1})])])])]),default:d(()=>[o("div",yt,[e(B(Ee),{options:he.value,height:"400px"},null,8,["options"])])]),_:1})])]),e(we,{modelValue:F.value,"onUpdate:modelValue":l[23]||(l[23]=a=>F.value=a),title:((de=M.value)==null?void 0:de.name)+"\u8BE6\u60C5",width:"90%","align-center":"","append-to-body":"",draggable:!0},{header:d(()=>{var a;return[o("div",kt,[o("span",null,g((a=M.value)==null?void 0:a.name)+"\u8BE6\u60C5",1),e(z,{type:"success",size:"small",onClick:q,loading:H.value},{default:d(()=>l[30]||(l[30]=[v(" \u5BFC\u51FA ")])),_:1},8,["loading"])])]}),default:d(()=>[r.value==="main"?(s(),b("div",bt,[e(T,{"row-config":{height:30},id:"mainTable","header-cell-style":{padding:0,height:"30px"},"cell-style":{padding:0,height:"30px"},"checkbox-config":{labelField:"check",range:!0},"column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"120",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[31]||(l[31]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[5]||(l[5]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:S},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"120",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"lastOrderQty",width:"120",title:"\u6700\u540E\u4E0B\u5355\u6570\u91CF"},{header:d(()=>l[32]||(l[32]=[o("div",null,"\u6700\u540E\u4E0B\u5355\u6570\u91CF",-1)])),_:1}),e(t,{field:"lastOrderDate",width:"120",title:"\u6700\u540E\u4E0B\u5355\u65E5\u671F"},{header:d(()=>l[33]||(l[33]=[o("div",null,"\u6700\u540E\u4E0B\u5355\u65E5\u671F",-1)])),_:1}),e(t,{field:"packing",width:"120",title:"\u5B9A\u5236\u5305\u6750\u7C7B"},{default:d(({row:a})=>[a.packing&&(a==null?void 0:a.packing)!="99"?(s(),h(j,{key:1,data:a.packingDetail},null,8,["data"])):(s(),h(O,{key:0,type:"success"},{default:d(()=>l[34]||(l[34]=[v("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),e(t,{field:"logo",width:"120",title:"\u5B9A\u5236logo\u7C7B"},{default:d(({row:a})=>[a.logo&&(a==null?void 0:a.logo)!="99"?(s(),h(j,{key:1,data:a.logoDetail},null,8,["data"])):(s(),h(O,{key:0,type:"success"},{default:d(()=>l[35]||(l[35]=[v("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),e(t,{field:"instruction",width:"120",title:"\u5B9A\u5236\u8BF4\u660E\u4E66\u7C7B"},{default:d(({row:a})=>[a.instruction&&(a==null?void 0:a.instruction)!="99"?(s(),h(j,{key:1,data:a.instructionDetail},null,8,["data"])):(s(),h(O,{key:0,type:"success"},{default:d(()=>l[36]||(l[36]=[v("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),e(t,{field:"program",width:"120",title:"\u5B9A\u5236\u7A0B\u5E8F\u7C7B"},{default:d(({row:a})=>[a.program&&(a==null?void 0:a.program)!="99"?(s(),h(j,{key:1,data:a.programDetail},null,8,["data"])):(s(),h(O,{key:0,type:"success"},{default:d(()=>l[37]||(l[37]=[v("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),e(t,{field:"structure",width:"120",title:"\u5B9A\u5236\u7ED3\u6784\u7C7B"},{default:d(({row:a})=>[a.structure&&(a==null?void 0:a.structure)!="99"?(s(),h(j,{key:1,data:a.structureDetail},null,8,["data"])):(s(),h(O,{key:0,type:"success"},{default:d(()=>l[38]||(l[38]=[v("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),e(t,{field:"remark",width:"120",title:"\u5907\u6CE8"}),e(t,{field:"planBomCompleteDate",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"planBomCompleteDateChange",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u53D8\u66F4\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"})]),_:1},8,["data","loading"]),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[6]||(l[6]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[7]||(l[7]=a=>i.value.pageNo=a),total:k.value,onPagination:S,size:"small"},null,8,["limit","page","total"])])):D("",!0),r.value==="logo"?(s(),b("div",Dt,[r.value==="logo"?(s(),h(T,{key:0,"row-config":{height:30},"header-cell-style":{padding:0,height:"30px"},id:"logoTable",align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},"show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[39]||(l[39]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[8]||(l[8]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:f},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"200",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{default:d(({row:a})=>[v(g(B(E)("eng_logo_dict",a.custom)),1)]),_:1}),e(t,{field:"description",width:"200",title:"\u4EFB\u52A1"}),e(t,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u8D44\u6599\u63A5\u6536\u65E5\u671F"}),e(t,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u8D44\u6599\u63A5\u6536\u65E5\u671F"}),e(t,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"}),e(t,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"}),e(t,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"remark",width:"200",title:"\u5907\u6CE8"})]),_:1},8,["data","loading"])):D("",!0),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[9]||(l[9]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[10]||(l[10]=a=>i.value.pageNo=a),total:k.value,onPagination:f,size:"small"},null,8,["limit","page","total"])])):D("",!0),r.value==="program"?(s(),b("div",xt,[r.value==="program"?(s(),h(T,{key:0,"row-config":{height:30},"header-cell-style":{padding:0,height:"30px"},id:"programTable",align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},"show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[40]||(l[40]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[11]||(l[11]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:f},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"200",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{default:d(({row:a})=>[v(g(B(E)("eng_program_dict",a.custom)),1)]),_:1}),e(t,{field:"description",width:"200",title:"\u4EFB\u52A1"}),e(t,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"}),e(t,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"}),e(t,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6D4B\u8BD5\u65E5\u671F"}),e(t,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6D4B\u8BD5\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"remark",width:"200",title:"\u5907\u6CE8"})]),_:1},8,["data","loading"])):D("",!0),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[12]||(l[12]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[13]||(l[13]=a=>i.value.pageNo=a),total:k.value,onPagination:f,size:"small"},null,8,["limit","page","total"])])):D("",!0),r.value==="structure"?(s(),b("div",Yt,[r.value==="structure"?(s(),h(T,{key:0,"row-config":{height:30},"header-cell-style":{padding:0,height:"30px"},id:"structureTable",align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},"show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[41]||(l[41]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[14]||(l[14]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:f},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"200",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{default:d(({row:a})=>[v(g(B(E)("eng_structure_dict",a.custom)),1)]),_:1}),e(t,{field:"description",width:"200",title:"\u4EFB\u52A1"}),e(t,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"}),e(t,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"}),e(t,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"}),e(t,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"}),e(t,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u627F\u8BA4\u65E5\u671F"}),e(t,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u627F\u8BA4\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"remark",width:"200",title:"\u5907\u6CE8"})]),_:1},8,["data","loading"])):D("",!0),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[15]||(l[15]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[16]||(l[16]=a=>i.value.pageNo=a),total:k.value,onPagination:f,size:"small"},null,8,["limit","page","total"])])):D("",!0),r.value==="packing"?(s(),b("div",_t,[r.value==="packing"?(s(),h(T,{key:0,"row-config":{height:30},"header-cell-style":{padding:0,height:"30px"},id:"packingTable",align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},"show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[42]||(l[42]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[17]||(l[17]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:f},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"200",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{default:d(({row:a})=>[v(g(B(E)("eng_packing_dict",a.custom)),1)]),_:1}),e(t,{field:"description",width:"200",title:"\u4EFB\u52A1"}),e(t,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"}),e(t,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"}),e(t,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"planQuotationDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u7A3F\u786E\u8BA4"}),e(t,{field:"actualQuotationDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u7A3F\u786E\u8BA4"}),e(t,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"}),e(t,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"}),e(t,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"planBomCompleteDate",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"planBomCompleteDateChange",width:"200",title:"BOM\u8BA1\u5212\u53D8\u66F4\u65E5\u671F"}),e(t,{field:"remark",width:"200",title:"\u5907\u6CE8"})]),_:1},8,["data","loading"])):D("",!0),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[18]||(l[18]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[19]||(l[19]=a=>i.value.pageNo=a),total:k.value,onPagination:f,size:"small"},null,8,["limit","page","total"])])):D("",!0),r.value==="instruction"?(s(),b("div",St,[r.value==="instruction"?(s(),h(T,{key:0,"row-config":{height:30},"header-cell-style":{padding:0,height:"30px"},id:"instructionTable",align:"center",border:"","show-overflow":"",height:"100%",data:c.value,ref:"tableRef",size:"small","column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},"show-header-overflow":"","show-overflow-tooltip":"",loading:y.value},{default:d(()=>[e(t,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:d(()=>[l[43]||(l[43]=o("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),e(R,{modelValue:i.value.taskStatus,"onUpdate:modelValue":l[20]||(l[20]=a=>i.value.taskStatus=a),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:f},{default:d(()=>[e(u,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),e(u,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),e(u,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),e(u,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1}),e(t,{field:"businessDate",width:"200",title:"\u65E5\u671F"}),e(t,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"}),e(t,{field:"customerName",width:"120",title:"\u5BA2\u6237"}),e(t,{field:"itemCode",width:"120",title:"\u54C1\u53F7"}),e(t,{field:"itemName",width:"140",title:"\u54C1\u540D"}),e(t,{field:"spec","min-width":"200",title:"\u89C4\u683C"}),e(t,{field:"custom",width:"120",title:"\u5B9A\u5236\u5185\u5BB9"},{default:d(({row:a})=>[v(g(B(E)("eng_instruction_dict",a.custom)),1)]),_:1}),e(t,{field:"description",width:"200",title:"\u4EFB\u52A1"}),e(t,{field:"planReceiptDate",width:"200",title:"\u8BA1\u5212\u63A5\u6536\u65E5\u671F"}),e(t,{field:"actualReceiptDate",width:"200",title:"\u5B9E\u9645\u63A5\u6536\u65E5\u671F"}),e(t,{field:"planDesignDate",width:"200",title:"\u8BA1\u5212\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"actualDesignDate",width:"200",title:"\u5B9E\u9645\u8BBE\u8BA1\u65E5\u671F"}),e(t,{field:"planTestingDate",width:"200",title:"\u8BA1\u5212\u6253\u6837\u65E5\u671F"}),e(t,{field:"actualTestingDate",width:"200",title:"\u5B9E\u9645\u6253\u6837\u65E5\u671F"}),e(t,{field:"planAdmitDate",width:"200",title:"\u8BA1\u5212\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"actualAdmitDate",width:"200",title:"\u5B9E\u9645\u786E\u8BA4\u65E5\u671F"}),e(t,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"}),e(t,{field:"remark",width:"200",title:"\u5907\u6CE8"})]),_:1},8,["data","loading"])):D("",!0),e(A,{limit:i.value.pageSize,"onUpdate:limit":l[21]||(l[21]=a=>i.value.pageSize=a),page:i.value.pageNo,"onUpdate:page":l[22]||(l[22]=a=>i.value.pageNo=a),total:k.value,onPagination:f,size:"small"},null,8,["limit","page","total"])])):D("",!0)]),_:1},8,["modelValue","title"])],64)}}}),[["__scopeId","data-v-8dd85591"]]);export{zt as default};
