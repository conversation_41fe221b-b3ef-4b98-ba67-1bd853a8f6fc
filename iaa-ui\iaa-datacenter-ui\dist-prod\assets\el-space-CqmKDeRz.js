import{b6 as E,d as w,bb as $,Y as y,bv as P,bf as z,j as A,b3 as R,bQ as f,b8 as v,ba as h,ci as L,bc as B,b7 as G,k as S,cA as c,v as N,cB as T,cC as I,bh as W}from"./index-C8b06LRn.js";const k=w({name:"ElSpaceItem",props:E({prefixCls:{type:String}}),setup(e,{slots:m}){const b=$("space"),g=y(()=>`${e.prefixCls||b.b()}__item`);return()=>P("div",{class:g.value},z(m,"default"))}}),O={small:8,default:12,large:16},_=w({name:"ElSpace",props:E({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:h([String,Object,Array]),default:""},style:{type:h([String,Array,Object]),default:""},alignment:{type:h(String),default:"center"},prefixCls:{type:String},spacer:{type:h([Object,String,Number,Array]),default:null,validator:e=>L(e)||v(e)||B(e)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:G,validator:e=>v(e)||f(e)&&e.length===2&&e.every(v)}}),setup(e,{slots:m}){const{classes:b,containerStyle:g,itemStyle:x}=function(a){const s=$("space"),n=y(()=>[s.b(),s.m(a.direction),a.class]),t=A(0),l=A(0),o=y(()=>[a.wrap||a.fill?{flexWrap:"wrap"}:{},{alignItems:a.alignment},{rowGap:`${l.value}px`,columnGap:`${t.value}px`},a.style]),i=y(()=>a.fill?{flexGrow:1,minWidth:`${a.fillRatio}%`}:{});return R(()=>{const{size:r="small",wrap:d,direction:p,fill:Y}=a;if(f(r)){const[u=0,j=0]=r;t.value=u,l.value=j}else{let u;u=v(r)?r:O[r||"small"]||O.small,(d||Y)&&p==="horizontal"?t.value=l.value=u:p==="horizontal"?(t.value=u,l.value=0):(l.value=u,t.value=0)}}),{classes:n,containerStyle:o,itemStyle:i}}(e);function C(a,s="",n=[]){const{prefixCls:t}=e;return a.forEach((l,o)=>{T(l)?f(l.children)&&l.children.forEach((i,r)=>{T(i)&&f(i.children)?C(i.children,`${s+r}-`,n):n.push(S(k,{style:x.value,prefixCls:t,key:`nested-${s+r}`},{default:()=>[i]},c.PROPS|c.STYLE,["style","prefixCls"]))}):I(l)&&n.push(S(k,{style:x.value,prefixCls:t,key:`LoopKey${s+o}`},{default:()=>[l]},c.PROPS|c.STYLE,["style","prefixCls"]))}),n}return()=>{var a;const{spacer:s,direction:n}=e,t=z(m,"default",{key:0},()=>[]);if(((a=t.children)!=null?a:[]).length===0)return null;if(f(t.children)){let l=C(t.children);if(s){const o=l.length-1;l=l.reduce((i,r,d)=>{const p=[...i,r];return d!==o&&p.push(S("span",{style:[x.value,n==="vertical"?"width: 100%":null],key:d},[L(s)?s:N(s,c.TEXT)],c.STYLE)),p},[])}return S("div",{class:b.value,style:g.value},l,c.STYLE|c.CLASS)}return t.children}}}),K=W(_);export{K as E};
