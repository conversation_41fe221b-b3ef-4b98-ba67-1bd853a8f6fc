import{aG as y,d as x,y as j,j as m,r as F,o as c,h as v,w as s,k as t,u as l,v as _,x as U,m as A,S as C,B as S,I as q,H as B,L as G}from"./index-C8b06LRn.js";import{_ as H}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const L=async()=>await y.get({url:"/system/area/tree"}),R=x({name:"SystemAreaForm",__name:"AreaForm",setup(z,{expose:V}){const b=j(),u=m(!1),d=m(!1),a=m({ip:"",result:void 0}),g=F({ip:[{required:!0,message:"IP \u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=m();V({open:async()=>{u.value=!0,I()}});const w=async()=>{if(i&&await i.value.validate()){d.value=!0;try{a.value.result=await(async o=>await y.get({url:"/system/area/get-by-ip?ip="+o}))(a.value.ip.trim()),b.success("\u67E5\u8BE2\u6210\u529F")}finally{d.value=!1}}},I=()=>{var o;a.value={ip:"",result:void 0},(o=i.value)==null||o.resetFields()};return(o,e)=>{const p=C,n=S,P=q,f=B,h=H,k=G;return c(),v(h,{modelValue:l(u),"onUpdate:modelValue":e[3]||(e[3]=r=>A(u)?u.value=r:null),title:"IP \u67E5\u8BE2"},{footer:s(()=>[t(f,{disabled:l(d),type:"primary",onClick:w},{default:s(()=>e[4]||(e[4]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),t(f,{onClick:e[2]||(e[2]=r=>u.value=!1)},{default:s(()=>e[5]||(e[5]=[_("\u53D6 \u6D88")])),_:1})]),default:s(()=>[U((c(),v(P,{ref_key:"formRef",ref:i,model:l(a),rules:l(g),"label-width":"80px"},{default:s(()=>[t(n,{label:"IP",prop:"ip"},{default:s(()=>[t(p,{modelValue:l(a).ip,"onUpdate:modelValue":e[0]||(e[0]=r=>l(a).ip=r),placeholder:"\u8BF7\u8F93\u5165 IP \u5730\u5740"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u5730\u5740",prop:"result"},{default:s(()=>[t(p,{modelValue:l(a).result,"onUpdate:modelValue":e[1]||(e[1]=r=>l(a).result=r),placeholder:"\u5C55\u793A\u67E5\u8BE2 IP \u7ED3\u679C",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[k,l(d)]])]),_:1},8,["modelValue"])}}});export{R as _,L as g};
