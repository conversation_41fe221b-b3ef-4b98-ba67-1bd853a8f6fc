import{d as n,p as i,aN as p,o as u,c as d,k as f,w as v,l as a,u as s,t}from"./index-C8b06LRn.js";import{E as x}from"./el-avatar-BVm8aVjJ.js";import{a as w}from"./avatar-CPqUN878.js";const _={class:"flex items-center"},g={class:"text-20px"},k=n({__name:"welcome-card",setup(U){const{t:c}=i(),e=p(),l=e.getUser.avatar,o=e.getUser.nickname;return(z,r)=>{const m=x;return u(),d("div",_,[f(m,{src:s(l),size:60,class:"mr-16px"},{default:v(()=>r[0]||(r[0]=[a("img",{src:w,alt:""},null,-1)])),_:1},8,["src"]),a("div",null,[a("div",g,t(s(o))+", "+t(s(c)("workplace.welcome"))+"! ",1)])])}}});export{k as _};
