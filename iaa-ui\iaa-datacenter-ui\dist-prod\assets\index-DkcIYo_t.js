import{d as e,e as s,Y as a,j as f,u as n,o as r,c as t,k as o}from"./index-C8b06LRn.js";import l from"./recordMobile-DDUpdyxm.js";import{_ as u}from"./recordPC.vue_vue_type_script_setup_true_lang-DhS40LvR.js";import"./claim-BPjhJe4B.js";import"./recordDialogMobile-BcRVzwqq.js";import"./index-DVzg-3-A.js";import"./index-584CLaw9.js";import"./VDatePicker.vue_vue_type_script_setup_true_lang-fYMpMkPC.js";import"./function-call-DKtMTaHE.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./Filter-Dzz2caxb.js";import"./recordDialogPC-D0S1AgEs.js";import"./PermissionForm.vue_vue_type_script_setup_true_lang-BbCkMcuR.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./index-rrqDtE6o.js";const c={key:0},d={key:1},_=e({__name:"index",setup(k){const i=s(),m=a(()=>i.getMobile),p=f();return(y,j)=>n(m)?(r(),t("div",d,[o(l)])):(r(),t("div",c,[o(u,{ref_key:"pcRef",ref:p},null,512)]))}});export{_ as default};
