import{_ as m}from"./PC.vue_vue_type_script_setup_true_lang-DFwiBPZo.js";import i from"./Mobile-CmDzQBdi.js";import{d as p,e as s,u as e,o,h as r}from"./index-C8b06LRn.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./index-rrqDtE6o.js";import"./vxeCustom-D2Re1O-c.js";import"./Filter-Dzz2caxb.js";import"./PermissionForm.vue_vue_type_script_setup_true_lang-BbCkMcuR.js";import"./download-D5Lb_h0f.js";const a=p({__name:"index",setup(n){const{mobile:t}=s();return(_,f)=>e(t)?(o(),r(i,{key:1})):(o(),r(m,{key:0}))}});export{a as default};
