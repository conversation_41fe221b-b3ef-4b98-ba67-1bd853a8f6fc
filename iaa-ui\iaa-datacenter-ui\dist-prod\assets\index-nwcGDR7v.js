import{bo as ao,bq as so,d as ge,bb as je,j as Y,b8 as ne,Y as O,u as o,br as io,f as Xe,bs as co,aE as it,aO as Ge,e7 as uo,i as St,bv as Ce,bc as Tt,F as Ht,bw as ho,bH as mo,bJ as dt,au as Je,dJ as Ae,bU as fo,bQ as Ve,bY as ct,bd as Qe,ci as Ie,bX as wo,co as po,aR as Ct,ba as G,cm as go,b6 as Le,bf as Ne,k as b,d4 as yo,ar as oe,av as It,e8 as xo,e9 as bo,be as Et,cN as Ro,bx as vo,aL as So,bh as Mt,o as To,c as Ho,w as Ze,v as Co,l as Io,G as Eo,H as Mo}from"./index-C8b06LRn.js";import{E as Oo}from"./el-empty-ag1-OZ0J.js";import{j as Ee,I as Ot,S as Wt,F as et,u as Wo,B as Kt,d as tt,A as ot,R as Ko,g as Dt,a as Do,b as At,k as Lt,c as zt,e as Ao,C as ze,E as ut,f as ht,h as Ft,D as kt,v as Lo,l as zo}from"./el-virtual-list-BIjfPDZX.js";import{_ as Fo}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ko}from"./index-CkzUfjB7.js";import{_ as jo,g as Go}from"./AreaForm.vue_vue_type_script_setup_true_lang-Bq0tRPq2.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const jt=({name:e,clearCache:t,getColumnPosition:l,getColumnStartIndexForOffset:r,getColumnStopIndexForStartIndex:n,getEstimatedTotalHeight:a,getEstimatedTotalWidth:i,getColumnOffset:s,getRowOffset:h,getRowPosition:u,getRowStartIndexForOffset:d,getRowStopIndexForStartIndex:m,initCache:v,injectToInstance:A,validateProps:W})=>ge({name:e??"ElVirtualList",props:Ee,emits:[Ot,Wt],setup(c,{emit:B,expose:P,slots:_}){const V=je("vl");W(c);const q=it(),x=Y(v(c,q));A==null||A(q,x);const D=Y(),N=Y(),U=Y(),Q=Y(null),L=Y({isScrolling:!1,scrollLeft:ne(c.initScrollLeft)?c.initScrollLeft:0,scrollTop:ne(c.initScrollTop)?c.initScrollTop:0,updateRequested:!1,xAxisScrollDir:et,yAxisScrollDir:et}),H=Wo(),K=O(()=>Number.parseInt(`${c.height}`,10)),j=O(()=>Number.parseInt(`${c.width}`,10)),X=O(()=>{const{totalColumn:f,totalRow:g,columnCache:w}=c,{isScrolling:M,xAxisScrollDir:y,scrollLeft:S}=o(L);if(f===0||g===0)return[0,0,0,0];const R=r(c,S,o(x)),C=n(c,R,S,o(x)),T=M&&y!==Kt?1:Math.max(1,w),I=M&&y!==et?1:Math.max(1,w);return[Math.max(0,R-T),Math.max(0,Math.min(f-1,C+I)),R,C]}),se=O(()=>{const{totalColumn:f,totalRow:g,rowCache:w}=c,{isScrolling:M,yAxisScrollDir:y,scrollTop:S}=o(L);if(f===0||g===0)return[0,0,0,0];const R=d(c,S,o(x)),C=m(c,R,S,o(x)),T=M&&y!==Kt?1:Math.max(1,w),I=M&&y!==et?1:Math.max(1,w);return[Math.max(0,R-T),Math.max(0,Math.min(g-1,C+I)),R,C]}),te=O(()=>a(c,o(x))),re=O(()=>i(c,o(x))),xe=O(()=>{var f;return[{position:"relative",overflow:"hidden",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:c.direction,height:ne(c.height)?`${c.height}px`:c.height,width:ne(c.width)?`${c.width}px`:c.width},(f=c.style)!=null?f:{}]}),be=O(()=>{const f=`${o(re)}px`;return{height:`${o(te)}px`,pointerEvents:o(L).isScrolling?"none":void 0,width:f}}),ae=()=>{const{totalColumn:f,totalRow:g}=c;if(f>0&&g>0){const[C,T,I,z]=o(X),[k,p,E,$]=o(se);B(Ot,{columnCacheStart:C,columnCacheEnd:T,rowCacheStart:k,rowCacheEnd:p,columnVisibleStart:I,columnVisibleEnd:z,rowVisibleStart:E,rowVisibleEnd:$})}const{scrollLeft:w,scrollTop:M,updateRequested:y,xAxisScrollDir:S,yAxisScrollDir:R}=o(L);B(Wt,{xAxisScrollDir:S,scrollLeft:w,yAxisScrollDir:R,scrollTop:M,updateRequested:y})},Re=f=>{const{clientHeight:g,clientWidth:w,scrollHeight:M,scrollLeft:y,scrollTop:S,scrollWidth:R}=f.currentTarget,C=o(L);if(C.scrollTop===S&&C.scrollLeft===y)return;let T=y;if(Lt(c.direction))switch(Dt()){case At:T=-y;break;case Ao:T=R-w-y}L.value={...C,isScrolling:!0,scrollLeft:T,scrollTop:Math.max(0,Math.min(S,M-g)),updateRequested:!0,xAxisScrollDir:tt(C.scrollLeft,T),yAxisScrollDir:tt(C.scrollTop,S)},Ge(()=>Se()),Ke(),ae()},ve=(f,g)=>{const w=o(K),M=(te.value-w)/g*f;ie({scrollTop:Math.min(te.value-w,M)})},ue=(f,g)=>{const w=o(j),M=(re.value-w)/g*f;ie({scrollLeft:Math.min(re.value-w,M)})},{onWheel:ce}=(({atXEndEdge:f,atXStartEdge:g,atYEndEdge:w,atYStartEdge:M},y)=>{let S=null,R=0,C=0;const T=(I,z)=>{const k=I<=0&&g.value||I>=0&&f.value,p=z<=0&&M.value||z>=0&&w.value;return k&&p};return{hasReachedEdge:T,onWheel:I=>{ao(S);let z=I.deltaX,k=I.deltaY;Math.abs(z)>Math.abs(k)?k=0:z=0,I.shiftKey&&k!==0&&(z=k,k=0),T(R,C)&&T(R+z,C+k)||(R+=z,C+=k,I.preventDefault(),S=so(()=>{y(R,C),R=0,C=0}))}}})({atXStartEdge:O(()=>L.value.scrollLeft<=0),atXEndEdge:O(()=>L.value.scrollLeft>=re.value-o(j)),atYStartEdge:O(()=>L.value.scrollTop<=0),atYEndEdge:O(()=>L.value.scrollTop>=te.value-o(K))},(f,g)=>{var w,M,y,S;(M=(w=N.value)==null?void 0:w.onMouseUp)==null||M.call(w),(S=(y=U.value)==null?void 0:y.onMouseUp)==null||S.call(y);const R=o(j),C=o(K);ie({scrollLeft:Math.min(L.value.scrollLeft+f,re.value-R),scrollTop:Math.min(L.value.scrollTop+g,te.value-C)})});io(D,"wheel",ce,{passive:!1});const ie=({scrollLeft:f=L.value.scrollLeft,scrollTop:g=L.value.scrollTop})=>{f=Math.max(f,0),g=Math.max(g,0);const w=o(L);g===w.scrollTop&&f===w.scrollLeft||(L.value={...w,xAxisScrollDir:tt(w.scrollLeft,f),yAxisScrollDir:tt(w.scrollTop,g),scrollLeft:f,scrollTop:g,updateRequested:!0},Ge(()=>Se()),Ke(),ae())},pe=(f,g)=>{const{columnWidth:w,direction:M,rowHeight:y}=c,S=H.value(t&&w,t&&y,t&&M),R=`${f},${g}`;if(ho(S,R))return S[R];{const[,C]=l(c,g,o(x)),T=o(x),I=Lt(M),[z,k]=u(c,f,T),[p]=l(c,g,T);return S[R]={position:"absolute",left:I?void 0:`${C}px`,right:I?`${C}px`:void 0,top:`${k}px`,height:`${z}px`,width:`${p}px`},S[R]}},Se=()=>{L.value.isScrolling=!1,Ge(()=>{H.value(-1,null,null)})};Xe(()=>{if(!co)return;const{initScrollLeft:f,initScrollTop:g}=c,w=o(D);w&&(ne(f)&&(w.scrollLeft=f),ne(g)&&(w.scrollTop=g)),ae()});const Ke=()=>{const{direction:f}=c,{scrollLeft:g,scrollTop:w,updateRequested:M}=o(L),y=o(D);if(M&&y){if(f===Ko)switch(Dt()){case At:y.scrollLeft=-g;break;case Do:y.scrollLeft=g;break;default:{const{clientWidth:S,scrollWidth:R}=y;y.scrollLeft=R-S-g;break}}else y.scrollLeft=Math.max(0,g);y.scrollTop=Math.max(0,w)}},{resetAfterColumnIndex:Te,resetAfterRowIndex:Fe,resetAfter:ke}=q.proxy;P({windowRef:D,innerRef:Q,getItemStyleCache:H,scrollTo:ie,scrollToItem:(f=0,g=0,w=ot)=>{const M=o(L);g=Math.max(0,Math.min(g,c.totalColumn-1)),f=Math.max(0,Math.min(f,c.totalRow-1));const y=uo(V.namespace.value),S=o(x),R=a(c,S),C=i(c,S);ie({scrollLeft:s(c,g,w,M.scrollLeft,S,C>c.width?y:0),scrollTop:h(c,f,w,M.scrollTop,S,R>c.height?y:0)})},states:L,resetAfterColumnIndex:Te,resetAfterRowIndex:Fe,resetAfter:ke});const F=()=>{const f=St(c.innerElement),g=(()=>{var w;const[M,y]=o(X),[S,R]=o(se),{data:C,totalColumn:T,totalRow:I,useIsScrolling:z,itemKey:k}=c,p=[];if(I>0&&T>0)for(let E=S;E<=R;E++)for(let $=M;$<=y;$++){const le=k({columnIndex:$,data:C,rowIndex:E});p.push(Ce(Ht,{key:le},(w=_.default)==null?void 0:w.call(_,{columnIndex:$,data:C,isScrolling:z?o(L).isScrolling:void 0,style:pe(E,$),rowIndex:E})))}return p})();return[Ce(f,{style:o(be),ref:Q},Tt(f)?g:{default:()=>g})]};return()=>{const f=St(c.containerElement),{horizontalScrollbar:g,verticalScrollbar:w}=(()=>{const{scrollbarAlwaysOn:y,scrollbarStartGap:S,scrollbarEndGap:R,totalColumn:C,totalRow:T}=c,I=o(j),z=o(K),k=o(re),p=o(te),{scrollLeft:E,scrollTop:$}=o(L);return{horizontalScrollbar:Ce(zt,{ref:N,alwaysOn:y,startGap:S,endGap:R,class:V.e("horizontal"),clientSize:I,layout:"horizontal",onScroll:ue,ratio:100*I/k,scrollFrom:E/(k-I),total:T,visible:!0}),verticalScrollbar:Ce(zt,{ref:U,alwaysOn:y,startGap:S,endGap:R,class:V.e("vertical"),clientSize:z,layout:"vertical",onScroll:ve,ratio:100*z/p,scrollFrom:$/(p-z),total:C,visible:!0})}})(),M=F();return Ce("div",{key:0,class:V.e("wrapper"),role:c.role},[Ce(f,{class:c.className,style:o(xe),onScroll:Re,ref:D},Tt(f)?M:{default:()=>M}),g,w])}}}),Vo=jt({name:"ElFixedSizeGrid",getColumnPosition:({columnWidth:e},t)=>[e,t*e],getRowPosition:({rowHeight:e},t)=>[e,t*e],getEstimatedTotalHeight:({totalRow:e,rowHeight:t})=>t*e,getEstimatedTotalWidth:({totalColumn:e,columnWidth:t})=>t*e,getColumnOffset:({totalColumn:e,columnWidth:t,width:l},r,n,a,i,s)=>{l=Number(l);const h=Math.max(0,e*t-l),u=Math.min(h,r*t),d=Math.max(0,r*t-l+s+t);switch(n==="smart"&&(n=a>=d-l&&a<=u+l?ot:ze),n){case ht:return u;case ut:return d;case ze:{const m=Math.round(d+(u-d)/2);return m<Math.ceil(l/2)?0:m>h+Math.floor(l/2)?h:m}default:return a>=d&&a<=u?a:d>u||a<d?d:u}},getRowOffset:({rowHeight:e,height:t,totalRow:l},r,n,a,i,s)=>{t=Number(t);const h=Math.max(0,l*e-t),u=Math.min(h,r*e),d=Math.max(0,r*e-t+s+e);switch(n===Ft&&(n=a>=d-t&&a<=u+t?ot:ze),n){case ht:return u;case ut:return d;case ze:{const m=Math.round(d+(u-d)/2);return m<Math.ceil(t/2)?0:m>h+Math.floor(t/2)?h:m}default:return a>=d&&a<=u?a:d>u||a<d?d:u}},getColumnStartIndexForOffset:({columnWidth:e,totalColumn:t},l)=>Math.max(0,Math.min(t-1,Math.floor(l/e))),getColumnStopIndexForStartIndex:({columnWidth:e,totalColumn:t,width:l},r,n)=>{const a=r*e,i=Math.ceil((l+n-a)/e);return Math.max(0,Math.min(t-1,r+i-1))},getRowStartIndexForOffset:({rowHeight:e,totalRow:t},l)=>Math.max(0,Math.min(t-1,Math.floor(l/e))),getRowStopIndexForStartIndex:({rowHeight:e,totalRow:t,height:l},r,n)=>{const a=r*e,i=Math.ceil((l+n-a)/e);return Math.max(0,Math.min(t-1,r+i-1))},initCache:()=>{},clearCache:!0,validateProps:({columnWidth:e,rowHeight:t})=>{}}),{max:lt,min:Gt,floor:Vt}=Math,No={column:"columnWidth",row:"rowHeight"},mt={column:"lastVisitedColumnIndex",row:"lastVisitedRowIndex"},we=(e,t,l,r)=>{const[n,a,i]=[l[r],e[No[r]],l[mt[r]]];if(t>i){let s=0;if(i>=0){const h=n[i];s=h.offset+h.size}for(let h=i+1;h<=t;h++){const u=a(h);n[h]={offset:s,size:u},s+=u}l[mt[r]]=t}return n[t]},Nt=(e,t,l,r,n,a)=>{for(;l<=r;){const i=l+Vt((r-l)/2),s=we(e,i,t,a).offset;if(s===n)return i;s<n?l=i+1:r=i-1}return lt(0,l-1)},Pt=(e,t,l,r)=>{const[n,a]=[t[r],t[mt[r]]];return(a>0?n[a].offset:0)>=l?Nt(e,t,0,a,l,r):((i,s,h,u,d)=>{const m=d==="column"?i.totalColumn:i.totalRow;let v=1;for(;h<m&&we(i,h,s,d).offset<u;)h+=v,v*=2;return Nt(i,s,Vt(h/2),Gt(h,m-1),u,d)})(e,t,lt(0,a),l,r)},$t=({totalRow:e},{estimatedRowHeight:t,lastVisitedRowIndex:l,row:r})=>{let n=0;if(l>=e&&(l=e-1),l>=0){const a=r[l];n=a.offset+a.size}return n+(e-l-1)*t},Bt=({totalColumn:e},{column:t,estimatedColumnWidth:l,lastVisitedColumnIndex:r})=>{let n=0;if(r>e&&(r=e-1),r>=0){const a=t[r];n=a.offset+a.size}return n+(e-r-1)*l},Po={column:Bt,row:$t},qt=(e,t,l,r,n,a,i)=>{const[s,h]=[a==="row"?e.height:e.width,Po[a]],u=we(e,t,n,a),d=h(e,n),m=lt(0,Gt(d-s,u.offset)),v=lt(0,u.offset-s+i+u.size);switch(l===Ft&&(l=r>=v-s&&r<=m+s?ot:ze),l){case ht:return m;case ut:return v;case ze:return Math.round(v+(m-v)/2);default:return r>=v&&r<=m?r:v>m||r<v?v:m}},$o=jt({name:"ElDynamicSizeGrid",getColumnPosition:(e,t,l)=>{const r=we(e,t,l,"column");return[r.size,r.offset]},getRowPosition:(e,t,l)=>{const r=we(e,t,l,"row");return[r.size,r.offset]},getColumnOffset:(e,t,l,r,n,a)=>qt(e,t,l,r,n,"column",a),getRowOffset:(e,t,l,r,n,a)=>qt(e,t,l,r,n,"row",a),getColumnStartIndexForOffset:(e,t,l)=>Pt(e,l,t,"column"),getColumnStopIndexForStartIndex:(e,t,l,r)=>{const n=we(e,t,r,"column"),a=l+e.width;let i=n.offset+n.size,s=t;for(;s<e.totalColumn-1&&i<a;)s++,i+=we(e,t,r,"column").size;return s},getEstimatedTotalHeight:$t,getEstimatedTotalWidth:Bt,getRowStartIndexForOffset:(e,t,l)=>Pt(e,l,t,"row"),getRowStopIndexForStartIndex:(e,t,l,r)=>{const{totalRow:n,height:a}=e,i=we(e,t,r,"row"),s=l+a;let h=i.size+i.offset,u=t;for(;u<n-1&&h<s;)u++,h+=we(e,u,r,"row").size;return u},injectToInstance:(e,t)=>{const l=({columnIndex:r,rowIndex:n},a)=>{var i,s;a=!!mo(a)||a,ne(r)&&(t.value.lastVisitedColumnIndex=Math.min(t.value.lastVisitedColumnIndex,r-1)),ne(n)&&(t.value.lastVisitedRowIndex=Math.min(t.value.lastVisitedRowIndex,n-1)),(i=e.exposed)==null||i.getItemStyleCache.value(-1,null,null),a&&((s=e.proxy)==null||s.$forceUpdate())};Object.assign(e.proxy,{resetAfterColumnIndex:(r,n)=>{l({columnIndex:r},n)},resetAfterRowIndex:(r,n)=>{l({rowIndex:r},n)},resetAfter:l})},initCache:({estimatedColumnWidth:e=kt,estimatedRowHeight:t=kt})=>({column:{},estimatedColumnWidth:e,estimatedRowHeight:t,lastVisitedColumnIndex:-1,lastVisitedRowIndex:-1,row:{}}),clearCache:!1,validateProps:({columnWidth:e,rowHeight:t})=>{}});var Pe=(e=>(e.ASC="asc",e.DESC="desc",e))(Pe||{}),$e=(e=>(e.CENTER="center",e.RIGHT="right",e))($e||{}),Ut=(e=>(e.LEFT="left",e.RIGHT="right",e))(Ut||{});const ft={asc:"desc",desc:"asc"},Be=Symbol("placeholder"),Bo=(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,tableInstance:n,ns:a,isScrolling:i})=>{const s=it(),{emit:h}=s,u=Ae(!1),d=Y(e.defaultExpandedRowKeys||[]),m=Y(-1),v=Ae(null),A=Y({}),W=Y({}),c=Ae({}),B=Ae({}),P=Ae({}),_=O(()=>ne(e.estimatedRowHeight)),V=fo(()=>{var x,D,N,U;u.value=!0,A.value={...o(A),...o(W)},q(o(v),!1),W.value={},v.value=null,(x=t.value)==null||x.forceUpdate(),(D=l.value)==null||D.forceUpdate(),(N=r.value)==null||N.forceUpdate(),(U=s.proxy)==null||U.$forceUpdate(),u.value=!1},0);function q(x,D=!1){o(_)&&[t,l,r].forEach(N=>{const U=o(N);U&&U.resetAfterRowIndex(x,D)})}return{expandedRowKeys:d,lastRenderedRowIndex:m,isDynamic:_,isResetting:u,rowHeights:A,resetAfterIndex:q,onRowExpanded:function({expanded:x,rowData:D,rowIndex:N,rowKey:U}){var Q,L;const H=[...o(d)],K=H.indexOf(U);x?K===-1&&H.push(U):K>-1&&H.splice(K,1),d.value=H,h("update:expandedRowKeys",H),(Q=e.onRowExpand)==null||Q.call(e,{expanded:x,rowData:D,rowIndex:N,rowKey:U}),(L=e.onExpandedRowsChange)==null||L.call(e,H)},onRowHovered:function({hovered:x,rowKey:D}){i.value||n.vnode.el.querySelectorAll(`[rowkey="${String(D)}"]`).forEach(N=>{x?N.classList.add(a.is("hovered")):N.classList.remove(a.is("hovered"))})},onRowsRendered:function(x){var D;(D=e.onRowsRendered)==null||D.call(e,x),x.rowCacheEnd>o(m)&&(m.value=x.rowCacheEnd)},onRowHeightChange:function({rowKey:x,height:D,rowIndex:N},U){U?U===Ut.RIGHT?P.value[x]=D:c.value[x]=D:B.value[x]=D;const Q=Math.max(...[c,P,B].map(L=>L.value[x]||0));o(A)[x]!==Q&&(function(L,H,K){const j=o(v);(j===null||j>K)&&(v.value=K),W.value[L]=H}(x,Q,N),V())}}},qo=(e,t)=>e+t,rt=e=>Ve(e)?e.reduce(qo,0):e,Me=(e,t,l={})=>ct(e)?e(t):e??l,ye=e=>(["width","maxWidth","minWidth","height"].forEach(t=>{e[t]=Qe(e[t])}),e),_t=e=>Ie(e)?t=>Ce(e,t):e;function Uo(e){const t=Y(),l=Y(),r=Y(),{columns:n,columnsStyles:a,columnsTotalWidth:i,fixedColumnsOnLeft:s,fixedColumnsOnRight:h,hasFixedColumns:u,mainColumns:d,onColumnSorted:m}=function(F,f,g){const w=O(()=>o(f).map((p,E)=>{var $,le;return{...p,key:(le=($=p.key)!=null?$:p.dataKey)!=null?le:E}})),M=O(()=>o(w).filter(p=>!p.hidden)),y=O(()=>o(M).filter(p=>p.fixed==="left"||p.fixed===!0)),S=O(()=>o(M).filter(p=>p.fixed==="right")),R=O(()=>o(M).filter(p=>!p.fixed)),C=O(()=>{const p=[];return o(y).forEach(E=>{p.push({...E,placeholderSign:Be})}),o(R).forEach(E=>{p.push(E)}),o(S).forEach(E=>{p.push({...E,placeholderSign:Be})}),p}),T=O(()=>o(y).length||o(S).length),I=O(()=>o(w).reduce((p,E)=>(p[E.key]=(($,le,he)=>{var me;const J={flexGrow:0,flexShrink:0,...he?{}:{flexGrow:$.flexGrow||0,flexShrink:$.flexShrink||1}};he||(J.flexShrink=1);const Z={...(me=$.style)!=null?me:{},...J,flexBasis:"auto",width:$.width};return le||($.maxWidth&&(Z.maxWidth=$.maxWidth),$.minWidth&&(Z.minWidth=$.minWidth)),Z})(E,o(g),F.fixed),p),{})),z=O(()=>o(M).reduce((p,E)=>p+E.width,0)),k=p=>o(w).find(E=>E.key===p);return{columns:w,columnsStyles:I,columnsTotalWidth:z,fixedColumnsOnLeft:y,fixedColumnsOnRight:S,hasFixedColumns:T,mainColumns:C,normalColumns:R,visibleColumns:M,getColumn:k,getColumnStyle:p=>o(I)[p],updateColumnWidth:(p,E)=>{p.width=E},onColumnSorted:function(p){var E;const{key:$}=p.currentTarget.dataset;if(!$)return;const{sortState:le,sortBy:he}=F;let me=Pe.ASC;me=dt(le)?ft[le[$]]:ft[he.order],(E=F.onColumnSort)==null||E.call(F,{column:k($),key:$,order:me})}}}(e,Ct(e,"columns"),Ct(e,"fixed")),{scrollTo:v,scrollToLeft:A,scrollToTop:W,scrollToRow:c,onScroll:B,onVerticalScroll:P,scrollPos:_}=((F,{mainTableRef:f,leftTableRef:g,rightTableRef:w,onMaybeEndReached:M})=>{const y=Y({scrollLeft:0,scrollTop:0});function S(T){var I,z,k;const{scrollTop:p}=T;(I=f.value)==null||I.scrollTo(T),(z=g.value)==null||z.scrollToTop(p),(k=w.value)==null||k.scrollToTop(p)}function R(T){y.value=T,S(T)}function C(T){y.value.scrollTop=T,S(o(y))}return Je(()=>o(y).scrollTop,(T,I)=>{T>I&&M()}),{scrollPos:y,scrollTo:R,scrollToLeft:function(T){var I,z;y.value.scrollLeft=T,(z=(I=f.value)==null?void 0:I.scrollTo)==null||z.call(I,o(y))},scrollToTop:C,scrollToRow:function(T,I="auto"){var z;(z=f.value)==null||z.scrollToRow(T,I)},onScroll:function(T){var I;R(T),(I=F.onScroll)==null||I.call(F,T)},onVerticalScroll:function({scrollTop:T}){const{scrollTop:I}=o(y);T!==I&&C(T)}}})(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,onMaybeEndReached:function(){const{onEndReached:F}=e;if(!F)return;const{scrollTop:f}=o(_),g=o(ce),w=o(ie),M=g-(f+w)+e.hScrollbarSize;o(N)>=0&&g===f+o(ae)-o(Te)&&F(M)}}),V=je("table-v2"),q=it(),x=Ae(!1),{expandedRowKeys:D,lastRenderedRowIndex:N,isDynamic:U,isResetting:Q,rowHeights:L,resetAfterIndex:H,onRowExpanded:K,onRowHeightChange:j,onRowHovered:X,onRowsRendered:se}=Bo(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,tableInstance:q,ns:V,isScrolling:x}),{data:te,depthMap:re}=((F,{expandedRowKeys:f,lastRenderedRowIndex:g,resetAfterIndex:w})=>{const M=Y({}),y=O(()=>{const R={},{data:C,rowKey:T}=F,I=o(f);if(!I||!I.length)return C;const z=[],k=new Set;I.forEach(E=>k.add(E));let p=C.slice();for(p.forEach(E=>R[E[T]]=0);p.length>0;){const E=p.shift();z.push(E),k.has(E[T])&&Array.isArray(E.children)&&E.children.length>0&&(p=[...E.children,...p],E.children.forEach($=>R[$[T]]=R[E[T]]+1))}return M.value=R,z}),S=O(()=>{const{data:R,expandColumnKey:C}=F;return C?o(y):R});return Je(S,(R,C)=>{R!==C&&(g.value=-1,w(0,!0))}),{data:S,depthMap:M}})(e,{expandedRowKeys:D,lastRenderedRowIndex:N,resetAfterIndex:H}),{bodyWidth:xe,fixedTableHeight:be,mainTableHeight:ae,leftTableWidth:Re,rightTableWidth:ve,headerWidth:ue,rowsHeight:ce,windowHeight:ie,footerHeight:pe,emptyStyle:Se,rootStyle:Ke,headerHeight:Te}=((F,{columnsTotalWidth:f,data:g,fixedColumnsOnLeft:w,fixedColumnsOnRight:M})=>{const y=O(()=>{const{fixed:J,width:Z,vScrollbarSize:ee}=F,De=Z-ee;return J?Math.max(Math.round(o(f)),De):De}),S=O(()=>o(y)+F.vScrollbarSize),R=O(()=>{const{height:J=0,maxHeight:Z=0,footerHeight:ee,hScrollbarSize:De}=F;if(Z>0){const _e=o(E),at=o(C),Ye=o(p)+_e+at+De;return Math.min(Ye,Z-ee)}return J-ee}),C=O(()=>{const{rowHeight:J,estimatedRowHeight:Z}=F,ee=o(g);return ne(Z)?ee.length*Z:ee.length*J}),T=O(()=>{const{maxHeight:J}=F,Z=o(R);if(ne(J)&&J>0)return Z;const ee=o(C)+o(p)+o(E);return Math.min(Z,ee)}),I=J=>J.width,z=O(()=>rt(o(w).map(I))),k=O(()=>rt(o(M).map(I))),p=O(()=>rt(F.headerHeight)),E=O(()=>{var J;return(((J=F.fixedData)==null?void 0:J.length)||0)*F.rowHeight}),$=O(()=>o(R)-o(p)-o(E)),le=O(()=>{const{style:J={},height:Z,width:ee}=F;return ye({...J,height:Z,width:ee})}),he=O(()=>ye({height:F.footerHeight})),me=O(()=>({top:Qe(o(p)),bottom:Qe(F.footerHeight),width:Qe(F.width)}));return{bodyWidth:y,fixedTableHeight:T,mainTableHeight:R,leftTableWidth:z,rightTableWidth:k,headerWidth:S,rowsHeight:C,windowHeight:$,footerHeight:he,emptyStyle:me,rootStyle:le,headerHeight:p}})(e,{columnsTotalWidth:i,data:te,fixedColumnsOnLeft:s,fixedColumnsOnRight:h}),Fe=Y(),ke=O(()=>{const F=o(te).length===0;return Ve(e.fixedData)?e.fixedData.length===0&&F:F});return Je(()=>e.expandedRowKeys,F=>D.value=F,{deep:!0}),{columns:n,containerRef:Fe,mainTableRef:t,leftTableRef:l,rightTableRef:r,isDynamic:U,isResetting:Q,isScrolling:x,hasFixedColumns:u,columnsStyles:a,columnsTotalWidth:i,data:te,expandedRowKeys:D,depthMap:re,fixedColumnsOnLeft:s,fixedColumnsOnRight:h,mainColumns:d,bodyWidth:xe,emptyStyle:Se,rootStyle:Ke,headerWidth:ue,footerHeight:pe,mainTableHeight:ae,fixedTableHeight:be,leftTableWidth:Re,rightTableWidth:ve,showEmpty:ke,getRowHeight:function(F){const{estimatedRowHeight:f,rowHeight:g,rowKey:w}=e;return f?o(L)[o(te)[F][w]]||f:g},onColumnSorted:m,onRowHovered:X,onRowExpanded:K,onRowsRendered:se,onRowHeightChange:j,scrollTo:v,scrollToLeft:A,scrollToTop:W,scrollToRow:c,onScroll:B,onVerticalScroll:P}}const wt=Symbol("tableV2"),Yt=String,qe={type:G(Array),required:!0},pt={type:G(Array)},Xt={...pt,required:!0},_o=String,Jt={type:G(Array),default:()=>go([])},Oe={type:Number,required:!0},Qt={type:G([String,Number,Symbol]),default:"id"},Zt={type:G(Object)},We=Le({class:String,columns:qe,columnsStyles:{type:G(Object),required:!0},depth:Number,expandColumnKey:_o,estimatedRowHeight:{...Ee.estimatedRowHeight,default:void 0},isScrolling:Boolean,onRowExpand:{type:G(Function)},onRowHover:{type:G(Function)},onRowHeightChange:{type:G(Function)},rowData:{type:G(Object),required:!0},rowEventHandlers:{type:G(Object)},rowIndex:{type:Number,required:!0},rowKey:Qt,style:{type:G(Object)}}),gt={type:Number,required:!0},yt=Le({class:String,columns:qe,fixedHeaderData:{type:G(Array)},headerData:{type:G(Array),required:!0},headerHeight:{type:G([Number,Array]),default:50},rowWidth:gt,rowHeight:{type:Number,default:50},height:gt,width:gt}),nt=Le({columns:qe,data:Xt,fixedData:pt,estimatedRowHeight:We.estimatedRowHeight,width:Oe,height:Oe,headerWidth:Oe,headerHeight:yt.headerHeight,bodyWidth:Oe,rowHeight:Oe,cache:Lo.cache,useIsScrolling:Boolean,scrollbarAlwaysOn:Ee.scrollbarAlwaysOn,scrollbarStartGap:Ee.scrollbarStartGap,scrollbarEndGap:Ee.scrollbarEndGap,class:Yt,style:Zt,containerStyle:Zt,getRowHeight:{type:G(Function),required:!0},rowKey:We.rowKey,onRowsRendered:{type:G(Function)},onScroll:{type:G(Function)}}),Yo=Le({cache:nt.cache,estimatedRowHeight:We.estimatedRowHeight,rowKey:Qt,headerClass:{type:G([String,Function])},headerProps:{type:G([Object,Function])},headerCellProps:{type:G([Object,Function])},headerHeight:yt.headerHeight,footerHeight:{type:Number,default:0},rowClass:{type:G([String,Function])},rowProps:{type:G([Object,Function])},rowHeight:{type:Number,default:50},cellProps:{type:G([Object,Function])},columns:qe,data:Xt,dataGetter:{type:G(Function)},fixedData:pt,expandColumnKey:We.expandColumnKey,expandedRowKeys:Jt,defaultExpandedRowKeys:Jt,class:Yt,fixed:Boolean,style:{type:G(Object)},width:Oe,height:Oe,maxHeight:Number,useIsScrolling:Boolean,indentSize:{type:Number,default:12},iconSize:{type:Number,default:12},hScrollbarSize:Ee.hScrollbarSize,vScrollbarSize:Ee.vScrollbarSize,scrollbarAlwaysOn:zo.alwaysOn,sortBy:{type:G(Object),default:()=>({})},sortState:{type:G(Object),default:void 0},onColumnSort:{type:G(Function)},onExpandedRowsChange:{type:G(Function)},onEndReached:{type:G(Function)},onRowExpand:We.onRowExpand,onScroll:nt.onScroll,onRowsRendered:nt.onRowsRendered,rowEventHandlers:We.rowEventHandlers}),xt=(e,{slots:t})=>{var l;const{cellData:r,style:n}=e,a=((l=r==null?void 0:r.toString)==null?void 0:l.call(r))||"",i=Ne(t,"default",e,()=>[a]);return b("div",{class:e.class,title:a,style:n},[i])};xt.displayName="ElTableV2Cell",xt.inheritAttrs=!1;const bt=(e,{slots:t})=>Ne(t,"default",e,()=>{var l,r;return[b("div",{class:e.class,title:(l=e.column)==null?void 0:l.title},[(r=e.column)==null?void 0:r.title])]});bt.displayName="ElTableV2HeaderCell",bt.inheritAttrs=!1;const Xo=Le({class:String,columns:qe,columnsStyles:{type:G(Object),required:!0},headerIndex:Number,style:{type:G(Object)}}),Jo=ge({name:"ElTableV2HeaderRow",props:Xo,setup:(e,{slots:t})=>()=>{const{columns:l,columnsStyles:r,headerIndex:n,style:a}=e;let i=l.map((s,h)=>t.cell({columns:l,column:s,columnIndex:h,headerIndex:n,style:r[s.key]}));return t.header&&(i=t.header({cells:i.map(s=>Ve(s)&&s.length===1?s[0]:s),columns:l,headerIndex:n})),b("div",{class:e.class,style:a,role:"row"},[i])}}),Qo=ge({name:"ElTableV2Header",props:yt,setup(e,{slots:t,expose:l}){const r=je("table-v2"),n=Y(),a=O(()=>ye({width:e.width,height:e.height})),i=O(()=>ye({width:e.rowWidth,height:e.height})),s=O(()=>yo(o(e.headerHeight))),h=()=>{const d=r.e("fixed-header-row"),{columns:m,fixedHeaderData:v,rowHeight:A}=e;return v==null?void 0:v.map((W,c)=>{var B;const P=ye({height:A,width:"100%"});return(B=t.fixed)==null?void 0:B.call(t,{class:d,columns:m,rowData:W,rowIndex:-(c+1),style:P})})},u=()=>{const d=r.e("dynamic-header-row"),{columns:m}=e;return o(s).map((v,A)=>{var W;const c=ye({width:"100%",height:v});return(W=t.dynamic)==null?void 0:W.call(t,{class:d,columns:m,headerIndex:A,style:c})})};return l({scrollToLeft:d=>{const m=o(n);Ge(()=>{m!=null&&m.scroll&&m.scroll({left:d})})}}),()=>{if(!(e.height<=0))return b("div",{ref:n,class:e.class,style:o(a),role:"rowgroup"},[b("div",{style:o(i),class:r.e("header")},[u(),h()])])}}}),Zo=e=>{const{isScrolling:t}=It(wt),l=Y(!1),r=Y(),n=O(()=>ne(e.estimatedRowHeight)&&e.rowIndex>=0),a=O(()=>{const{rowData:i,rowIndex:s,rowKey:h,onRowHover:u}=e,d=e.rowEventHandlers||{},m={};return Object.entries(d).forEach(([v,A])=>{ct(A)&&(m[v]=W=>{A({event:W,rowData:i,rowIndex:s,rowKey:h})})}),u&&[{name:"onMouseleave",hovered:!1},{name:"onMouseenter",hovered:!0}].forEach(({name:v,hovered:A})=>{const W=m[v];m[v]=c=>{u({event:c,hovered:A,rowData:i,rowIndex:s,rowKey:h}),W==null||W(c)}}),m});return Xe(()=>{o(n)&&((i=!1)=>{const s=o(r);if(!s)return;const{columns:h,onRowHeightChange:u,rowKey:d,rowIndex:m,style:v}=e,{height:A}=s.getBoundingClientRect();l.value=!0,Ge(()=>{if(i||A!==Number.parseInt(v.height)){const W=h[0],c=(W==null?void 0:W.placeholderSign)===Be;u==null||u({rowKey:d,height:A,rowIndex:m},W&&!c&&W.fixed)}})})(!0)}),{isScrolling:t,measurable:n,measured:l,rowRef:r,eventHandlers:a,onExpand:i=>{const{onRowExpand:s,rowData:h,rowIndex:u,rowKey:d}=e;s==null||s({expanded:i,rowData:h,rowIndex:u,rowKey:d})}}},el=ge({name:"ElTableV2TableRow",props:We,setup(e,{expose:t,slots:l,attrs:r}){const{eventHandlers:n,isScrolling:a,measurable:i,measured:s,rowRef:h,onExpand:u}=Zo(e);return t({onExpand:u}),()=>{const{columns:d,columnsStyles:m,expandColumnKey:v,depth:A,rowData:W,rowIndex:c,style:B}=e;let P=d.map((_,V)=>{const q=Ve(W.children)&&W.children.length>0&&_.key===v;return l.cell({column:_,columns:d,columnIndex:V,depth:A,style:m[_.key],rowData:W,rowIndex:c,isScrolling:o(a),expandIconProps:q?{rowData:W,rowIndex:c,onExpand:u}:void 0})});if(l.row&&(P=l.row({cells:P.map(_=>Ve(_)&&_.length===1?_[0]:_),style:B,columns:d,depth:A,rowData:W,rowIndex:c,isScrolling:o(a)})),o(i)){const{height:_,...V}=B||{},q=o(s);return b("div",oe({ref:h,class:e.class,style:q?B:V,role:"row"},r,o(n)),[P])}return b("div",oe(r,{ref:h,class:e.class,style:B,role:"row"},o(n)),[P])}}}),tl=e=>{const{sortOrder:t}=e;return b(Et,{size:14,class:e.class},{default:()=>[t===Pe.ASC?b(xo,null,null):b(bo,null,null)]})},ol=e=>{const{expanded:t,expandable:l,onExpand:r,style:n,size:a}=e,i={onClick:l?()=>r(!t):void 0,class:e.class};return b(Et,oe(i,{size:a,style:n}),{default:()=>[b(Ro,null,null)]})},Rt=ge({name:"ElTableV2Grid",props:nt,setup(e,{slots:t,expose:l}){const{ns:r}=It(wt),{bodyRef:n,fixedRowHeight:a,gridHeight:i,hasHeader:s,headerRef:h,headerHeight:u,totalHeight:d,forceUpdate:m,itemKey:v,onItemRendered:A,resetAfterRowIndex:W,scrollTo:c,scrollToTop:B,scrollToRow:P}=(V=>{const q=Y(),x=Y(),D=O(()=>{const{data:H,rowHeight:K,estimatedRowHeight:j}=V;if(!j)return H.length*K}),N=O(()=>{const{fixedData:H,rowHeight:K}=V;return((H==null?void 0:H.length)||0)*K}),U=O(()=>rt(V.headerHeight)),Q=O(()=>{const{height:H}=V;return Math.max(0,H-o(U)-o(N))}),L=O(()=>o(U)+o(N)>0);return{bodyRef:x,forceUpdate:function(){var H,K;(H=o(x))==null||H.$forceUpdate(),(K=o(q))==null||K.$forceUpdate()},fixedRowHeight:N,gridHeight:Q,hasHeader:L,headerHeight:U,headerRef:q,totalHeight:D,itemKey:({data:H,rowIndex:K})=>H[K][V.rowKey],onItemRendered:function({rowCacheStart:H,rowCacheEnd:K,rowVisibleStart:j,rowVisibleEnd:X}){var se;(se=V.onRowsRendered)==null||se.call(V,{rowCacheStart:H,rowCacheEnd:K,rowVisibleStart:j,rowVisibleEnd:X})},resetAfterRowIndex:function(H,K){var j;(j=x.value)==null||j.resetAfterRowIndex(H,K)},scrollTo:function(H,K){const j=o(q),X=o(x);dt(H)?(j==null||j.scrollToLeft(H.scrollLeft),X==null||X.scrollTo(H)):(j==null||j.scrollToLeft(H),X==null||X.scrollTo({scrollLeft:H,scrollTop:K}))},scrollToTop:function(H){var K;(K=o(x))==null||K.scrollTo({scrollTop:H})},scrollToRow:function(H,K){var j;(j=o(x))==null||j.scrollToItem(H,1,K)}}})(e);l({forceUpdate:m,totalHeight:d,scrollTo:c,scrollToTop:B,scrollToRow:P,resetAfterRowIndex:W});const _=()=>e.bodyWidth;return()=>{const{cache:V,columns:q,data:x,fixedData:D,useIsScrolling:N,scrollbarAlwaysOn:U,scrollbarEndGap:Q,scrollbarStartGap:L,style:H,rowHeight:K,bodyWidth:j,estimatedRowHeight:X,headerWidth:se,height:te,width:re,getRowHeight:xe,onScroll:be}=e,ae=ne(X),Re=ae?$o:Vo,ve=o(u);return b("div",{role:"table",class:[r.e("table"),e.class],style:H},[b(Re,{ref:n,data:x,useIsScrolling:N,itemKey:v,columnCache:0,columnWidth:ae?_:j,totalColumn:1,totalRow:x.length,rowCache:V,rowHeight:ae?xe:K,width:re,height:o(i),class:r.e("body"),role:"rowgroup",scrollbarStartGap:L,scrollbarEndGap:Q,scrollbarAlwaysOn:U,onScroll:be,onItemRendered:A,perfMode:!1},{default:ue=>{var ce;const ie=x[ue.rowIndex];return(ce=t.row)==null?void 0:ce.call(t,{...ue,columns:q,rowData:ie})}}),o(s)&&b(Qo,{ref:h,class:r.e("header-wrapper"),columns:q,headerData:x,headerHeight:e.headerHeight,fixedHeaderData:D,rowWidth:se,rowHeight:K,width:re,height:Math.min(ve+o(a),te)},{dynamic:t.header,fixed:t.row})])}}}),ll=(e,{slots:t})=>{const{mainTableRef:l,...r}=e;return b(Rt,oe({ref:l},r),typeof(n=t)=="function"||Object.prototype.toString.call(n)==="[object Object]"&&!Ie(n)?t:{default:()=>[t]});var n},rl=(e,{slots:t})=>{if(!e.columns.length)return;const{leftTableRef:l,...r}=e;return b(Rt,oe({ref:l},r),typeof(n=t)=="function"||Object.prototype.toString.call(n)==="[object Object]"&&!Ie(n)?t:{default:()=>[t]});var n},nl=(e,{slots:t})=>{if(!e.columns.length)return;const{rightTableRef:l,...r}=e;return b(Rt,oe({ref:l},r),typeof(n=t)=="function"||Object.prototype.toString.call(n)==="[object Object]"&&!Ie(n)?t:{default:()=>[t]});var n},al=(e,{slots:t})=>{const{columns:l,columnsStyles:r,depthMap:n,expandColumnKey:a,expandedRowKeys:i,estimatedRowHeight:s,hasFixedColumns:h,rowData:u,rowIndex:d,style:m,isScrolling:v,rowProps:A,rowClass:W,rowKey:c,rowEventHandlers:B,ns:P,onRowHovered:_,onRowExpanded:V}=e,q=Me(W,{columns:l,rowData:u,rowIndex:d},""),x=Me(A,{columns:l,rowData:u,rowIndex:d}),D=u[c],N=n[D]||0,U=!!a,Q=d<0,L=[P.e("row"),q,{[P.e(`row-depth-${N}`)]:U&&d>=0,[P.is("expanded")]:U&&i.includes(D),[P.is("fixed")]:!N&&Q,[P.is("customized")]:!!t.row}],H=h?_:void 0,K={...x,columns:l,columnsStyles:r,class:L,depth:N,expandColumnKey:a,estimatedRowHeight:Q?void 0:s,isScrolling:v,rowIndex:d,rowData:u,rowKey:D,rowEventHandlers:B,style:m};return b(el,oe(K,{onRowExpand:V,onMouseenter:X=>{H==null||H({hovered:!0,rowKey:D,event:X,rowData:u,rowIndex:d})},onMouseleave:X=>{H==null||H({hovered:!1,rowKey:D,event:X,rowData:u,rowIndex:d})},rowkey:D}),typeof(j=t)=="function"||Object.prototype.toString.call(j)==="[object Object]"&&!Ie(j)?t:{default:()=>[t]});var j},vt=({columns:e,column:t,columnIndex:l,depth:r,expandIconProps:n,isScrolling:a,rowData:i,rowIndex:s,style:h,expandedRowKeys:u,ns:d,cellProps:m,expandColumnKey:v,indentSize:A,iconSize:W,rowKey:c},{slots:B})=>{const P=ye(h);if(t.placeholderSign===Be)return b("div",{class:d.em("row-cell","placeholder"),style:P},null);const{cellRenderer:_,dataKey:V,dataGetter:q}=t,x=ct(q)?q({columns:e,column:t,columnIndex:l,rowData:i,rowIndex:s}):vo(i,V??""),D=Me(m,{cellData:x,columns:e,column:t,columnIndex:l,rowIndex:s,rowData:i}),N={class:d.e("cell-text"),columns:e,column:t,columnIndex:l,cellData:x,isScrolling:a,rowData:i,rowIndex:s},U=_t(_),Q=U?U(N):Ne(B,"default",N,()=>[b(xt,N,null)]),L=[d.e("row-cell"),t.class,t.align===$e.CENTER&&d.is("align-center"),t.align===$e.RIGHT&&d.is("align-right")],H=s>=0&&v&&t.key===v,K=s>=0&&u.includes(i[c]);let j;const X=`margin-inline-start: ${r*A}px;`;return H&&(j=dt(n)?b(ol,oe(n,{class:[d.e("expand-icon"),d.is("expanded",K)],size:W,expanded:K,style:X,expandable:!0}),null):b("div",{style:[X,`width: ${W}px; height: ${W}px;`].join(" ")},null)),b("div",oe({class:L,style:P},D,{role:"cell"}),[j,Q])};vt.inheritAttrs=!1;const sl=({columns:e,columnsStyles:t,headerIndex:l,style:r,headerClass:n,headerProps:a,ns:i},{slots:s})=>{const h={columns:e,headerIndex:l},u=[i.e("header-row"),Me(n,h,""),{[i.is("customized")]:!!s.header}],d={...Me(a,h),columnsStyles:t,class:u,columns:e,headerIndex:l,style:r};return b(Jo,d,typeof(m=s)=="function"||Object.prototype.toString.call(m)==="[object Object]"&&!Ie(m)?s:{default:()=>[s]});var m},eo=(e,{slots:t})=>{const{column:l,ns:r,style:n,onColumnSorted:a}=e,i=ye(n);if(l.placeholderSign===Be)return b("div",{class:r.em("header-row-cell","placeholder"),style:i},null);const{headerCellRenderer:s,headerClass:h,sortable:u}=l,d={...e,class:r.e("header-cell-text")},m=_t(s),v=m?m(d):Ne(t,"default",d,()=>[b(bt,d,null)]),{sortBy:A,sortState:W,headerCellProps:c}=e;let B,P;if(W){const q=W[l.key];B=!!ft[q],P=B?q:Pe.ASC}else B=l.key===A.key,P=B?A.order:Pe.ASC;const _=[r.e("header-cell"),Me(h,e,""),l.align===$e.CENTER&&r.is("align-center"),l.align===$e.RIGHT&&r.is("align-right"),u&&r.is("sortable")],V={...Me(c,e),onClick:l.sortable?a:void 0,class:_,style:i,"data-key":l.key};return b("div",oe(V,{role:"columnheader"}),[v,u&&b(tl,{class:[r.e("sort-icon"),B&&r.is("sorting")],sortOrder:P},null)])},to=(e,{slots:t})=>{var l;return b("div",{class:e.class,style:e.style},[(l=t.default)==null?void 0:l.call(t)])};to.displayName="ElTableV2Footer";const oo=(e,{slots:t})=>{const l=Ne(t,"default",{},()=>[b(Oo,null,null)]);return b("div",{class:e.class,style:e.style},[l])};oo.displayName="ElTableV2Empty";const lo=(e,{slots:t})=>{var l;return b("div",{class:e.class,style:e.style},[(l=t.default)==null?void 0:l.call(t)])};function Ue(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Ie(e)}lo.displayName="ElTableV2Overlay";const il=ge({name:"ElTableV2",props:Yo,setup(e,{slots:t,expose:l}){const r=je("table-v2"),{columnsStyles:n,fixedColumnsOnLeft:a,fixedColumnsOnRight:i,mainColumns:s,mainTableHeight:h,fixedTableHeight:u,leftTableWidth:d,rightTableWidth:m,data:v,depthMap:A,expandedRowKeys:W,hasFixedColumns:c,mainTableRef:B,leftTableRef:P,rightTableRef:_,isDynamic:V,isResetting:q,isScrolling:x,bodyWidth:D,emptyStyle:N,rootStyle:U,headerWidth:Q,footerHeight:L,showEmpty:H,scrollTo:K,scrollToLeft:j,scrollToTop:X,scrollToRow:se,getRowHeight:te,onColumnSorted:re,onRowHeightChange:xe,onRowHovered:be,onRowExpanded:ae,onRowsRendered:Re,onScroll:ve,onVerticalScroll:ue}=Uo(e);return l({scrollTo:K,scrollToLeft:j,scrollToTop:X,scrollToRow:se}),So(wt,{ns:r,isResetting:q,isScrolling:x}),()=>{const{cache:ce,cellProps:ie,estimatedRowHeight:pe,expandColumnKey:Se,fixedData:Ke,headerHeight:Te,headerClass:Fe,headerProps:ke,headerCellProps:F,sortBy:f,sortState:g,rowHeight:w,rowClass:M,rowEventHandlers:y,rowKey:S,rowProps:R,scrollbarAlwaysOn:C,indentSize:T,iconSize:I,useIsScrolling:z,vScrollbarSize:k,width:p}=e,E=o(v),$={cache:ce,class:r.e("main"),columns:o(s),data:E,fixedData:Ke,estimatedRowHeight:pe,bodyWidth:o(D)+k,headerHeight:Te,headerWidth:o(Q),height:o(h),mainTableRef:B,rowKey:S,rowHeight:w,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:k,useIsScrolling:z,width:p,getRowHeight:te,onRowsRendered:Re,onScroll:ve},le=o(d),he=o(u),me={cache:ce,class:r.e("left"),columns:o(a),data:E,estimatedRowHeight:pe,leftTableRef:P,rowHeight:w,bodyWidth:le,headerWidth:le,headerHeight:Te,height:he,rowKey:S,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:k,useIsScrolling:z,width:le,getRowHeight:te,onScroll:ue},J=o(m)+k,Z={cache:ce,class:r.e("right"),columns:o(i),data:E,estimatedRowHeight:pe,rightTableRef:_,rowHeight:w,bodyWidth:J,headerWidth:J,headerHeight:Te,height:he,rowKey:S,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:k,width:J,style:`--${o(r.namespace)}-table-scrollbar-size: ${k}px`,useIsScrolling:z,getRowHeight:te,onScroll:ue},ee=o(n),De={ns:r,depthMap:o(A),columnsStyles:ee,expandColumnKey:Se,expandedRowKeys:o(W),estimatedRowHeight:pe,hasFixedColumns:o(c),rowProps:R,rowClass:M,rowKey:S,rowEventHandlers:y,onRowHovered:be,onRowExpanded:ae,onRowHeightChange:xe},_e={cellProps:ie,expandColumnKey:Se,indentSize:T,iconSize:I,rowKey:S,expandedRowKeys:o(W),ns:r},at={ns:r,headerClass:Fe,headerProps:ke,columnsStyles:ee},Ye={ns:r,sortBy:f,sortState:g,headerCellProps:F,onColumnSorted:re},fe={row:st=>b(al,oe(st,De),{row:t.row,cell:de=>{let He;return t.cell?b(vt,oe(de,_e,{style:ee[de.column.key]}),Ue(He=t.cell(de))?He:{default:()=>[He]}):b(vt,oe(de,_e,{style:ee[de.column.key]}),null)}}),header:st=>b(sl,oe(st,at),{header:t.header,cell:de=>{let He;return t["header-cell"]?b(eo,oe(de,Ye,{style:ee[de.column.key]}),Ue(He=t["header-cell"](de))?He:{default:()=>[He]}):b(eo,oe(de,Ye,{style:ee[de.column.key]}),null)}})},ro=[e.class,r.b(),r.e("root"),{[r.is("dynamic")]:o(V)}],no={class:r.e("footer"),style:o(L)};return b("div",{class:ro,style:o(U)},[b(ll,$,Ue(fe)?fe:{default:()=>[fe]}),b(rl,me,Ue(fe)?fe:{default:()=>[fe]}),b(nl,Z,Ue(fe)?fe:{default:()=>[fe]}),t.footer&&b(to,no,{default:t.footer}),o(H)&&b(oo,{class:r.e("empty"),style:o(N)},{default:t.empty}),t.overlay&&b(lo,{class:r.e("overlay")},{default:t.overlay})])}}}),dl=Le({disableWidth:Boolean,disableHeight:Boolean,onResize:{type:G(Function)}}),cl=ge({name:"ElAutoResizer",props:dl,setup(e,{slots:t}){const l=je("auto-resizer"),{height:r,width:n,sizer:a}=(s=>{const h=Y(),u=Y(0),d=Y(0);let m;return Xe(()=>{m=wo(h,([v])=>{const{width:A,height:W}=v.contentRect,{paddingLeft:c,paddingRight:B,paddingTop:P,paddingBottom:_}=getComputedStyle(v.target),V=Number.parseInt(c)||0,q=Number.parseInt(B)||0,x=Number.parseInt(P)||0,D=Number.parseInt(_)||0;u.value=A-V-q,d.value=W-x-D}).stop}),po(()=>{m==null||m()}),Je([u,d],([v,A])=>{var W;(W=s.onResize)==null||W.call(s,{width:v,height:A})}),{sizer:h,width:u,height:d}})(e),i={width:"100%",height:"100%"};return()=>{var s;return b("div",{ref:a,class:l.b(),style:i},[(s=t.default)==null?void 0:s.call(t,{height:r.value,width:n.value})])}}}),ul=Mt(il),hl=Mt(cl),ml={style:{width:"100%",height:"700px"}},fl=ge({name:"SystemArea",__name:"index",setup(e){const t=[{dataKey:"id",title:"\u7F16\u53F7",width:400,fixed:!0,key:"id"},{dataKey:"name",title:"\u5730\u540D",width:200}],l=Y([]),r=Y();return Xe(()=>{(async()=>l.value=await Go())()}),(n,a)=>{const i=ko,s=Eo,h=Mo,u=Fo,d=ul,m=hl;return To(),Ho(Ht,null,[b(i,{title:"\u5730\u533A & IP",url:"https://doc.iocoder.cn/area-and-ip/"}),b(u,null,{default:Ze(()=>[b(h,{type:"primary",plain:"",onClick:a[0]||(a[0]=v=>{r.value.open()})},{default:Ze(()=>[b(s,{icon:"ep:plus",class:"mr-5px"}),a[1]||(a[1]=Co(" IP \u67E5\u8BE2 "))]),_:1})]),_:1}),b(u,null,{default:Ze(()=>[Io("div",ml,[b(m,null,{default:Ze(({height:v,width:A})=>[b(d,{columns:t,data:o(l),width:A,height:v,"expand-column-key":"id"},null,8,["data","width","height"])]),_:1})])]),_:1}),b(jo,{ref_key:"formRef",ref:r},null,512)],64)}}});export{fl as default};
