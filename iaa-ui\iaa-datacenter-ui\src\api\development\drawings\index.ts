import request from '@/config/axios'

export const DrawingApi = {

  getQueryPartKind: () => {
    return request.post({ url: `/development/drawings/queryPartKind`})
  },

  getQueryDocumentByItemCode: (params: any) => {
    return request.post({ url: `/development/drawings/queryDocumentByItemCode`,data:params })
  },

  getPage: (params: any) => {
    return request.post({ url: `/development/drawings/page`,data:params })
  },

  getDocPathOrWx: (params: any) => {
    return request.post({ url: `/butt-joint/plm/attribute/getDocPathOrWx`,data:params })
  },
  destoryOrWx: (params: any) => {
    return request.post({ url: `/butt-joint/plm/attribute/destoryOrWx`,data:params })
  },

  addDrawingLog: (params: any) => {
    return request.post({ url: `/development/drawings/addDrawingLog`,data:params })
  },
  getDrawingLogPage: (params: any) => {
    return request.post({ url: `/development/drawings/pageLog`,data:params })
  }
}