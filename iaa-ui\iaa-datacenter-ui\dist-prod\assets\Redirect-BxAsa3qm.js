import{d as o,u as n,o as d,c as i,a as u}from"./index-C8b06LRn.js";const y=o({name:"Redirect",__name:"Redirect",setup(m){const{currentRoute:p,replace:s}=u(),{params:e,query:a}=n(p),{path:r,_redirect_type:c="path"}=e;Reflect.deleteProperty(e,"_redirect_type"),Reflect.deleteProperty(e,"path");const t=Array.isArray(r)?r.join("/"):r;return s(c==="name"?{name:t,query:a,params:e}:{path:t.startsWith("/")?t:"/"+t,query:a}),(l,_)=>(d(),i("div"))}});export{y as default};
