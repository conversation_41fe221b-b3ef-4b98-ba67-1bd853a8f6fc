import{_ as ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as we}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as Ve,j as v,Y as Ae,dl as w,y as Ye,dm as Pe,f as Se,e4 as ze,aO as Ue,u as a,aF as j,T as Ne,o as D,h as ae,w as i,l as f,k as e,n as Me,x as te,v as y,b$ as Oe,c as C,F as S,g as z,t as ie,$ as Te,ax as re,aD as Be,aq as qe,C as Re,G as je,H as Fe,aC as Ee,B as Ie,a8 as Ge,z as Le,A as He,ab as Xe,I as $e,at as Qe,aB as Je,_ as Ke}from"./index-C8b06LRn.js";import{O as We,a as L}from"./OrderTrackingDetailForm-UY8C3wrG.js";import{c as Ze}from"./permission-DVzqLl93.js";import{t as T,n as el,d as c}from"./Filter-Dzz2caxb.js";import{c as ll}from"./vxeCustom-D2Re1O-c.js";import{f as al}from"./dateUtil-D9m5ek6U.js";import{g as tl}from"./index-GtctjSt3.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";import"./index-WiqCEeob.js";import"./el-drawer-C5TFtzfV.js";import"./CardTitle-00NfZwLk.js";import"./el-empty-ag1-OZ0J.js";import"./index-B2m4kQ_G.js";import"./DetailDate-DCB2Zfz0.js";const il={class:"h-[calc(100vh-140px)]"},rl={class:"ml-10px"},ol={class:"text-right mt-10px"},dl={key:0,class:"mr-10px"},sl={class:"text-right"},nl={class:"h-[calc(100%-100px)]"},ul={class:"flex"},fl={class:"w-50%"},cl=Ve({__name:"index",setup(ml){const N=v(!1),g=v({id:void 0,approveDeviation:void 0,schedulingDeviation:void 0,completeSetDeviation:void 0,rcvDeviation:void 0,shipDeviation:void 0}),V=[{label:"\u6B63\u5E38-\u7EFF\u8272\uFF0C\u767D\u8272",value:"10"},{label:"\u9884\u8B66-\u6A59\u8272",value:"11"},{label:"\u8D85\u671F-\u7EA2\u8272",value:"12"},{label:"\u8D85\u671F\u5B8C\u6210-\u6A59\u7EA2",value:"13"}],H={10:{label:"\u5236\u9020\u4EF6",value:"10"},9:{label:"\u91C7\u8D2D\u4EF6",value:"9"},4:{label:"\u59D4\u5916\u4EF6",value:"4"},6:{label:"\u865A\u62DF\u4EF6",value:"6"}},oe=Ae(()=>Object.values(H)),s=v({sellerName:[{data:""}],customerName:[{data:""}],docNo:[{data:""}],itemCode:[{data:""}],itemName:[{data:""}],spec:[{data:""}],qty:[{data:{condition:"10",value:void 0}}],bomPlanDate:[{data:[]}],bomActualDate:[{data:[]}],approvePlanDate:[{data:[]}],approveActualDate:[{data:[]}],schedulingPlanDate:[{data:[]}],schedulingActualDate:[{data:[]}],completeSetPlanDate:[{data:[]}],completeSetActualDate:[{data:[]}],assemblyPlanDate:[{data:[]}],assemblyActualDate:[{data:[]}],packingPlanDate:[{data:[]}],packingActualDate:[{data:[]}],rcvPlanDate:[{data:[]}],rcvActualDate:[{data:[]}],shipPlanDate:[{data:[]}],shipActualDate:[{data:[]}],planShipDate:[{data:[]}]}),r=v({pageNo:1,pageSize:100,approveDateType:"year",approveDate:w().format("YYYY-MM-DD"),sorting:[],hasUnComplete:!1,hasBomAfterScheduling:!1,bomColor:void 0,approvalColor:void 0,schedulingColor:void 0,completeSetColor:void 0,assemblyColor:void 0,packingColor:void 0,rcvColor:void 0,shipColor:void 0}),B=v("YYYY\u5E74"),F=v(!1),X=v([]),$=v(0),Q=v(),J=v(),K=v(),de=Ye(),se=async()=>{if(N.value=!N.value,!N.value)return;const o=await L.getOrderTrackingEdit();o&&(g.value=o)},ne=async()=>{await L.saveOrderTrackingEdit(g.value),de.success("\u4FDD\u5B58\u6210\u529F"),N.value=!1,A()},E=async()=>{F.value=!0;try{const o=re(r.value);r.value.approveDate instanceof Array&&r.value.approveDate.length>0&&(o.approveDate=r.value.approveDate[0]+","+r.value.approveDate[1]);const l=await L.getOrderTrackingPage(o);X.value=l.list,$.value=l.total}finally{F.value=!1}},A=()=>{r.value.pageNo=1,E()},ue=({row:o,column:l})=>{let u,p,b;if(l.field.includes("Plan")){if(!o[l.field]&&!o[l.field.replace("Plan","Actual")])return{};u=o[l.field]?w(o[l.field]):void 0,p=o[l.field.replace("Plan","Actual")]?w(o[l.field.replace("Plan","Actual")]):w(),b=o[l.field.replace("Plan","Actual")]}else{if(!l.field.includes("Actual"))return{};if(!o[o[l.field.replace("Actual","Plan")]]&&!o[l.field])return{};u=o[l.field.replace("Actual","Plan")]?w(o[l.field.replace("Actual","Plan")]):void 0,p=o[l.field]?w(o[l.field]):w(),b=o[l.field]}if(!u&&b)return{backgroundColor:"var(--el-color-success-light-3)",color:"#ffffff",cursor:"pointer"};const U=u.startOf("day"),h=p.startOf("day"),O=U.diff(h,"days");return b?O>=0?{backgroundColor:"var(--el-color-success-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"#ee7959",color:"#ffffff",cursor:"pointer"}:O>=2?{cursor:"pointer"}:O>=0?{backgroundColor:"var(--el-color-warning-light-3)",color:"#ffffff",cursor:"pointer"}:{backgroundColor:"#e60012",color:"#ffffff",cursor:"pointer"}},fe=({row:o})=>o.shipActualDate?{backgroundColor:"var(--el-color-success-light-7) !important",cursor:"pointer"}:{cursor:"pointer"},ce=()=>{switch(r.value.approveDateType){case"year":B.value="YYYY\u5E74";break;case"month":B.value="YYYY\u5E74MM\u6708";break;case"week":B.value="YYYY\u5E74ww\u5468";break;case"daterange":B.value="YYYY\u5E74MM\u6708DD\u65E5",r.value.approveDate=[]}r.value.approveDateType!=="daterange"&&(r.value.approveDate=w().format("YYYY-MM-DD"),A())},pe=o=>{const l=["orgName","property","currency","attribute"],u={};o.filterList.forEach(p=>{const{field:b,values:U,datas:h}=p;l.includes(b)&&U.length>0?u[b]=U:h.length>0&&(u[b]=h[0])}),Object.keys(r.value).forEach(p=>{["pageNo","pageSize","sorting","approveDate","approveDateType","hasUnComplete"].includes(p)||o.filterList.some(b=>b.field===p)||(r.value[p]=void 0)}),Object.assign(r.value,u),A()},me=({field:o,order:l})=>{if(l){const u=r.value.sorting.find(p=>p.field==o);u?u.order=l:r.value.sorting.push({field:o,order:l})}else r.value.sorting=r.value.sorting.filter(u=>u.field!=o);A()},I=v([]),W=v(),ve=()=>{r.value={pageNo:1,pageSize:100,approveDateType:"year",approveDate:w().format("YYYY-MM-DD"),sorting:[],hasUnComplete:!1,hasBomAfterScheduling:!1,bomColor:void 0,approvalColor:void 0,schedulingColor:void 0,completeSetColor:void 0,assemblyColor:void 0,packingColor:void 0,rcvColor:void 0,shipColor:void 0},G.delete("order-tracking-query"),A(),M.value=!1},{wsCache:G}=Pe(),ge=()=>{M.value=!1;const o=re(r.value);delete o.pageNo,delete o.pageSize,delete o.sorting,delete o.approveDateType,delete o.approveDate,G.set("order-tracking-query",o),A()},M=v(!1),be=o=>{var u,p;const l=(p=(u=W.value)==null?void 0:u.popperRef)==null?void 0:p.contentRef;M.value&&!(l!=null&&l.contains(o.target))&&(M.value=!1)};return Se(()=>{const o=G.get("order-tracking-query");o&&ze(r.value,o),E(),(async()=>{const l=await tl({pageNo:1,pageSize:10,jobId:50});I.value=l.list})(),Ue(()=>{var l;(l=a(J))==null||l.connect(a(Q))})}),(o,l)=>{const u=Be,p=qe,b=Re,U=je,h=Fe,O=Ee,m=Ie,_=Ge,Y=Le,P=He,he=Xe,Z=$e,ee=Qe,q=Je,De=j("vxe-toolbar"),d=j("vxe-column"),k=j("vxe-colgroup"),ye=j("vxe-table"),_e=we,xe=ke,Ce=Ne("hasPermi");return D(),ae(xe,null,{default:i(()=>[f("div",il,[e(De,{size:"small",custom:"",ref_key:"toolbarRef",ref:Q},{buttons:i(()=>[e(p,{modelValue:a(r).approveDateType,"onUpdate:modelValue":l[0]||(l[0]=t=>a(r).approveDateType=t),size:"small",onChange:ce},{default:i(()=>[e(u,{label:"\u5E74",value:"year"}),e(u,{label:"\u6708",value:"month"}),e(u,{label:"\u5468",value:"week"}),e(u,{label:"\u8303\u56F4",value:"daterange"})]),_:1},8,["modelValue"]),f("div",rl,[e(b,{class:Me([a(r).approveDateType==="daterange"?"!w-240px":"!w-120px"]),type:a(r).approveDateType,"value-format":"YYYY-MM-DD",modelValue:a(r).approveDate,"onUpdate:modelValue":l[1]||(l[1]=t=>a(r).approveDate=t),size:"small","unlink-panels":"",format:a(B),clearable:!1,onChange:A},null,8,["class","type","modelValue","format"])]),e(ee,{ref_key:"popoverRef",ref:W,width:"500",placement:"bottom-start",trigger:"click",teleported:!1,visible:a(M)},{reference:i(()=>[te((D(),ae(h,{type:"primary",plain:"",size:"small",class:"ml-10px",onClick:l[2]||(l[2]=t=>M.value=!0)},{default:i(()=>[l[22]||(l[22]=y(" \u7B5B\u9009 ")),e(U,{icon:"ep:filter"})]),_:1})),[[a(Oe),be]])]),default:i(()=>[e(Z,{"label-position":"top",class:"custom-form",size:"small"},{default:i(()=>[e(he,null,{default:i(()=>[e(_,{span:12},{default:i(()=>[e(m,{label:"\u9690\u85CF\u5DF2\u5B8C\u6210"},{default:i(()=>[e(O,{modelValue:a(r).hasUnComplete,"onUpdate:modelValue":l[3]||(l[3]=t=>a(r).hasUnComplete=t),class:"ml-10px","active-value":!0,"inactive-value":!1},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"BOM\u8BA1\u5212\u665A\u4E8E\u6392\u4EA7"},{default:i(()=>[e(O,{modelValue:a(r).hasBomAfterScheduling,"onUpdate:modelValue":l[4]||(l[4]=t=>a(r).hasBomAfterScheduling=t)},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u8BA2\u5355BOM\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).bomColor,"onUpdate:modelValue":l[5]||(l[5]=t=>a(r).bomColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u8BA2\u5355\u5BA1\u6838\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).approvalColor,"onUpdate:modelValue":l[6]||(l[6]=t=>a(r).approvalColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u6392\u4EA7\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).schedulingColor,"onUpdate:modelValue":l[7]||(l[7]=t=>a(r).schedulingColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u9F50\u5957\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).completeSetColor,"onUpdate:modelValue":l[8]||(l[8]=t=>a(r).completeSetColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u7EC4\u88C5\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).assemblyColor,"onUpdate:modelValue":l[9]||(l[9]=t=>a(r).assemblyColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u5305\u88C5\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).packingColor,"onUpdate:modelValue":l[10]||(l[10]=t=>a(r).packingColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u5165\u5E93\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).rcvColor,"onUpdate:modelValue":l[11]||(l[11]=t=>a(r).rcvColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:i(()=>[e(m,{label:"\u51FA\u8D27\u989C\u8272\u7B5B\u9009"},{default:i(()=>[e(P,{modelValue:a(r).shipColor,"onUpdate:modelValue":l[12]||(l[12]=t=>a(r).shipColor=t),teleported:!1,multiple:"","collapse-tags":""},{default:i(()=>[(D(),C(S,null,z(V,(t,n)=>e(Y,{label:t.label,value:t.value,key:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),f("div",ol,[e(h,{type:"warning",size:"small",onClick:ve},{default:i(()=>l[23]||(l[23]=[y(" \u91CD\u7F6E\u67E5\u8BE2 ")])),_:1}),e(h,{type:"primary",size:"small",onClick:ge},{default:i(()=>l[24]||(l[24]=[y(" \u4FDD\u5B58\u6761\u4EF6\u5E76\u67E5\u8BE2 ")])),_:1})])]),_:1},8,["visible"]),e(h,{type:"primary",plain:"",size:"small",class:"ml-10px",onClick:A},{default:i(()=>l[25]||(l[25]=[y("\u5237\u65B0\u6570\u636E")])),_:1})]),tools:i(()=>{var t,n,R,le;return[(n=(t=a(I))==null?void 0:t[0])!=null&&n.endTime?(D(),C("div",dl," \u6570\u636E\u540C\u6B65\u65F6\u95F4\uFF1A"+ie(a(al)((le=(R=a(I))==null?void 0:R[0])==null?void 0:le.endTime)),1)):Te("",!0),e(ee,{visible:a(N),placement:"bottom-end",width:450,trigger:"click"},{reference:i(()=>[e(h,{circle:"",class:"mr-10px",onClick:se},{default:i(()=>[e(U,{icon:"ep:setting"})]),_:1})]),default:i(()=>[e(Z,{"label-width":"250px",size:"small",disabled:!a(Ze)(["order:tracking:edit-save"])},{default:i(()=>[e(m,{label:"\u8BA2\u5355\u5BA1\u6838\u8BA1\u5212\u65E5\u671F = \u4E1A\u52A1\u4E0B\u5355\u65E5\u671F +"},{default:i(()=>[e(q,{modelValue:a(g).approveDeviation,"onUpdate:modelValue":l[13]||(l[13]=x=>a(g).approveDeviation=x)},null,8,["modelValue"]),l[26]||(l[26]=y(" \xA0\xA0\xA0\xA0\u5929 "))]),_:1}),e(m,{label:"\u6392\u4EA7\u8BA1\u5212\u65E5\u671F = \u8BA2\u5355\u5BA1\u6838\u65E5\u671F +"},{default:i(()=>[e(q,{modelValue:a(g).schedulingDeviation,"onUpdate:modelValue":l[14]||(l[14]=x=>a(g).schedulingDeviation=x)},null,8,["modelValue"]),l[27]||(l[27]=y(" \xA0\xA0\xA0\xA0\u5929 "))]),_:1}),e(m,{label:"\u7269\u6599\u9F50\u5957\u8BA1\u5212\u65E5\u671F = \u5DE5\u5355\u6700\u65E9\u5F00\u5DE5\u65E5\u671F +"},{default:i(()=>[e(q,{modelValue:a(g).completeSetDeviation,"onUpdate:modelValue":l[15]||(l[15]=x=>a(g).completeSetDeviation=x)},null,8,["modelValue"]),l[28]||(l[28]=y(" \xA0\xA0\xA0\xA0\u5929 "))]),_:1}),e(m,{label:"\u5165\u5E93\u8BA1\u5212\u65E5\u671F = \u6700\u65E9\u6210\u54C1\u5165\u5E93\u5355\u521B\u5EFA\u65E5\u671F +"},{default:i(()=>[e(q,{modelValue:a(g).rcvDeviation,"onUpdate:modelValue":l[16]||(l[16]=x=>a(g).rcvDeviation=x)},null,8,["modelValue"]),l[29]||(l[29]=y(" \xA0\xA0\xA0\xA0\u5929 "))]),_:1}),e(m,{label:"\u51FA\u8D27\u8BA1\u5212\u65E5\u671F = \u5B9E\u9645\u5165\u5E93\u65E5\u671F +"},{default:i(()=>[e(q,{modelValue:a(g).shipDeviation,"onUpdate:modelValue":l[17]||(l[17]=x=>a(g).shipDeviation=x)},null,8,["modelValue"]),l[30]||(l[30]=y(" \xA0\xA0\xA0\xA0\u5929 "))]),_:1})]),_:1},8,["disabled"]),te((D(),C("div",sl,[e(h,{size:"small",onClick:l[18]||(l[18]=x=>N.value=!1)},{default:i(()=>l[31]||(l[31]=[y("\u5173\u95ED")])),_:1}),e(h,{type:"primary",size:"small",onClick:ne},{default:i(()=>l[32]||(l[32]=[y("\u4FDD\u5B58")])),_:1})])),[[Ce,["order:tracking:edit-save"]]])]),_:1},8,["visible"])]}),_:1},512),f("div",nl,[e(ye,{id:"order-tracking-table","header-cell-config":{height:24},"header-cell-style":{fontSize:"13px"},"cell-config":{height:24},data:a(X),loading:a(F),"cell-style":ue,"custom-config":a(ll),"filter-config":{remote:!0},"sort-config":{remote:!0,multiple:!0},"column-config":{resizable:!0,maxFixedSize:0},"row-style":fe,"row-config":{isCurrent:!0,isHover:!0},"virtual-y-config":{enabled:!0,gt:0},size:"small",height:"100%","show-overflow":"",border:"",align:"center",ref_key:"tableRef",ref:J,onFilterChange:pe,onSortChange:me,onCellClick:l[19]||(l[19]=t=>{var R;let n="";n=t.column.field.includes("bom")?"bom":t.column.field.includes("scheduling")?"scheduling":t.column.field.includes("completeSet")?"completeSet":t.column.field.includes("assembly")?"assembly":t.column.field.includes("packing")?"packing":t.column.field.includes("rcv")?"rcv":t.column.field.includes("ship")?"ship":"bom",(R=a(K))==null||R.openForm(t.row,n)})},{default:i(()=>[e(k,{title:"\u8BA2\u5355\u4FE1\u606F",field:"order-info"},{default:i(()=>[e(d,{title:"\u4E1A\u52A1\u5458",field:"sellerName",width:"100",filters:a(s).sellerName,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u5BA2\u6237",field:"customerName",width:"100",filters:a(s).customerName,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u8BA2\u5355\u53F7",field:"docNo",width:"130",filters:a(s).docNo,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u54C1\u53F7",field:"itemCode",width:"90",filters:a(s).itemCode,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u54C1\u540D",field:"itemName",width:"100",filters:a(s).itemName,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u5F62\u6001\u5C5E\u6027",field:"attribute",width:"100",filters:a(oe)},{default:i(({row:t})=>{var n;return[y(ie((n=H[t==null?void 0:t.attribute])==null?void 0:n.label),1)]}),_:1},8,["filters"]),e(d,{title:"\u89C4\u683C",field:"spec",width:"200",filters:a(s).spec,"filter-render":T},null,8,["filters","filter-render"]),e(d,{title:"\u4E0B\u5355\u6570\u91CF",field:"qty",width:"110",sortable:"",filters:a(s).qty,"filter-render":el},null,8,["filters","filter-render"]),e(d,{title:"\u4E0B\u5355\u5BA1\u6838\u65E5\u671F",field:"approveDate",width:"120"}),e(d,{title:"\u9884\u51FA\u8D27\u65E5\u671F",field:"planShipDate",width:"120",filters:a(s).planShipDate,"filter-render":c},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u8BA2\u5355BOM-\u76D8\u5FB7\u6CE2",field:"order-bom"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"bomPlanDate",width:"100",filters:a(s).bomPlanDate,"filter-render":c,"title-suffix":{content:"\u5F53\u7CFB\u7EDF\u5185\u6709\u5B9A\u5236\u6570\u636E\u65F6\uFF0C\u53D6BOM\u5B9A\u5236\u8DDF\u8FDB\u8BA1\u5212\u5B8C\u6210\u65F6\u95F4"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"bomActualDate",width:"100",filters:a(s).bomActualDate,"filter-render":c,"title-suffix":{content:"\u5F53\u7CFB\u7EDF\u5185\u6709\u5B9A\u5236\u6570\u636E\u65F6\uFF0C\u53D6BOM\u5B9A\u5236\u8DDF\u8FDB\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4\uFF0C\u65E0\u5B9A\u5236\u53D6ERP\u4E2DBOM\u66F4\u65B0\u65F6\u95F4\uFF0C\u6210\u54C1BOM\u9700\u6EE1\u8DB3\u5B8C\u6574\u6027\u6761\u4EF6"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u8BA2\u5355\u5BA1\u6838-\u9646\u537F",field:"order-approve"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"approvePlanDate",width:"100",filters:a(s).approvePlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u8BA2\u5355\u4E0B\u5355\u65F6\u95F4+\u504F\u79FB\u5929\u6570(\u53EF\u70B9\u51FB\u53F3\u4FA7\u9F7F\u8F6E\u56FE\u6807\u67E5\u770B)"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"approveActualDate",width:"100",filters:a(s).approveActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u6700\u65B0\u8BA2\u5355\u5B9E\u9645\u5BA1\u6838\u65F6\u95F4\uFF0C\u5373\u6253\u56DE\u91CD\u65B0\u5BA1\u6838\u4E5F\u4F1A\u53D6\u6700\u65B0\u7684\u5BA1\u6838\u65F6\u95F4"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u8BA2\u5355\u6392\u4EA7-\u5218\u601D\u5B87",field:"order-scheduling"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"schedulingPlanDate",width:"100",filters:a(s).schedulingPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u8BA2\u5355\u5BA1\u6838\u65E5\u671F+\u504F\u79FB\u5929\u6570(\u53EF\u70B9\u51FB\u53F3\u4FA7\u9F7F\u8F6E\u56FE\u6807\u67E5\u770B)"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"schedulingActualDate",width:"100",filters:a(s).schedulingActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u5DE5\u5355\u7D2F\u8BA1\u6570\u91CF\u5927\u4E8E\u7B49\u4E8E\u4E0B\u5355\u6570\u91CF\u65F6\uFF0C\u6700\u665A\u7684\u5F00\u5DE5\u65E5\u671F"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u7269\u6599\u9F50\u5957-\u4F0D\u4E3D\u4E3D",field:"order-complete-set"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"completeSetPlanDate",width:"100",filters:a(s).completeSetPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u5DE5\u5355\u6700\u65E9\u5F00\u5DE5\u65F6\u95F4+\u504F\u79FB\u5929\u6570(\u53EF\u70B9\u51FB\u53F3\u4FA7\u9F7F\u8F6E\u56FE\u6807\u67E5\u770B)"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"completeSetActualDate",width:"100",filters:a(s).completeSetActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u6240\u6709\u5DE5\u5355\u5907\u6599\u8868\u4E2D\uFF0C\u63A8\u5F0F\u7269\u6599\u5DF2\u5168\u90E8\u9886\u6599\u65F6\uFF0C\u6700\u540E\u4E00\u5F20\u9886\u6599\u5355\u7684\u5BA1\u6838\u65E5\u671F"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u751F\u4EA7\u7EC4\u88C5-\u5F20\u5175\u53E4",field:"order-assembly"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"assemblyPlanDate",width:"100",filters:a(s).assemblyPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u6807\u51C6\u5DE5\u65F6\u8868\uFF0C\uFF08\u5F53\u524D\u578B\u53F7\u5355\u53F0\u6807\u51C6\u5DE5\u65F6X\u4E0B\u5355\u6570\u91CF/\u6BCF\u65E511\u5C0F\u65F6\u5DE5\u4F5C\u5468\u671F\uFF09+\u7269\u6599\u5B9E\u9645\u9F50\u5957\u65F6\u95F4"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"assemblyActualDate",width:"100",filters:a(s).assemblyActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u751F\u4EA7\u62A5\u5DE5\u6570\u636E\uFF0C\u5B9E\u9645\u7EC4\u88C5\u3001\u7EC4\u5305\u3001\u7EC4\u5305\u819C\u5DE5\u5E8F\u7D2F\u8BA1\u6570\u91CF\u5927\u4E8E\u7B49\u4E8E\u4E0B\u5355\u6570\u91CF\u7684\u6700\u540E\u62A5\u5DE5\u65E5\u671F"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u751F\u4EA7\u5305\u88C5-\u5F20\u5175\u53E4",field:"order-packing"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"packingPlanDate",width:"100",filters:a(s).packingPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u6807\u51C6\u5DE5\u65F6\u8868\uFF0C\uFF08\u5F53\u524D\u578B\u53F7\u5355\u53F0\u6807\u51C6\u5DE5\u65F6X\u4E0B\u5355\u6570\u91CF/\u6BCF\u65E511\u5C0F\u65F6\u5DE5\u4F5C\u5468\u671F\uFF09+\u7269\u6599\u5B9E\u9645\u9F50\u5957\u65F6\u95F4"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"packingActualDate",width:"100",filters:a(s).packingActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u751F\u4EA7\u62A5\u5DE5\u6570\u636E\uFF0C\u5B9E\u9645\u5305\u88C5\u3001\u7EC4\u5305\u3001\u7EC4\u5305\u819C\u5DE5\u5E8F\u7D2F\u8BA1\u6570\u91CF\u5927\u4E8E\u7B49\u4E8E\u4E0B\u5355\u6570\u91CF\u7684\u6700\u540E\u62A5\u5DE5\u65E5\u671F"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u5165\u5E93-\u90B9\u5BB6\u8D85",field:"order-rcv"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"rcvPlanDate",width:"100",filters:a(s).rcvPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u6700\u65E9\u6210\u54C1\u5165\u5E93\u5355\u521B\u5EFA\u65F6\u95F4+\u504F\u79FB\u5929\u6570(\u53EF\u70B9\u51FB\u53F3\u4FA7\u9F7F\u8F6E\u56FE\u6807\u67E5\u770B)"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"rcvActualDate",width:"100",filters:a(s).rcvActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u5B8C\u5DE5\u5165\u5E93\u5355\u5B8C\u5DE5\u5165\u5E93\u6570\u91CF\u5927\u4E8E\u7B49\u4E8E\u4E0B\u5355\u6570\u91CF\u65F6\uFF0C\u6700\u540E\u4E00\u5F20\u5B8C\u5DE5\u5165\u5E93\u5355\u7684\u5BA1\u6838\u65F6\u95F4"}},null,8,["filters","filter-render"])]),_:1}),e(k,{title:"\u51FA\u8D27-\u9646\u537F",field:"order-ship"},{default:i(()=>[e(d,{title:"\u8BA1\u5212",field:"shipPlanDate",width:"100",filters:a(s).shipPlanDate,"filter-render":c,"title-suffix":{content:"\u53D6\u5B9E\u9645\u5165\u5E93\u65F6\u95F4+\u504F\u79FB\u5929\u6570(\u53EF\u70B9\u51FB\u53F3\u4FA7\u9F7F\u8F6E\u56FE\u6807\u67E5\u770B)"}},null,8,["filters","filter-render"]),e(d,{title:"\u5B9E\u9645",field:"shipActualDate",width:"100",filters:a(s).shipActualDate,"filter-render":c,"title-suffix":{content:"\u53D6\u51FA\u8D27\u5355\u51FA\u8D27\u6570\u91CF\u5927\u4E8E\u7B49\u4E8E\u4E0B\u5355\u6570\u91CF\u65F6\uFF0C\u6700\u540E\u4E00\u5F20\u51FA\u8D27\u5355\u7684\u5BA1\u6838\u65F6\u95F4"}},null,8,["filters","filter-render"])]),_:1})]),_:1},8,["data","loading","custom-config"])]),f("div",ul,[l[33]||(l[33]=f("div",{class:"w-50% flex h-50px items-center"},[f("div",{class:"flex ml-10px"},[f("div",{class:"bg-[var(--el-color-success-light-3)] w-20px h-20px"}),f("span",{class:"ml-10px"},"\u6B63\u5E38")]),f("div",{class:"flex ml-10px"},[f("div",{class:"bg-[var(--el-color-warning-light-3)] w-20px h-20px"}),f("span",{class:"ml-10px"},"\u9884\u8B66")]),f("div",{class:"flex ml-10px"},[f("div",{class:"bg-#e60012 w-20px h-20px"}),f("span",{class:"ml-10px"},"\u8D85\u671F")]),f("div",{class:"flex ml-10px"},[f("div",{class:"bg-#ee7959 w-20px h-20px"}),f("span",{class:"ml-10px"},"\u8D85\u671F\u5B8C\u6210")])],-1)),f("div",fl,[e(_e,{total:a($),page:a(r).pageNo,"onUpdate:page":l[20]||(l[20]=t=>a(r).pageNo=t),limit:a(r).pageSize,"onUpdate:limit":l[21]||(l[21]=t=>a(r).pageSize=t),onPagination:E,size:"small"},null,8,["total","page","limit"])])])]),e(We,{ref_key:"orderTrackingDetailFormRef",ref:K},null,512)]),_:1})}}}),pl=Ke(cl,[["__scopeId","data-v-1079efa3"]]);export{pl as default};
