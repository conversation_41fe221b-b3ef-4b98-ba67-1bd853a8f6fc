import{aG as N,d as W,y as X,j as c,r as Z,f as ee,T as le,o as s,c as I,k as l,w as t,u as r,M as G,F as R,g as z,N as L,D as x,h as n,v as m,x as f,$ as M,S as ae,B as re,z as te,A as oe,C as pe,G as se,H as ie,I as ue,J as ne,K as de,L as ce}from"./index-C8b06LRn.js";import{_ as me}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ye}from"./index-CkzUfjB7.js";import{d as ge}from"./formatTime-COZ9Bl52.js";import{d as we}from"./download-D5Lb_h0f.js";import{_ as be}from"./ApiErrorLogDetail.vue_vue_type_script_setup_true_lang-Bvfyps7W.js";import{I as _}from"./constants-D3f7Z3TX.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./el-descriptions-item-Ctb8GMnZ.js";const ve=W({name:"InfraApiErrorLog",__name:"index",setup(xe){const S=X(),h=c(!0),V=c(0),C=c([]),o=Z({pageNo:1,pageSize:10,userId:null,userType:null,applicationName:null,requestUrl:null,processStatus:null,exceptionTime:[]}),U=c(),T=c(!1),y=async()=>{h.value=!0;try{const e=await(i=o,N.get({url:"/infra/api-error-log/page",params:i}));C.value=e.list,V.value=e.total}finally{h.value=!1}var i},g=()=>{o.pageNo=1,y()},H=()=>{U.value.resetFields(),g()},E=c(),O=async(i,e)=>{try{const w=e===_.DONE?"\u5DF2\u5904\u7406":"\u5DF2\u5FFD\u7565";await S.confirm("\u786E\u8BA4\u6807\u8BB0\u4E3A"+w+"?"),await((b,u)=>N.put({url:"/infra/api-error-log/update-status?id="+b+"&processStatus="+u}))(i,e),await S.success(w),await y()}catch{}},K=async()=>{try{await S.exportConfirm(),T.value=!0;const e=await(i=o,N.download({url:"/infra/api-error-log/export-excel",params:i}));we.excel(e,"\u5F02\u5E38\u65E5\u5FD7.xls")}catch{}finally{T.value=!1}var i};return ee(()=>{y()}),(i,e)=>{const w=ye,b=ae,u=re,P=te,A=oe,j=pe,k=se,d=ie,B=ue,D=_e,p=ne,F=fe,J=de,$=me,v=le("hasPermi"),Q=ce;return s(),I(R,null,[l(w,{title:"\u7CFB\u7EDF\u65E5\u5FD7",url:"https://doc.iocoder.cn/system-log/"}),l(D,null,{default:t(()=>[l(B,{class:"-mb-15px",model:r(o),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:t(()=>[l(u,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:t(()=>[l(b,{modelValue:r(o).userId,"onUpdate:modelValue":e[0]||(e[0]=a=>r(o).userId=a),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:G(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(u,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:t(()=>[l(A,{modelValue:r(o).userType,"onUpdate:modelValue":e[1]||(e[1]=a=>r(o).userType=a),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),I(R,null,z(r(L)(r(x).USER_TYPE),a=>(s(),n(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"\u5E94\u7528\u540D",prop:"applicationName"},{default:t(()=>[l(b,{modelValue:r(o).applicationName,"onUpdate:modelValue":e[2]||(e[2]=a=>r(o).applicationName=a),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:G(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(u,{label:"\u5F02\u5E38\u65F6\u95F4",prop:"exceptionTime"},{default:t(()=>[l(j,{modelValue:r(o).exceptionTime,"onUpdate:modelValue":e[3]||(e[3]=a=>r(o).exceptionTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),l(u,{label:"\u5904\u7406\u72B6\u6001",prop:"processStatus"},{default:t(()=>[l(A,{modelValue:r(o).processStatus,"onUpdate:modelValue":e[4]||(e[4]=a=>r(o).processStatus=a),placeholder:"\u8BF7\u9009\u62E9\u5904\u7406\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),I(R,null,z(r(L)(r(x).INFRA_API_ERROR_LOG_PROCESS_STATUS),a=>(s(),n(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,null,{default:t(()=>[l(d,{onClick:g},{default:t(()=>[l(k,{icon:"ep:search",class:"mr-5px"}),e[7]||(e[7]=m(" \u641C\u7D22"))]),_:1}),l(d,{onClick:H},{default:t(()=>[l(k,{icon:"ep:refresh",class:"mr-5px"}),e[8]||(e[8]=m(" \u91CD\u7F6E"))]),_:1}),f((s(),n(d,{type:"success",plain:"",onClick:K,loading:r(T)},{default:t(()=>[l(k,{icon:"ep:download",class:"mr-5px"}),e[9]||(e[9]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[v,["infra:api-error-log:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),l(D,null,{default:t(()=>[f((s(),n(J,{data:r(C)},{default:t(()=>[l(p,{label:"\u65E5\u5FD7\u7F16\u53F7",align:"center",prop:"id"}),l(p,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"userId"}),l(p,{label:"\u7528\u6237\u7C7B\u578B",align:"center",prop:"userType"},{default:t(a=>[l(F,{type:r(x).USER_TYPE,value:a.row.userType},null,8,["type","value"])]),_:1}),l(p,{label:"\u5E94\u7528\u540D",align:"center",prop:"applicationName",width:"200"}),l(p,{label:"\u8BF7\u6C42\u65B9\u6CD5",align:"center",prop:"requestMethod",width:"80"}),l(p,{label:"\u8BF7\u6C42\u5730\u5740",align:"center",prop:"requestUrl",width:"180"}),l(p,{label:"\u5F02\u5E38\u53D1\u751F\u65F6\u95F4",align:"center",prop:"exceptionTime",width:"180",formatter:r(ge)},null,8,["formatter"]),l(p,{label:"\u5F02\u5E38\u540D",align:"center",prop:"exceptionName",width:"180"}),l(p,{label:"\u5904\u7406\u72B6\u6001",align:"center",prop:"processStatus"},{default:t(a=>[l(F,{type:r(x).INFRA_API_ERROR_LOG_PROCESS_STATUS,value:a.row.processStatus},null,8,["type","value"])]),_:1}),l(p,{label:"\u64CD\u4F5C",align:"center",width:"200"},{default:t(a=>[f((s(),n(d,{link:"",type:"primary",onClick:Y=>{return q=a.row,void E.value.open(q);var q}},{default:t(()=>e[10]||(e[10]=[m(" \u8BE6\u7EC6 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:query"]]]),a.row.processStatus===r(_).INIT?f((s(),n(d,{key:0,link:"",type:"primary",onClick:Y=>O(a.row.id,r(_).DONE)},{default:t(()=>e[11]||(e[11]=[m(" \u5DF2\u5904\u7406 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:update-status"]]]):M("",!0),a.row.processStatus===r(_).INIT?f((s(),n(d,{key:1,link:"",type:"primary",onClick:Y=>O(a.row.id,r(_).IGNORE)},{default:t(()=>e[12]||(e[12]=[m(" \u5DF2\u5FFD\u7565 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:update-status"]]]):M("",!0)]),_:1})]),_:1},8,["data"])),[[Q,r(h)]]),l($,{total:r(V),page:r(o).pageNo,"onUpdate:page":e[5]||(e[5]=a=>r(o).pageNo=a),limit:r(o).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>r(o).pageSize=a),onPagination:y},null,8,["total","page","limit"])]),_:1}),l(be,{ref_key:"detailRef",ref:E},null,512)],64)}}});export{ve as default};
