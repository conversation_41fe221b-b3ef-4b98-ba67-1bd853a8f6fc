import{_ as D}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as c,aj as M,j as i,f as E,h as T,w as e,O as j,o as A,k as t,u as a,D as O,v as r,t as o}from"./index-C8b06LRn.js";import{E as g,a as w}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as x}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as p}from"./formatTime-COZ9Bl52.js";import{g as B}from"./index-HYWuysMX.js";import"./el-card-CaOo8U9P.js";import"./color-DXkOL5Tu.js";const L=c({name:"BpmOALeaveDetail",__name:"detail",props:{id:M.number.def(void 0)},setup(f,{expose:d}){const{query:n}=j(),_=f,m=i(!1),l=i({}),Y=n.id,u=async()=>{m.value=!0;try{l.value=await B(_.id||Y)}finally{m.value=!1}};return d({open:u}),E(()=>{u()}),(P,h)=>{const v=x,s=g,y=w,b=D;return A(),T(b,null,{default:e(()=>[t(y,{column:1,border:""},{default:e(()=>[t(s,{label:"\u8BF7\u5047\u7C7B\u578B"},{default:e(()=>[t(v,{type:a(O).BPM_OA_LEAVE_TYPE,value:a(l).type},null,8,["type","value"])]),_:1}),t(s,{label:"\u5F00\u59CB\u65F6\u95F4"},{default:e(()=>[r(o(a(p)(a(l).startTime,"YYYY-MM-DD")),1)]),_:1}),t(s,{label:"\u7ED3\u675F\u65F6\u95F4"},{default:e(()=>[r(o(a(p)(a(l).endTime,"YYYY-MM-DD")),1)]),_:1}),t(s,{label:"\u539F\u56E0"},{default:e(()=>[r(o(a(l).reason),1)]),_:1})]),_:1})]),_:1})}}});export{L as default};
