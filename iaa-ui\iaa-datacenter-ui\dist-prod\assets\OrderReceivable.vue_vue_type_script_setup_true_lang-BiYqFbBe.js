import{_ as L}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{B as N}from"./index-CSCiSrUr.js";import{d as _,t as S}from"./Filter-Dzz2caxb.js";import{d as j,j as l,r as O,au as R,aF as y,o as E,c as U,l as B,k as i,w as q,u as o}from"./index-C8b06LRn.js";const D={class:"h-[calc(100vh-347px)] mt-20px"},G=j({__name:"OrderReceivable",props:{orderCodes:{}},setup(A){const C=l([{data:""}]),F=l([{data:[]}]),m=l(0),h=l(0),p=l(0),v=l(0),b=l(0),P=l([{customersCode:"\u5408\u8BA1\u603B\u548C",salesPrice:m,shipPrice:h,collectionAmount:p,receivableAmount:v,receivableAmountLocal:b}]),u=l(!1),r=l([]),w=l(0),k=l(),f=A,a=O({pageNo:1,pageSize:20,orderCodes:f.orderCodes}),g=async()=>{a.orderCodes=f.orderCodes,u.value=!0;try{const c=await N.getOrderReceivablePage(a);r.value=c.list,w.value=c.total,m.value=r.value.reduce((t,e)=>t+e.salesPrice,0).toFixed(3),h.value=r.value.reduce((t,e)=>t+e.shipPrice,0).toFixed(3),p.value=r.value.reduce((t,e)=>t+e.collectionAmount,0).toFixed(3),v.value=r.value.reduce((t,e)=>t+e.receivableAmount,0).toFixed(3),b.value=r.value.reduce((t,e)=>t+e.receivableAmountLocal,0).toFixed(3)}finally{u.value=!1}},z=c=>{const t=["classification"],e={};c.filterList.forEach(d=>{const{field:s,values:n,datas:x}=d;t.includes(s)&&n.length>0?e[s]=n:x.length>0&&(e[s]=x[0])}),Object.keys(a).forEach(d=>{["pageNo","pageSize","orderCodes"].includes(d)||c.filterList.some(s=>s.field===d)||(a[d]=void 0)}),Object.assign(a,e),g()};return R(()=>f.orderCodes,()=>{g()},{immediate:!0}),(c,t)=>{const e=y("vxe-column"),d=y("vxe-table"),s=L;return E(),U("div",null,[B("div",D,[i(d,{"row-config":{height:27,keyField:"id"},ref_key:"tableRef",ref:k,data:o(r),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:o(u),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"footer-data":o(P),"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},tabindex:"0",size:"mini",onFilterChange:z},{default:q(()=>[i(e,{field:"customersCode",width:"150",title:"\u5BA2\u6237\u7F16\u7801"}),i(e,{field:"customersName",width:"150",title:"\u5BA2\u6237\u540D\u79F0"}),i(e,{field:"dateStr",width:"120",title:"\u8BA2\u5355\u65E5\u671F",filters:o(F),"filter-render":_},null,8,["filters","filter-render"]),i(e,{field:"orderNo",title:"\u8BA2\u5355\u53F7",width:"200",filters:o(C),"filter-render":S},null,8,["filters","filter-render"]),i(e,{field:"currency",title:"\u5E01\u79CD",width:"100"}),i(e,{field:"salesPrice",title:"\u8BA2\u5355\u91D1\u989D",width:"150"}),i(e,{field:"shipPrice",title:"\u5DF2\u51FA\u8D27\u91D1\u989D",width:"150"}),i(e,{field:"collectionAmount",title:"\u6536\u6B3E\u91D1\u989D",width:"150"}),i(e,{field:"collectionAmountLocal",title:"\u6536\u6B3E\u91D1\u989D\uFF08\u672C\u5E01\uFF09",width:"150"}),i(e,{field:"receivableAmount",title:"\u5E94\u6536\u91D1\u989D","min-width":"120"}),i(e,{field:"receivableAmountLocal",title:"\u5E94\u6536\u91D1\u989D\uFF08\u672C\u5E01\uFF09","min-width":"120"}),i(e,{field:"rate",title:"\u6C47\u7387",width:"150"})]),_:1},8,["data","loading","footer-data"])]),i(s,{total:o(w),page:o(a).pageNo,"onUpdate:page":t[0]||(t[0]=n=>o(a).pageNo=n),limit:o(a).pageSize,"onUpdate:limit":t[1]||(t[1]=n=>o(a).pageSize=n),onPagination:g},null,8,["total","page","limit"])])}}});export{G as _};
