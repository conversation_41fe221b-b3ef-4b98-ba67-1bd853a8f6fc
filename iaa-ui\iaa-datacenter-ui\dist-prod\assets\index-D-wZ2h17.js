import{d as le,j as u,Y as oe,di as O,au as se,aO as x,f as ie,co as ne,o as f,c as p,l,x as re,Z as ce,F as z,g as j,n as F,t as m,u as W,k,w as B,dj as G,dk as de,C as ue,G as ve,H as fe,_ as pe}from"./index-C8b06LRn.js";import{w as U,av as Z}from"./echarts-cfVEL83D.js";import{D as he}from"./deptindicator-DmEoRemC.js";import{d as me}from"./dateUtil-D9m5ek6U.js";const be={class:"dept-list"},ye=["onClick"],ge={class:"dept-name"},ke={class:"dept-indicator-count"},we={class:"dashboard-header"},xe={class:"filter-section"},Fe=["id"],Ce={class:"dept-section-header"},Se={class:"dept-summary"},_e={class:"charts-container"},Ee=pe(le({__name:"index",setup(Ye){const C=u(!1),S=u(!1),I=u(""),w=u(me().format("YYYY")),T=u(),b=u(!1),_=oe(()=>O("first_level_department")),V=u([]),E=u({}),n=u({}),H=()=>{b.value=!b.value,x(()=>{setTimeout(()=>{D()},300)})},Y=t=>{const e=E.value[t];return e?Object.entries(e).map(([a,i])=>({indicator:a,data:i})):[]},q=(t,e)=>de(`${t}_Indicator`,e)||e,$=async()=>{C.value=!0;try{Object.values(n.value).forEach(a=>{a&&!a.isDisposed()&&a.dispose()}),n.value={};const t={year:w.value},e=await he.getDeptIndicatorList(t);V.value=(e==null?void 0:e.filter(a=>!a.indicator.includes("plan")))||[],J()}catch{}finally{C.value=!1}},J=()=>{const t={};V.value.forEach(e=>{t[e.dept]||(t[e.dept]={}),t[e.dept][e.indicator]||(t[e.dept][e.indicator]=[]),t[e.dept][e.indicator].push(e)}),E.value=t},y=["#00D4FF","#0099CC","#0066FF","#3366FF","#6699FF"],K=[{offset:0,color:"#00D4FF"},{offset:1,color:"#0066FF"}],R=(t,e)=>{const a=n.value[t];if(a&&!a.isDisposed())if(e&&e.length!==0)try{const i=[...e].sort((s,r)=>s.month-r.month),c=i.map(s=>`${s.month}\u6708`),o=i.map(s=>Number(s.value)||0),v={backgroundColor:"transparent",title:{show:!1},tooltip:{trigger:"axis",backgroundColor:"rgba(0, 0, 0, 0.8)",borderColor:y[0],borderWidth:1,textStyle:{color:"#fff"},formatter:s=>{if(!s||!s[0])return"";const r=s[0];return`${r.name}<br/>${r.seriesName}: ${r.value}`}},grid:{left:"10%",right:"10%",top:"10%",bottom:"15%",containLabel:!0},xAxis:{type:"category",data:c,axisLine:{show:!0,lineStyle:{color:y[0]}},axisLabel:{color:"#fff",fontSize:12},axisTick:{show:!0,lineStyle:{color:y[0]}}},yAxis:{type:"value",axisLine:{show:!0,lineStyle:{color:y[0]}},axisLabel:{color:"#fff",fontSize:12},axisTick:{show:!0,lineStyle:{color:y[0]}},splitLine:{lineStyle:{color:"rgba(0, 212, 255, 0.2)",type:"dashed"}}},series:[{name:"\u6307\u6807\u503C",type:"bar",data:o,label:{show:!0,position:"top",color:"#fff",fontSize:12,formatter:"{c}"},itemStyle:{color:new Z(0,0,0,1,K),borderRadius:[4,4,0,0]},emphasis:{itemStyle:{color:new Z(0,0,0,1,[{offset:0,color:"#00FFFF"},{offset:1,color:"#0099FF"}])}},animationDuration:1e3,animationEasing:"cubicOut"}]};a.clear(),a.setOption(v,!0),setTimeout(()=>{a&&!a.isDisposed()&&a.resize()},100)}catch{const c={backgroundColor:"transparent",title:{text:"\u56FE\u8868\u6E32\u67D3\u5931\u8D25",left:"center",top:"center",textStyle:{color:"#ff6b6b",fontSize:16}}};a.setOption(c,!0)}else{const i={backgroundColor:"transparent",title:{text:"\u6682\u65E0\u6570\u636E",left:"center",top:"center",textStyle:{color:"#666",fontSize:16}}};a.setOption(i,!0)}},D=()=>{x(()=>{Object.values(n.value).forEach(t=>{if(t&&!t.isDisposed())try{t.resize()}catch{}})})};se(E,t=>{Object.keys(t).length>0&&x(()=>{setTimeout(()=>{x(()=>{setTimeout(()=>{_.value.forEach(e=>{Y(e.value).forEach(({indicator:a,data:i})=>{const c=`${e.value}-${a}`;n.value[c]&&R(c,i)})})},300)})},500)})},{deep:!0});const A=t=>{t.key==="Escape"&&b.value&&(t.preventDefault(),H())};return ie(()=>{$(),window.addEventListener("resize",D),document.addEventListener("keydown",A)}),ne(()=>{window.removeEventListener("resize",D),document.removeEventListener("keydown",A),Object.values(n.value).forEach(t=>{t&&!t.isDisposed()&&t.dispose()})}),(t,e)=>{const a=ue,i=ve,c=fe;return f(),p("div",{class:F(["dept-bi-dashboard",{"fullscreen-mode":b.value}])},[l("div",{class:F(["sidebar",{"sidebar-collapsed":S.value}])},[e[1]||(e[1]=l("div",{class:"sidebar-header"},[l("h3",null,"\u90E8\u95E8\u5217\u8868")],-1)),re(l("div",be,[(f(!0),p(z,null,j(_.value,o=>{var v;return f(),p("div",{key:o.value,class:F(["dept-item",{active:I.value===o.value}]),onClick:s=>(r=>{I.value=r;const d=document.getElementById(`dept-${r}`);d&&T.value&&d.scrollIntoView({behavior:"smooth",block:"start"})})(o.value)},[l("div",ge,m(o.label),1),l("div",ke,m((v=W(O)(o.value+"_Indicator"))==null?void 0:v.filter(s=>!s.value.includes("plan")).length)+" \u9879\u6307\u6807",1)],10,ye)}),128))],512),[[ce,!S.value]])],2),l("div",{class:F(["main-content",{"sidebar-collapsed":S.value}])},[l("div",we,[e[2]||(e[2]=l("h1",null,"\u90E8\u95E8\u5173\u952E\u6307\u6807\u770B\u677F",-1)),l("div",xe,[k(a,{modelValue:w.value,"onUpdate:modelValue":e[0]||(e[0]=o=>w.value=o),type:"year","value-format":"YYYY",format:"YYYY",placeholder:"\u9009\u62E9\u5E74\u4EFD",onChange:$,class:"year-picker !w-100px",clearable:!1},null,8,["modelValue"]),k(c,{onClick:$,type:"primary",loading:C.value,class:"fullscreen-btn"},{default:B(()=>[k(i,{icon:"ep:refresh"})]),_:1},8,["loading"]),k(c,{onClick:H,type:"info",class:"fullscreen-btn"},{default:B(()=>[k(i,{icon:b.value?"ep:close-bold":"ep:full-screen"},null,8,["icon"])]),_:1})])]),l("div",{class:"dashboard-content",ref_key:"dashboardContent",ref:T},[(f(!0),p(z,null,j(_.value,o=>{var v;return f(),p("div",{key:o.value,id:`dept-${o.value}`,class:"dept-section"},[l("div",Ce,[l("h2",null,m(o.label),1),l("div",Se,[l("span",null,m((v=W(O)(o.value+"_Indicator"))==null?void 0:v.filter(s=>!s.value.includes("plan")).length)+"\u9879\u6307\u6807",1),l("span",null,m(w.value)+"\u5E74\u6570\u636E",1)])]),l("div",_e,[(f(!0),p(z,null,j(Y(o.value),s=>(f(),p("div",{key:`${o.value}-${s.indicator}`,class:"chart-item"},[l("h3",null,m(q(o.value,s.indicator)),1),l("div",{ref_for:!0,ref:r=>((d,h)=>{if(h&&!n.value[d])if(h.offsetWidth>0&&h.offsetHeight>0)try{const g=G(U(h));n.value[d]=g}catch{}else{const g=new ResizeObserver(M=>{for(const P of M){const{width:Q,height:X}=P.contentRect;if(Q>0&&X>0&&!n.value[d])try{const N=G(U(h));n.value[d]=N;const[ee,te]=d.split("-"),L=Y(ee).find(ae=>ae.indicator===te);L&&L.data.length>0&&R(d,L.data),g.disconnect()}catch{}}});g.observe(h)}})(`${o.value}-${s.indicator}`,r),class:"chart"},null,512)]))),128))])],8,Fe)}),128))],512)],2)],2)}}}),[["__scopeId","data-v-c31e865d"]]);export{Ee as default};
