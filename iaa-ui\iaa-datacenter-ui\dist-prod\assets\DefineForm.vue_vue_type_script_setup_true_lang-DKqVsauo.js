import{d as q,j as u,y as D,r as j,o as c,h as f,w as d,k as s,u as e,v as C,x as F,m as B,S as H,B as I,I as L,H as O,L as R}from"./index-C8b06LRn.js";import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{D as z}from"./index-a4vw0wEg.js";const A=q({__name:"DefineForm",emits:["success"],setup(E,{expose:V,emit:w}){const m=u(!1),p=u(),t=u(!1),g=D(),_=w,a=u({id:void 0,code:void 0,name:void 0,remarks:void 0}),b=j({code:[{required:!0,message:"\u8BF7\u8F93\u5165\u7F16\u7801",trigger:"blur"}],name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:"blur"}]}),v=()=>{a.value={id:void 0,code:void 0,name:void 0,remarks:void 0}},h=async()=>{var r;await((r=e(p))==null?void 0:r.validate()),t.value=!0;try{await z.saveOrUpdate(a.value),g.success("\u4FDD\u5B58\u6210\u529F"),_("success"),v(),m.value=!1}finally{t.value=!1}};return V({openForm:r=>{v(),m.value=!0,r&&(a.value=r)}}),(r,l)=>{const i=H,n=I,k=L,y=O,x=S,U=R;return c(),f(x,{title:"\u5B9A\u4E49\u5DE5\u827A",modelValue:e(m),"onUpdate:modelValue":l[3]||(l[3]=o=>B(m)?m.value=o:null)},{footer:d(()=>[s(y,{type:"primary",loading:e(t),onClick:h},{default:d(()=>l[4]||(l[4]=[C("\u4FDD\u5B58")])),_:1},8,["loading"])]),default:d(()=>[F((c(),f(k,{model:e(a),rules:e(b),"label-width":"100",ref_key:"formRef",ref:p},{default:d(()=>[s(n,{label:"\u5DE5\u827A\u7F16\u7801",prop:"code"},{default:d(()=>[s(i,{modelValue:e(a).code,"onUpdate:modelValue":l[0]||(l[0]=o=>e(a).code=o),maxlength:64,"show-word-limit":"",disabled:e(a).id},null,8,["modelValue","disabled"])]),_:1}),s(n,{label:"\u5DE5\u827A\u540D\u79F0",prop:"name"},{default:d(()=>[s(i,{modelValue:e(a).name,"onUpdate:modelValue":l[1]||(l[1]=o=>e(a).name=o),maxlength:255,"show-word-limit":""},null,8,["modelValue"])]),_:1}),s(n,{label:"\u5907\u6CE8"},{default:d(()=>[s(i,{modelValue:e(a).remarks,"onUpdate:modelValue":l[2]||(l[2]=o=>e(a).remarks=o),type:"textarea",rows:6,maxlength:500,"show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[U,e(t)]])]),_:1},8,["modelValue"])}}});export{A as _};
