import{_ as W}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as Z,aj as d,j as n,cj as T,Y as P,di as D,y as ee,f as te,aF as c,T as le,o as m,c as ae,l as I,k as e,w as r,u as a,v as p,x as ie,t as y,h as g,$ as w,F as re,B as oe,H as se,I as ne,aa as ue,dH as fe,aM as de}from"./index-C8b06LRn.js";import{p as me,e as pe,_ as ce}from"./report-C-l2KLXX.js";import{c as ye,t as ge}from"./vxeCustom-D2Re1O-c.js";import he from"./OrderForm-MAaNE6mZ.js";import{d as ve}from"./download-D5Lb_h0f.js";const we={style:{height:"50px"}},_e={style:{height:"calc(100vh - 200px - 60px)"}},xe=Z({__name:"CustomerDevelopment",props:{userList:d.oneOfType([Array]).isRequired,departmentList:d.oneOfType([Array]).isRequired,customerPortrait:d.oneOfType([Array]).isRequired,customerStage:d.oneOfType([Array]).isRequired,customerRevenue:d.oneOfType([Array]).isRequired,customerOilRevenue:d.oneOfType([Array]).isRequired},setup(z){const _=n(),k=n(),R=n(),s=n({pageNo:1,pageSize:30,opportunityTime:[T().subtract(3,"months").format("YYYY-MM-DD"),T().format("YYYY-MM-DD")],salesperson:[],departmentName:[],customerName:"",countryName:"",regionOrProvince:"",city:"",trailStatus:[],customerPortraits:[],nature:"",homepage:"",revenueScale:[],fragranceRevenueScale:[],developmentType:[],stageInfo:[],combatProduct:""}),N=n([]),S=n(0),x=n(!1),f=z,M=()=>{s.value.pageNo=1,h()},h=async()=>{x.value=!0;try{const i=await me(s.value);N.value=i.list,S.value=i.total}finally{x.value=!1}},u=n([{data:""}]),Y=i=>{typeof s.value[i.field]=="string"?s.value[i.field]=i.datas.join(","):s.value[i.field]=i.values,M()},A=P(()=>D("xiaoman_development_type").map(i=>({label:i.label,value:i.value}))),q=P(()=>D("xiaoman_opportunity_stage").map(i=>({label:i.label,value:i.value}))),v=n(),j=ee(),L=async()=>{try{await j.exportConfirm(),v.value=!0;const i=await pe(s.value);ve.excel(i,`\u5C0F\u6EE1\u9500\u552E\u4F5C\u6218\u5BA2\u6237\u5F00\u53D1\u660E\u7EC6\u8868${T().unix()}.xls`)}catch{}finally{v.value=!1}};return te(()=>{h(),(()=>{const i=_.value,o=k.value;i&&o&&i.connect(o)})()}),(i,o)=>{const H=ce,C=oe,U=se,V=ne,$=c("vxe-button"),B=c("vxe-tooltip"),X=c("vxe-toolbar"),t=c("vxe-column"),F=ue,E=fe,O=de,G=c("vxe-table"),J=W,K=le("hasPermi");return m(),ae(re,null,[I("div",we,[e(X,{ref_key:"toolbarRef",ref:k,custom:"",size:"small",class:"h-full"},{buttons:r(()=>[e(V,{inline:""},{default:r(()=>[e(C,{label:"\u5546\u673A\u65F6\u95F4\u8303\u56F4"},{default:r(()=>[e(H,{modelValue:a(s).opportunityTime,"onUpdate:modelValue":o[0]||(o[0]=l=>a(s).opportunityTime=l),selected:"last3Month",onChange:M},null,8,["modelValue"])]),_:1}),e(C,null,{default:r(()=>[e(U,{onClick:h},{default:r(()=>o[4]||(o[4]=[p("\u67E5\u8BE2")])),_:1})]),_:1})]),_:1})]),tools:r(()=>[e(B,{content:"\u5BFC\u51FA"},{default:r(()=>[ie(e($,{icon:"vxe-icon-save",circle:"",loading:a(v),onClick:L},null,8,["loading"]),[[K,["xiaoman:report:export-customer-development"]]])]),_:1})]),_:1},512)]),I("div",_e,[e(G,{"header-cell-style":{padding:0,fontSize:"12px",height:"24px"},"cell-style":{padding:0,fontSize:"12px"},"row-config":{isCurrent:!0,isHover:!0,height:30},"column-config":{isCurrent:!0},"custom-config":a(ye),"export-config":{},"filter-config":{remote:!0},data:a(N),loading:a(x)||a(v),onCellClick:o[1]||(o[1]=l=>a(ge)(l,a(_))),onFilterChange:Y,height:"100%",border:"",align:"center",stripe:"",ref_key:"tableRef",ref:_,id:"vxe-table-customer-development"},{default:r(()=>[e(t,{title:"\u4E1A\u52A1\u5458",width:"100",field:"salesperson",fixed:"left",filters:f.userList},{default:r(({row:l})=>{var b;return[p(y((b=l.salesperson)==null?void 0:b.join(",")),1)]}),_:1},8,["filters"]),e(t,{title:"\u5BA2\u6237\u6240\u5C5E\u7EC4\u522B",field:"departmentName",filters:f.departmentList,width:"110",fixed:"left"},null,8,["filters"]),(m(),g(t,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName",align:"left",width:"140",key:3,fixed:"left",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},{default:r(({row:l})=>[l.customerName?(m(),g(F,{key:0,type:"primary",size:"small",link:"",onClick:b=>(Q=>{R.value.open(Q,"customer")})(l),underline:!1},{default:r(()=>[p(y(l.customerName),1)]),_:2},1032,["onClick"])):w("",!0)]),_:1},8,["filters"])),e(t,{title:"\u56FD\u5BB6/\u5730\u533A",field:"countryName",width:"100",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),e(t,{title:"\u6D32/\u7701",field:"regionOrProvince",width:"100",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),e(t,{title:"\u57CE\u5E02",field:"city",width:"100",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),e(t,{title:"\u5BA2\u6237\u753B\u50CF",field:"customerPortraits",width:"100",filters:f.customerPortrait},null,8,["filters"]),e(t,{title:"\u5BA2\u6237\u6027\u8D28",field:"nature",width:"100",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),e(t,{title:"\u5408\u4F5C\u9636\u6BB5",field:"trailStatus",width:"100",filters:f.customerStage},null,8,["filters"]),e(t,{title:"\u5BA2\u6237\u5B98\u7F51",field:"homepage",align:"left",width:"200",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},{default:r(({row:l})=>[l.homepage?(m(),g(F,{key:0,size:"small",link:"",underline:"",href:l.homepage,target:"_blank"},{default:r(()=>[p(y(l.homepage),1)]),_:2},1032,["href"])):w("",!0)]),_:1},8,["filters"]),e(t,{title:"\u5BA2\u6237\u8425\u6536\u89C4\u6A21\u8BC4\u4F30",field:"revenueScale",width:"150",filters:f.customerRevenue},null,8,["filters"]),e(t,{title:"\u9999\u6C1B\u4EA7\u54C1\u89C4\u6A21\u8BC4\u4F30",field:"fragranceRevenueScale",width:"150",filters:f.customerOilRevenue},null,8,["filters"]),e(t,{title:"\u9884\u4F30\u5E74\u9500\u552E\u989D(\u4E07\u5143)",field:"estimatedAnnualSales",width:"100"}),e(t,{title:"\u5F00\u53D1\u7C7B\u578B",field:"developmentType",width:"100",filters:a(A)},null,8,["filters"]),e(t,{title:"\u4F5C\u6218\u4EA7\u54C1",field:"combatProduct",width:"200",filters:a(u),"filter-render":{name:"MyTableFilterInput"}},null,8,["filters"]),e(t,{title:"\u5F00\u53D1\u76EE\u6807",field:"developmentTarget",width:"100"}),e(t,{title:"\u9884\u8BA1\u5F00\u53D1\u5929\u6570",field:"planDevelopmentDays",width:"100"}),e(t,{title:"\u5F00\u59CB\u65F6\u95F4",field:"createTime",width:"140"}),e(t,{title:"\u5DF2\u5F00\u53D1\u5929\u6570",field:"developmentDays",width:"100"}),e(t,{title:"\u65F6\u95F4\u8FDB\u5EA6",field:"progress",width:"150"},{default:r(({row:l})=>[e(E,{percentage:l.progress,color:l.hasOverdue?"red":"green"},null,8,["percentage","color"])]),_:1}),e(t,{title:"\u8FDB\u5C55",field:"stageInfo",width:"200",filters:a(q)},null,8,["filters"]),e(t,{title:"\u7ED3\u679C",field:"result",width:"100"},{default:r(({row:l})=>[l.result==="\u8D62\u5355"?(m(),g(O,{key:0,type:"success"},{default:r(()=>[p(y(l.result),1)]),_:2},1024)):w("",!0),l.result==="\u8F93\u5355"?(m(),g(O,{key:1,type:"danger"},{default:r(()=>[p(y(l.result),1)]),_:2},1024)):w("",!0)]),_:1}),e(t,{title:"\u5931\u8D25\u539F\u56E0",field:"failTypeName",width:"100"}),e(t,{title:"\u5931\u8D25\u63CF\u8FF0",field:"failRemark",width:"200"}),e(t,{title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F",field:"accountDate",width:"140"}),e(t,{title:"\u5B9E\u9645\u7528\u65F6(\u5929)",field:"accountDays",width:"100"})]),_:1},8,["custom-config","data","loading"]),e(J,{total:a(S),size:"small",page:a(s).pageNo,"onUpdate:page":o[2]||(o[2]=l=>a(s).pageNo=l),limit:a(s).pageSize,"onUpdate:limit":o[3]||(o[3]=l=>a(s).pageSize=l),onPagination:h,class:"!float-left"},null,8,["total","page","limit"])]),e(he,{ref_key:"orderFormRef",ref:R},null,512)],64)}}});export{xe as _};
