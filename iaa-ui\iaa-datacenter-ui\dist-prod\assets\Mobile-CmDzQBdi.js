import{d as aa,j as _,f as la,dl as x,aF as p,o as f,c as r,k as a,w as l,u as s,m as C,l as S,v as u,F as h,g as q,h as F,$ as k,t as n,z as ea,A as ta,B as ua,a8 as sa,ab as na,I as oa,_ as da}from"./index-C8b06LRn.js";import{R as z}from"./index-rrqDtE6o.js";import{e as ia}from"./Filter-Dzz2caxb.js";const ca={class:"overflow-hidden"},ma={class:"flex justify-between"},fa={class:"h-[calc(100%-52px)]"},pa={class:"h-[calc(100vh-300px)] overflow-auto"},_a=da(aa({__name:"Mobile",setup(ra){const m=_("balance"),i=_({pageNo:1,pageSize:10,hasStatistics:!0}),M=_(!1),Y=_(0),v=_({}),b=_([]),N=_(!1),P=_(!1),V=_(!1),R=_(""),I=_([]),B=c=>{const[t,w]=c;V.value=!1;const U=x(t).format("YYYY-MM-DD"),D=x(w).format("YYYY-MM-DD");R.value=U+"\u81F3"+D,i.value.businessDate=[U,D]},j=async()=>{N.value=!0;try{if(m.value==="rec"){const c=await z.getRecPaymentPage(i.value);Y.value=c.total,b.value=b.value.concat(c.list),v.value=c.statistics}else if(m.value==="ship"){const c=await z.getShipPage(i.value);Y.value=c.total,b.value=b.value.concat(c.list),v.value=c.statistics}else if(m.value==="rma"){const c=await z.getRmaPage(i.value);Y.value=c.total,b.value=b.value.concat(c.list),v.value=c.statistics}else if(m.value==="balance"){const c=await z.getTotalPage(i.value);Y.value=c.total,b.value=b.value.concat(c.list),v.value=c.statistics}}finally{N.value=!1}},T=()=>{i.value.pageNo++,i.value.pageNo>Math.ceil(Y.value/i.value.pageSize)?P.value=!0:j()},L=()=>{i.value.pageNo=1,b.value=[],j(),M.value=!1,P.value=!1};return la(()=>{const c=x().startOf("years"),t=x();i.value.businessDate=[c.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")],I.value=[c.toDate(),t.toDate()],R.value=c.format("YYYY-MM-DD")+"\u81F3"+t.format("YYYY-MM-DD"),j()}),(c,t)=>{const w=p("van-tab"),U=p("van-tabs"),D=p("van-cell"),O=p("van-button"),$=ea,E=ta,y=p("van-field"),G=p("van-calendar"),H=p("van-switch"),J=p("van-form"),K=p("van-popup"),o=ua,d=sa,Q=na,W=oa,X=p("van-list"),g=p("van-grid-item"),Z=p("van-grid");return f(),r("div",ca,[a(U,{active:s(m),"onUpdate:active":t[0]||(t[0]=e=>C(m)?m.value=e:null),onChange:L},{default:l(()=>[a(w,{title:"\u4F59\u989D\u660E\u7EC6",name:"balance"}),a(w,{title:"\u6536\u6B3E\u5355",name:"rec"}),a(w,{title:"\u51FA\u8D27\u5355",name:"ship"}),a(w,{title:"\u9000\u8D27\u5355",name:"rma"})]),_:1},8,["active"]),a(D,{title:"\u67E5\u8BE2\u65B9\u6848","is-link":"",onClick:t[1]||(t[1]=e=>M.value=!0),class:"mt-5px mb-5px"}),a(K,{show:s(M),"onUpdate:show":t[12]||(t[12]=e=>C(M)?M.value=e:null),position:"bottom",style:{height:"60%",padding:"10px"},"close-on-click-overlay":!1},{default:l(()=>[S("div",ma,[a(O,{type:"warning",size:"small",onClick:t[2]||(t[2]=e=>M.value=!1)},{default:l(()=>t[14]||(t[14]=[u(" \u53D6\u6D88 ")])),_:1}),t[16]||(t[16]=S("div",null,"\u786E\u5B9A\u67E5\u8BE2\u5185\u5BB9",-1)),a(O,{type:"primary",size:"small",onClick:L},{default:l(()=>t[15]||(t[15]=[u(" \u786E\u5B9A ")])),_:1})]),S("div",fa,[a(J,null,{default:l(()=>[a(y,{label:"\u7EC4\u7EC7"},{input:l(()=>[a(E,{modelValue:s(i).orgName,"onUpdate:modelValue":t[3]||(t[3]=e=>s(i).orgName=e),multiple:""},{default:l(()=>[(f(!0),r(h,null,q(ia,(e,A)=>(f(),F($,{key:A,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(D,{title:"\u65E5\u671F",value:s(R),placeholder:"\u9009\u62E9\u65E5\u671F\u8303\u56F4",onClick:t[4]||(t[4]=e=>V.value=!0)},null,8,["value"]),a(G,{show:s(V),"onUpdate:show":t[5]||(t[5]=e=>C(V)?V.value=e:null),type:"range","switch-mode":"year-month","default-date":s(I),"min-date":s(x)().add(-2,"years").toDate(),"max-date":s(x)().endOf("months").toDate(),onConfirm:B},null,8,["show","default-date","min-date","max-date"]),["rec","ship","rma"].includes(s(m))?(f(),r(h,{key:0},[a(y,{modelValue:s(i).docNo,"onUpdate:modelValue":t[6]||(t[6]=e=>s(i).docNo=e),label:"\u5355\u53F7",placeholder:"\u5355\u53F7"},null,8,["modelValue"]),a(y,{modelValue:s(i).srcDocNo,"onUpdate:modelValue":t[7]||(t[7]=e=>s(i).srcDocNo=e),label:"\u6765\u6E90\u5355\u53F7",placeholder:"\u6765\u6E90\u5355\u53F7"},null,8,["modelValue"]),a(y,{modelValue:s(i).customerCode,"onUpdate:modelValue":t[8]||(t[8]=e=>s(i).customerCode=e),label:"\u5BA2\u6237\u7F16\u7801",placeholder:"\u5BA2\u6237\u7F16\u7801"},null,8,["modelValue"])],64)):k("",!0),a(y,{modelValue:s(i).customerName,"onUpdate:modelValue":t[9]||(t[9]=e=>s(i).customerName=e),label:"\u5BA2\u6237\u540D\u79F0",placeholder:"\u5BA2\u6237\u540D\u79F0"},null,8,["modelValue"]),a(y,{modelValue:s(i).sellerName,"onUpdate:modelValue":t[10]||(t[10]=e=>s(i).sellerName=e),label:"\u4E1A\u52A1\u5458",placeholder:"\u4E1A\u52A1\u5458"},null,8,["modelValue"]),a(y,{name:"switch",label:"\u7EDF\u8BA1"},{input:l(()=>[a(H,{modelValue:s(i).hasStatistics,"onUpdate:modelValue":t[11]||(t[11]=e=>s(i).hasStatistics=e)},null,8,["modelValue"])]),_:1})]),_:1})])]),_:1},8,["show"]),S("div",pa,[a(X,{loading:s(N),"onUpdate:loading":t[13]||(t[13]=e=>C(N)?N.value=e:null),finished:s(P),"finished-text":"\u6CA1\u6709\u66F4\u591A\u6570\u636E\u4E86",onLoad:T},{default:l(()=>[(f(!0),r(h,null,q(s(b),(e,A)=>(f(),F(D,{key:A,class:"mb-5px"},{default:l(()=>[a(W,{class:"!w-100%",size:"small","label-width":"60"},{default:l(()=>[a(Q,{class:"!w-100%"},{default:l(()=>[["rec","ship","rma"].includes(s(m))?(f(),F(d,{key:0,span:24},{default:l(()=>[a(o,{label:"\u5355\u53F7",class:"bg-[var(--el-color-primary-light-9)]"},{default:l(()=>[u(n(e.docNo),1)]),_:2},1024)]),_:2},1024)):k("",!0),a(d,{span:12},{default:l(()=>[a(o,{label:"\u7EC4\u7EC7"},{default:l(()=>[u(n(e.orgName),1)]),_:2},1024)]),_:2},1024),["rec","ship","rma"].includes(s(m))?(f(),r(h,{key:1},[a(d,{span:12},{default:l(()=>[a(o,{label:"\u4E1A\u52A1\u65E5\u671F"},{default:l(()=>[u(n(e.businessDate),1)]),_:2},1024)]),_:2},1024),a(d,{span:24},{default:l(()=>[a(o,{label:"\u6765\u6E90\u5355\u53F7"},{default:l(()=>[u(n(e.srcDocNo),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u5BA2\u6237\u7F16\u7801"},{default:l(()=>[u(n(e.customerCode),1)]),_:2},1024)]),_:2},1024)],64)):k("",!0),a(d,{span:24},{default:l(()=>[a(o,{label:"\u5BA2\u6237\u540D\u79F0"},{default:l(()=>[u(n(e.customerName),1)]),_:2},1024)]),_:2},1024),s(m)==="rec"?(f(),r(h,{key:2},[a(d,{span:12},{default:l(()=>[a(o,{label:"\u7528\u9014"},{default:l(()=>[u(n(e.property),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u5E01\u79CD"},{default:l(()=>[u(n(e.currency),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u72B6\u6001"},{default:l(()=>[u(n(e.status),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u91D1\u989D"},{default:l(()=>[u(n(e.money),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u672C\u5E01"},{default:l(()=>[u(n(e.domesticMoney),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u6C47\u7387"},{default:l(()=>[u(n(e.exchangeRate),1)]),_:2},1024)]),_:2},1024),a(d,{span:24},{default:l(()=>[a(o,{label:"\u6536\u6B3E\u8D26\u53F7"},{default:l(()=>[u(n(e.bankAccount),1)]),_:2},1024)]),_:2},1024),a(d,{span:24},{default:l(()=>[a(o,{label:"\u5907\u6CE8"},{default:l(()=>[u(n(e.remark),1)]),_:2},1024)]),_:2},1024)],64)):["ship","rma"].includes(s(m))?(f(),r(h,{key:3},[a(d,{span:12},{default:l(()=>[a(o,{label:"\u54C1\u53F7"},{default:l(()=>[u(n(e.itemCode),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u54C1\u540D"},{default:l(()=>[u(n(e.itemName),1)]),_:2},1024)]),_:2},1024),a(d,{span:24},{default:l(()=>[a(o,{label:"\u89C4\u683C"},{default:l(()=>[u(n(e.spec),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u6570\u91CF"},{default:l(()=>[u(n(e.qty),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u5355\u4F4D"},{default:l(()=>[u(n(e.unit),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u5355\u4EF7"},{default:l(()=>[u(n(e.price),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u91D1\u989D"},{default:l(()=>[u(n(e.money),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u72B6\u6001"},{default:l(()=>[u(n(e.status),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u672C\u5E01"},{default:l(()=>[u(n(e.domesticMoney),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u6C47\u7387"},{default:l(()=>[u(n(e.exchangeRate),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u7A0E\u7EC4\u5408"},{default:l(()=>[u(n(e.taxName),1)]),_:2},1024)]),_:2},1024)],64)):["balance"].includes(s(m))?(f(),r(h,{key:4},[a(d,{span:12},{default:l(()=>[a(o,{label:"\u4E1A\u52A1\u5458"},{default:l(()=>[u(n(e.sellerName),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u6536\u6B3E\u91D1\u989D"},{default:l(()=>[u(n(e.recpaymentMoney),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u51FA\u8D27\u91D1\u989D"},{default:l(()=>[u(n(e.shipMoney),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u9000\u8D27\u91D1\u989D"},{default:l(()=>[u(n(e.rmaMoney),1)]),_:2},1024)]),_:2},1024),a(d,{span:12},{default:l(()=>[a(o,{label:"\u4F59\u989D"},{default:l(()=>[u(n(e.totalMoney),1)]),_:2},1024)]),_:2},1024)],64)):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["loading","finished"])]),a(Z,{class:"bg-white mt-5px","column-num":5},{default:l(()=>[a(g,null,{icon:l(()=>t[17]||(t[17]=[u(" \u603B\u6570 ")])),text:l(()=>[u(n(s(Y))+"\u6761 ",1)]),_:1}),s(i).hasStatistics?(f(),r(h,{key:0},[["rec","ship","rma"].includes(s(m))?(f(),r(h,{key:0},[a(g,null,{icon:l(()=>t[18]||(t[18]=[u(" \u91D1\u989D ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.money),1)]}),_:1}),a(g,null,{icon:l(()=>t[19]||(t[19]=[u(" \u672C\u5E01 ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.domesticMoney),1)]}),_:1})],64)):(f(),r(h,{key:1},[a(g,null,{icon:l(()=>t[20]||(t[20]=[u(" \u6536\u6B3E\u91D1\u989D ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.recpaymentMoney),1)]}),_:1}),a(g,null,{icon:l(()=>t[21]||(t[21]=[u(" \u51FA\u8D27\u91D1\u989D ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.shipMoney),1)]}),_:1}),a(g,null,{icon:l(()=>t[22]||(t[22]=[u(" \u9000\u8D27\u91D1\u989D ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.rmaMoney),1)]}),_:1}),a(g,null,{icon:l(()=>t[23]||(t[23]=[u(" \u4F59\u989D ")])),text:l(()=>{var e;return[u(n((e=s(v))==null?void 0:e.totalMoney),1)]}),_:1})],64))],64)):k("",!0)]),_:1})])}}}),[["__scopeId","data-v-e94b61d5"]]);export{_a as default};
