import{d as G,j as m,b as K,Y as W,r as X,au as Y,f as Z,x as D,Z as H,u as o,o as V,h as N,w as l,k as n,$ as J,M as Q,v as aa,t as ea,p as oa,y as na,a as ta,a0 as la,aU as ra,ad as f,a4 as ia,a5 as sa,a6 as pa,a7 as da,a2 as ma,aV as ua,ac as k,aW as ca,B as fa,a8 as ga,S as ha,a9 as wa,ab as ya,I as xa,_ as _a}from"./index-C8b06LRn.js";import{_ as ba}from"./Verify-BrbFSGD0.js";import{_ as va}from"./XButton-BOgar_Ex.js";import{u as Fa,_ as Va,L as Na,a as ka}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";import{u as g}from"./useIcon-CwemBubV.js";import{r as h}from"./formRules-Upspu04w.js";const La=_a(G({name:"LoginForm",__name:"LoginForm",setup(Ma){const{t:p}=oa(),L=na(),M=g({icon:"ep:house"}),U=g({icon:"ep:avatar"}),E=g({icon:"ep:lock"}),w=m(),{validForm:S}=ka(w),{setLoginState:Ua,getLoginState:I}=Fa(),{currentRoute:P,push:z}=ta(),R=K(),d=m(""),u=m(!1),y=m(),C=m("blockPuzzle"),T=W(()=>o(I)===Na.LOGIN),$={tenantName:[h],username:[h],password:[h]},t=X({isShowPassword:!1,captchaEnable:"true",tenantEnable:"false",loginForm:{tenantName:"\u828B\u9053\u6E90\u7801",username:"",password:"",captchaVerification:"",rememberMe:!0}}),x=async()=>{t.captchaEnable==="false"?await v({}):y.value.show()},_=async()=>{if(t.tenantEnable==="true"){const e=await k(t.loginForm.tenantName);f(e)}},b=m(),v=async e=>{u.value=!0;try{if(await _(),!await S())return;const a={...t.loginForm};a.captchaVerification=e.captchaVerification;const i=await ia(a);if(!i)return;b.value=sa.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),a.rememberMe?pa(a):da(),ma(i),d.value||(d.value="/"),d.value.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):z({path:d.value||R.addRouters[0].path})}finally{u.value=!1,b.value.close()}},j=async e=>{{if(u.value=!0,t.tenantEnable==="true"&&(await _(),!ua()))try{const r=await L.prompt("\u8BF7\u8F93\u5165\u79DF\u6237\u540D\u79F0",p("common.reminder"));if((r==null?void 0:r.action)!=="confirm")throw"cancel";const c=await k(r.value);f(c)}catch(r){if(r==="cancel")return}finally{u.value=!1}const a=location.origin+"/social-login?"+encodeURIComponent(`type=${e}&redirect=${d.value||"/"}`),i=await ca(e,encodeURIComponent(a));window.location.href=i}};return Y(()=>P.value,e=>{var a;d.value=(a=e==null?void 0:e.query)==null?void 0:a.redirect},{immediate:!0}),Z(()=>{(()=>{const e=la();e&&(t.loginForm={...t.loginForm,username:e.username?e.username:t.loginForm.username,password:e.password?e.password:t.loginForm.password,rememberMe:e.rememberMe,tenantName:e.tenantName?e.tenantName:t.loginForm.tenantName})})(),(async()=>{const e=location.host,a=await ra(e);a&&(t.loginForm.tenantName=a.name,f(a.id))})(),/wxwork/i.test(navigator.userAgent)&&j(30)}),(e,a)=>{const i=fa,r=ga,c=ha,O=wa,F=ya,q=va,A=ba,B=xa;return D((V(),N(B,{ref_key:"formLogin",ref:w,model:o(t).loginForm,rules:$,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[n(F,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[n(i,null,{default:l(()=>[n(Va,{style:{width:"100%"}})]),_:1})]),_:1}),n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[o(t).tenantEnable==="true"?(V(),N(i,{key:0,prop:"tenantName"},{default:l(()=>[n(c,{modelValue:o(t).loginForm.tenantName,"onUpdate:modelValue":a[0]||(a[0]=s=>o(t).loginForm.tenantName=s),placeholder:o(p)("login.tenantNamePlaceholder"),"prefix-icon":o(M),link:"",type:"primary"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):J("",!0)]),_:1}),n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[n(i,{prop:"username"},{default:l(()=>[n(c,{modelValue:o(t).loginForm.username,"onUpdate:modelValue":a[1]||(a[1]=s=>o(t).loginForm.username=s),placeholder:o(p)("login.usernamePlaceholder"),"prefix-icon":o(U)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[n(i,{prop:"password"},{default:l(()=>[n(c,{modelValue:o(t).loginForm.password,"onUpdate:modelValue":a[2]||(a[2]=s=>o(t).loginForm.password=s),placeholder:o(p)("login.passwordPlaceholder"),"prefix-icon":o(E),"show-password":"",type:"password",onKeyup:a[3]||(a[3]=Q(s=>x(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px","margin-top":"-20px","margin-bottom":"-20px"}},{default:l(()=>[n(i,null,{default:l(()=>[n(F,{justify:"space-between",style:{width:"100%"}},{default:l(()=>[n(r,{span:6},{default:l(()=>[n(O,{modelValue:o(t).loginForm.rememberMe,"onUpdate:modelValue":a[4]||(a[4]=s=>o(t).loginForm.rememberMe=s)},{default:l(()=>[aa(ea(o(p)("login.remember")),1)]),_:1},8,["modelValue"])]),_:1}),n(r,{offset:6,span:12})]),_:1})]),_:1})]),_:1}),n(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[n(i,null,{default:l(()=>[n(q,{loading:o(u),title:o(p)("login.login"),class:"w-[100%]",type:"primary",onClick:a[5]||(a[5]=s=>x())},null,8,["loading","title"])]),_:1})]),_:1}),n(A,{ref_key:"verify",ref:y,captchaType:o(C),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:v},null,8,["captchaType"])]),_:1})]),_:1},8,["model"])),[[H,o(T)]])}}}),[["__scopeId","data-v-7468d836"]]);export{La as default};
