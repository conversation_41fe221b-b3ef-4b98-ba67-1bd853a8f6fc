import{d as h,y as w,j as r,f as y,o as I,h as C,w as d,k as i,u as m,m as g,c9 as x,a8 as b,ab as N,_ as V}from"./index-C8b06LRn.js";import{_ as k}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{g as B,a as L,b as j,d as U}from"./index-584CLaw9.js";import"./el-card-CaOo8U9P.js";const q=V(h({__name:"index",setup(z){const l=w(),u=r([]),n=r([]),c=r([]),p=async(s,a,e)=>{try{if(a==="right"){const t=n.value.filter(o=>e.includes(o.accountId)).map(o=>({accountId:o.accountId,accountName:o.accountName,accountCode:o.accountCode}));await j(t),l.success("\u64CD\u4F5C\u6210\u529F")}else a==="left"&&(await U(e),l.success("\u64CD\u4F5C\u6210\u529F"));c.value=s}catch{l.error("\u64CD\u4F5C\u5931\u8D25")}},f=(s,a)=>{var e;return(e=a.accountName)==null?void 0:e.toLowerCase().includes(s.toLowerCase())};return y(()=>{(async()=>{try{const s=await B();u.value=s||[],c.value=u.value.map(t=>t.accountId).filter(Boolean);const a=u.value.map(t=>t.id).filter(Boolean),e=await L(a);n.value=e||[]}catch{l.error("\u83B7\u53D6\u8D26\u6237\u6570\u636E\u5931\u8D25")}})()}),(s,a)=>{const e=x,t=k,o=b,_=N;return I(),C(_,{gutter:20},{default:d(()=>[i(o,{span:24,xs:24},{default:d(()=>[i(t,null,{default:d(()=>[i(e,{modelValue:m(c),"onUpdate:modelValue":a[0]||(a[0]=v=>g(c)?c.value=v:null),data:m(n),props:{key:"accountId",label:"accountName"},titles:["\u53EF\u6DFB\u52A0\u8D26\u6237","\u5DF2\u6DFB\u52A0\u8D26\u6237"],onChange:p,class:"mt-20px",filterable:"","filter-method":f},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-a08c9ee3"]]);export{q as default};
