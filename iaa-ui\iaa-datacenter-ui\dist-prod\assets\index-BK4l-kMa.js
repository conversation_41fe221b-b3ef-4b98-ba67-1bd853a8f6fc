import{d as L,y as E,p as Q,j as m,r as W,f as X,T as Y,o as n,c as S,k as e,w as t,u as s,M as Z,F as x,g as U,N as $,D as A,h as p,v as i,x as _,l as ee,t as v,S as ae,B as le,z as te,A as se,G as oe,H as re,I as ne,J as ie,aM as pe,K as ue,L as de}from"./index-C8b06LRn.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as me}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as fe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as _e}from"./index-CkzUfjB7.js";import{d as ye}from"./formatTime-COZ9Bl52.js";import{_ as ge,g as he,d as be}from"./ClientForm.vue_vue_type_script_setup_true_lang-D4YWM6Oq.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./constants-D3f7Z3TX.js";const ke=["src"],we=L({name:"SystemOAuth2Client",__name:"index",setup(Se){const T=E(),{t:F}=Q(),y=m(!0),C=m(0),V=m([]),o=W({pageNo:1,pageSize:10,name:null,status:void 0}),N=m(),u=async()=>{y.value=!0;try{const d=await he(o);V.value=d.list,C.value=d.total}finally{y.value=!1}},g=()=>{o.pageNo=1,u()},G=()=>{N.value.resetFields(),g()},O=m(),z=(d,l)=>{O.value.open(d,l)};return X(()=>{u()}),(d,l)=>{const I=_e,R=ae,h=le,K=te,P=se,b=oe,c=re,j=ne,M=fe,r=ie,q=me,B=pe,D=ue,H=ce,k=Y("hasPermi"),J=de;return n(),S(x,null,[e(I,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),e(M,null,{default:t(()=>[e(j,{class:"-mb-15px",model:s(o),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[e(h,{label:"\u5E94\u7528\u540D",prop:"name"},{default:t(()=>[e(R,{modelValue:s(o).name,"onUpdate:modelValue":l[0]||(l[0]=a=>s(o).name=a),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:Z(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(h,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(P,{modelValue:s(o).status,"onUpdate:modelValue":l[1]||(l[1]=a=>s(o).status=a),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),S(x,null,U(s($)(s(A).COMMON_STATUS),a=>(n(),p(K,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(c,{onClick:g},{default:t(()=>[e(b,{icon:"ep:search",class:"mr-5px"}),l[5]||(l[5]=i(" \u641C\u7D22"))]),_:1}),e(c,{onClick:G},{default:t(()=>[e(b,{icon:"ep:refresh",class:"mr-5px"}),l[6]||(l[6]=i(" \u91CD\u7F6E"))]),_:1}),_((n(),p(c,{plain:"",type:"primary",onClick:l[2]||(l[2]=a=>z("create"))},{default:t(()=>[e(b,{icon:"ep:plus",class:"mr-5px"}),l[7]||(l[7]=i(" \u65B0\u589E "))]),_:1})),[[k,["system:oauth2-client:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(M,null,{default:t(()=>[_((n(),p(D,{data:s(V)},{default:t(()=>[e(r,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",align:"center",prop:"clientId"}),e(r,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",align:"center",prop:"secret"}),e(r,{label:"\u5E94\u7528\u540D",align:"center",prop:"name"}),e(r,{label:"\u5E94\u7528\u56FE\u6807",align:"center",prop:"logo"},{default:t(a=>[ee("img",{width:"40px",height:"40px",src:a.row.logo},null,8,ke)]),_:1}),e(r,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(a=>[e(q,{type:s(A).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(r,{label:"\u8BBF\u95EE\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"accessTokenValiditySeconds"},{default:t(a=>[i(v(a.row.accessTokenValiditySeconds)+" \u79D2",1)]),_:1}),e(r,{label:"\u5237\u65B0\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"refreshTokenValiditySeconds"},{default:t(a=>[i(v(a.row.refreshTokenValiditySeconds)+" \u79D2",1)]),_:1}),e(r,{label:"\u6388\u6743\u7C7B\u578B",align:"center",prop:"authorizedGrantTypes"},{default:t(a=>[(n(!0),S(x,null,U(a.row.authorizedGrantTypes,(w,f)=>(n(),p(B,{"disable-transitions":!0,key:f,index:f,class:"mr-5px"},{default:t(()=>[i(v(w),1)]),_:2},1032,["index"]))),128))]),_:1}),e(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:s(ye)},null,8,["formatter"]),e(r,{label:"\u64CD\u4F5C",align:"center"},{default:t(a=>[_((n(),p(c,{link:"",type:"primary",onClick:w=>z("update",a.row.id)},{default:t(()=>l[8]||(l[8]=[i(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[k,["system:oauth2-client:update"]]]),_((n(),p(c,{link:"",type:"danger",onClick:w=>(async f=>{try{await T.delConfirm(),await be(f),T.success(F("common.delSuccess")),await u()}catch{}})(a.row.id)},{default:t(()=>l[9]||(l[9]=[i(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[k,["system:oauth2-client:delete"]]])]),_:1})]),_:1},8,["data"])),[[J,s(y)]]),e(H,{total:s(C),page:s(o).pageNo,"onUpdate:page":l[3]||(l[3]=a=>s(o).pageNo=a),limit:s(o).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>s(o).pageSize=a),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(ge,{ref_key:"formRef",ref:O,onSuccess:u},null,512)],64)}}});export{we as default};
