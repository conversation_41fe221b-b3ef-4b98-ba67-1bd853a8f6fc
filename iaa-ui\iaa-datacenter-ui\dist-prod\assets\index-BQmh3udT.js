import{aG as y,d as A,j as m,y as B,p as F,f as G,o as s,h as i,w as o,x as H,u as n,k as c,c as b,F as f,g as k,v as L,z as O,A as q,H as D,L as E}from"./index-C8b06LRn.js";import{_ as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as J}from"./index-COdQIXZX.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";const K=A({__name:"index",setup(M){const g=m([]),w=m(0),V=m([]),r=m([]),p=m(!1),j=B(),{t:h}=F(),d=m({pageNo:1,pageSize:30}),C=(t,l)=>{for(let u in t)t[u]?d.value[u]=t[u]:d.value.hasOwnProperty(u)&&delete d.value[u];l||(d.value.pageNo=1),_()},_=async()=>{p.value=!0;try{const t=await(async l=>await y.get({url:"/butt-joint/ekuaibao/subject/page",params:l}))(d.value);g.value=t.list,w.value=t.total}finally{p.value=!1}},U=async()=>{const t=await(async()=>await y.get({url:"/butt-joint/ekuaibao/subject/get-columns"}))();V.value=t;const l=await(async()=>await y.get({url:"/butt-joint/ekuaibao/subject/get-erp-all-subject"}))();r.value=l},z=async t=>{p.value=!0;try{await(async l=>await y.post({url:"/butt-joint/ekuaibao/subject/save",data:l}))(t),j.success(h("common.updateSuccess"))}finally{p.value=!1}};return G(()=>{U(),_()}),(t,l)=>{const u=O,v=q,x=D,S=J,N=I,P=E;return s(),i(N,null,{default:o(()=>[H((s(),i(S,{data:n(g),total:n(w),columns:n(V),page:n(d),onSearch:C,onPagination:l[0]||(l[0]=e=>C(e,!0)),stripe:"","highlight-current":"",height:"calc(100vh - 350px)"},{managementCode:o(({row:e})=>[c(v,{modelValue:e.managementCode,"onUpdate:modelValue":a=>e.managementCode=a,filterable:"",size:"small"},{default:o(()=>[(s(!0),b(f,null,k(n(r),a=>(s(),i(u,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),salesCode:o(({row:e})=>[c(v,{modelValue:e.salesCode,"onUpdate:modelValue":a=>e.salesCode=a,filterable:"",size:"small"},{default:o(()=>[(s(!0),b(f,null,k(n(r),a=>(s(),i(u,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),manufactureCode:o(({row:e})=>[c(v,{modelValue:e.manufactureCode,"onUpdate:modelValue":a=>e.manufactureCode=a,filterable:"",size:"small"},{default:o(()=>[(s(!0),b(f,null,k(n(r),a=>(s(),i(u,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),developmentCode:o(({row:e})=>[c(v,{modelValue:e.developmentCode,"onUpdate:modelValue":a=>e.developmentCode=a,filterable:"",size:"small"},{default:o(()=>[(s(!0),b(f,null,k(n(r),a=>(s(),i(u,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),operation:o(({row:e})=>[c(x,{type:"primary",size:"small",link:"",onClick:a=>z(e)},{default:o(()=>l[1]||(l[1]=[L(" \u4FDD\u5B58 ")])),_:2},1032,["onClick"])]),_:1},8,["data","total","columns","page"])),[[P,n(p)]])]),_:1})}}});export{K as default};
