import{d as Pe,y as ze,aN as Ee,j as p,r as Be,cj as q,Y as le,au as te,T as je,o as m,c as b,k as o,w as n,l as Y,t as V,u,v,x as oe,h as g,$ as L,m as $,F as H,g as ne,N as Fe,D as qe,C as He,B as Je,a8 as Ke,S as Ge,z as Qe,A as We,cc as Xe,ab as Ze,az as ea,H as aa,J as la,K as ta,I as oa,cy as na,L as ua,_ as ra}from"./index-C8b06LRn.js";import{C as x}from"./claim-BPjhJe4B.js";import{C as da}from"./index-DVzg-3-A.js";import{g as ma}from"./index-584CLaw9.js";const ca={key:0,class:"mt--8px"},ia={key:1},sa={key:1},pa={key:1},ya={key:1,class:"mt--8px"},fa={key:1},va={key:1},ha={key:1},ba={key:0},Ca={class:"mr-15px"},ga={key:1},Aa={key:2},_a={key:0,class:"mr-5"},ka={class:"mb-15px"},Va={class:"mb-15px"},wa=ra(Pe({__name:"recordDialogPC",props:{show:{type:Boolean},id:{},readOnly:{type:Boolean},isAdd:{type:Boolean},isEdit:{type:Boolean}},emits:["update:show","success"],setup(be,{emit:Ce}){var ve;const s=ze(),ue=Ee(),c=be,M=Ce,re=p(!1),ge=p(),J=p(!1),R=p(!1),P=p(!1),I=p(null),K=p(-1),a=Be({claimDate:q().format("YYYY-MM-DD"),type:2,status:0,salesmanName:((ve=ue.user)==null?void 0:ve.nickname)||"",customerName:"",customerCode:"",totalAmount:0,currency:"",currencyCode:"",customerList:[],collectionAccount:"",creditInsurance:"",collectionId:"",remark:""}),_=p([]),k=p([]),N=p([{type:3,amount:0}]),D=p(!1),z=p(""),de=p([]),E=p(null),U=p(!1),T=p(""),me=p([]),w=p(null),G=le(()=>a.customerName&&a.customerCode?`${a.customerName}`:""),ce=le(()=>_.value.reduce((l,e)=>l+Number(e.amount||0),0)+k.value.reduce((l,e)=>l+Number(e.amount||0),0)+N.value.reduce((l,e)=>l+Number(e.amount||0),0)),ie=p(0);te([_,k,N],()=>{a.totalAmount=ce.value,ie.value=ce.value},{deep:!0});const Q=p(!1),W=async()=>{try{Q.value=!0;const l=await x.getCustomer(z.value||"");de.value=l||[]}catch{s.error("\u83B7\u53D6\u5BA2\u6237\u6570\u636E\u5931\u8D25")}finally{Q.value=!1}},Ae=l=>{E.value=l},_e=()=>{E.value?(a.customerName=E.value.name,a.customerCode=E.value.code,D.value=!1,_.value=[]):s.error("\u8BF7\u9009\u62E9\u5BA2\u6237")},X=async()=>{try{const l=await x.getOrders({code:a.customerCode,DocNo:T.value});me.value=l||[]}catch{s.error("\u83B7\u53D6\u8BA2\u5355\u6570\u636E\u5931\u8D25")}},ke=l=>{const e=B.value.find(d=>d.Code===a.currencyCode);if(e&&l){const d=e.Name;if(d!==l.currency)return s.warning(`\u6240\u9009\u8BA2\u5355\u5E01\u79CD\u4E3A ${l.currency}\uFF0C\u4E0E\u5F53\u524D\u9009\u62E9\u7684\u5E01\u79CD ${d} \u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u9009\u62E9`),void(w.value=null)}w.value=l},Ve=()=>{if(!w.value||K.value<0)return void s.error("\u8BF7\u9009\u62E9\u8BA2\u5355");const l=_.value[K.value];l.orderNo=w.value.DocNo,l.orderAmount=w.value.salesPrice,l.remainingAmount=w.value.remainingAmount,l.amount=w.value.remainingAmount,l.currency=w.value.currency,l.claimRatio=Math.round(l.amount/l.orderAmount*100),U.value=!1},we=()=>{c.readOnly||_.value.push({type:1,orderNo:"",orderAmount:0,amount:0,remainingAmount:0,shipAmount:0,claimRatio:100})};te(()=>a.currency,l=>{l&&k.value.forEach(e=>{e.currency=l})});const Ne=()=>{c.readOnly||k.value.push({type:2,expenseType:"",amount:0,currency:a.currency||"\u7F8E\u5143"})},se=()=>[..._.value.map(l=>({type:1,orderNo:l.orderNo,orderAmount:l.orderAmount,amount:Number(l.amount||0),remainingAmount:Number(l.remainingAmount||0),shipAmount:Number(l.shipAmount||0)})),...k.value.map(l=>({type:2,expenseType:l.expenseType,amount:Number(l.amount||0),remainingAmount:0,expenseRemark:l.expenseRemark})),...N.value.map(l=>({type:3,amount:Number(l.amount||0)})).filter(l=>l.amount>0)],xe=async()=>{if(P.value)return;const l={id:I.value||void 0,claimDate:a.claimDate||q().format("YYYY-MM-DD"),type:a.type,status:2,salesmanName:a.salesmanName,customerName:a.customerName,customerCode:a.customerCode,currency:a.currency,currencyCode:a.currencyCode,customerList:a.customerList,totalAmount:a.totalAmount,collectionAccount:a.collectionAccount,creditInsurance:a.creditInsurance,collectionId:a.collectionId,remark:a.remark,detailList:se()};try{P.value=!0,await x.createClaim(l),I.value=null,s.success("\u6682\u5B58\u6210\u529F"),M("success")}catch{s.error("\u6682\u5B58\u5931\u8D25")}finally{P.value=!1}},pe=async()=>{if(R.value||!await Ie())return;let l=3;c.isEdit&&(l=1);const e={id:I.value||void 0,claimDate:a.claimDate||q().format("YYYY-MM-DD"),type:a.type,status:l,salesmanName:a.salesmanName,customerName:a.customerName,customerCode:a.customerCode,currency:a.currency,currencyCode:a.currencyCode,totalAmount:a.totalAmount,customerList:a.customerList,collectionAccount:a.collectionAccount,creditInsurance:a.creditInsurance,collectionId:a.collectionId,remark:a.remark,detailList:se()};try{R.value=!0,await x.createClaim(e),I.value=null,s.success("\u5F55\u6B3E\u6210\u529F"),M("success")}catch{s.error("\u5F55\u6B3E\u5931\u8D25")}finally{R.value=!1}},Ie=async()=>{if(!a.claimDate)return s.error("\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A"),!1;if(!a.salesmanName)return s.error("\u4E1A\u52A1\u5458\u4E0D\u80FD\u4E3A\u7A7A"),!1;if(!a.customerName||!a.customerCode)return s.error("\u5BA2\u6237\u540D\u79F0\u548C\u5BA2\u6237\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"),!1;if(!a.currency)return s.error("\u5E01\u79CD\u4E0D\u80FD\u4E3A\u7A7A"),!1;if(!a.collectionAccount)return s.error("\u6536\u6B3E\u8D26\u6237\u4E0D\u80FD\u4E3A\u7A7A"),!1;if(a.collectionAccount==="\u4FE1\u4FDD\uFF08\u7F8E\u91D1\uFF09"&&!a.creditInsurance)return s.error("\u8BF7\u9009\u62E9\u4FE1\u4FDD\u8D26\u53F7\u8BF4\u660E"),!1;if(a.totalAmount<=0)return s.error("\u603B\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0"),!1;for(let l=0;l<_.value.length;l++)if(_.value[l].amount<=0)return s.error(`\u7B2C${l+1}\u6761\u8BA2\u5355\u660E\u7EC6\u7684\u8BA4\u6B3E\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`),!1;for(let l=0;l<k.value.length;l++)if(k.value[l].amount<=0)return s.error(`\u7B2C${l+1}\u6761\u8D39\u7528\u660E\u7EC6\u7684\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0`),!1;return!0},ye=()=>{M("update:show",!1),setTimeout(()=>{fe(),M("success")},300)},De=async()=>{await s.confirm("\u662F\u5426\u786E\u8BA4\u6536\u6B3E\uFF1F"),await x.updateClaim([c.id]),s.success("\u786E\u8BA4\u6536\u6B3E\u6210\u529F"),M("success")},fe=()=>{var l;Object.assign(a,{claimDate:q().format("YYYY-MM-DD"),type:2,status:0,salesmanName:((l=ue.user)==null?void 0:l.nickname)||"",customerName:"",customerCode:"",totalAmount:0,currency:"",collectionAccount:"",customerList:[],collectionId:""}),_.value=[],k.value=[],I.value=null},Z=p([]),Ue=le(()=>Z.value.map(l=>({label:l.accountName,value:l.accountName,accountId:l.accountId}))),B=p([]),Oe=async()=>{try{const l=await ma();Z.value=l||[]}catch{s.error("\u83B7\u53D6\u8D26\u6237\u5217\u8868\u5931\u8D25")}},Ye=async()=>{try{const l=await x.getCurrency();B.value=l||[]}catch{s.error("\u83B7\u53D6\u5E01\u79CD\u5217\u8868\u5931\u8D25")}},Le=l=>{const e=Z.value.find(d=>d.accountName===l);a.collectionId=e?e.accountId:""},Me=l=>{const e=B.value.find(d=>d.Code===l);e?(a.currency=e.Name,a.currencyCode=e.Code):a.currencyCode=""};return te(()=>c.show,async l=>{l&&setTimeout(async()=>{try{await Promise.all([Ye(),Oe()]),c.id&&!c.isAdd?await(async e=>{if(e)try{J.value=!0;const d=await x.getClaimDetail(e);if(d)if(I.value=d.id,a.claimDate=d.claimDate,a.type=d.type,a.status=d.status,a.salesmanName=d.salesmanName,a.customerName=d.customerName,a.customerCode=d.customerCode,a.totalAmount=d.totalAmount,a.currency=d.currency,a.currencyCode=d.currencyCode,a.customerList=d.customerList,a.collectionAccount=d.collectionAccount,a.creditInsurance=d.creditInsurance,a.collectionId=d.collectionId,a.remark=d.remark,d.detailList&&Array.isArray(d.detailList)){_.value=d.detailList.filter(i=>i.type===1).map(i=>{let h=100;return i.orderAmount>0&&(h=Math.round(Number(i.amount||0)/i.orderAmount*100)),{type:1,orderNo:i.orderNo||"",orderAmount:i.orderAmount||0,amount:i.amount||0,remainingAmount:i.remainingAmount||0,shipAmount:i.shipAmount||0,claimRatio:h}}),k.value=d.detailList.filter(i=>i.type===2).map(i=>({type:2,expenseType:i.expenseType||"",amount:i.amount||0,expenseRemark:i.expenseRemark||""}));const A=d.detailList.filter(i=>i.type===3).map(i=>({type:3,amount:i.amount||0}));A.length>0?N.value=A:N.value=[{type:3,amount:0}]}else N.value=[{type:3,amount:0}]}catch{s.error("\u83B7\u53D6\u8BA4\u6B3E\u8BE6\u60C5\u5931\u8D25")}finally{J.value=!1}})(c.id):c.isAdd&&fe()}catch{s.error("\u521D\u59CB\u5316\u6570\u636E\u5931\u8D25")}},50),re.value=await da.getDate()},{immediate:!0}),(l,e)=>{const d=He,A=Je,i=Ke,h=Ge,O=Qe,j=We,Re=Xe,Te=Ze,ee=ea,C=aa,f=la,S=ta,Se=oa,ae=na,he=ua,$e=je("hasPermi");return m(),b(H,null,[o(ae,{"model-value":l.show,title:"\u5F55\u6B3E\u8BE6\u60C5",width:"40%","close-on-click-modal":!1,"destroy-on-close":!0,draggable:!0,style:{"margin-top":"1vh"},onClose:ye},{footer:n(()=>[c.readOnly||c.isEdit?!c.readOnly&&c.isEdit?(m(),b("div",ga,[o(C,{type:"primary",onClick:pe,loading:u(R)},{default:n(()=>e[26]||(e[26]=[v("\u786E\u5B9A\u4FEE\u6539")])),_:1},8,["loading"])])):(m(),b("div",Aa,[u(a).status===3?(m(),b("span",_a,[oe((m(),g(C,{type:"success",onClick:De},{default:n(()=>e[27]||(e[27]=[v("\u786E\u8BA4\u6536\u6B3E")])),_:1})),[[$e,["record:money:status"]]])])):L("",!0),o(C,{type:"primary",onClick:ye},{default:n(()=>e[28]||(e[28]=[v("\u5173\u95ED")])),_:1})])):(m(),b("div",ba,[Y("span",Ca,"\u5DF2\u586B\u5199\u603B\u91D1\u989D\uFF1A"+V(u(ie)),1),o(C,{onClick:xe,loading:u(P)},{default:n(()=>e[24]||(e[24]=[v("\u6682\u5B58")])),_:1},8,["loading"]),o(C,{type:"primary",onClick:pe,loading:u(R)},{default:n(()=>e[25]||(e[25]=[v("\u786E\u8BA4\u63D0\u4EA4")])),_:1},8,["loading"])]))]),default:n(()=>[oe((m(),g(Se,{ref_key:"formRef",ref:ge,model:u(a),"label-width":"100px",disabled:c.readOnly},{default:n(()=>[o(Te,{gutter:20},{default:n(()=>[o(i,{span:12},{default:n(()=>[o(A,{label:"\u65E5\u671F",prop:"claimDate"},{default:n(()=>[o(d,{modelValue:u(a).claimDate,"onUpdate:modelValue":e[0]||(e[0]=t=>u(a).claimDate=t),type:"date","value-format":"YYYY-MM-DD",placeholder:"\u9009\u62E9\u65E5\u671F",style:{width:"100%"},disabled:!u(re)},null,8,["modelValue","disabled"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u4E1A\u52A1\u5458",prop:"salesmanName"},{default:n(()=>[o(h,{modelValue:u(a).salesmanName,"onUpdate:modelValue":e[1]||(e[1]=t=>u(a).salesmanName=t),placeholder:"\u9ED8\u8BA4\u5F53\u524D\u767B\u5F55\u4EBA"},null,8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName"},{default:n(()=>[o(h,{modelValue:u(G),"onUpdate:modelValue":e[2]||(e[2]=t=>$(G)?G.value=t:null),placeholder:"\u8F93\u5165\u9009\u62E9",readonly:"",onClick:e[3]||(e[3]=t=>!c.readOnly&&(async()=>{c.readOnly||(D.value=!0,await W())})())},null,8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u5BA2\u6237\u7F16\u7801",prop:"customerCode"},{default:n(()=>[o(h,{modelValue:u(a).customerCode,"onUpdate:modelValue":e[4]||(e[4]=t=>u(a).customerCode=t),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u603B\u91D1\u989D",prop:"totalAmount"},{default:n(()=>[o(h,{modelValue:u(a).totalAmount,"onUpdate:modelValue":e[5]||(e[5]=t=>u(a).totalAmount=t),readonly:""},null,8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u5E01\u79CD",prop:"currency"},{default:n(()=>[o(j,{modelValue:u(a).currency,"onUpdate:modelValue":e[6]||(e[6]=t=>u(a).currency=t),placeholder:"\u8BF7\u9009\u62E9\u5E01\u79CD",style:{width:"100%"},onChange:Me},{default:n(()=>[(m(!0),b(H,null,ne(u(B),t=>(m(),g(O,{key:t.Code,label:t.Name,value:t.Code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u5BA2\u6237\u6C34\u5355",prop:"customerList"},{default:n(()=>[o(Re,{modelValue:u(a).customerList,"onUpdate:modelValue":e[7]||(e[7]=t=>u(a).customerList=t),height:"80px",width:"80px",limit:5},null,8,["modelValue"])]),_:1})]),_:1}),o(i,{span:12},{default:n(()=>[o(A,{label:"\u6536\u6B3E\u8D26\u6237",prop:"collectionAccount"},{default:n(()=>[o(j,{modelValue:u(a).collectionAccount,"onUpdate:modelValue":e[8]||(e[8]=t=>u(a).collectionAccount=t),placeholder:"\u8BF7\u9009\u62E9\u6536\u6B3E\u8D26\u6237",style:{width:"100%"},disabled:c.readOnly,onChange:Le},{default:n(()=>[(m(!0),b(H,null,ne(u(Ue),t=>(m(),g(O,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),o(A,{label:"\u5907\u6CE8",prop:"remark"},{default:n(()=>[o(h,{modelValue:u(a).remark,"onUpdate:modelValue":e[9]||(e[9]=t=>u(a).remark=t)},null,8,["modelValue"])]),_:1}),u(a).collectionAccount==="\u4FE1\u4FDD\uFF08\u7F8E\u91D1\uFF09"?(m(),g(A,{key:0,label:"\u8BF4\u660E",prop:"creditInsurance",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u4FE1\u4FDD\u8D26\u53F7\u8BF4\u660E",trigger:"change"}]},{default:n(()=>[o(j,{modelValue:u(a).creditInsurance,"onUpdate:modelValue":e[10]||(e[10]=t=>u(a).creditInsurance=t),placeholder:"\u8BF7\u9009\u62E9\u4FE1\u4FDD\u8D26\u53F7\u8BF4\u660E",style:{width:"100%"},disabled:c.readOnly},{default:n(()=>[o(O,{label:"ScentaChina",value:"ScentaChina"}),o(O,{label:"ScentMachine",value:"ScentMachine"}),o(O,{label:"ScentMarketing",value:"ScentMarketing"})]),_:1},8,["modelValue","disabled"])]),_:1})):L("",!0)]),_:1})]),_:1}),Y("div",null,[o(ee,{class:"p-2px"},{default:n(()=>e[17]||(e[17]=[v("\u8BA2\u5355\u660E\u7EC6")])),_:1}),c.readOnly?L("",!0):(m(),b("div",ca,[o(C,{type:"primary",plain:"",onClick:we,size:"small"},{default:n(()=>e[18]||(e[18]=[v("+ \u6DFB\u52A0\u8BA2\u5355")])),_:1})])),o(S,{data:u(_),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:n(()=>[o(f,{label:"\u8BA2\u5355\u53F7","min-width":"160"},{default:n(({row:t,$index:y})=>[c.readOnly?(m(),b("span",ia,V(t.orderNo),1)):(m(),g(h,{key:0,modelValue:t.orderNo,"onUpdate:modelValue":r=>t.orderNo=r,readonly:"",onClick:r=>{return F=y,void(c.readOnly||(a.customerCode?(K.value=F,T.value="",U.value=!0,X()):s.error("\u8BF7\u5148\u9009\u62E9\u5BA2\u6237")));var F}},null,8,["modelValue","onUpdate:modelValue","onClick"]))]),_:1}),o(f,{label:"\u8BA2\u5355\u91D1\u989D","min-width":"100"},{default:n(({row:t})=>[Y("span",null,V(t.orderAmount),1)]),_:1}),o(f,{label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D","min-width":"100"},{default:n(({row:t})=>[Y("span",null,V(t.remainingAmount),1)]),_:1}),o(f,{label:"\u6536\u6B3E\u6BD4\u4F8B(%)","min-width":"90"},{default:n(({row:t})=>[l.readOnly?(m(),b("span",sa,V(t.claimRatio),1)):(m(),g(h,{key:0,modelValue:t.claimRatio,"onUpdate:modelValue":y=>t.claimRatio=y,type:"number",step:1,min:0,max:100,style:{width:"100%"},onChange:y=>(r=>{if(r.claimRatio!==void 0&&r.claimRatio!==null){const F=Math.round(r.orderAmount*r.claimRatio/100);r.amount=F,r.amount>r.remainingAmount&&(r.amount=r.remainingAmount,r.orderAmount>0&&(r.claimRatio=Math.round(r.amount/r.orderAmount*100)),s.warning("\u8BA4\u6B3E\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5269\u4F59\u53EF\u8BA4\u6B3E\u91D1\u989D\uFF0C\u6536\u6B3E\u6BD4\u4F8B\u5DF2\u81EA\u52A8\u8C03\u6574"))}})(t)},null,8,["modelValue","onUpdate:modelValue","onChange"]))]),_:1}),o(f,{label:"\u8BA4\u6B3E\u91D1\u989D","min-width":"120"},{default:n(({row:t})=>[c.readOnly?(m(),b("span",pa,V(t.amount),1)):(m(),g(h,{key:0,modelValue:t.amount,"onUpdate:modelValue":y=>t.amount=y,type:"number",step:1,min:0,max:t.orderAmount,"controls-position":"right",style:{width:"100%"},onChange:y=>(r=>{r.amount>r.remainingAmount?(r.amount=r.remainingAmount,r.orderAmount>0&&(r.claimRatio=Math.round(r.amount/r.orderAmount*100)),s.warning("\u8BA4\u6B3E\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7\u5269\u4F59\u53EF\u8BA4\u6B3E\u91D1\u989D\uFF0C\u6536\u6B3E\u6BD4\u4F8B\u5DF2\u81EA\u52A8\u8C03\u6574")):r.orderAmount>0&&(r.claimRatio=Math.round(r.amount/r.orderAmount*100))})(t)},null,8,["modelValue","onUpdate:modelValue","max","onChange"]))]),_:1}),c.readOnly?L("",!0):(m(),g(f,{key:0,label:"\u64CD\u4F5C",width:"80"},{default:n(({$index:t})=>[o(C,{type:"danger",size:"small",onClick:y=>{return r=t,void(c.readOnly||_.value.splice(r,1));var r}},{default:n(()=>e[19]||(e[19]=[v(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),o(ee,{class:"p-2px"},{default:n(()=>e[20]||(e[20]=[v("\u8D39\u7528\u7C7B\u522B")])),_:1}),c.readOnly?L("",!0):(m(),b("div",ya,[o(C,{type:"primary",plain:"",onClick:Ne,size:"small"},{default:n(()=>e[21]||(e[21]=[v("+ \u6DFB\u52A0\u8D39\u7528")])),_:1})])),o(S,{data:u(k),border:"","max-height":150,height:"150","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:n(()=>[o(f,{label:"\u8D39\u7528\u7C7B\u522B","min-width":"80"},{default:n(({row:t})=>[o(j,{modelValue:t.expenseType,"onUpdate:modelValue":y=>t.expenseType=y,placeholder:"\u8BF7\u9009\u62E9\u8D39\u7528\u7C7B\u522B"},{default:n(()=>[(m(!0),b(H,null,ne(u(Fe)(u(qe).FINANCIAL_COSTS_TYPE),y=>(m(),g(O,{key:y.value,label:y.label,value:y.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),o(f,{label:"\u91D1\u989D","min-width":"80"},{default:n(({row:t})=>[c.readOnly?(m(),b("span",fa,V(t.amount),1)):(m(),g(h,{key:0,modelValue:t.amount,"onUpdate:modelValue":y=>t.amount=y,precision:2,type:"number",min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),o(f,{label:"\u8D39\u7528\u5907\u6CE8","min-width":"120"},{default:n(({row:t})=>[c.readOnly?(m(),b("span",va,V(t.expenseRemark),1)):(m(),g(h,{key:0,modelValue:t.expenseRemark,"onUpdate:modelValue":y=>t.expenseRemark=y},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),c.readOnly?L("",!0):(m(),g(f,{key:0,label:"\u64CD\u4F5C",width:"80"},{default:n(({$index:t})=>[o(C,{type:"danger",size:"small",onClick:y=>{return r=t,void(c.readOnly||k.value.splice(r,1));var r}},{default:n(()=>e[22]||(e[22]=[v(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1}))]),_:1},8,["data"]),o(ee,{class:"p-2px"},{default:n(()=>e[23]||(e[23]=[v("\u8BA2\u5355\u672A\u4E0B")])),_:1}),o(S,{data:u(N),border:"","max-height":150,height:"100","header-cell-style":{background:"#f5f7fa",color:"#606266"}},{default:n(()=>[o(f,{label:"\u91D1\u989D","min-width":"120"},{default:n(({row:t})=>[l.readOnly?(m(),b("span",ha,V(t.amount),1)):(m(),g(h,{key:0,modelValue:t.amount,"onUpdate:modelValue":y=>t.amount=y,precision:2,type:"number",min:0,step:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"]))]),_:1})]),_:1},8,["data"])])]),_:1},8,["model","disabled"])),[[he,u(J)]])]),_:1},8,["model-value"]),o(ae,{modelValue:u(D),"onUpdate:modelValue":e[13]||(e[13]=t=>$(D)?D.value=t:null),title:"\u9009\u62E9\u5BA2\u6237",width:"600px","append-to-body":""},{footer:n(()=>[o(C,{onClick:e[12]||(e[12]=t=>D.value=!1)},{default:n(()=>e[30]||(e[30]=[v("\u53D6\u6D88")])),_:1}),o(C,{type:"primary",onClick:_e},{default:n(()=>e[31]||(e[31]=[v("\u786E\u8BA4")])),_:1})]),default:n(()=>[Y("div",ka,[o(h,{modelValue:u(z),"onUpdate:modelValue":e[11]||(e[11]=t=>$(z)?z.value=t:null),placeholder:"\u8F93\u5165\u5BA2\u6237\u540D\u79F0\u68C0\u7D22",clearable:"",onInput:W},{append:n(()=>[o(C,{onClick:W},{default:n(()=>e[29]||(e[29]=[v("\u641C\u7D22")])),_:1})]),_:1},8,["modelValue"])]),oe((m(),g(S,{data:u(de),height:"300","highlight-current-row":"",onCurrentChange:Ae},{default:n(()=>[o(f,{prop:"name",label:"\u5BA2\u6237\u540D\u79F0"}),o(f,{prop:"code",label:"\u5BA2\u6237\u7F16\u7801"})]),_:1},8,["data"])),[[he,u(Q)]])]),_:1},8,["modelValue"]),o(ae,{modelValue:u(U),"onUpdate:modelValue":e[16]||(e[16]=t=>$(U)?U.value=t:null),title:"\u9009\u62E9\u8BA2\u5355",width:"40%","append-to-body":""},{footer:n(()=>[o(C,{onClick:e[15]||(e[15]=t=>U.value=!1)},{default:n(()=>e[33]||(e[33]=[v("\u53D6\u6D88")])),_:1}),o(C,{type:"primary",onClick:Ve},{default:n(()=>e[34]||(e[34]=[v("\u786E\u8BA4")])),_:1})]),default:n(()=>[Y("div",Va,[o(h,{modelValue:u(T),"onUpdate:modelValue":e[14]||(e[14]=t=>$(T)?T.value=t:null),placeholder:"\u8F93\u5165\u8BA2\u5355\u53F7\u68C0\u7D22",clearable:"",onInput:X},{append:n(()=>[o(C,{onClick:X},{default:n(()=>e[32]||(e[32]=[v("\u641C\u7D22")])),_:1})]),_:1},8,["modelValue"])]),o(S,{data:u(me),height:"300","highlight-current-row":"",onCurrentChange:ke},{default:n(()=>[o(f,{prop:"DocNo",label:"\u8BA2\u5355\u53F7",width:"160"}),o(f,{prop:"currency",label:"\u5E01\u79CD",width:"110"}),o(f,{prop:"salesPrice",label:"\u8BA2\u5355\u603B\u91D1\u989D",width:"110"}),o(f,{prop:"shipPrice",label:"\u5DF2\u51FA\u8D27\u91D1\u989D",width:"110"}),o(f,{prop:"claimedAmount",label:"\u5DF2\u8BA4\u9886\u91D1\u989D",width:"120"}),o(f,{prop:"remainingAmount",label:"\u5269\u4F59\u8BA4\u6B3E\u4F59\u989D",width:"120"})]),_:1},8,["data"])]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-d3f7b468"]]);export{wa as default};
