import{d as F,p as j,y as B,j as r,r as D,o as y,h as V,w as o,k as i,u as a,v as g,x as H,m as L,S as R,B as z,I as A,H as E,L as G}from"./index-C8b06LRn.js";import{_ as J}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{a as K,b as M,d as N}from"./index-D7MUd3Bn.js";const O=F({__name:"Demo03CourseForm",emits:["success"],setup(P,{expose:w,emit:x}){const{t:c}=j(),v=B(),u=r(!1),p=r(""),t=r(!1),f=r(""),l=r({id:void 0,studentId:void 0,name:void 0,score:void 0}),h=D({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],score:[{required:!0,message:"\u5206\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r();w({open:async(s,e,m)=>{if(u.value=!0,p.value=c("action."+s),f.value=s,q(),l.value.studentId=m,e){t.value=!0;try{l.value=await K(e)}finally{t.value=!1}}}});const k=x,I=async()=>{await n.value.validate(),t.value=!0;try{const s=l.value;f.value==="create"?(await M(s),v.success(c("common.createSuccess"))):(await N(s),v.success(c("common.updateSuccess"))),u.value=!1,k("success")}finally{t.value=!1}},q=()=>{var s;l.value={id:void 0,studentId:void 0,name:void 0,score:void 0},(s=n.value)==null||s.resetFields()};return(s,e)=>{const m=R,_=z,C=A,b=E,S=J,U=G;return y(),V(S,{title:a(p),modelValue:a(u),"onUpdate:modelValue":e[3]||(e[3]=d=>L(u)?u.value=d:null)},{footer:o(()=>[i(b,{onClick:I,type:"primary",disabled:a(t)},{default:o(()=>e[4]||(e[4]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),i(b,{onClick:e[2]||(e[2]=d=>u.value=!1)},{default:o(()=>e[5]||(e[5]=[g("\u53D6 \u6D88")])),_:1})]),default:o(()=>[H((y(),V(C,{ref_key:"formRef",ref:n,model:a(l),rules:a(h),"label-width":"100px"},{default:o(()=>[i(_,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[i(m,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=d=>a(l).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),i(_,{label:"\u5206\u6570",prop:"score"},{default:o(()=>[i(m,{modelValue:a(l).score,"onUpdate:modelValue":e[1]||(e[1]=d=>a(l).score=d),placeholder:"\u8BF7\u8F93\u5165\u5206\u6570"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[U,a(t)]])]),_:1},8,["title","modelValue"])}}});export{O as _};
