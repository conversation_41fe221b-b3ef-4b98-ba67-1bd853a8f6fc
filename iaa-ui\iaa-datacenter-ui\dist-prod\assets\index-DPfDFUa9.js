import{d as P,y as j,p as q,j as _,r as J,f as Q,T as W,o as i,c as A,k as a,w as t,u as o,M as X,F as U,g as Z,N as $,D as C,h as d,v as p,x as g,S as ee,B as ae,z as le,A as te,C as oe,G as re,H as ie,I as ne,J as se,K as pe,L as ce}from"./index-C8b06LRn.js";import{_ as ue}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as de}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as fe}from"./index-CkzUfjB7.js";import{_ as _e,g as ge,d as ye,u as we,t as ke}from"./FileConfigForm.vue_vue_type_script_setup_true_lang-cHdoW5Fo.js";import{d as be}from"./formatTime-COZ9Bl52.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const ve=P({name:"InfraFileConfig",__name:"index",setup(he){const m=j(),{t:x}=q(),k=_(!0),N=_(0),S=_([]),r=J({pageNo:1,pageSize:10,name:void 0,storage:void 0,createTime:[]}),F=_(),c=async()=>{k.value=!0;try{const f=await ge(r);S.value=f.list,N.value=f.total}finally{k.value=!1}},b=()=>{r.pageNo=1,c()},D=()=>{F.value.resetFields(),b()},T=_(),V=(f,e)=>{T.value.open(f,e)};return Q(()=>{c()}),(f,e)=>{const E=fe,Y=ee,y=ae,z=le,G=te,L=oe,v=re,n=ie,O=ne,I=me,s=se,R=de,H=pe,M=ue,w=W("hasPermi"),B=ce;return i(),A(U,null,[a(E,{title:"\u4E0A\u4F20\u4E0B\u8F7D",url:"https://doc.iocoder.cn/file/"}),a(I,null,{default:t(()=>[a(O,{class:"-mb-15px",model:o(r),ref_key:"queryFormRef",ref:F,inline:!0,"label-width":"68px"},{default:t(()=>[a(y,{label:"\u914D\u7F6E\u540D",prop:"name"},{default:t(()=>[a(Y,{modelValue:o(r).name,"onUpdate:modelValue":e[0]||(e[0]=l=>o(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u914D\u7F6E\u540D",clearable:"",onKeyup:X(b,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(y,{label:"\u5B58\u50A8\u5668",prop:"storage"},{default:t(()=>[a(G,{modelValue:o(r).storage,"onUpdate:modelValue":e[1]||(e[1]=l=>o(r).storage=l),placeholder:"\u8BF7\u9009\u62E9\u5B58\u50A8\u5668",clearable:"",class:"!w-240px"},{default:t(()=>[(i(!0),A(U,null,Z(o($)(o(C).INFRA_FILE_STORAGE),l=>(i(),d(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(y,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(L,{modelValue:o(r).createTime,"onUpdate:modelValue":e[2]||(e[2]=l=>o(r).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(y,null,{default:t(()=>[a(n,{onClick:b},{default:t(()=>[a(v,{icon:"ep:search",class:"mr-5px"}),e[6]||(e[6]=p(" \u641C\u7D22"))]),_:1}),a(n,{onClick:D},{default:t(()=>[a(v,{icon:"ep:refresh",class:"mr-5px"}),e[7]||(e[7]=p(" \u91CD\u7F6E"))]),_:1}),g((i(),d(n,{type:"primary",plain:"",onClick:e[3]||(e[3]=l=>V("create"))},{default:t(()=>[a(v,{icon:"ep:plus",class:"mr-5px"}),e[8]||(e[8]=p(" \u65B0\u589E "))]),_:1})),[[w,["infra:file-config:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(I,null,{default:t(()=>[g((i(),d(H,{data:o(S)},{default:t(()=>[a(s,{label:"\u7F16\u53F7",align:"center",prop:"id"}),a(s,{label:"\u914D\u7F6E\u540D",align:"center",prop:"name"}),a(s,{label:"\u5B58\u50A8\u5668",align:"center",prop:"storage"},{default:t(l=>[a(R,{type:o(C).INFRA_FILE_STORAGE,value:l.row.storage},null,8,["type","value"])]),_:1}),a(s,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(s,{label:"\u4E3B\u914D\u7F6E",align:"center",prop:"primary"},{default:t(l=>[a(R,{type:o(C).INFRA_BOOLEAN_STRING,value:l.row.master},null,8,["type","value"])]),_:1}),a(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:o(be)},null,8,["formatter"]),a(s,{label:"\u64CD\u4F5C",align:"center",width:"240px"},{default:t(l=>[g((i(),d(n,{link:"",type:"primary",onClick:h=>V("update",l.row.id)},{default:t(()=>e[9]||(e[9]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["infra:file-config:update"]]]),g((i(),d(n,{link:"",type:"primary",disabled:l.row.master,onClick:h=>(async u=>{try{await m.confirm('\u662F\u5426\u786E\u8BA4\u4FEE\u6539\u914D\u7F6E\u7F16\u53F7\u4E3A"'+u+'"\u7684\u6570\u636E\u9879\u4E3A\u4E3B\u914D\u7F6E?'),await we(u),m.success(x("common.updateSuccess")),await c()}catch{}})(l.row.id)},{default:t(()=>e[10]||(e[10]=[p(" \u4E3B\u914D\u7F6E ")])),_:2},1032,["disabled","onClick"])),[[w,["infra:file-config:update"]]]),a(n,{link:"",type:"primary",onClick:h=>(async u=>{try{const K=await ke(u);await m.confirm("\u662F\u5426\u8981\u8BBF\u95EE\u8BE5\u6587\u4EF6\uFF1F","\u6D4B\u8BD5\u4E0A\u4F20\u6210\u529F"),window.open(K,"_blank")}catch{}})(l.row.id)},{default:t(()=>e[11]||(e[11]=[p(" \u6D4B\u8BD5 ")])),_:2},1032,["onClick"]),g((i(),d(n,{link:"",type:"danger",onClick:h=>(async u=>{try{await m.delConfirm(),await ye(u),m.success(x("common.delSuccess")),await c()}catch{}})(l.row.id)},{default:t(()=>e[12]||(e[12]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["infra:file-config:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,o(k)]]),a(M,{total:o(N),page:o(r).pageNo,"onUpdate:page":e[4]||(e[4]=l=>o(r).pageNo=l),limit:o(r).pageSize,"onUpdate:limit":e[5]||(e[5]=l=>o(r).pageSize=l),onPagination:c},null,8,["total","page","limit"])]),_:1}),a(_e,{ref_key:"formRef",ref:T,onSuccess:c},null,512)],64)}}});export{ve as default};
