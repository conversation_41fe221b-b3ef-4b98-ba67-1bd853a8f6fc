import{d as H,y as I,p as J,a as L,j as p,r as Q,au as W,f as X,aF as Y,T as Z,o as m,c as $,k as a,w as l,u as t,M as ee,v as u,x as c,h as f,D as ae,m as le,F as te,S as oe,B as re,G as ne,H as se,I as ie,J as pe,K as me,L as ue}from"./index-C8b06LRn.js";import{_ as de}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ye}from"./index-CkzUfjB7.js";import{d as be}from"./formatTime-COZ9Bl52.js";import{b as ge,d as ke,g as we}from"./index-CBsqkafF.js";import{b as ve}from"./formCreate-CdPDb26P.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";const Ce=H({name:"BpmForm",__name:"index",setup(he){const C=I(),{t:T}=J(),{currentRoute:q,push:z}=L(),b=p(!0),h=p(0),x=p([]),r=Q({pageNo:1,pageSize:10,name:null}),S=p(),d=async()=>{b.value=!0;try{const n=await ge(r);x.value=n.list,h.value=n.total}finally{b.value=!1}},g=()=>{r.pageNo=1,d()},B=()=>{S.value.resetFields(),g()},F=n=>{const e={name:"BpmFormEditor"};typeof n=="number"&&(e.query={id:n}),z(e)},_=p(!1),k=p({rule:[],option:{}});return W(()=>q.value,()=>{d()},{immediate:!0}),X(()=>{d()}),(n,e)=>{const P=ye,E=oe,V=re,w=ne,s=se,G=ie,N=_e,i=pe,K=fe,O=me,R=ce,j=Y("form-create"),A=de,y=Z("hasPermi"),D=ue;return m(),$(te,null,[a(P,{title:"\u5BA1\u6279\u63A5\u5165\uFF08\u6D41\u7A0B\u8868\u5355\uFF09",url:"https://doc.iocoder.cn/bpm/use-bpm-form/"}),a(N,null,{default:l(()=>[a(G,{ref_key:"queryFormRef",ref:S,inline:!0,model:t(r),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(V,{label:"\u8868\u5355\u540D",prop:"name"},{default:l(()=>[a(E,{modelValue:t(r).name,"onUpdate:modelValue":e[0]||(e[0]=o=>t(r).name=o),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D",onKeyup:ee(g,["enter"])},null,8,["modelValue"])]),_:1}),a(V,null,{default:l(()=>[a(s,{onClick:g},{default:l(()=>[a(w,{class:"mr-5px",icon:"ep:search"}),e[4]||(e[4]=u(" \u641C\u7D22 "))]),_:1}),a(s,{onClick:B},{default:l(()=>[a(w,{class:"mr-5px",icon:"ep:refresh"}),e[5]||(e[5]=u(" \u91CD\u7F6E "))]),_:1}),c((m(),f(s,{plain:"",type:"primary",onClick:F},{default:l(()=>[a(w,{class:"mr-5px",icon:"ep:plus"}),e[6]||(e[6]=u(" \u65B0\u589E "))]),_:1})),[[y,["bpm:form:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(N,null,{default:l(()=>[c((m(),f(O,{data:t(x)},{default:l(()=>[a(i,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(i,{align:"center",label:"\u8868\u5355\u540D",prop:"name"}),a(i,{align:"center",label:"\u72B6\u6001",prop:"status"},{default:l(o=>[a(K,{type:t(ae).COMMON_STATUS,value:o.row.status},null,8,["type","value"])]),_:1}),a(i,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(i,{formatter:t(be),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},null,8,["formatter"]),a(i,{align:"center",label:"\u64CD\u4F5C"},{default:l(o=>[c((m(),f(s,{link:"",type:"primary",onClick:U=>F(o.row.id)},{default:l(()=>e[7]||(e[7]=[u(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["bpm:form:update"]]]),c((m(),f(s,{link:"",onClick:U=>(async v=>{const M=await we(v);ve(k,M.conf,M.fields),_.value=!0})(o.row.id)},{default:l(()=>e[8]||(e[8]=[u(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])),[[y,["bpm:form:query"]]]),c((m(),f(s,{link:"",type:"danger",onClick:U=>(async v=>{try{await C.delConfirm(),await ke(v),C.success(T("common.delSuccess")),await d()}catch{}})(o.row.id)},{default:l(()=>e[9]||(e[9]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["bpm:form:delete"]]])]),_:1})]),_:1},8,["data"])),[[D,t(b)]]),a(R,{limit:t(r).pageSize,"onUpdate:limit":e[1]||(e[1]=o=>t(r).pageSize=o),page:t(r).pageNo,"onUpdate:page":e[2]||(e[2]=o=>t(r).pageNo=o),total:t(h),onPagination:d},null,8,["limit","page","total"])]),_:1}),a(A,{modelValue:t(_),"onUpdate:modelValue":e[3]||(e[3]=o=>le(_)?_.value=o:null),title:"\u8868\u5355\u8BE6\u60C5",width:"800"},{default:l(()=>[a(j,{option:t(k).option,rule:t(k).rule},null,8,["option","rule"])]),_:1},8,["modelValue"])],64)}}});export{Ce as default};
