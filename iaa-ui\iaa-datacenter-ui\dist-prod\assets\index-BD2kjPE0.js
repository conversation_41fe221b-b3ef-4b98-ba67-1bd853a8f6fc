import{d as e,e as s,Y as a,j as f,f as n,u as d,o,c as r,k as t}from"./index-C8b06LRn.js";import u from"./Mobile-FkAC0cV0.js";import{_ as l}from"./PC.vue_vue_type_script_setup_true_lang-Bg_ZHEfO.js";import"./index-BX2KAvdx.js";import"./claim-BPjhJe4B.js";import"./ClaimMobile-DyyspMXP.js";import"./VDatePicker.vue_vue_type_script_setup_true_lang-fYMpMkPC.js";import"./index-DVzg-3-A.js";import"./function-call-DKtMTaHE.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DatabaseUploadForm.vue_vue_type_script_setup_true_lang-C15tI4yC.js";import"./download-D5Lb_h0f.js";import"./PermissionForm.vue_vue_type_script_setup_true_lang-BbCkMcuR.js";import"./index-rrqDtE6o.js";import"./Filter-Dzz2caxb.js";import"./ClaimDialogPC-DR2FIPAd.js";const _={key:0},c={key:1},k=e({__name:"index",setup(j){const i=s(),m=a(()=>i.getMobile),p=f();return n(()=>{}),(v,y)=>d(m)?(o(),r("div",c,[t(u)])):(o(),r("div",_,[t(l,{ref_key:"pcRef",ref:p},null,512)]))}});export{k as default};
