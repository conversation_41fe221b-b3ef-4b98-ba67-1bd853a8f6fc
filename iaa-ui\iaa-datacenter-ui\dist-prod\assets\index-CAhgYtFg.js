import{aG as a}from"./index-C8b06LRn.js";const t=e=>a.get({url:"/infra/codegen/table/list?dataSourceConfigId="+e}),d=e=>a.get({url:"/infra/codegen/table/page",params:e}),r=e=>a.get({url:"/infra/codegen/detail?tableId="+e}),l=e=>a.put({url:"/infra/codegen/update",data:e}),n=e=>a.put({url:"/infra/codegen/sync-from-db?tableId="+e}),o=e=>a.get({url:"/infra/codegen/preview?tableId="+e}),g=e=>a.download({url:"/infra/codegen/download?tableId="+e}),s=e=>a.get({url:"/infra/codegen/db/table/list",params:e}),i=e=>a.post({url:"/infra/codegen/create-list",data:e}),c=e=>a.delete({url:"/infra/codegen/delete?tableId="+e});export{t as a,s as b,i as c,d,c as e,g as f,r as g,o as p,n as s,l as u};
