import{d as Z,r as G,dl as x,j as S,y as J,f as Q,bj as I,aF as O,o as f,c,k as l,w as r,u as s,F as U,g as N,N as P,h as Y,l as B,v as C,t as v,dk as ee,$ as te,aO as q,dj as ae,aD as le,aq as oe,C as ie,B as se,I as ne,a8 as re,ab as de,aa as fe,z as me,A as ue,ax as we,_ as pe}from"./index-C8b06LRn.js";import{F as H}from"./index-6IF2xVYO.js";import{f as z,a as V}from"./dateUtil-D9m5ek6U.js";import{c as K}from"./permission-DVzqLl93.js";import{w as ce}from"./echarts-cfVEL83D.js";const ve={class:"h-[calc(100vh-180px)] overflow-auto"},he=["id"],ye={class:"h-[calc(100%-200px-60px)]"},be={key:1},ge={key:1},xe={key:0,class:"bg-red text-white"},_e={key:1},ke={key:0,class:"bg-red text-white"},De={key:1},Pe=pe(Z({__name:"TimelyDelivery",setup(Ye){const b=G({date:x().format("YYYY-MM-DD"),dateType:"month"}),T=S(!1),M=S([]),L=J(),g=S(new Map),E=async()=>{T.value=!0;try{const t=await H.getFlowList(b);M.value=t,await q(),$()}catch{L.error("\u83B7\u53D6\u6D41\u7A0B\u6570\u636E\u5931\u8D25")}finally{T.value=!1}},R=()=>{const t={};return P("information_person").forEach(a=>{t[a.value]={}}),M.value.forEach(a=>{if(!a.flowPerson||!Array.isArray(a.flowPerson))return;const w=W(a.flowCreateTime,a.flowPlanDate);a.flowPerson.forEach(p=>{const n=p.toString();t[n]&&w.forEach(i=>{switch(t[n][i]||(t[n][i]={normal:0,delayed:0,unfinished:0}),((_,k,D,o)=>{const h=x(_);if(o!==we(h).startOf("month").format("YYYY-MM"))return"\u6B63\u5E38";const y=x(D||k);return h.isValid()&&y.isValid()?y.diff(h,"days")<=2?"\u6B63\u5E38":"\u5EF6\u671F":"\u672A\u7ED3\u675F"})(a.flowPlanDate,a.flowPublishTime,a.developmentDate,i)){case"\u6B63\u5E38":t[n][i].normal++;break;case"\u5EF6\u671F":t[n][i].delayed++;break;case"\u672A\u7ED3\u675F":t[n][i].unfinished++}})})}),t},W=(t,a)=>{const w=x(V(t)).startOf("month"),p=x(V(a)).startOf("month"),n=[];let i=w.clone();for(;i<=p;)n.push(i.format("YYYY-MM")),i.add(1,"months");return n},$=()=>{const t=R();P("information_person").forEach(a=>{X(a.value.toString(),a.label,t[a.value]||{})})},X=(t,a,w)=>{const p=`person-${t}-line`,n=document.getElementById(p);if(!n)return;g.value.has(p)&&g.value.get(p).dispose();const i=Object.keys(w).sort(),_=i.map(m=>{var d;return((d=w[m])==null?void 0:d.normal)||0}),k=i.map(m=>{var d;return((d=w[m])==null?void 0:d.delayed)||0}),D=i.map(m=>{var d;return((d=w[m])==null?void 0:d.unfinished)||0});if(i.length===0)return void(n.innerHTML=`
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${a}</div>
          <div class="text-sm mt-2">\u6682\u65E0\u6570\u636E</div>
        </div>
      </div>
    `);const o=ae(ce(n)),h={title:{text:a,left:"center",textStyle:{fontSize:14,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:m=>{let d=`${m[0].axisValue}<br/>`;return m.forEach(e=>{d+=`${e.marker}${e.seriesName}: ${e.value}<br/>`}),d}},legend:{data:["\u6B63\u5E38","\u5EF6\u671F","\u672A\u7ED3\u675F"],bottom:0,textStyle:{fontSize:10}},grid:{left:"3%",right:"4%",bottom:"15%",top:"20%",containLabel:!0},xAxis:{type:"category",data:i.map(m=>x(m).format("MM\u6708")),axisLabel:{fontSize:10,rotate:45}},yAxis:{type:"value",axisLabel:{fontSize:10}},series:[{name:"\u6B63\u5E38",type:"bar",stack:"total",data:_,itemStyle:{color:"#67C23A"}},{name:"\u5EF6\u671F",type:"bar",stack:"total",data:k,itemStyle:{color:"#F56C6C"}},{name:"\u672A\u7ED3\u675F",type:"bar",stack:"total",data:D,itemStyle:{color:"#E6A23C"}}]};o.setOption(h),g.value.set(p,o);const y=()=>{o.resize()};window.addEventListener("resize",y),I(()=>{window.removeEventListener("resize",y),o.dispose()})},j=async t=>{await H.saveFlowPerson({flowId:t.flowId,flowPerson:t.flowPerson,developmentDate:t.developmentDate}),L.success("\u66F4\u65B0\u6210\u529F"),await q(),$()},A=()=>{g.value.forEach(t=>{t.resize()})};return Q(()=>{E(),window.addEventListener("resize",A)}),I(()=>{window.removeEventListener("resize",A),g.value.forEach(t=>{t.dispose()}),g.value.clear()}),(t,a)=>{const w=le,p=oe,n=ie,i=se,_=ne,k=re,D=de,o=O("vxe-column"),h=fe,y=me,m=ue,d=O("vxe-table");return f(),c("div",ve,[l(_,{inline:"",class:"custom-form",size:"small"},{default:r(()=>[l(i,{label:"\u65E5\u671F"},{default:r(()=>[l(p,{modelValue:s(b).dateType,"onUpdate:modelValue":a[0]||(a[0]=e=>s(b).dateType=e),onChange:E},{default:r(()=>[l(w,{label:"\u5E74",value:"year"}),l(w,{label:"\u6708",value:"month"})]),_:1},8,["modelValue"]),l(n,{class:"!w-100px",type:s(b).dateType,modelValue:s(b).date,"onUpdate:modelValue":a[1]||(a[1]=e=>s(b).date=e),"value-format":"YYYY-MM-DD",clearable:!1,onChange:E},null,8,["type","modelValue"])]),_:1})]),_:1}),l(D,null,{default:r(()=>[(f(!0),c(U,null,N(s(P)("information_person"),e=>(f(),Y(k,{key:e.value,span:6,xs:24,sm:24,md:12,lg:6,xl:6},{default:r(()=>[B("div",{id:`person-${e.value}-line`,class:"h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"},null,8,he)]),_:2},1024))),128))]),_:1}),B("div",ye,[l(d,{height:"100%",align:"center",border:"",loading:s(T),data:s(M),"show-overflow":"",stripe:""},{default:r(()=>[l(o,{title:"\u6D41\u7A0B\u6A21\u677F",field:"flowTemplate",width:"200"}),l(o,{title:"\u6D41\u7A0B\u7F16\u7801",field:"flowNumber",width:"100"}),l(o,{title:"\u6D41\u7A0B\u540D\u79F0",field:"flowName","min-width":"300",align:"left"},{default:r(({row:e})=>[l(h,{type:"primary",onClick:u=>{return F=e.flowId,void window.open("http://oa.iaa360.cn:8686/km/review/km_review_main/kmReviewMain.do?method=view&fdId="+F,"_blank");var F}},{default:r(()=>[C(v(e.flowName),1)]),_:2},1032,["onClick"])]),_:1}),l(o,{title:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",field:"flowCreator",width:"100"}),l(o,{title:"\u6D41\u7A0B\u53D1\u8D77\u65F6\u95F4",field:"flowCreateTime",width:"150"},{default:r(({row:e})=>[C(v(s(z)(e.flowCreateTime)),1)]),_:1}),l(o,{title:"\u6D41\u7A0B\u671F\u671B\u5B8C\u6210\u65F6\u95F4",field:"flowPlanDate",width:"150"},{default:r(({row:e})=>[C(v(s(V)(e.flowPlanDate)),1)]),_:1}),l(o,{title:"\u8D1F\u8D23\u4EBA",field:"flowPerson",width:"150"},{default:r(({row:e})=>[s(K)(["report:ekp-flow:save-person"])?(f(),Y(m,{key:0,modelValue:e.flowPerson,"onUpdate:modelValue":u=>e.flowPerson=u,multiple:"",onChange:u=>j(e),"collapse-tags":""},{default:r(()=>[(f(!0),c(U,null,N(s(P)("information_person"),u=>(f(),Y(y,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(f(),c("span",be,v(e.flowPerson.map(u=>s(ee)("information_person",u)).join(",")),1))]),_:1}),l(o,{title:"\u6D41\u7A0B\u7ED3\u675F\u65F6\u95F4",field:"flowPublishTime",width:"150"},{default:r(({row:e})=>[C(v(e.flowPublishTime?s(z)(e.flowPublishTime):""),1)]),_:1}),l(o,{title:"\u5B9E\u9645\u4FE1\u606F\u90E8\u5904\u7406\u5B8C\u6210\u65F6\u95F4",field:"developmentDate",width:"180"},{default:r(({row:e})=>[s(K)(["report:ekp-flow:save-person"])?(f(),Y(n,{key:0,modelValue:e.developmentDate,"onUpdate:modelValue":u=>e.developmentDate=u,type:"date","value-format":"YYYY-MM-DD",onChange:u=>j(e),class:"!w-full"},null,8,["modelValue","onUpdate:modelValue","onChange"])):e.developmentDate||e.flowPublishTime?(f(),c("span",ge,[e.developmentDate?(f(),c("span",xe,v(s(V)(e.developmentDate)),1)):(f(),c("span",_e,v(s(z)(e.flowPublishTime)),1))])):te("",!0)]),_:1}),l(o,{title:"\u6D41\u7A0B\u7ED3\u675F\u65F6\u95F4\u8D85\u671F\u539F\u56E0",field:"reason",width:"200"},{default:r(({row:e})=>[e.developmentDate?(f(),c("span",ke,v(e.reason),1)):(f(),c("span",De,"\u65E0\u5F02\u5E38"))]),_:1}),l(o,{title:"\u6D41\u7A0B\u72B6\u6001",field:"flowStatus",width:"150"}),l(o,{title:"\u53CA\u65F6\u6027\u8BC4\u5206",field:"timeliness",width:"100"}),l(o,{title:"\u670D\u52A1\u6001\u5EA6\u8BC4\u5206",field:"attitude",width:"100"}),l(o,{title:"\u7CFB\u7EDF\u7A33\u5B9A\u6027\u8BC4\u5206",field:"stability",width:"120"}),l(o,{title:"\u6574\u4F53\u8BC4\u5206",field:"whole",width:"100"})]),_:1},8,["loading","data"])])])}}}),[["__scopeId","data-v-1e995f9e"]]);export{Pe as default};
