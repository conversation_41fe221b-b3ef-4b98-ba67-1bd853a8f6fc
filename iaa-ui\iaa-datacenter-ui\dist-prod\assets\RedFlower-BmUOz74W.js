import{aG as L,d as B,r as F,dl as T,j as Y,f as I,bj as C,aF as V,o as M,c as k,k as a,w as o,u as c,F as O,g as R,N as $,h as A,l as D,v as g,t as y,aO as N,dj as q,aD as G,aq as H,C as P,B as W,I as J,a8 as K,ab as Q,aM as X,_ as Z}from"./index-C8b06LRn.js";import{f as ee}from"./dateUtil-D9m5ek6U.js";import{w as te,av as h}from"./echarts-cfVEL83D.js";const ae=E=>L.get({url:"/butt-joint/red/flower/list",params:E}),le={class:"h-[calc(100vh-180px)] overflow-auto"},oe=["id"],se={class:"h-[calc(100%-200px-60px)]"},re=Z(B({__name:"RedFlower",setup(E){const p=F({dateType:"year",sTime:T().format("YYYY-MM-DD")}),x=Y(!1),_=Y([]),v=Y(new Map),S=async()=>{x.value=!0;try{const r=await ae(p);_.value=r,await N(),U()}finally{x.value=!1}},U=()=>{const r=(()=>{const t={},d=$("information_person");return d.forEach(l=>{t[l.value]={}}),_.value.forEach(l=>{if(!l.endUser)return;const n=T(l.dateTime).format("YYYY-MM"),f=d.find(w=>w.label===l.endUser);if(!f)return;const u=f.value.toString();t[u]&&(t[u][n]||(t[u][n]={red:0,yellow:0}),l.type==="\u5C0F\u7EA2\u82B1"?t[u][n].red+=1:l.type==="\u5C0F\u9EC4\u82B1"&&(t[u][n].yellow+=1))}),t})();$("information_person").forEach(t=>{j(t.value.toString(),t.label,r[t.value]||{})})},j=(r,t,d)=>{const l=`person-${r}-line`,n=document.getElementById(l);if(!n)return;v.value.has(l)&&v.value.get(l).dispose();const f=Object.keys(d).sort(),u=f.map(s=>{var i;return((i=d[s])==null?void 0:i.red)||0}),w=f.map(s=>{var i;return((i=d[s])==null?void 0:i.yellow)||0});if(f.length===0)return void(n.innerHTML=`
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${t}</div>
          <div class="text-sm mt-2">\u6682\u65E0\u7EA2\u9EC4\u82B1\u6570\u636E</div>
        </div>
      </div>
    `);const b=q(te(n)),m={title:{text:t,left:"center",textStyle:{fontSize:14,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:s=>{let i=`${s[0].axisValue}<br/>`;return s.forEach(e=>{i+=`${e.marker}${e.seriesName}: ${e.value}\u6735<br/>`}),i}},legend:{data:["\u5C0F\u7EA2\u82B1","\u5C0F\u9EC4\u82B1"],bottom:0,textStyle:{fontSize:10}},grid:{left:"3%",right:"4%",bottom:"15%",top:"20%",containLabel:!0},xAxis:{type:"category",data:f.map(s=>T(s).format("MM\u6708")),axisLabel:{fontSize:10,rotate:45}},yAxis:{type:"value",name:"\u6570\u91CF(\u6735)",nameTextStyle:{fontSize:10},axisLabel:{fontSize:10}},series:[{name:"\u5C0F\u7EA2\u82B1",type:"bar",data:u,itemStyle:{color:new h(0,0,0,1,[{offset:0,color:"#ff6b6b"},{offset:1,color:"#ee5a52"}])},emphasis:{itemStyle:{color:new h(0,0,0,1,[{offset:0,color:"#ff5252"},{offset:1,color:"#d32f2f"}])}}},{name:"\u5C0F\u9EC4\u82B1",type:"bar",data:w,itemStyle:{color:new h(0,0,0,1,[{offset:0,color:"#ffd93d"},{offset:1,color:"#f57f17"}])},emphasis:{itemStyle:{color:new h(0,0,0,1,[{offset:0,color:"#ffcc02"},{offset:1,color:"#ff8f00"}])}}}]};b.setOption(m),v.value.set(l,b)},z=()=>{v.value.forEach(r=>{r.resize()})};return I(()=>{S(),window.addEventListener("resize",z)}),C(()=>{window.removeEventListener("resize",z),v.value.forEach(r=>{r.dispose()}),v.value.clear()}),(r,t)=>{const d=G,l=H,n=P,f=W,u=J,w=K,b=Q,m=V("vxe-column"),s=X,i=V("vxe-table");return M(),k("div",le,[a(u,{inline:"",class:"custom-form",size:"small"},{default:o(()=>[a(f,{label:"\u65E5\u671F"},{default:o(()=>[a(l,{modelValue:c(p).dateType,"onUpdate:modelValue":t[0]||(t[0]=e=>c(p).dateType=e),onChange:S},{default:o(()=>[a(d,{label:"\u5E74",value:"year"}),a(d,{label:"\u6708",value:"month"})]),_:1},8,["modelValue"]),a(n,{class:"!w-100px",type:c(p).dateType,modelValue:c(p).sTime,"onUpdate:modelValue":t[1]||(t[1]=e=>c(p).sTime=e),"value-format":"YYYY-MM-DD",clearable:!1,onChange:S},null,8,["type","modelValue"])]),_:1})]),_:1}),a(b,null,{default:o(()=>[(M(!0),k(O,null,R(c($)("information_person"),e=>(M(),A(w,{key:e.value,span:6,xs:24,sm:24,md:12,lg:6,xl:6},{default:o(()=>[D("div",{id:`person-${e.value}-line`,class:"h-200px border border-gray-200 rounded-lg mb-4 bg-white shadow-sm"},null,8,oe)]),_:2},1024))),128))]),_:1}),D("div",se,[a(i,{height:"100%",align:"center",border:"",loading:c(x),data:c(_),"show-overflow":"",stripe:""},{default:o(()=>[a(m,{title:"\u65E5\u671F",field:"dateTime"},{default:o(({row:e})=>[g(y(c(ee)(e.dateTime)),1)]),_:1}),a(m,{title:"\u6807\u9898",field:"type"},{default:o(({row:e})=>[g(" \u3010"+y(`${e.endDept}-${e.endUser}`)+"\u3011\u6536\u5230\u4E86\u6765\u81EA\u3010"+y(`${e.startDept}-${e.startUser}`)+"\u3011\u7684 ",1),a(s,{type:e.type==="\u5C0F\u7EA2\u82B1"?"danger":"warning"},{default:o(()=>[g(y(e.type),1)]),_:2},1032,["type"])]),_:1}),a(m,{title:"\u4E8B\u4EF6",field:"title"}),a(m,{title:"\u5185\u5BB9",field:"content"}),a(m,{title:"\u603B\u8BA1\u7EA2\u82B1",field:"countRed"},{default:o(({row:e})=>[a(s,{type:"danger"},{default:o(()=>[g(y(e.countRed),1)]),_:2},1024)]),_:1}),a(m,{title:"\u603B\u8BA1\u9EC4\u82B1",field:"countBlack"},{default:o(({row:e})=>[a(s,{type:"warning"},{default:o(()=>[g(y(e.countBlack),1)]),_:2},1024)]),_:1})]),_:1},8,["loading","data"])])])}}}),[["__scopeId","data-v-099bf2c0"]]);export{re as default};
