import{_ as N}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{aG as g,d as S,r as T,y as W,j as p,f as I,aF as x,o as u,c as q,l as D,k as o,w as f,u as e,h as v,v as m,t as B,aM as E,_ as F}from"./index-C8b06LRn.js";import{t as z,d as K}from"./Filter-Dzz2caxb.js";import{f as M}from"./formatTime-COZ9Bl52.js";const L={getQueryPartKind:()=>g.post({url:"/development/drawings/queryPartKind"}),getQueryDocumentByItemCode:t=>g.post({url:"/development/drawings/queryDocumentByItemCode",data:t}),getPage:t=>g.post({url:"/development/drawings/page",data:t}),getDocPathOrWx:t=>g.post({url:"/butt-joint/plm/attribute/getDocPathOrWx",data:t}),destoryOrWx:t=>g.post({url:"/butt-joint/plm/attribute/destoryOrWx",data:t}),addDrawingLog:t=>g.post({url:"/development/drawings/addDrawingLog",data:t}),getDrawingLogPage:t=>g.post({url:"/development/drawings/pageLog",data:t})},Q={class:"center-table"},U={class:"h-[calc(100vh-380px)]"},G={class:"h-[calc(100%-5px)]"},P=F(S({__name:"DrawingsLog",setup(t){const d=T({pageNo:1,pageSize:30});W();const y=p(!0),w=p([]),b=p(0),O=p([{data:""}]),j=p([{data:""}]),k=p([{data:[]}]),_=p([{label:"\u9884\u89C8",value:0},{label:"\u4E0B\u8F7D",value:1}]),h=async()=>{try{y.value=!0;const s=await L.getDrawingLogPage(d);w.value=s.list,b.value=s.total}catch{}finally{y.value=!1}},C=s=>{const a=["type"],i={};s.filterList.forEach(l=>{const{field:n,values:c,datas:r}=l;a.includes(n)&&c.length>0?i[n]=c:r.length>0&&(i[n]=r[0])}),Object.keys(d).forEach(l=>{["pageNo","pageSize"].includes(l)||s.filterList.some(n=>n.field===l)||(d[l]=void 0)}),Object.assign(d,i),h()};return I(()=>{h()}),(s,a)=>{const i=x("vxe-column"),l=E,n=x("vxe-table"),c=N;return u(),q("div",Q,[D("div",U,[D("div",G,[o(n,{ref:"tableRef",data:e(w),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:e(y),"row-config":{isCurrent:!0,height:25},"filter-config":{remote:!0},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},onFilterChange:C,tabindex:"0",size:"mini","cell-class-name":"cursor-pointer"},{default:f(()=>[o(i,{field:"itemCode",title:"\u7269\u6599\u7F16\u7801",width:"150",filters:e(O),"filter-render":z},null,8,["filters","filter-render"]),o(i,{field:"itemVersion",title:"\u7248\u672C",width:"80"}),o(i,{field:"wordName",title:"\u6587\u6863\u540D\u79F0","min-width":"200",filters:e(j),"filter-render":z},null,8,["filters","filter-render"]),o(i,{field:"wordCode",title:"\u6587\u6863\u7F16\u7801",width:"180"}),o(i,{field:"type",title:"\u64CD\u4F5C\u7C7B\u578B",width:"80",filters:e(_),"edit-render":{name:"$select",options:e(_),props:{value:"value",label:"label"}}},{default:f(({row:r})=>[r.type===0?(u(),v(l,{key:0,type:"primary"},{default:f(()=>a[2]||(a[2]=[m("\u9884\u89C8")])),_:1})):r.type===1?(u(),v(l,{key:1,type:"success"},{default:f(()=>a[3]||(a[3]=[m("\u4E0B\u8F7D")])),_:1})):(u(),v(l,{key:2,type:"danger"},{default:f(()=>a[4]||(a[4]=[m("\u672A\u77E5")])),_:1}))]),_:1},8,["filters","edit-render"]),o(i,{field:"operationName",title:"\u64CD\u4F5C\u4EBA",width:"80"}),o(i,{field:"createTime",width:"200",title:"\u64CD\u4F5C\u65F6\u95F4",filters:e(k),"filter-render":K},{default:f(({row:r})=>[m(B(e(M)(r.createTime)),1)]),_:1},8,["filters","filter-render"])]),_:1},8,["data","loading"])]),o(c,{total:e(b),page:e(d).pageNo,"onUpdate:page":a[0]||(a[0]=r=>e(d).pageNo=r),limit:e(d).pageSize,"onUpdate:limit":a[1]||(a[1]=r=>e(d).pageSize=r),onPagination:h,size:"small"},null,8,["total","page","limit"])])])}}}),[["__scopeId","data-v-eac68d20"]]),R=Object.freeze(Object.defineProperty({__proto__:null,default:P},Symbol.toStringTag,{value:"Module"}));export{P as D,L as a,R as b};
