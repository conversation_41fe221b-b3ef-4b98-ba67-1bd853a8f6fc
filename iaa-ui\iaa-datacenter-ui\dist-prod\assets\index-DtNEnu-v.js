import{_ as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as he}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as we,j as a,y as be,r as ke,f as xe,aF as V,T as _e,o as n,h as p,w as d,k as s,x as Q,c as f,l as N,u as t,F as Ce,g as Te,v as m,t as w,$ as W,m as I,N as Se,D as Ne,z as ze,A as Ae,H as De,aC as Le,aM as Me,_ as Ue}from"./index-C8b06LRn.js";import{C as b}from"./index-DVzg-3-A.js";import{d as Ve,t as k,n as X}from"./Filter-Dzz2caxb.js";import{I as Z}from"./index-BX2KAvdx.js";import{R as Ie}from"./index-rrqDtE6o.js";import Pe from"./replacementDialogPC-BtQg5Du4.js";import Fe from"./ClaimDialogPC-DR2FIPAd.js";import{d as Re}from"./download-D5Lb_h0f.js";import{g as $e}from"./index-584CLaw9.js";import{C as je}from"./claim-BPjhJe4B.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";const Ee={key:0,class:"ml-20px inline-flex items-center"},Oe={class:"text-14px font-bold"},He={class:"h-[calc(100vh-180px)]"},Ye={class:"h-[calc(100%-50px)]"},qe={key:0},Be={key:1},Ge={key:2},Je={key:3},Ke={key:4},Qe={key:0},We={key:1},Xe={key:2},Ze=we({__name:"index",setup(ll){const ee=a([{data:""}]),le=a([{data:""}]),te=a([{data:""}]),ae=a([{data:[]}]),ie=a([{data:{condition:"10",value:void 0}}]),se=a([{data:{condition:"10",value:void 0}}]),ne=a([{data:""}]),re=a([{data:""}]),z=a([]),A=a([]),P=a([{label:"ScentaChina",value:"ScentaChina"},{label:"ScentMachine",value:"ScentMachine"},{label:"ScentMarketing",value:"ScentMarketing"}]),F=a([{label:"\u6536\u6B3E\u4FE1\u606F",value:0},{label:"\u8BA2\u5355\u6536\u6B3E",value:1},{label:"\u8D39\u7528\u6536\u6B3E",value:2},{label:"\u8BA2\u5355\u672A\u4E0B",value:3}]),D=be(),L=a(!0),R=a([]),$=a(0),j=a([]),M=a(!1),U=a(0),y=a(!1),u=ke({salesmanNameList:[],pageNo:1,pageSize:30}),oe=a(),de=a(),ce=i=>{const e=["currencyCode","collectionAccount","collectionType"],r={};i.filterList.forEach(o=>{const{field:g,values:C,datas:T}=o;e.includes(g)&&C.length>0?r[g]=C:T.length>0&&(r[g]=T[0])}),Object.keys(u).forEach(o=>{["pageNo","pageSize"].includes(o)||i.filterList.some(g=>g.field===o)||(u[o]=void 0)}),Object.assign(u,r),v()},ue=({row:i})=>i.collectionType===3?"cursor-pointer":"",x=a(!1),E=a(0),O=a(),H=a(),Y=a(null),_=a(!1),q=a([]),B=a(null),G=a(!1),fe=async({row:i})=>{try{if(i.status!==2&&i.collectionType===3){const e=await Z.getSuspended(i.id),r=e.detailList.filter(o=>o.type=="3");r.length>0&&(E.value=r[0].amount||0,O.value=i.id,H.value=i.customerCode,Y.value=e,x.value=!0)}else if(i.status===2&&i.collectionType===3){let e=[];e=[i.id];const r=await Z.getSuspended(e[0]);q.value=[e[0]],B.value=r||null,G.value=!1,_.value=!0}}catch{v()}},pe=i=>{const e=Se(Ne.FINANCIAL_COSTS_TYPE).find(r=>r.value==i);return e?e.label:i},v=async()=>{L.value=!0;try{const i=await b.getCollectionDetailsPage(u);R.value=i.list,$.value=i.total;const e=await b.getRateAndTotal(u);U.value=e}finally{L.value=!1}},J=async()=>{const i=await b.getDate();y.value=i};return xe(async()=>{v(),(async()=>j.value=await Ie.getAllSellerList())(),J();const i=await $e();if(i){const r=i.map(o=>({value:o.accountName,label:o.accountName}));z.value=r}const e=await je.getCurrency();if(e){const r=e.map(o=>({value:o.Code,label:o.Name}));A.value=r}}),(i,e)=>{const r=ze,o=Ae,g=De,C=Le,T=V("vxe-toolbar"),c=V("vxe-column"),h=Me,me=V("vxe-table"),ye=he,ve=ge,K=_e("hasPermi");return n(),p(ve,null,{default:d(()=>[s(T,{custom:"",ref_key:"toolbarRef",ref:de,size:"mini"},{buttons:d(()=>[Q((n(),f("div",null,[e[8]||(e[8]=N("span",{class:"mr-10px"},"\u8BF7\u9009\u62E9\u4E1A\u52A1\u5458\uFF1A",-1)),s(o,{modelValue:t(u).salesmanNameList,"onUpdate:modelValue":e[0]||(e[0]=l=>t(u).salesmanNameList=l),multiple:"",filterable:"",size:"small",style:{width:"500px"},onChange:v},{default:d(()=>[(n(!0),f(Ce,null,Te(t(j),(l,S)=>(n(),p(r,{key:S,value:l,label:l},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])])),[[K,["collection:details:financial"]]]),s(g,{type:"info",plain:"",class:"ml-10px",size:"small",onClick:e[1]||(e[1]=l=>(async()=>{try{await D.exportConfirm(),M.value=!0;const S=await b.exportCollectionDetail(u);Re.excel(S,"\u6536\u6B3E\u660E\u7EC6\u5217\u8868.xlsx")}catch{}finally{M.value=!1}})()),title:"\u5BFC\u51FA",loading:t(M)},{default:d(()=>e[9]||(e[9]=[m(" \u5BFC\u51FA ")])),_:1},8,["loading"]),t(U)>0?(n(),f("div",Ee,[N("span",Oe,"\u4EBA\u6C11\u5E01\u603B\u91D1\u989D\uFF1A"+w(t(U))+" \u5143",1)])):W("",!0),Q((n(),f("span",null,[s(C,{modelValue:t(y),"onUpdate:modelValue":e[2]||(e[2]=l=>I(y)?y.value=l:null),"active-text":"\u53EF\u4FEE\u6539\u65E5\u671F","inactive-text":"\u4E0D\u53EF\u4FEE\u6539\u65E5\u671F",class:"ml-20px",onChange:e[3]||(e[3]=l=>(async()=>{try{await b.updateDate(y.value?0:1),J(),D.success("\u64CD\u4F5C\u6210\u529F")}catch{y.value=!y.value,D.error("\u64CD\u4F5C\u5931\u8D25")}})())},null,8,["modelValue"])])),[[K,["collection:details:updateDate"]]])]),_:1},512),N("div",He,[N("div",Ye,[s(me,{"row-config":{height:25},ref_key:"tableRef",ref:oe,data:t(R),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:t(L),"checkbox-config":{reserve:!0,highlight:!1,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},onFilterChange:ce,onCellClick:fe,tabindex:"0",size:"mini","cell-class-name":ue},{default:d(()=>[s(c,{field:"claimDate",title:"\u6536\u6B3E\u65E5\u671F","min-width":"60",filters:t(ae),"filter-render":Ve},null,8,["filters","filter-render"]),s(c,{field:"salesmanName",title:"\u4E1A\u52A1\u5458",width:"120",filters:t(ee),"filter-render":k},null,8,["filters","filter-render"]),s(c,{field:"customerCode",title:"\u5BA2\u6237\u7F16\u7801","min-width":"100",filters:t(le),"filter-render":k},null,8,["filters","filter-render"]),s(c,{field:"customerName",title:"\u5BA2\u6237\u540D\u79F0","min-width":"100",filters:t(te),"filter-render":k},null,8,["filters","filter-render"]),s(c,{field:"collectionAccount",title:"\u6536\u6B3E\u8D26\u53F7","min-width":"150",filters:t(z),"edit-render":{name:"$select",options:t(z),props:{value:"value",label:"label"}}},null,8,["filters","edit-render"]),s(c,{field:"currencyCode",title:"\u5E01\u79CD",width:"120",filters:t(A),"edit-render":{name:"$select",options:t(A),props:{value:"value",label:"label"}}},{default:d(({row:l})=>[m(w(l.currency),1)]),_:1},8,["filters","edit-render"]),s(c,{field:"collectionType",title:"\u6536\u6B3E\u7C7B\u522B",width:"120",filters:t(F),"edit-render":{name:"$select",options:t(F),props:{value:"value",label:"label"}}},{default:d(({row:l})=>[l.collectionType===1?(n(),f("span",qe,"\u8BA2\u5355\u6536\u6B3E")):l.collectionType===2?(n(),f("span",Be,"\u8D39\u7528\u6536\u6B3E")):l.collectionType===3?(n(),f("span",Ge,"\u8BA2\u5355\u672A\u4E0B")):l.collectionType===0?(n(),f("span",Je,"\u6536\u6B3E\u4FE1\u606F")):(n(),f("span",Ke,w(l.collectionType),1))]),_:1},8,["filters","edit-render"]),s(c,{field:"orderType",title:"\u8BA2\u5355\u53F7/\u8D39\u7528\u7C7B\u522B",width:"120",filters:t(re),"filter-render":k},{default:d(({row:l})=>[l.collectionType===2?(n(),f("span",Qe,w(pe(l.orderType)),1)):l.collectionType===1?(n(),f("span",We,w(l.orderType),1)):(n(),f("span",Xe,w(l.orderType),1))]),_:1},8,["filters","filter-render"]),s(c,{field:"rate",title:"\u6C47\u7387",width:"50"}),s(c,{field:"itemAmount",title:"\u5355\u9879\u91D1\u989D",width:"120",filters:t(se),"filter-render":X},null,8,["filters","filter-render"]),s(c,{field:"totalAmount",title:"\u603B\u91D1\u989D",width:"120",filters:t(ie),"filter-render":X},null,8,["filters","filter-render"]),s(c,{field:"remark",title:"\u5907\u6CE8",width:"120",filters:t(ne),"filter-render":k},null,8,["filters","filter-render"]),s(c,{field:"creditInsurance",title:"\u8D26\u53F7\u8BF4\u660E",width:"120",filters:t(P),"edit-render":{name:"$select",options:t(P),props:{value:"value",label:"label"}}},null,8,["filters","edit-render"]),s(c,{field:"status",title:"\u72B6\u6001",width:"120"},{default:d(({row:l})=>[l.status===2?(n(),p(h,{key:0,class:"status-danger"},{default:d(()=>e[10]||(e[10]=[m("\u6682\u5B58")])),_:1})):l.collectionType===3?(n(),p(h,{key:1,class:"status-danger"},{default:d(()=>e[11]||(e[11]=[m("\u4E1A\u52A1\u5F85\u786E\u8BA4")])),_:1})):l.status!==1||l.type!=="1"&&l.type!=="2"?l.status!==0||l.type!=="1"&&l.type!=="2"?l.status!==3||l.type!=="1"&&l.type!=="2"?l.status===0&&l.type==="3"?(n(),p(h,{key:5,class:"status-gray"},{default:d(()=>e[15]||(e[15]=[m("\u5F85\u8BA4\u9886")])),_:1})):W("",!0):(n(),p(h,{key:4,class:"status-gray"},{default:d(()=>e[14]||(e[14]=[m("\u8D22\u52A1\u5F85\u786E\u8BA4")])),_:1})):(n(),p(h,{key:3,class:"status-gray"},{default:d(()=>e[13]||(e[13]=[m("\u4E1A\u52A1\u5DF2\u786E\u8BA4")])),_:1})):(n(),p(h,{key:2,class:"status-gray"},{default:d(()=>e[12]||(e[12]=[m("\u8D22\u52A1\u5DF2\u786E\u8BA4")])),_:1}))]),_:1})]),_:1},8,["data","loading"])]),s(ye,{total:t($),page:t(u).pageNo,"onUpdate:page":e[4]||(e[4]=l=>t(u).pageNo=l),limit:t(u).pageSize,"onUpdate:limit":e[5]||(e[5]=l=>t(u).pageSize=l),onPagination:v,size:"small"},null,8,["total","page","limit"])]),s(Pe,{show:t(x),"onUpdate:show":e[6]||(e[6]=l=>I(x)?x.value=l:null),maxAmount:t(E),id:t(O),customerCode:t(H),claimDetail:t(Y),onSuccess:v},null,8,["show","maxAmount","id","customerCode","claimDetail"]),s(Fe,{show:t(_),"onUpdate:show":e[7]||(e[7]=l=>I(_)?_.value=l:null),ids:t(q),prefill:t(B),"read-only":t(G),onSuccess:v},null,8,["show","ids","prefill","read-only"])]),_:1})}}}),el=Ue(Ze,[["__scopeId","data-v-07a76b16"]]);export{el as default};
