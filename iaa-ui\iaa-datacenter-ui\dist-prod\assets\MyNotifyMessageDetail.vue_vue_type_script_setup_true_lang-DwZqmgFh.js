import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as M,j as r,o as f,h as i,w as l,k as t,v as o,t as m,u as e,D as y,$ as S,m as g}from"./index-C8b06LRn.js";import{E as D,a as h}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as k}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as v}from"./formatTime-COZ9Bl52.js";const x=M({name:"MyNotifyMessageDetailDetail",__name:"MyNotifyMessageDetail",setup(A,{expose:c}){const u=r(!1),n=r(!1),a=r({});return c({open:async p=>{u.value=!0,n.value=!0;try{a.value=p}finally{n.value=!1}}}),(p,_)=>{const s=D,d=k,T=h,b=E;return f(),i(b,{modelValue:e(u),"onUpdate:modelValue":_[0]||(_[0]=N=>g(u)?u.value=N:null),"max-height":500,scroll:!0,title:"\u6D88\u606F\u8BE6\u60C5"},{default:l(()=>[t(T,{column:1,border:""},{default:l(()=>[t(s,{label:"\u53D1\u9001\u4EBA"},{default:l(()=>[o(m(e(a).templateNickname),1)]),_:1}),t(s,{label:"\u53D1\u9001\u65F6\u95F4"},{default:l(()=>[o(m(e(v)(e(a).createTime)),1)]),_:1}),t(s,{label:"\u6D88\u606F\u7C7B\u578B"},{default:l(()=>[t(d,{type:e(y).SYSTEM_NOTIFY_TEMPLATE_TYPE,value:e(a).templateType},null,8,["type","value"])]),_:1}),t(s,{label:"\u662F\u5426\u5DF2\u8BFB"},{default:l(()=>[t(d,{type:e(y).INFRA_BOOLEAN_STRING,value:e(a).readStatus},null,8,["type","value"])]),_:1}),e(a).readStatus?(f(),i(s,{key:0,label:"\u9605\u8BFB\u65F6\u95F4"},{default:l(()=>[o(m(e(v)(e(a).readTime)),1)]),_:1})):S("",!0),t(s,{label:"\u5185\u5BB9"},{default:l(()=>[o(m(e(a).templateContent),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{x as _};
