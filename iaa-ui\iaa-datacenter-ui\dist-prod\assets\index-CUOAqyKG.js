import{aG as k,d as j,y as B,r as E,j as m,au as G,f as H,o as g,h as y,w as o,k as e,u as n,v as p,x as J,c as K,F as L,g as P,t as w,C as Q,B as q,S as O,H as R,I as T,J as W,K as X,L as Z,_ as ee}from"./index-C8b06LRn.js";import{_ as ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as te}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as le}from"./download-D5Lb_h0f.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";const oe=async c=>await k.post({url:"/report/bom/inventory/page",data:c}),ie=async c=>await k.download({url:"/report/bom/inventory/export",params:c}),ne=j({__name:"index",setup(c){const D=B(),h=new Date,I=`${h.getFullYear()}-${String(h.getMonth()+1).padStart(2,"0")}-${String(h.getDate()).padStart(2,"0")}`,l=E({pageNo:1,pageSize:20,dateStr:I,itemCode:void 0,itemName:void 0,spec:void 0,model:void 0}),f=m(!1),S=m([]),V=()=>{const s=parseInt(l.dateStr.slice(0,4)),a=parseInt(l.dateStr.slice(5,7)),_=new Date(s,a,0).getDate();S.value=Array.from({length:_},(d,r)=>r+1)};V(),G(()=>l.dateStr,V);const C=m([]),N=m(0),b=()=>{l.pageNo=1,U()},v=m(!1),U=async()=>{f.value=!0;try{const s=await oe(l);C.value=s.list,N.value=s.total}finally{f.value=!1}};return H(()=>{b()}),(s,a)=>{const _=Q,d=q,r=O,$=R,Y=T,i=W,z=X,M=te,A=ae,F=Z;return g(),y(A,{style:{height:"100%"}},{default:o(()=>[e(Y,{inline:!0},{default:o(()=>[e(d,{label:"\u65E5\u671F\u9009\u62E9"},{default:o(()=>[e(_,{style:{width:"140px"},onChange:a[0]||(a[0]=t=>{b()}),modelValue:n(l).dateStr,"onUpdate:modelValue":a[1]||(a[1]=t=>n(l).dateStr=t),"value-format":"YYYY-MM-DD",placeholder:"\u65E5\u671F",clearable:!1},null,8,["modelValue"])]),_:1}),e(d,{label:"\u6599\u53F7"},{default:o(()=>[e(r,{modelValue:n(l).itemCode,"onUpdate:modelValue":a[2]||(a[2]=t=>n(l).itemCode=t),class:"input-width",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"\u54C1\u540D"},{default:o(()=>[e(r,{modelValue:n(l).itemName,"onUpdate:modelValue":a[3]||(a[3]=t=>n(l).itemName=t),class:"input-width-1",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"\u89C4\u683C"},{default:o(()=>[e(r,{modelValue:n(l).spec,"onUpdate:modelValue":a[4]||(a[4]=t=>n(l).spec=t),class:"input-width-1",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"\u578B\u53F7"},{default:o(()=>[e(r,{modelValue:n(l).model,"onUpdate:modelValue":a[5]||(a[5]=t=>n(l).model=t),class:"input-width",clearable:""},null,8,["modelValue"])]),_:1}),e(d,null,{default:o(()=>[e($,{type:"primary",onClick:a[6]||(a[6]=t=>b())},{default:o(()=>a[10]||(a[10]=[p("\u67E5\u8BE2")])),_:1}),e($,{type:"warning",loading:v.value,onClick:a[7]||(a[7]=t=>(async()=>{try{await D.exportConfirm(),v.value=!0;const u=await ie(l);le.excel(u,`\u5E93\u5B58\u53CA\u51FA\u5165\u5E93\u660E\u7EC6_${l.dateStr}.xls`)}catch{}finally{v.value=!1}})())},{default:o(()=>a[11]||(a[11]=[p(" \u5BFC\u51FA ")])),_:1},8,["loading"])]),_:1})]),_:1}),J((g(),y(z,{height:"calc(100vh - 260px)","header-cell-style":{padding:"0px",color:"#555"},data:C.value,border:""},{default:o(()=>[e(i,{label:"\u6599\u53F7",prop:"itemCode",align:"center",width:"100px"}),e(i,{label:"\u65E7\u6599\u53F7",prop:"beforeCode",align:"center","min-width":"140px"}),e(i,{label:"\u54C1\u540D",prop:"itemName",align:"center",width:"100px","show-overflow-tooltip":""}),e(i,{label:"\u89C4\u683C",prop:"spec",align:"center",width:"100px","show-overflow-tooltip":""}),e(i,{label:"\u578B\u53F7",prop:"model",align:"center",width:"100px","show-overflow-tooltip":""}),e(i,{label:"\u5305\u88C5\u89C4\u683C",prop:"packing",align:"center",width:"100px","show-overflow-tooltip":""}),e(i,{label:"\u5B89\u5168\u5E93\u5B58",prop:"safetyQty",align:"center","min-width":"100px","show-overflow-tooltip":""}),e(i,{label:"\u671F\u521D\u5E93\u5B58",prop:"monthStart",align:"center","min-width":"100px","show-overflow-tooltip":""}),(g(!0),K(L,null,P(S.value,(t,u)=>(g(),y(i,{label:`${t}\u53F7`,align:"center",key:u},{default:o(()=>[e(i,{label:"\u5165\u5E93",align:"center",width:"80px"},{default:o(({row:x})=>[p(w(x.into[u]),1)]),_:2},1024),e(i,{label:"\u51FA\u5E93",align:"center",width:"80px"},{default:o(({row:x})=>[p(w(x.out[u]),1)]),_:2},1024)]),_:2},1032,["label"]))),128)),e(i,{label:"\u5408\u8BA1",align:"center"},{default:o(()=>[e(i,{label:"\u5165\u5E93",align:"center",width:"80px"},{default:o(({row:t})=>[p(w(t.into[t.into.length-1]),1)]),_:1}),e(i,{label:"\u51FA\u5E93",align:"center",width:"80px"},{default:o(({row:t})=>[p(w(t.out[t.out.length-1]),1)]),_:1})]),_:1}),e(i,{label:"\u7ED3\u4F59\u5E93\u5B58",prop:"monthEnd",align:"center","min-width":"100px","show-overflow-tooltip":"",fixed:"right"})]),_:1},8,["data"])),[[F,f.value]]),e(M,{total:N.value,page:n(l).pageNo,"onUpdate:page":a[8]||(a[8]=t=>n(l).pageNo=t),limit:n(l).pageSize,"onUpdate:limit":a[9]||(a[9]=t=>n(l).pageSize=t),onPagination:U},null,8,["total","page","limit"])]),_:1})}}}),de=ee(ne,[["__scopeId","data-v-901cb7cc"]]);export{de as default};
