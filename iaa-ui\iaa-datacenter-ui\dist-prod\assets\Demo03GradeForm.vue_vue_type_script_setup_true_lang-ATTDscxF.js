import{d as b,j as u,r as _,au as V,x,u as l,o as I,h as y,w as o,k as r,S as q,B as w,I as k,L as D}from"./index-C8b06LRn.js";import{b as U}from"./index-DkMRCZG0.js";const j=b({__name:"Demo03GradeForm",props:{studentId:{}},setup(p,{expose:f}){const v=p,t=u(!1),e=u([]),c=_({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],teacher:[{required:!0,message:"\u73ED\u4E3B\u4EFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=u();return V(()=>v.studentId,async d=>{if(e.value={id:void 0,studentId:void 0,name:void 0,teacher:void 0},d)try{t.value=!0;const a=await U(d);if(!a)return;e.value=a}finally{t.value=!1}},{immediate:!0}),f({validate:()=>m.value.validate(),getData:()=>e.value}),(d,a)=>{const n=q,i=w,g=k,h=D;return x((I(),y(g,{ref_key:"formRef",ref:m,model:l(e),rules:l(c),"label-width":"100px"},{default:o(()=>[r(i,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[r(n,{modelValue:l(e).name,"onUpdate:modelValue":a[0]||(a[0]=s=>l(e).name=s),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),r(i,{label:"\u73ED\u4E3B\u4EFB",prop:"teacher"},{default:o(()=>[r(n,{modelValue:l(e).teacher,"onUpdate:modelValue":a[1]||(a[1]=s=>l(e).teacher=s),placeholder:"\u8BF7\u8F93\u5165\u73ED\u4E3B\u4EFB"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[h,l(t)]])}}});export{j as _};
