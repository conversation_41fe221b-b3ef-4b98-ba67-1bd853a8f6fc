import{d as c,aj as m,c as p,l as s,t as o,k as v,w as d,p as x,H as g,o as y,v as f}from"./index-C8b06LRn.js";const T={class:"flex justify-center"},k={class:"text-center"},E=["src"],b={class:"text-14px text-[var(--el-color-info)]"},C={class:"mt-20px"},H=c({name:"Error",__name:"Error",props:{type:m.string.validate(r=>["404","500","403"].includes(r)).def("404")},emits:["errorClick"],setup(r,{emit:a}){const{t:e}=x(),t={404:{url:"/assets/404-B3JyPfEa.svg",message:e("error.pageError"),buttonText:e("error.returnToHome")},500:{url:"/assets/500-BGu8fdSB.svg",message:e("error.networkError"),buttonText:e("error.returnToHome")},403:{url:"/assets/403-RqeqO19C.svg",message:e("error.noPermission"),buttonText:e("error.returnToHome")}},n=r,i=a,l=()=>{i("errorClick",n.type)};return(_,w)=>{const u=g;return y(),p("div",T,[s("div",k,[s("img",{src:t[r.type].url,alt:"",width:"350"},null,8,E),s("div",b,o(t[r.type].message),1),s("div",C,[v(u,{type:"primary",onClick:l},{default:d(()=>[f(o(t[r.type].buttonText),1)]),_:1})])])])}}});export{H as _};
