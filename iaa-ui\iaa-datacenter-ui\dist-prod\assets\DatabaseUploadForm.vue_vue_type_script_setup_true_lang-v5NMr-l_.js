import{_ as I}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{aG as n,d as L,y as O,j as c,o as D,h as T,w as s,k as u,u as d,v,m as _,l as m,dP as A,aV as B,aO as H,G as S,aa as q,bn as E,H as J}from"./index-C8b06LRn.js";import{d as K}from"./download-D5Lb_h0f.js";const y={exportTemplate:()=>n.download({url:"/collection/receivable/export-template"}),deleteReceivable:l=>n.post({url:"/collection/receivable/deletes",data:l}),getReceivablePage:l=>n.post({url:"/collection/receivable/page",data:l}),getReceivableStatementsPage:l=>n.post({url:"/collection/receivable/statementsPage",data:l}),exportInformation:async l=>await n.download({url:"/collection/receivable/export-excel",params:l})},M={class:"el-upload__tip text-center"},N=L({__name:"DatabaseUploadForm",emits:["success"],setup(l,{expose:g,emit:w}){const o=O(),r=c(!1),i=c([]),t=c(),f=c(),h=w,x=c();g({open:()=>{r.value=!0,i.value=[],j()}});const k=()=>{o.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),t.value=!1},C=()=>{o.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},P=async()=>{if(i.value.length==0)return void o.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6");const a=i.value[0].name,e=a.slice(a.lastIndexOf(".")).toLowerCase();[".xls",".xlsx"].includes(e)?(f.value={Authorization:"Bearer "+A(),"tenant-id":B()},t.value=!0,x.value.submit()):o.error("\u53EA\u80FD\u4E0A\u4F20 xls \u6216 xlsx \u683C\u5F0F\u7684\u6587\u4EF6")},R=a=>{if(a.code!==0)return o.error(a.msg),void(t.value=!1);let e=a.data.join(`;
`);o.alert(e),t.value=!1,r.value=!1,h("success")},V=async()=>{const a=await y.exportTemplate();K.excel(a,"\u8D22\u52A1\u5E94\u6536\u5BFC\u5165\u6A21\u7248.xlsx")},j=async()=>{var a;t.value=!1,await H(),(a=x.value)==null||a.clearFiles()};return(a,e)=>{const F=S,U=q,z=E,b=J,G=I;return D(),T(G,{modelValue:d(r),"onUpdate:modelValue":e[2]||(e[2]=p=>_(r)?r.value=p:null),title:"\u5BFC\u5165\u6536\u6B3E"},{footer:s(()=>[u(b,{disabled:d(t),type:"primary",onClick:P},{default:s(()=>e[6]||(e[6]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),u(b,{onClick:e[1]||(e[1]=p=>r.value=!1)},{default:s(()=>e[7]||(e[7]=[v("\u53D6 \u6D88")])),_:1})]),default:s(()=>[u(z,{ref_key:"uploadRef",ref:x,"file-list":d(i),"onUpdate:fileList":e[0]||(e[0]=p=>_(i)?i.value=p:null),action:"https://sj.iaa360.cn:13141/admin-api/collection/receivable/import","auto-upload":!1,disabled:d(t),headers:d(f),limit:1,"on-error":k,"on-exceed":C,"on-success":R,accept:".xlsx, .xls",drag:""},{tip:s(()=>[m("div",M,[e[4]||(e[4]=m("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),u(U,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:V},{default:s(()=>e[3]||(e[3]=[v(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:s(()=>[u(F,{icon:"ep:upload"}),e[5]||(e[5]=m("div",{class:"el-upload__text"},[v("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),m("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","disabled","headers"])]),_:1},8,["modelValue"])}}});export{y as F,N as _};
