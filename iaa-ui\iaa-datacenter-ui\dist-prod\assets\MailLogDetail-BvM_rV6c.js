import{_ as t,__tla as r}from"./MailLogDetail.vue_vue_type_script_setup_true_lang-nY_GAqv0.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{__tla as a}from"./index-C8b06LRn.js";import"./Descriptions-iRMIdIt1.js";import"./el-descriptions-item-Ctb8GMnZ.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./formatTime-COZ9Bl52.js";import"./useCrudSchemas-HzMMRa-v.js";let o=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
