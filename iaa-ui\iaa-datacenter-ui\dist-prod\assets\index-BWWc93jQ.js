import{d as p,j as o,f as u,o as e,c as d,k as n,w as f,u as t,x as g,h as y,$ as _,F as h,L as x}from"./index-C8b06LRn.js";import{_ as j}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as w}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as b}from"./index-CkzUfjB7.js";import{b as v}from"./index-C78nCjiS.js";import"./el-card-CaOo8U9P.js";const k=p({name:"InfraSwagger",__name:"index",setup(S){const s=o(!0),r=o("https://sj.iaa360.cn:13141/doc.html");return u(async()=>{try{const a=await v("url.swagger");a&&a.length>0&&(r.value=a)}finally{s.value=!1}}),(a,F)=>{const l=b,c=w,m=j,i=x;return e(),d(h,null,[n(l,{title:"\u63A5\u53E3\u6587\u6863",url:"https://doc.iocoder.cn/api-doc/"}),n(m,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[t(s)?_("",!0):g((e(),y(c,{key:0,src:t(r)},null,8,["src"])),[[i,t(s)]])]),_:1})],64)}}});export{k as default};
