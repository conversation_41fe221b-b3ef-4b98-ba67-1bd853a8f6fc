import{aG as P,d as B,y as J,j as i,r as O,f as Q,T as W,o as d,c as R,k as e,w as r,u as t,M as g,F as Y,g as X,N as Z,D as v,h as b,v as c,x,l as $,t as T,S as ee,B as le,z as ae,A as te,C as re,G as oe,H as pe,I as ne,J as ue,K as se,L as ie}from"./index-C8b06LRn.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ce}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as fe}from"./index-CkzUfjB7.js";import{d as ge}from"./download-D5Lb_h0f.js";import{f as be}from"./formatTime-COZ9Bl52.js";import{_ as _e}from"./ApiAccessLogDetail.vue_vue_type_script_setup_true_lang-QZx-w4tq.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./el-descriptions-item-Ctb8GMnZ.js";const ye=B({name:"InfraApiAccessLog",__name:"index",setup(we){const A=J(),_=i(!0),V=i(0),N=i([]),o=O({pageNo:1,pageSize:10,userId:null,userType:null,applicationName:null,requestUrl:null,duration:null,resultCode:null,beginTime:[]}),U=i(),y=i(!1),w=async()=>{_.value=!0;try{const a=await(s=o,P.get({url:"/infra/api-access-log/page",params:s}));N.value=a.list,V.value=a.total}finally{_.value=!1}var s},u=()=>{o.pageNo=1,w()},M=()=>{U.value.resetFields(),u()},C=i(),S=async()=>{try{await A.exportConfirm(),y.value=!0;const a=await(s=o,P.download({url:"/infra/api-access-log/export-excel",params:s}));ge.excel(a,"API \u8BBF\u95EE\u65E5\u5FD7.xls")}catch{}finally{y.value=!1}var s};return Q(()=>{w()}),(s,a)=>{const q=fe,m=ee,n=le,D=ae,z=te,F=re,h=oe,f=pe,H=ne,k=me,p=ue,I=ce,G=se,L=de,E=W("hasPermi"),j=ie;return d(),R(Y,null,[e(q,{title:"\u7CFB\u7EDF\u65E5\u5FD7",url:"https://doc.iocoder.cn/system-log/"}),e(k,null,{default:r(()=>[e(H,{class:"-mb-15px",model:t(o),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:r(()=>[e(n,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:r(()=>[e(m,{modelValue:t(o).userId,"onUpdate:modelValue":a[0]||(a[0]=l=>t(o).userId=l),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:g(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:r(()=>[e(z,{modelValue:t(o).userType,"onUpdate:modelValue":a[1]||(a[1]=l=>t(o).userType=l),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:r(()=>[(d(!0),R(Y,null,X(t(Z)(t(v).USER_TYPE),l=>(d(),b(D,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u5E94\u7528\u540D",prop:"applicationName"},{default:r(()=>[e(m,{modelValue:t(o).applicationName,"onUpdate:modelValue":a[2]||(a[2]=l=>t(o).applicationName=l),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:g(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u8BF7\u6C42\u65F6\u95F4",prop:"beginTime"},{default:r(()=>[e(F,{modelValue:t(o).beginTime,"onUpdate:modelValue":a[3]||(a[3]=l=>t(o).beginTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(n,{label:"\u6267\u884C\u65F6\u957F",prop:"duration"},{default:r(()=>[e(m,{modelValue:t(o).duration,"onUpdate:modelValue":a[4]||(a[4]=l=>t(o).duration=l),placeholder:"\u8BF7\u8F93\u5165\u6267\u884C\u65F6\u957F",clearable:"",onKeyup:g(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u7ED3\u679C\u7801",prop:"resultCode"},{default:r(()=>[e(m,{modelValue:t(o).resultCode,"onUpdate:modelValue":a[5]||(a[5]=l=>t(o).resultCode=l),placeholder:"\u8BF7\u8F93\u5165\u7ED3\u679C\u7801",clearable:"",onKeyup:g(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(n,null,{default:r(()=>[e(f,{onClick:u},{default:r(()=>[e(h,{icon:"ep:search",class:"mr-5px"}),a[8]||(a[8]=c(" \u641C\u7D22"))]),_:1}),e(f,{onClick:M},{default:r(()=>[e(h,{icon:"ep:refresh",class:"mr-5px"}),a[9]||(a[9]=c(" \u91CD\u7F6E"))]),_:1}),x((d(),b(f,{type:"success",plain:"",onClick:S,loading:t(y)},{default:r(()=>[e(h,{icon:"ep:download",class:"mr-5px"}),a[10]||(a[10]=c(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[E,["infra:api-access-log:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,null,{default:r(()=>[x((d(),b(G,{data:t(N)},{default:r(()=>[e(p,{label:"\u65E5\u5FD7\u7F16\u53F7",align:"center",prop:"id",width:"100",fix:"right"}),e(p,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"userId"}),e(p,{label:"\u7528\u6237\u7C7B\u578B",align:"center",prop:"userType"},{default:r(l=>[e(I,{type:t(v).USER_TYPE,value:l.row.userType},null,8,["type","value"])]),_:1}),e(p,{label:"\u5E94\u7528\u540D",align:"center",prop:"applicationName",width:"150"}),e(p,{label:"\u8BF7\u6C42\u65B9\u6CD5",align:"center",prop:"requestMethod",width:"80"}),e(p,{label:"\u8BF7\u6C42\u5730\u5740",align:"center",prop:"requestUrl",width:"500"}),e(p,{label:"\u8BF7\u6C42\u65F6\u95F4",align:"center",prop:"beginTime",width:"180"},{default:r(l=>[$("span",null,T(t(be)(l.row.beginTime)),1)]),_:1}),e(p,{label:"\u6267\u884C\u65F6\u957F",align:"center",prop:"duration",width:"180"},{default:r(l=>[c(T(l.row.duration)+" ms ",1)]),_:1}),e(p,{label:"\u64CD\u4F5C\u7ED3\u679C",align:"center",prop:"status"},{default:r(l=>[c(T(l.row.resultCode===0?"\u6210\u529F":"\u5931\u8D25("+l.row.resultMsg+")"),1)]),_:1}),e(p,{label:"\u64CD\u4F5C\u6A21\u5757",align:"center",prop:"operateModule",width:"180"}),e(p,{label:"\u64CD\u4F5C\u540D",align:"center",prop:"operateName",width:"180"}),e(p,{label:"\u64CD\u4F5C\u7C7B\u578B",align:"center",prop:"operateType"},{default:r(l=>[e(I,{type:t(v).INFRA_OPERATE_TYPE,value:l.row.operateType},null,8,["type","value"])]),_:1}),e(p,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"60"},{default:r(l=>[x((d(),b(f,{link:"",type:"primary",onClick:he=>{return K=l.row,void C.value.open(K);var K}},{default:r(()=>a[11]||(a[11]=[c(" \u8BE6\u7EC6 ")])),_:2},1032,["onClick"])),[[E,["infra:api-access-log:query"]]])]),_:1})]),_:1},8,["data"])),[[j,t(_)]]),e(L,{total:t(V),page:t(o).pageNo,"onUpdate:page":a[6]||(a[6]=l=>t(o).pageNo=l),limit:t(o).pageSize,"onUpdate:limit":a[7]||(a[7]=l=>t(o).pageSize=l),onPagination:w},null,8,["total","page","limit"])]),_:1}),e(_e,{ref_key:"detailRef",ref:C},null,512)],64)}}});export{ye as default};
