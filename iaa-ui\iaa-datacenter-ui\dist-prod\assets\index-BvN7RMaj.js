import{d as J,y as L,p as O,j as u,r as Q,f as W,T as Z,o as n,c as U,k as e,w as o,u as l,M as $,F as D,g as ee,N as ae,D as E,h as d,v as m,x as f,S as le,B as te,z as oe,A as re,C as ne,G as se,H as ie,I as pe,J as ue,K as de,L as me}from"./index-C8b06LRn.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ye}from"./index-CkzUfjB7.js";import{d as Y}from"./formatTime-COZ9Bl52.js";import{d as ge}from"./download-D5Lb_h0f.js";import{d as xe,e as we,f as be}from"./index-DkMRCZG0.js";import{_ as ve}from"./Demo03StudentForm.vue_vue_type_script_setup_true_lang-BM6sxeU6.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-DBgcgn_x.js";import"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-ATTDscxF.js";const he=J({name:"Demo03Student",__name:"index",setup(Se){const x=L(),{t:z}=O(),w=u(!0),h=u([]),S=u(0),r=Q({pageNo:1,pageSize:10,name:null,sex:null,description:null,createTime:[]}),k=u(),b=u(!1),c=async()=>{w.value=!0;try{const s=await xe(r);h.value=s.list,S.value=s.total}finally{w.value=!1}},v=()=>{r.pageNo=1,c()},M=()=>{k.value.resetFields(),v()},C=u(),V=(s,a)=>{C.value.open(s,a)},N=async()=>{try{await x.exportConfirm(),b.value=!0;const s=await be(r);ge.excel(s,"\u5B66\u751F.xls")}catch{}finally{b.value=!1}};return W(()=>{c()}),(s,a)=>{const R=ye,F=le,_=te,H=oe,A=re,G=ne,y=se,p=ie,K=pe,T=_e,i=ue,P=fe,X=de,j=ce,g=Z("hasPermi"),q=me;return n(),U(D,null,[e(R,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u4E3B\u5B50\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/master-sub/"}),e(T,null,{default:o(()=>[e(K,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:o(()=>[e(_,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[e(F,{modelValue:l(r).name,"onUpdate:modelValue":a[0]||(a[0]=t=>l(r).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57",clearable:"",onKeyup:$(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u6027\u522B",prop:"sex"},{default:o(()=>[e(A,{modelValue:l(r).sex,"onUpdate:modelValue":a[1]||(a[1]=t=>l(r).sex=t),placeholder:"\u8BF7\u9009\u62E9\u6027\u522B",clearable:"",class:"!w-240px"},{default:o(()=>[(n(!0),U(D,null,ee(l(ae)(l(E).SYSTEM_USER_SEX),t=>(n(),d(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[e(G,{modelValue:l(r).createTime,"onUpdate:modelValue":a[2]||(a[2]=t=>l(r).createTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(_,null,{default:o(()=>[e(p,{onClick:v},{default:o(()=>[e(y,{icon:"ep:search",class:"mr-5px"}),a[6]||(a[6]=m(" \u641C\u7D22"))]),_:1}),e(p,{onClick:M},{default:o(()=>[e(y,{icon:"ep:refresh",class:"mr-5px"}),a[7]||(a[7]=m(" \u91CD\u7F6E"))]),_:1}),f((n(),d(p,{type:"primary",plain:"",onClick:a[3]||(a[3]=t=>V("create"))},{default:o(()=>[e(y,{icon:"ep:plus",class:"mr-5px"}),a[8]||(a[8]=m(" \u65B0\u589E "))]),_:1})),[[g,["infra:demo03-student:create"]]]),f((n(),d(p,{type:"success",plain:"",onClick:N,loading:l(b)},{default:o(()=>[e(y,{icon:"ep:download",class:"mr-5px"}),a[9]||(a[9]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[g,["infra:demo03-student:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(T,null,{default:o(()=>[f((n(),d(X,{data:l(h),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[e(i,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(i,{label:"\u540D\u5B57",align:"center",prop:"name"}),e(i,{label:"\u6027\u522B",align:"center",prop:"sex"},{default:o(t=>[e(P,{type:l(E).SYSTEM_USER_SEX,value:t.row.sex},null,8,["type","value"])]),_:1}),e(i,{label:"\u51FA\u751F\u65E5\u671F",align:"center",prop:"birthday",formatter:l(Y),width:"180px"},null,8,["formatter"]),e(i,{label:"\u7B80\u4ECB",align:"center",prop:"description"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(Y),width:"180px"},null,8,["formatter"]),e(i,{label:"\u64CD\u4F5C",align:"center"},{default:o(t=>[f((n(),d(p,{link:"",type:"primary",onClick:B=>V("update",t.row.id)},{default:o(()=>a[10]||(a[10]=[m(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[g,["infra:demo03-student:update"]]]),f((n(),d(p,{link:"",type:"danger",onClick:B=>(async I=>{try{await x.delConfirm(),await we(I),x.success(z("common.delSuccess")),await c()}catch{}})(t.row.id)},{default:o(()=>a[11]||(a[11]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[q,l(w)]]),e(j,{total:l(S),page:l(r).pageNo,"onUpdate:page":a[4]||(a[4]=t=>l(r).pageNo=t),limit:l(r).pageSize,"onUpdate:limit":a[5]||(a[5]=t=>l(r).pageSize=t),onPagination:c},null,8,["total","page","limit"])]),_:1}),e(ve,{ref_key:"formRef",ref:C,onSuccess:c},null,512)],64)}}});export{he as default};
