import{d as K,j as p,r as L,f as M,o as n,c as O,k as l,w as o,x as $,u as a,h as m,l as v,t as w,v as y,$ as z,m as h,ar as A,F as E,a as W,O as Y,J as Z,H as G,aM as I,K as Q,aF as R,L as ee}from"./index-C8b06LRn.js";import{_ as ae}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as le}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as oe}from"./index-CkzUfjB7.js";import{d as re}from"./formatTime-COZ9Bl52.js";import{k as ie}from"./bpmn-embedded-DWy7HXvQ.js";import{g as pe,a as ne}from"./index-DDzAA47d.js";import{b as me}from"./formCreate-CdPDb26P.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";import"./XTextButton-BSf0iZhI.js";import"./XButton-BOgar_Ex.js";import"./el-collapse-item-JANV_ocl.js";import"./index-CBsqkafF.js";import"./el-tree-select-E9FCZb0j.js";import"./index-C0LhU1J1.js";import"./index-Dz9lR_me.js";import"./index-Cgv48ZKs.js";import"./constants-D3f7Z3TX.js";import"./index-SMELiJYy.js";import"./el-drawer-C5TFtzfV.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./index-B9cOKMOY.js";const se=K({name:"BpmProcessDefinition",__name:"index",setup(ue){const{push:P}=W(),{query:T}=Y(),g=p(!0),b=p(0),C=p([]),s=L({pageNo:1,pageSize:10,key:T.key}),V=async()=>{g.value=!0;try{const r=await pe(s);C.value=r.list,b.value=r.total}finally{g.value=!1}},d=p(!1),_=p({rule:[],option:{}}),x=async r=>{r.formType==10?(me(_,r.formConf,r.formFields),d.value=!0):await P({path:r.formCustomCreatePath})},f=p(!1),u=p(null),F=p({prefix:"flowable"});return M(()=>{V()}),(r,t)=>{const D=oe,i=Z,k=G,c=I,X=Q,j=le,q=te,B=R("form-create"),N=ae,H=ee;return n(),O(E,null,[l(D,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),l(q,null,{default:o(()=>[$((n(),m(X,{data:a(C)},{default:o(()=>[l(i,{label:"\u5B9A\u4E49\u7F16\u53F7",align:"center",prop:"id",width:"400"}),l(i,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name",width:"200"},{default:o(e=>[l(k,{type:"primary",link:"",onClick:S=>(async J=>{var U;u.value=(U=await ne(J.id))==null?void 0:U.bpmnXml,f.value=!0})(e.row)},{default:o(()=>[v("span",null,w(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),l(i,{label:"\u5B9A\u4E49\u5206\u7C7B",align:"center",prop:"categoryName",width:"100"}),l(i,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType",width:"200"},{default:o(e=>[e.row.formType===10?(n(),m(k,{key:0,type:"primary",link:"",onClick:S=>x(e.row)},{default:o(()=>[v("span",null,w(e.row.formName),1)]),_:2},1032,["onClick"])):(n(),m(k,{key:1,type:"primary",link:"",onClick:S=>x(e.row)},{default:o(()=>[v("span",null,w(e.row.formCustomCreatePath),1)]),_:2},1032,["onClick"]))]),_:1}),l(i,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"processDefinition.version",width:"80"},{default:o(e=>[e.row?(n(),m(c,{key:0},{default:o(()=>[y("v"+w(e.row.version),1)]),_:2},1024)):(n(),m(c,{key:1,type:"warning"},{default:o(()=>t[5]||(t[5]=[y("\u672A\u90E8\u7F72")])),_:1}))]),_:1}),l(i,{label:"\u72B6\u6001",align:"center",prop:"version",width:"80"},{default:o(e=>[e.row.suspensionState===1?(n(),m(c,{key:0,type:"success"},{default:o(()=>t[6]||(t[6]=[y("\u6FC0\u6D3B")])),_:1})):z("",!0),e.row.suspensionState===2?(n(),m(c,{key:1,type:"warning"},{default:o(()=>t[7]||(t[7]=[y("\u6302\u8D77")])),_:1})):z("",!0)]),_:1}),l(i,{label:"\u90E8\u7F72\u65F6\u95F4",align:"center",prop:"deploymentTime",width:"180",formatter:a(re)},null,8,["formatter"]),l(i,{label:"\u5B9A\u4E49\u63CF\u8FF0",align:"center",prop:"description",width:"300","show-overflow-tooltip":""})]),_:1},8,["data"])),[[H,a(g)]]),l(j,{total:a(b),page:a(s).pageNo,"onUpdate:page":t[0]||(t[0]=e=>a(s).pageNo=e),limit:a(s).pageSize,"onUpdate:limit":t[1]||(t[1]=e=>a(s).pageSize=e),onPagination:V},null,8,["total","page","limit"])]),_:1}),l(N,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:a(d),"onUpdate:modelValue":t[2]||(t[2]=e=>h(d)?d.value=e:null),width:"800"},{default:o(()=>[l(B,{rule:a(_).rule,option:a(_).option},null,8,["rule","option"])]),_:1},8,["modelValue"]),l(N,{title:"\u6D41\u7A0B\u56FE",modelValue:a(f),"onUpdate:modelValue":t[4]||(t[4]=e=>h(f)?f.value=e:null),width:"800"},{default:o(()=>[l(a(ie),A({key:"designer",modelValue:a(u),"onUpdate:modelValue":t[3]||(t[3]=e=>h(u)?u.value=e:null),value:a(u)},a(F),{prefix:a(F).prefix}),null,16,["modelValue","value","prefix"])]),_:1},8,["modelValue"])],64)}}});export{se as default};
