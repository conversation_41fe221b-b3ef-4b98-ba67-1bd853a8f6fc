import{b6 as z,ba as b,bA as A,cu as F,by as O,bD as k,d as y,cj as S,cv as M,bb as U,j as w,cw as E,bK as N,Y as i,o as f,h as p,w as v,u as c,be as D,n as K,i as P,$ as Y,c as q,F as G,g as J,A as x,bg as L,bh as Q}from"./index-C8b06LRn.js";const R=z({format:{type:String,default:"HH:mm"},modelValue:String,disabled:Boolean,editable:{type:Boolean,default:!0},effect:{type:b(String),default:"light"},clearable:{type:Boolean,default:!0},size:A,placeholder:String,start:{type:String,default:"09:00"},end:{type:String,default:"18:00"},step:{type:String,default:"00:30"},minTime:String,maxTime:String,name:String,prefixIcon:{type:b([String,Object]),default:()=>F},clearIcon:{type:b([String,Object]),default:()=>O},...k}),o=n=>{const u=(n||"").split(":");if(u.length>=2){let l=Number.parseInt(u[0],10);const s=Number.parseInt(u[1],10),t=n.toUpperCase();return t.includes("AM")&&l===12?l=0:t.includes("PM")&&l!==12&&(l+=12),{hours:l,minutes:s}}return null},h=(n,u)=>{const l=o(n);if(!l)return-1;const s=o(u);if(!s)return-1;const t=l.minutes+60*l.hours,d=s.minutes+60*s.hours;return t===d?0:t>d?1:-1},$=n=>`${n}`.padStart(2,"0"),m=n=>`${$(n.hours)}:${$(n.minutes)}`,W=(n,u)=>{const l=o(n);if(!l)return"";const s=o(u);if(!s)return"";const t={hours:l.hours,minutes:l.minutes};return t.minutes+=s.minutes,t.hours+=s.hours,t.hours+=Math.floor(t.minutes/60),t.minutes=t.minutes%60,m(t)},X=y({name:"ElTimeSelect"}),Z=Q(L(y({...X,props:R,emits:["change","blur","focus","clear","update:modelValue"],setup(n,{expose:u}){const l=n;S.extend(M);const{Option:s}=x,t=U("input"),d=w(),I=E(),{lang:V}=N(),B=i(()=>l.modelValue),C=i(()=>{const e=o(l.start);return e?m(e):null}),g=i(()=>{const e=o(l.end);return e?m(e):null}),T=i(()=>{const e=o(l.step);return e?m(e):null}),j=i(()=>{const e=o(l.minTime||"");return e?m(e):null}),H=i(()=>{const e=o(l.maxTime||"");return e?m(e):null}),_=i(()=>{const e=[];if(l.start&&l.end&&l.step){let r,a=C.value;for(;a&&g.value&&h(a,g.value)<=0;)r=S(a,"HH:mm").locale(V.value).format(l.format),e.push({value:r,disabled:h(a,j.value||"-1:-1")<=0||h(a,H.value||"100:100")>=0}),a=W(a,T.value)}return e});return u({blur:()=>{var e,r;(r=(e=d.value)==null?void 0:e.blur)==null||r.call(e)},focus:()=>{var e,r;(r=(e=d.value)==null?void 0:e.focus)==null||r.call(e)}}),(e,r)=>(f(),p(c(x),{ref_key:"select",ref:d,"model-value":c(B),disabled:c(I),clearable:e.clearable,"clear-icon":e.clearIcon,size:e.size,effect:e.effect,placeholder:e.placeholder,"default-first-option":"",filterable:e.editable,"empty-values":e.emptyValues,"value-on-clear":e.valueOnClear,"onUpdate:modelValue":a=>e.$emit("update:modelValue",a),onChange:a=>e.$emit("change",a),onBlur:a=>e.$emit("blur",a),onFocus:a=>e.$emit("focus",a),onClear:()=>e.$emit("clear")},{prefix:v(()=>[e.prefixIcon?(f(),p(c(D),{key:0,class:K(c(t).e("prefix-icon"))},{default:v(()=>[(f(),p(P(e.prefixIcon)))]),_:1},8,["class"])):Y("v-if",!0)]),default:v(()=>[(f(!0),q(G,null,J(c(_),a=>(f(),p(c(s),{key:a.value,label:a.value,value:a.value,disabled:a.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["model-value","disabled","clearable","clear-icon","size","effect","placeholder","filterable","empty-values","value-on-clear","onUpdate:modelValue","onChange","onBlur","onFocus","onClear"]))}}),[["__file","time-select.vue"]]));export{Z as E};
