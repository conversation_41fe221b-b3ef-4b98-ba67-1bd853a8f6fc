import{aG as e}from"./index-C8b06LRn.js";const r={getProcessExpressionPage:async s=>await e.get({url:"/bpm/process-expression/page",params:s}),getProcessExpression:async s=>await e.get({url:"/bpm/process-expression/get?id="+s}),createProcessExpression:async s=>await e.post({url:"/bpm/process-expression/create",data:s}),updateProcessExpression:async s=>await e.put({url:"/bpm/process-expression/update",data:s}),deleteProcessExpression:async s=>await e.delete({url:"/bpm/process-expression/delete?id="+s}),exportProcessExpression:async s=>await e.download({url:"/bpm/process-expression/export-excel",params:s})};export{r as P};
