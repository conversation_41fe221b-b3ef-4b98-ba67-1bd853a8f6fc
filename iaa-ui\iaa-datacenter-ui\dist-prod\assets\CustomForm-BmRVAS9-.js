import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as A,aj as B,j as c,y as D,r as H,bU as L,o as v,h as y,w as t,k as s,u as o,v as $,c as E,F as G,g as J,di as K,m as M,S as O,B as P,c3 as Q,z as T,A as W,I as X,H as Y,_ as Z}from"./index-C8b06LRn.js";import{C}from"./index-B2m4kQ_G.js";const ee=Z(A({__name:"CustomForm",props:{type:B.string.isRequired},emits:["success"],setup(U,{expose:h,emit:w}){const d=c(!1),n=c(!1),f=c(),N=D(),a=c({id:void 0,seller:void 0,custom:void 0,customerCode:void 0,customerName:void 0,description:void 0}),k=H({description:[{required:!0,message:"\u4EFB\u52A1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],custom:[{required:!0,message:"\u5B9A\u5236\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=U,x=w,V=L(async(m,e,i)=>{let u={};u[i]=m,e((await C.getCustomerList(u)).map(r=>({value:r.code,label:r.name})))},500),_=(m,e)=>{e==="code"?a.value.customerName=m.label:a.value.customerCode=m.value},F=async()=>{n.value=!0;try{await f.value.validate(),await C.saveDetail({...a.value,type:g.type}),b(),N.success("\u4FDD\u5B58\u6210\u529F"),d.value=!1,x("success")}finally{n.value=!1}},b=()=>{a.value={id:void 0,seller:void 0,custom:void 0,customerCode:void 0,customerName:void 0,description:void 0}};return h({openForm:()=>{b(),d.value=!0}}),(m,e)=>{const i=O,u=P,r=Q,j=T,q=W,R=X,S=Y,I=z;return v(),y(I,{modelValue:o(d),"onUpdate:modelValue":e[9]||(e[9]=l=>M(d)?d.value=l:null),title:"\u6DFB\u52A0\u81EA\u5B9A\u4E49\u4EFB\u52A1"},{footer:t(()=>[s(S,{type:"primary",plain:"",onClick:F,loading:o(n)},{default:t(()=>e[10]||(e[10]=[$("\u4FDD\u5B58")])),_:1},8,["loading"])]),default:t(()=>[s(R,{ref_key:"formRef",ref:f,"label-width":"120",model:o(a),rules:o(k)},{default:t(()=>[s(u,{label:"\u4E1A\u52A1\u5458"},{default:t(()=>[s(i,{modelValue:o(a).seller,"onUpdate:modelValue":e[0]||(e[0]=l=>o(a).seller=l)},null,8,["modelValue"])]),_:1}),s(u,{label:"\u5BA2\u6237\u7F16\u7801"},{default:t(()=>[s(r,{modelValue:o(a).customerCode,"onUpdate:modelValue":e[1]||(e[1]=l=>o(a).customerCode=l),"fetch-suggestions":(l,p)=>o(V)(l,p,"code"),clearable:"",onSelect:e[2]||(e[2]=l=>_(l,"code")),onClear:e[3]||(e[3]=()=>{o(a).customerName=void 0})},null,8,["modelValue","fetch-suggestions"])]),_:1}),s(u,{label:"\u5BA2\u6237\u540D\u79F0"},{default:t(()=>[s(r,{modelValue:o(a).customerName,"onUpdate:modelValue":e[4]||(e[4]=l=>o(a).customerName=l),"fetch-suggestions":(l,p)=>o(V)(l,p,"name"),clearable:"",onSelect:e[5]||(e[5]=l=>_(l,"name")),onClear:e[6]||(e[6]=()=>{o(a).customerCode=void 0})},null,8,["modelValue","fetch-suggestions"])]),_:1}),s(u,{label:"\u5B9A\u5236\u5185\u5BB9",prop:"custom"},{default:t(()=>[s(q,{modelValue:o(a).custom,"onUpdate:modelValue":e[7]||(e[7]=l=>o(a).custom=l)},{default:t(()=>[(v(!0),E(G,null,J(o(K)(`eng_${g.type}_dict`),l=>(v(),y(j,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(u,{label:"\u4EFB\u52A1",prop:"description"},{default:t(()=>[s(i,{type:"textarea",modelValue:o(a).description,"onUpdate:modelValue":e[8]||(e[8]=l=>o(a).description=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-98529bf9"]]);export{ee as default};
