import{d as w,p as _,y as b,j as l,f as v,o as n,h as p,w as i,x as g,u as s,k as t,J as h,K as y,L as x}from"./index-C8b06LRn.js";import{_ as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as L}from"./formatTime-COZ9Bl52.js";import{b as j}from"./index-ANJNvVVB.js";const k=w({__name:"Demo03GradeList",props:{studentId:{}},setup(u){_(),b();const m=u,e=l(!1),o=l([]);return v(()=>{(async()=>{e.value=!0;try{const r=await j(m.studentId);if(!r)return;o.value.push(r)}finally{e.value=!1}})()}),(r,D)=>{const a=h,d=y,f=I,c=x;return n(),p(f,null,{default:i(()=>[g((n(),p(d,{data:s(o),stripe:!0,"show-overflow-tooltip":!0},{default:i(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u73ED\u4E3B\u4EFB",align:"center",prop:"teacher"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:s(L),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[c,s(e)]])]),_:1})}}});export{k as _};
