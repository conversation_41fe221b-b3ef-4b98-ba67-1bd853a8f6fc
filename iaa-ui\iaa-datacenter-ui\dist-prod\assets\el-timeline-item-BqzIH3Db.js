import{d as m,bb as d,aL as y,bv as g,bf as c,b6 as h,b9 as k,Y as w,o as l,c as o,l as p,n as a,u as t,aK as S,h as u,w as T,i as $,be as B,$ as n,t as v,bg as E,bh as _,cS as x}from"./index-C8b06LRn.js";const z=m({name:"ElTimeline",setup(r,{slots:i}){const e=d("timeline");return y("timeline",i),()=>g("ul",{class:[e.b()]},[c(i,"default")])}}),I=h({timestamp:{type:String,default:""},hideTimestamp:Boolean,center:Boolean,placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:k},hollow:Boolean}),C=m({name:"ElTimelineItem"});var f=E(m({...C,props:I,setup(r){const i=r,e=d("timeline-item"),b=w(()=>[e.e("node"),e.em("node",i.size||""),e.em("node",i.type||""),e.is("hollow",i.hollow)]);return(s,Y)=>(l(),o("li",{class:a([t(e).b(),{[t(e).e("center")]:s.center}])},[p("div",{class:a(t(e).e("tail"))},null,2),s.$slots.dot?n("v-if",!0):(l(),o("div",{key:0,class:a(t(b)),style:S({backgroundColor:s.color})},[s.icon?(l(),u(t(B),{key:0,class:a(t(e).e("icon"))},{default:T(()=>[(l(),u($(s.icon)))]),_:1},8,["class"])):n("v-if",!0)],6)),s.$slots.dot?(l(),o("div",{key:1,class:a(t(e).e("dot"))},[c(s.$slots,"dot")],2)):n("v-if",!0),p("div",{class:a(t(e).e("wrapper"))},[s.hideTimestamp||s.placement!=="top"?n("v-if",!0):(l(),o("div",{key:0,class:a([t(e).e("timestamp"),t(e).is("top")])},v(s.timestamp),3)),p("div",{class:a(t(e).e("content"))},[c(s.$slots,"default")],2),s.hideTimestamp||s.placement!=="bottom"?n("v-if",!0):(l(),o("div",{key:1,class:a([t(e).e("timestamp"),t(e).is("bottom")])},v(s.timestamp),3))],2)],2))}}),[["__file","timeline-item.vue"]]);const K=_(z,{TimelineItem:f}),L=x(f);export{L as E,K as a};
