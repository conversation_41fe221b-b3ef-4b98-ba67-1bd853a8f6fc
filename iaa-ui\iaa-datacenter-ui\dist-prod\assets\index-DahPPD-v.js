import{d as b,j as a,y as C,f as S,T as j,o as m,h as n,w as e,x as d,u as s,k as T,v as U,S as z,H,L}from"./index-C8b06LRn.js";import{_ as P}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as R}from"./index-COdQIXZX.js";import{g as q,b as A,u as B}from"./erp-BaintkYS.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";const D=b({__name:"index",setup(E){const r=a([]),i=a([]),u=a(0),f=a({}),w=a(!1),y=C(),t=async()=>{const l=await A();i.value=l,u.value=l.lenght};return S(()=>{(async()=>r.value=await q())(),t()}),(l,p)=>{const _=z,h=H,k=R,g=P,v=j("hasPermi"),V=L;return m(),n(g,null,{default:e(()=>[d((m(),n(k,{data:s(i),columns:s(r),page:s(f),total:s(u),onRefresh:t,onSearch:t},{name:e(({row:o})=>[T(_,{modelValue:o.name,"onUpdate:modelValue":c=>o.name=c},null,8,["modelValue","onUpdate:modelValue"])]),operation:e(({row:o})=>[d((m(),n(h,{type:"primary",link:"",size:"small",onClick:c=>(async x=>{await B(x),y.success("\u66F4\u65B0\u6210\u529F")})(o.name)},{default:e(()=>p[0]||(p[0]=[U(" \u66F4\u65B0 ")])),_:2},1032,["onClick"])),[[v,["ekuaibao:custom:list"]]])]),_:1},8,["data","columns","page","total"])),[[V,s(w)]])]),_:1})}}});export{D as default};
