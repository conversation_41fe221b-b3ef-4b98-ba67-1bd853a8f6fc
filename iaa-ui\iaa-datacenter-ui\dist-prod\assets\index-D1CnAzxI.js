import{aG as a}from"./index-C8b06LRn.js";const e=async t=>await a.get({url:"/system/notify-template/page",params:t}),s=async t=>await a.get({url:"/system/notify-template/get?id="+t}),y=async t=>await a.post({url:"/system/notify-template/create",data:t}),i=async t=>await a.put({url:"/system/notify-template/update",data:t}),m=async t=>await a.delete({url:"/system/notify-template/delete?id="+t}),l=t=>a.post({url:"/system/notify-template/send-notify",data:t});export{e as a,y as c,m as d,s as g,l as s,i as u};
