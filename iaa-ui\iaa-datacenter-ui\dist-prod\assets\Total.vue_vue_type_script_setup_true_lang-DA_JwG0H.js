import{d as A,j as u,Y as j,au as C,f as T,o as t,c as n,l as m,k as z,w as B,F as y,g as f,u as s,h as D,m as F,t as i,v as U,z as Y,A as q}from"./index-C8b06LRn.js";import{B as g}from"./index-CSCiSrUr.js";const E={class:"flex flex-col justify-start text-1.2rem bg-[var(--el-color-primary-light-9)] rounded-5px p-2px mt-5px"},G=A({__name:"Total",props:{modelValue:{}},emits:["update:modelValue"],setup(x,{emit:h}){const V=x,b=h,r=u(0),p=u([]),d=u(!1),o=j({get:()=>V.modelValue||[],set:e=>b("update:modelValue",e)}),v=async(e="")=>{d.value=!0;try{const a=await g.getCustomers(e);c.value=a}catch{c.value=[]}finally{d.value=!1}};C(o,async e=>{try{const a=await g.getTotalAmount({orderCodes:e});r.value=a.totalAmount,p.value=a.currencyDetails||[]}catch{r.value=0}},{immediate:!0});const c=u([]);return T(async()=>{await v()}),(e,a)=>{const w=Y,_=q;return t(),n("div",null,[a[2]||(a[2]=m("div",{class:"mb-10px font-600"},"\u5BA2\u6237\u9009\u62E9",-1)),z(_,{modelValue:s(o),"onUpdate:modelValue":a[0]||(a[0]=l=>F(o)?o.value=l:null),multiple:"",filterable:"",remote:"","remote-method":v,loading:s(d),"collapse-tags":"","collapse-tags-tooltip":"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",style:{width:"20%"}},{default:B(()=>[(t(!0),n(y,null,f(s(c),l=>(t(),D(w,{key:l.code,label:l.name,value:l.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]),m("div",E,[m("div",null,"\u672C\u5E01\u603B\u5E94\u6536\uFF1A"+i(s(r))+";",1),m("div",null,[a[1]||(a[1]=U("\u6838\u5E01\u5E94\u6536: ")),(t(!0),n(y,null,f(s(p),(l,k)=>(t(),n("span",{key:k,class:"ml-10px"},i(l.currency)+"\uFF1A"+i(l.amount)+"; ",1))),128))])])])}}});export{G as _};
