import{d as z,a as B,j as o,r as F,f as H,o as v,c as M,k as e,w as l,u as t,M as P,v as g,x as q,h as K,F as j,S as G,B as J,C as L,G as A,H as R,I as E,J as O,K as Q,L as W}from"./index-C8b06LRn.js";import{_ as X}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as Z}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as $}from"./index-CkzUfjB7.js";import{d as N}from"./formatTime-COZ9Bl52.js";import{i as ee}from"./index-czoBMNFJ.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";const ae=z({name:"BpmProcessInstanceCopy",__name:"index",setup(te){const{push:x}=B(),i=o(!1),h=o(0),_=o([]),r=F({pageNo:1,pageSize:10,processInstanceId:"",processInstanceName:"",createTime:[]}),I=o(),p=async()=>{i.value=!0;try{const m=await ee(r);_.value=m.list,h.value=m.total}finally{i.value=!1}},c=()=>{r.pageNo=1,p()},k=()=>{I.value.resetFields(),c()};return H(()=>{p()}),(m,a)=>{const T=$,V=G,d=J,C=L,w=A,u=R,D=E,y=Z,n=O,S=Q,U=X,Y=W;return v(),M(j,null,[e(T,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),e(y,null,{default:l(()=>[e(D,{ref_key:"queryFormRef",ref:I,inline:!0,class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(d,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:l(()=>[e(V,{modelValue:t(r).processInstanceName,"onUpdate:modelValue":a[0]||(a[0]=s=>t(r).processInstanceName=s),onKeyup:P(c,["enter"]),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u6284\u9001\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(C,{modelValue:t(r).createTime,"onUpdate:modelValue":a[1]||(a[1]=s=>t(r).createTime=s),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(d,null,{default:l(()=>[e(u,{onClick:c},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:search"}),a[4]||(a[4]=g(" \u641C\u7D22 "))]),_:1}),e(u,{onClick:k},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:refresh"}),a[5]||(a[5]=g(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},512)]),_:1}),e(y,null,{default:l(()=>[q((v(),K(S,{data:t(_)},{default:l(()=>[e(n,{align:"center",label:"\u6D41\u7A0B\u540D",prop:"processInstanceName","min-width":"180"}),e(n,{align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",prop:"startUserName","min-width":"100"}),e(n,{formatter:t(N),align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u65F6\u95F4",prop:"processInstanceStartTime",width:"180"},null,8,["formatter"]),e(n,{align:"center",label:"\u6284\u9001\u4EFB\u52A1",prop:"taskName","min-width":"180"}),e(n,{align:"center",label:"\u6284\u9001\u4EBA",prop:"creatorName","min-width":"100"}),e(n,{align:"center",label:"\u6284\u9001\u65F6\u95F4",prop:"createTime",width:"180",formatter:t(N)},null,8,["formatter"]),e(n,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:l(s=>[e(u,{link:"",type:"primary",onClick:le=>(f=>{const b={id:f.processInstanceId,activityId:void 0};f.activityId&&(b.activityId=f.activityId),x({name:"BpmProcessInstanceDetail",query:b})})(s.row)},{default:l(()=>a[6]||(a[6]=[g("\u8BE6\u60C5")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Y,t(i)]]),e(U,{limit:t(r).pageSize,"onUpdate:limit":a[2]||(a[2]=s=>t(r).pageSize=s),page:t(r).pageNo,"onUpdate:page":a[3]||(a[3]=s=>t(r).pageNo=s),total:t(h),onPagination:p},null,8,["limit","page","total"])]),_:1})],64)}}});export{ae as default};
