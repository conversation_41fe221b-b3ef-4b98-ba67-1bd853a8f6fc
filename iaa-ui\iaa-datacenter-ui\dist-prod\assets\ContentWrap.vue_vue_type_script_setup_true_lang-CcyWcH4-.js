import{d as x,aj as a,h as n,c0 as y,w as s,n as g,u as b,X as u,o,l as t,t as d,k as w,$ as h,bf as r,G as v,bm as _}from"./index-C8b06LRn.js";import{E as k}from"./el-card-CaOo8U9P.js";const j={class:"flex items-center"},C={class:"text-16px font-700"},$={class:"max-w-200px"},S={class:"flex flex-grow pl-20px"},W=x({name:"ContentWrap",__name:"ContentWrap",props:{title:a.string.def(""),message:a.string.def(""),bodyStyle:a.object.def({padding:"10px"})},setup(e){const{getPrefixCls:p}=u(),i=p("content-wrap");return(l,q)=>{const c=v,f=_,m=k;return o(),n(m,{"body-style":e.bodyStyle,class:g([b(i),"mb-15px"]),shadow:"never"},y({default:s(()=>[r(l.$slots,"default")]),_:2},[e.title?{name:"header",fn:s(()=>[t("div",j,[t("span",C,d(e.title),1),e.message?(o(),n(f,{key:0,effect:"dark",placement:"right"},{content:s(()=>[t("div",$,d(e.message),1)]),default:s(()=>[w(c,{size:14,class:"ml-5px",icon:"ep:question-filled"})]),_:1})):h("",!0),t("div",S,[r(l.$slots,"header")])])]),key:"0"}:void 0]),1032,["body-style","class"])}}});export{W as _};
