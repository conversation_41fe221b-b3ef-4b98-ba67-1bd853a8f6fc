import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as A,y as B,j as i,o as G,h as H,w as r,k as d,u as o,v as f,m as x,l as v,dP as L,aV as P,aO as D,es as R,G as q,a9 as E,aa as J,bn as K,H as M}from"./index-C8b06LRn.js";import{d as N}from"./download-D5Lb_h0f.js";const Q={class:"el-upload__tip text-center"},T={class:"el-upload__tip"},W=A({name:"SystemUserImportForm",__name:"UserImportForm",emits:["success"],setup(X,{expose:h,emit:b}){const c=B(),n=i(!1),s=i(!1),_=i(),y=i(),m=i([]),p=i(0);h({open:()=>{n.value=!0,p.value=0,m.value=[],j()}});const g=async()=>{m.value.length!=0?(y.value={Authorization:"Bearer "+L(),"tenant-id":P()},s.value=!0,_.value.submit()):c.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6")},V=b,k=a=>{if(a.code!==0)return c.error(a.msg),void(s.value=!1);const e=a.data;let l="\u4E0A\u4F20\u6210\u529F\u6570\u91CF\uFF1A"+e.createUsernames.length+";";for(let t of e.createUsernames)l+="< "+t+" >";l+="\u66F4\u65B0\u6210\u529F\u6570\u91CF\uFF1A"+e.updateUsernames.length+";";for(const t of e.updateUsernames)l+="< "+t+" >";l+="\u66F4\u65B0\u5931\u8D25\u6570\u91CF\uFF1A"+Object.keys(e.failureUsernames).length+";";for(const t in e.failureUsernames)l+="< "+t+": "+e.failureUsernames[t]+" >";c.alert(l),s.value=!1,n.value=!1,V("success")},w=()=>{c.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),s.value=!1},j=async()=>{var a;s.value=!1,await D(),(a=_.value)==null||a.clearFiles()},C=()=>{c.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},F=async()=>{const a=await R();N.excel(a,"\u7528\u6237\u5BFC\u5165\u6A21\u7248.xls")};return(a,e)=>{const l=q,t=E,z=J,I=K,U=M,O=S;return G(),H(O,{modelValue:o(n),"onUpdate:modelValue":e[3]||(e[3]=u=>x(n)?n.value=u:null),title:"\u7528\u6237\u5BFC\u5165",width:"400"},{footer:r(()=>[d(U,{disabled:o(s),type:"primary",onClick:g},{default:r(()=>e[8]||(e[8]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),d(U,{onClick:e[2]||(e[2]=u=>n.value=!1)},{default:r(()=>e[9]||(e[9]=[f("\u53D6 \u6D88")])),_:1})]),default:r(()=>[d(I,{ref_key:"uploadRef",ref:_,"file-list":o(m),"onUpdate:fileList":e[1]||(e[1]=u=>x(m)?m.value=u:null),action:"https://sj.iaa360.cn:13141/admin-api/system/user/import?updateSupport="+o(p),"auto-upload":!1,disabled:o(s),headers:o(y),limit:1,"on-error":w,"on-exceed":C,"on-success":k,accept:".xlsx, .xls",drag:""},{tip:r(()=>[v("div",Q,[v("div",T,[d(t,{modelValue:o(p),"onUpdate:modelValue":e[0]||(e[0]=u=>x(p)?p.value=u:null)},null,8,["modelValue"]),e[4]||(e[4]=f(" \u662F\u5426\u66F4\u65B0\u5DF2\u7ECF\u5B58\u5728\u7684\u7528\u6237\u6570\u636E "))]),e[6]||(e[6]=v("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),d(z,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:F},{default:r(()=>e[5]||(e[5]=[f(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:r(()=>[d(l,{icon:"ep:upload"}),e[7]||(e[7]=v("div",{class:"el-upload__text"},[f("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),v("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","action","disabled","headers"])]),_:1},8,["modelValue"])}}});export{W as _};
