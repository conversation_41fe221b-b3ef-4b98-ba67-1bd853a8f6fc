import{d as ue,j as v,Y as X,r as Z,au as re,f as pe,aF as y,T as me,o as i,c as g,k as d,l as c,w as s,u as t,m as U,F as ee,g as ae,t as b,h as p,b4 as _,$ as k,v as u,x as L,aO as ve,df as fe,de as le,_ as ye}from"./index-C8b06LRn.js";import{I as R}from"./index-BX2KAvdx.js";import{C as te}from"./claim-BPjhJe4B.js";import ge from"./ClaimMobile-DyyspMXP.js";import{s as he}from"./function-call-DKtMTaHE.js";import"./VDatePicker.vue_vue_type_script_setup_true_lang-fYMpMkPC.js";import"./index-DVzg-3-A.js";const ke={class:"h-[100%]"},xe={class:"fixed top-44px left-0 right-0 z-10 bg-white"},we={key:0},be={class:"h-[14vh] w-[30vh] p-1"},_e={class:"text-sm truncate"},Ce={class:"text-sm truncate"},ze={class:"text-sm truncate"},Ve={class:"text-sm truncate"},Se={class:"text-sm truncate font-bold"},Ue={class:"w-full flex items-center justify-between"},Ne={class:"flex items-center gap-2"},Ae={key:1},Me={class:"w-full flex items-center justify-between"},je={class:"flex items-center gap-2"},Le={class:"flex items-center gap-2"},$e={key:0,class:"flex gap-2"},Ee={key:1},Ie={class:"fixed bottom-10 left-0 right-0 bg-white border-t p-10px flex justify-between items-center"},Oe={key:0},Pe=ye(ue({__name:"Mobile",setup(Fe){const C=v("pending"),D=X(()=>C.value==="mine"),N=v(null),$=v(""),Q=v(!1),n=Z({pageNo:1,pageSize:10,collectionAccount:"",status:[0],isMe:!1,global:""}),x=v([]),O=v(0),o=v(!1),A=v(!1),f=Z({}),T=X(()=>Object.keys(f).filter(l=>f[l]).map(l=>Number(l))),Y=()=>{n.global=$.value,z()},q=v(null),z=()=>{x.value=[],O.value=0,n.pageNo=1,A.value=!1,o.value=!1,se(),ve(()=>{var l;return(l=q.value)==null?void 0:l.scrollTo({top:0})}),P()},se=()=>{for(const l in f)delete f[l]},ie=()=>{if(C.value==="mine")n.status=[1,2],n.isMe=!0;else{n.status=[0],n.isMe=!1;for(const l in f)delete f[l]}$.value="",n.global="",z()};re(()=>n.dateStr,()=>z());const P=async()=>{if(!A.value){o.value=!0;try{let l;l=C.value==="mine"?await te.getClaimPage({pageSize:n.pageSize,pageNo:n.pageNo,global:n.global,type:1}):await R.getInformationPage({...n,pageNo:n.pageNo}),O.value=(l==null?void 0:l.total)||0;const e=(l==null?void 0:l.list)||[],M=new Set(x.value.map(w=>w.id)),V=e.filter(w=>!M.has(w.id));n.pageNo===1?x.value=e:x.value.push(...e),V.forEach(w=>{f[w.id]===void 0&&(f[w.id]=!1)}),o.value=!1,x.value.length>=O.value?A.value=!0:n.pageNo++}catch{o.value=!1,A.value=!0}}},F=v(!1),B=v([]),E=l=>{B.value=l,F.value=!0};pe(()=>{P()});const ne=l=>{let e="";if(l.collectionList&&l.collectionList.length>0){const M=l.collectionList.map(V=>`${V.collectionAccount}-${V.collectionAmount}`).join(", ");M&&(e+=` ${M}`)}return e},G=async l=>{he({title:"\u53D6\u6D88\u8BA4\u9886",message:"\u662F\u5426\u786E\u8BA4\u53D6\u6D88\u8BA4\u9886\u8BE5\u7B14\u6536\u6B3E\uFF1F"}).then(async()=>{o.value=!0,await te.deleteClaim([l]),o.value=!1,fe("\u53D6\u6D88\u6210\u529F"),z()}).catch(()=>{})},H=async l=>{try{const e=await R.getSuspended(l.id);N.value={...e,readOnly:!0},E([l.id])}catch{}};return(l,e)=>{const M=y("van-nav-bar"),V=y("van-tab"),w=y("van-tabs"),oe=y("van-search"),ce=y("van-checkbox"),I=y("van-tag"),h=y("van-button"),J=y("van-card"),K=y("van-list"),W=y("van-pull-refresh"),j=me("hasPermi");return i(),g("div",ke,[d(M,{title:"\u6536\u6B3E\u4FE1\u606F\u767B\u8BB0",fixed:""}),c("div",xe,[d(w,{active:t(C),"onUpdate:active":e[0]||(e[0]=a=>U(C)?C.value=a:null),onChange:ie},{default:s(()=>[d(V,{title:"\u5F85\u8BA4\u9886",name:"pending"}),d(V,{title:"\u6211\u7684",name:"mine"})]),_:1},8,["active"]),d(oe,{modelValue:t($),"onUpdate:modelValue":e[1]||(e[1]=a=>U($)?$.value=a:null),onSearch:Y,onCancel:Y,placeholder:"\u8BF7\u8F93\u5165\u641C\u7D22\u5173\u952E\u8BCD"},null,8,["modelValue"])]),c("div",{ref_key:"scrollEl",ref:q,class:"h-[calc(100vh-230px)] overflow-auto px-10px mt-100px"},[t(C)==="pending"?(i(),g("div",we,[d(W,{modelValue:t(o),"onUpdate:modelValue":e[4]||(e[4]=a=>U(o)?o.value=a:null),onRefresh:z},{default:s(()=>[d(K,{loading:t(o),"onUpdate:loading":e[3]||(e[3]=a=>U(o)?o.value=a:null),finished:t(A),"immediate-check":!0,"finished-text":"\u6CA1\u6709\u66F4\u591A\u6570\u636E\u4E86",onLoad:P},{default:s(()=>[(i(!0),g(ee,null,ae(t(x),a=>(i(),g("div",{key:a.id,class:"mb-10px"},[d(J,{title:a.collectionAccount,currency:"",class:"rounded-lg shadow-sm"},{title:s(()=>[c("div",be,[c("div",_e,"\u4ED8\u6B3E\u4EBA\uFF1A"+b(a.payer),1),c("div",Ce,"\u6536\u6B3E\u8D26\u6237\uFF1A"+b(a.collectionAccount),1),c("div",ze,"\u6536\u6B3E\u5E01\u79CD\uFF1A"+b(a.currency),1),c("div",Ve,"\u6536\u6B3E\u65E5\u671F\uFF1A"+b(a.dateStr),1),c("div",Se,"\u6536\u6B3E\u91D1\u989D\uFF1A"+b(a.collectionAmount),1)])]),footer:s(()=>[c("div",Ue,[t(n).isMe?k("",!0):(i(),p(ce,{key:0,modelValue:t(f)[a.id],"onUpdate:modelValue":m=>t(f)[a.id]=m,onClick:e[2]||(e[2]=_(()=>{},["stop"])),"icon-size":"18px"},{default:s(()=>e[9]||(e[9]=[c("span",{class:"text-sm"},"\u9009\u62E9",-1)])),_:2},1032,["modelValue","onUpdate:modelValue"])),c("div",Ne,[t(n).isMe&&a.status===1?(i(),p(I,{key:0,type:"primary"},{default:s(()=>e[10]||(e[10]=[u("\u8BA4\u9886\u4E2D")])),_:1})):k("",!0),a.status===2?(i(),p(I,{key:1,type:"success"},{default:s(()=>e[11]||(e[11]=[u("\u5DF2\u8BA4\u9886")])),_:1})):k("",!0),t(n).isMe?a.status===2?(i(),p(h,{key:3,size:"small",round:"",type:"default",onClick:_(m=>H(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[13]||(e[13]=[u("\u8BE6\u60C5")])),_:2},1032,["onClick"])):(i(),p(h,{key:4,size:"small",round:"",type:"default",disabled:"",class:"min-w-60px"},{default:s(()=>e[14]||(e[14]=[u("\u4E0D\u53EF\u8BA4\u9886")])),_:1})):L((i(),p(h,{key:2,size:"small",round:"",type:"primary",onClick:_(m=>(r=>{N.value=null,E([r.id])})(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[12]||(e[12]=[u("\u8BA4\u9886")])),_:2},1032,["onClick"])),[[j,["collection:information:claim"]]])])])]),_:2},1032,["title"])]))),128))]),_:1},8,["loading","finished"])]),_:1},8,["modelValue"])])):(i(),g("div",Ae,[d(W,{modelValue:t(o),"onUpdate:modelValue":e[6]||(e[6]=a=>U(o)?o.value=a:null),onRefresh:z},{default:s(()=>[d(K,{loading:t(o),"onUpdate:loading":e[5]||(e[5]=a=>U(o)?o.value=a:null),finished:t(A),"immediate-check":!0,"finished-text":"\u6CA1\u6709\u66F4\u591A\u6570\u636E\u4E86",onLoad:P},{default:s(()=>[(i(!0),g(ee,null,ae(t(x),a=>(i(),g("div",{key:a.id,class:"mb-10px"},[d(J,{title:ne(a),desc:`\u65E5\u671F\uFF1A${a.claimDate}`,price:String(a.totalAmount),currency:"",class:"rounded-lg shadow-sm"},{tags:s(()=>[d(I,{plain:"",type:"danger"},{default:s(()=>[u(b(a.currency),1)]),_:2},1024)]),footer:s(()=>[c("div",Me,[c("div",je,[a.status===2?(i(),p(I,{key:0,type:"primary"},{default:s(()=>e[15]||(e[15]=[u("\u8BA4\u9886\u4E2D")])),_:1})):k("",!0),a.status===0?(i(),p(I,{key:1,type:"success"},{default:s(()=>e[16]||(e[16]=[u("\u5DF2\u8BA4\u9886")])),_:1})):k("",!0)]),c("div",Le,[a.status===2?(i(),g("span",$e,[L((i(),p(h,{size:"small",round:"",type:"primary",onClick:_(m=>(async r=>{try{const S=await R.getSuspended(r.id);N.value=S||null,E([r.id])}catch{}})(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[17]||(e[17]=[u("\u7EE7\u7EED")])),_:2},1032,["onClick"])),[[j,["collection:information:claim"]]]),L((i(),p(h,{size:"small",round:"",type:"danger",onClick:_(m=>G(a.id),["stop"]),class:"min-w-60px"},{default:s(()=>e[18]||(e[18]=[u("\u53D6\u6D88")])),_:2},1032,["onClick"])),[[j,["collection:information:cancel"]]])])):k("",!0),a.status===0?(i(),g("div",Ee,[L((i(),p(h,{size:"small",round:"",type:"primary",onClick:_(m=>(async r=>{try{Q.value=!0;const S=await R.getSuspended(r.id);N.value=S||null,E([r.id])}catch{}})(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[19]||(e[19]=[u("\u4FEE\u6539")])),_:2},1032,["onClick"])),[[j,["record:money:admin"]]]),d(h,{size:"small",round:"",type:"default",onClick:_(m=>H(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[20]||(e[20]=[u("\u8BE6\u60C5")])),_:2},1032,["onClick"]),L((i(),p(h,{size:"small",round:"",type:"danger",onClick:_(m=>G(a.id),["stop"]),class:"min-w-60px"},{default:s(()=>e[21]||(e[21]=[u("\u53D6\u6D88")])),_:2},1032,["onClick"])),[[j,["collection:information:cancel"]]])])):k("",!0)])])]),_:2},1032,["title","desc","price"])]))),128))]),_:1},8,["loading","finished"])]),_:1},8,["modelValue"])]))],512),c("div",Ie,[c("div",null,"\u5171 "+b(t(O))+" \u6761",1),t(D)?k("",!0):(i(),g("div",Oe,"\u5DF2\u9009 "+b(t(T).length)+" \u6761",1)),t(D)?k("",!0):L((i(),p(h,{key:1,type:"success",size:"small",disabled:t(T).length===0,onClick:e[7]||(e[7]=a=>(async m=>{if(m.length===0)return void le("\u8BF7\u9009\u62E9\u6536\u6B3E\u8BB0\u5F55");(r=>{if(r.length===0)return!0;const S=r[0].currency;return r.every(de=>de.currency===S)})(x.value.filter(r=>m.includes(r.id)))?(N.value=null,E(m)):le("\u6240\u9009\u6536\u6B3E\u8BB0\u5F55\u5E01\u79CD\u4E0D\u4E00\u81F4\uFF0C\u65E0\u6CD5\u6279\u91CF\u8BA4\u9886")})(t(T)))},{default:s(()=>e[22]||(e[22]=[u("\u6279\u91CF\u8BA4\u9886")])),_:1},8,["disabled"])),[[j,["collection:information:claim"]]])]),d(ge,{show:t(F),"onUpdate:show":e[8]||(e[8]=a=>U(F)?F.value=a:null),ids:t(B),prefill:t(N),isEdit:t(Q),onSuccess:z},null,8,["show","ids","prefill","isEdit"])])}}}),[["__scopeId","data-v-88bbfc55"]]);export{Pe as default};
