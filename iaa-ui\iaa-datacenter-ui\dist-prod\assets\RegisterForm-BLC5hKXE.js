import{d as C,j as p,b as $,Y as j,r as G,au as O,f as Y,x as Z,Z as A,u as e,o as _,h as F,w as s,k as r,$ as D,p as H,a as J,aV as K,a_ as M,a5 as Q,a7 as W,a2 as X,ac as ee,ad as V,aU as ae,B as re,a8 as te,S as se,ab as le,I as ie,_ as oe}from"./index-C8b06LRn.js";import{_ as ne}from"./Verify-BrbFSGD0.js";import{_ as de}from"./XButton-BOgar_Ex.js";import{u as pe,_ as me,L as ge}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";import{u as c}from"./useIcon-CwemBubV.js";const ue=oe(C({name:"RegisterForm",__name:"RegisterForm",setup(ce){const{t:d}=H(),k=c({icon:"ep:house"}),f=c({icon:"ep:avatar"}),h=c({icon:"ep:lock"}),v=p(),{handleBackLogin:E,getLoginState:z}=pe(),{currentRoute:N,push:P}=J(),q=$(),m=p(""),w=p(!1),x=p(),U=p("blockPuzzle"),R=j(()=>e(z)===ge.REGISTER),S={tenantName:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u6240\u5C5E\u7684\u79DF\u6237"},{min:2,max:20,message:"\u79DF\u6237\u8D26\u53F7\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 2 \u548C 20 \u4E4B\u95F4",trigger:"blur"}],username:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u8D26\u53F7"},{min:4,max:30,message:"\u7528\u6237\u8D26\u53F7\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 4 \u548C 30 \u4E4B\u95F4",trigger:"blur"}],nickname:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u6635\u79F0"},{min:0,max:30,message:"\u6635\u79F0\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 0 \u548C 30 \u4E4B\u95F4",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u5BC6\u7801"},{min:5,max:20,message:"\u7528\u6237\u5BC6\u7801\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 5 \u548C 20 \u4E4B\u95F4",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`\u4E0D\u80FD\u5305\u542B\u975E\u6CD5\u5B57\u7B26\uFF1A< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"\u8BF7\u518D\u6B21\u8F93\u5165\u60A8\u7684\u5BC6\u7801"},{required:!0,validator:(l,a,o)=>{t.registerForm.password!==a?o(new Error("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u4E00\u81F4")):o()},trigger:"blur"}]},t=G({isShowPassword:!1,captchaEnable:"true",tenantEnable:"false",registerForm:{tenantName:"\u828B\u9053\u6E90\u7801",nickname:"",tenantId:0,username:"",password:"",confirmPassword:"",captchaVerification:""}}),y=async l=>{u.value=!0;try{t.tenantEnable&&(await I(),t.registerForm.tenantId=K()),t.captchaEnable&&(t.registerForm.captchaVerification=l.captchaVerification);const a=await M(t.registerForm);if(!a)return;u.value=Q.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),W(),X(a),m.value||(m.value="/"),m.value.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):P({path:m.value||q.addRouters[0].path})}finally{w.value=!1,u.value.close()}},I=async()=>{if(t.tenantEnable==="true"){const l=await ee(t.registerForm.tenantName);V(l)}},u=p();return O(()=>N.value,l=>{var a;m.value=(a=l==null?void 0:l.query)==null?void 0:a.redirect},{immediate:!0}),Y(()=>{(async()=>{const l=location.host,a=await ae(l);a&&(t.registerForm.tenantName=a.name,V(a.id))})()}),(l,a)=>{const o=re,n=te,g=se,b=de,L=ne,T=le,B=ie;return Z((_(),F(B,{ref_key:"formLogin",ref:v,model:e(t).registerForm,rules:S,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:s(()=>[r(T,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:s(()=>[r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,null,{default:s(()=>[r(me,{style:{width:"100%"}})]),_:1})]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[e(t).tenantEnable==="true"?(_(),F(o,{key:0,prop:"tenantName"},{default:s(()=>[r(g,{modelValue:e(t).registerForm.tenantName,"onUpdate:modelValue":a[0]||(a[0]=i=>e(t).registerForm.tenantName=i),placeholder:e(d)("login.tenantname"),"prefix-icon":e(k),link:"",type:"primary",size:"large"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):D("",!0)]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,{prop:"username"},{default:s(()=>[r(g,{modelValue:e(t).registerForm.username,"onUpdate:modelValue":a[1]||(a[1]=i=>e(t).registerForm.username=i),placeholder:e(d)("login.username"),size:"large","prefix-icon":e(f)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,{prop:"username"},{default:s(()=>[r(g,{modelValue:e(t).registerForm.nickname,"onUpdate:modelValue":a[2]||(a[2]=i=>e(t).registerForm.nickname=i),placeholder:"\u6635\u79F0",size:"large","prefix-icon":e(f)},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,{prop:"password"},{default:s(()=>[r(g,{modelValue:e(t).registerForm.password,"onUpdate:modelValue":a[3]||(a[3]=i=>e(t).registerForm.password=i),type:"password","auto-complete":"off",placeholder:e(d)("login.password"),size:"large","prefix-icon":e(h),"show-password":""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,{prop:"confirmPassword"},{default:s(()=>[r(g,{modelValue:e(t).registerForm.confirmPassword,"onUpdate:modelValue":a[4]||(a[4]=i=>e(t).registerForm.confirmPassword=i),type:"password",size:"large","auto-complete":"off",placeholder:e(d)("login.checkPassword"),"prefix-icon":e(h),"show-password":""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),r(n,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:s(()=>[r(o,null,{default:s(()=>[r(b,{loading:e(w),title:e(d)("login.register"),class:"w-[100%]",type:"primary",onClick:a[5]||(a[5]=i=>(async()=>{t.captchaEnable==="false"?await y({}):x.value.show()})())},null,8,["loading","title"])]),_:1})]),_:1}),r(L,{ref_key:"verify",ref:x,captchaType:e(U),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:y},null,8,["captchaType"])]),_:1}),r(b,{title:e(d)("login.hasUser"),class:"w-[100%]",onClick:a[6]||(a[6]=i=>e(E)())},null,8,["title"])]),_:1},8,["model"])),[[A,e(R)]])}}}),[["__scopeId","data-v-4cbabf13"]]);export{ue as default};
