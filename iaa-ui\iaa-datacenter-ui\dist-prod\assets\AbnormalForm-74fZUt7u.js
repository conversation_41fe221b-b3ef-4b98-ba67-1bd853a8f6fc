import{d as Ee,y as xe,j as _,e as Re,Y as Ue,r as Ie,dm as We,au as $e,bj as Ae,T as le,x as oe,Z as De,o as h,c as V,h as A,w as r,l as d,t as u,k as t,v as S,u as Fe,$ as z,F as Be,g as Me,b4 as ze,aK as Ke,M as te,aO as je,H as qe,a9 as Je,G as Ye,B as Le,a8 as Ge,c3 as He,S as Pe,ab as Xe,I as Ze,J as Qe,K as ea,cy as aa,_ as la}from"./index-C8b06LRn.js";import{_ as oa}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as ta}from"./el-card-CaOo8U9P.js";import{E as na}from"./el-time-select-BrN8x4_E.js";import{D as R}from"./index-SUTJ2GLg.js";const ra={class:"dialogContainer"},da={key:0,style:{"margin-bottom":"10px"}},ma={style:{"margin-left":"10px",color:"#666"}},sa={key:1,class:"mobile-batch-container"},ua={class:"batch-assignment-sticky"},ia={key:2,class:"card-list-container"},pa={class:"card-title"},ca={key:0,class:"mobile-body-info"},ba={class:"info-row"},fa={class:"info-value"},va={class:"info-row"},ha={class:"info-value"},ga={class:"info-row"},ya={class:"info-value"},ka={class:"info-row"},Na={class:"info-value"},Va={class:"info-row"},wa={class:"info-value"},Ca={class:"info-row"},Ta={class:"info-value"},_a={class:"info-row"},Oa={class:"info-value"},Sa={class:"info-row"},Ea={class:"info-value"},xa={class:"info-row"},Ra={class:"info-value"},Ua={class:"info-row"},Ia={class:"info-value"},Wa={class:"info-row"},$a={class:"info-value"},Aa={class:"info-row"},Da={class:"info-value"},Fa={class:"time-range-row"},Ba={key:0},Ma={key:1},za={class:"time-range-row"},Ka={key:0},ja={class:"autocomplete-item"},qa={class:"main-text"},Ja={key:1},Ya={key:0},La={class:"autocomplete-item"},Ga={class:"main-text"},Ha={key:1},Pa={key:0},Xa={class:"autocomplete-item"},Za={class:"main-text"},Qa={key:1},el={key:0},al={class:"autocomplete-item"},ll={class:"main-text"},ol={key:1},tl={key:0},nl={class:"autocomplete-item"},rl={class:"main-text"},dl={key:1},ml={key:0},sl={class:"autocomplete-item"},ul={class:"main-text"},il={key:1},pl={class:"time-range-row"},cl={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"100%"}},bl={style:{"font-size":"14px",color:"#333"}},fl={class:"time-range-row"},vl=la(Ee({__name:"AbnormalForm",emits:["success"],setup(hl,{expose:ne,emit:re}){const y=xe(),U=_(!1),Y=_(""),E=_(!1),de=Re(),D=Ue(()=>de.getMobile),me=Ie({number:[{required:!0,message:"\u8BF7\u8F93\u5165\u4EBA\u6570",trigger:"blur"}],abnormalFormTimeStart:[{required:!0,message:"\u8BF7\u9009\u62E9\u8D77\u59CB\u65F6\u95F4",trigger:"change"}],abnormalFormTimeEnd:[{required:!0,message:"\u8BF7\u9009\u62E9\u7ED3\u675F\u65F6\u95F4",trigger:"change"}]}),se=_();We();const I=o=>{const e=new Date,[l,m]=o.split(":").map(Number);return new Date(e.getFullYear(),e.getMonth(),e.getDate(),l,m)},ue=o=>{const e=[{start:"12:00",end:"13:00"},{start:"17:30",end:"18:00"}];for(const l of e){const m=I(l.start),s=I(l.end);if(o>=m&&o<s)return!0}return!1},L=_(0),f=_([]);$e(f,o=>{const e=new Map;o.forEach(l=>{if(l.abnormalTimeStart!==void 0&&l.abnormalTimeEnd!==void 0&&l.abnormalNum>0){const m=((s,g)=>{const p=I(s),v=I(g);if(v<=p)return 0;let w=0,k=new Date(p);for(;k<v;){const c=new Date(k.getTime()+6e4),O=c>v?v:c;ue(k)||(w+=(O.getTime()-k.getTime())/6e4),k=O}return w})(l.abnormalTimeStart,l.abnormalTimeEnd);l.abnormalWork=parseFloat((m/60*(l.abnormalNum||0)).toFixed(3))}if(l.abnormalRemark&&l.abnormalTimeStart&&l.abnormalTimeEnd&&l.abnormalNum>0){const m=`${`${l.abnormalTimeStart}-${l.abnormalTimeEnd}`}_${l.abnormalRemark.trim()}`;e.has(m)||e.set(m,l.abnormalWork||0)}}),L.value=Array.from(e.values()).reduce((l,m)=>l+m,0)},{deep:!0,immediate:!0});const N=_([]),ie=_(),pe=o=>{N.value=o},F=(o,e,l)=>{if(!o)return void e([]);const m={[l===0?"productionOrderCode":"salesOrderCode"]:"\u52A0\u8F7D\u4E2D...",loading:!0},s={[l===0?"productionOrderCode":"salesOrderCode"]:"\u6682\u65E0\u6570\u636E",noData:!0},g={[l===0?"productionOrderCode":"salesOrderCode"]:"\u67E5\u8BE2\u5931\u8D25",error:!0};e([m]);const p={docNo:o,type:l};R.getWorkSalesOptions(p).then(v=>{const w=v||[];e(w.length>0?w:[s])}).catch(v=>{e([g])})},G=async(o,e)=>{const l=f.value[e];l&&o&&(l.productionOrderCode=o.productionOrderCode,l.salesOrderCode=o.salesOrderCode,l.productNo=o.productNo,l.modelsOrColor=o.modelsOrColor,l.workOrderNum=o.workOrderNum,l.units=o.units,l.salesman=o.salesman)},H=(o,e,l,m)=>{if(!o)return void e([]);e([{productNo:"\u52A0\u8F7D\u4E2D...",loading:!0}]);const s={productionOrderCode:l,salesOrderCode:m,productNo:o};R.getProductNo(s).then(g=>{const p=g||[];e(p.length>0?p:[{productNo:"\u6682\u65E0\u6570\u636E",noData:!0}])}).catch(g=>{e([{productNo:"\u67E5\u8BE2\u5931\u8D25",error:!0}])})},B=async(o,e)=>{const l=f.value[e];l.productionOrderCode=o.productionOrderCode,l.salesOrderCode=o.salesOrderCode,l.productNo=o.productNo,l.modelsOrColor=o.modelsOrColor,l.workOrderNum=o.workOrderNum,l.units=o.units,l.salesman=o.salesman},P=()=>{const o=ve();f.value.push(o)},K=_(!1),ce=()=>{const o=!K.value;K.value=o,f.value.forEach(e=>{e._checked=o}),N.value=o?[...f.value]:[]},be=()=>{N.value.length!==0?(T.value={abnormalTimeStart:"",abnormalTimeEnd:"",abnormalRemark:"",abnormalCountermeasures:"",abnormalNum:0,abnormalWork:0},$.value=!0):y.error("\u8BF7\u5148\u9009\u62E9\u884C")},T=_({abnormalTimeStart:"",abnormalTimeEnd:"",abnormalRemark:"",abnormalCountermeasures:"",abnormalNum:0,abnormalWork:0}),fe=()=>{const{abnormalTimeStart:o,abnormalTimeEnd:e,abnormalRemark:l,abnormalCountermeasures:m,abnormalNum:s,abnormalWork:g}=T.value;if(o&&e){const p=I(o);if(I(e)<=p)return void y.error("\u7ED3\u675F\u65F6\u95F4\u5FC5\u987B\u665A\u4E8E\u5F00\u59CB\u65F6\u95F4")}N.value.forEach(p=>{o!==""&&(p.abnormalTimeStart=o),e!==""&&(p.abnormalTimeEnd=e),l!==""&&(p.abnormalRemark=l),m!==""&&(p.abnormalCountermeasures=m),s!==null&&(p.abnormalNum=s),g!==null&&(p.abnormalWork=g)}),y.success(`\u5DF2\u5BF9 ${N.value.length} \u884C\u8FDB\u884C\u6279\u91CF\u8D4B\u503C`),$.value=!1},$=_(!1),ve=()=>({dateStr:j.value,productionLine:q.value,productionTime:"",productionTimeStart:"",productionTimeEnd:"",abnormalTime:"",abnormalTimeStart:"",abnormalTimeEnd:"",productionOrderCode:"",salesOrderCode:"",productNo:"",modelsOrColor:"",workOrderNum:"",units:"",abnormalRemark:"",abnormalCountermeasures:"",abnormalNum:0,abnormalWork:0,displayAbnormal:0,isNew:!0,isEditing:!0}),X=o=>{y.confirm("\u786E\u5B9A\u8981\u7B2C"+(o+1)+"\u884C\u5220\u9664\u5417\uFF1F").then(()=>{if(f.value[o].isRework===1||f.value[o].isTrial===1)return y.alert("\u8BE5\u5F02\u5E38\u4E3A\u8FD4\u5DE5/\u8BD5\u4EA7\uFF0C\u8BF7\u56DE\u5230\u4E0A\u4E00\u9875\u9762\u53D6\u6D88\u9009\u4E2D\u5373\u53EF\uFF01"),!1;R.updateDisplayAbnormal([f.value[o].id]),f.value.splice(o,1)})},M=_(),j=_(),q=_();ne({open,openForm:async(o,e,l,m,s)=>{var k;M.value=l,U.value=!0,Y.value=e,j.value=m,q.value=s;let g=[];((k=o==null?void 0:o.value)==null?void 0:k.length)>0&&(g=o.value.map(c=>{const O=c.abnormalReportNum===null||c.abnormalReportNum===void 0||c.abnormalReportNum===0?c.hoursReportNum:c.abnormalReportNum;return{...c,isNew:!1,isOldNew:!1,isEditing:!1,workType:0,abnormalReportNum:O}}));const p=await R.getAbnormalByBatchesId(l);let v=[];p.length>0&&(v=p.map(c=>{const[O="",b=""]=(c.productionTime||"").split("-"),[W="",J=""]=(c.abnormalTime||"").split("-");return{...c,productionTimeStart:O,productionTimeEnd:b,abnormalTimeStart:W,abnormalTimeEnd:J,isNew:!0,isOldNew:!1}}));let w=[...g];for(const c of v){const O=w.findIndex(b=>b.productionOrderCode===c.productionOrderCode&&b.salesOrderCode===c.salesOrderCode&&b.productNo===c.productNo);O>-1?(c.isNew=!1,c.isOldNew=!0,w.splice(O+1,0,c)):w.push(c)}f.value=w.filter(c=>c.displayAbnormal===0),N.value=[]}});const he=re,ge=async()=>{if(E.value)y.warning("\u6570\u636E\u63D0\u4EA4\u4E2D\uFF0C\u8BF7\u52FF\u91CD\u590D\u70B9\u51FB");else{E.value=!0;try{let o=!0;for(let e=0;e<f.value.filter(l=>l.displayAbnormal!==1).length;e++){const l=f.value.filter(m=>m.displayAbnormal!==1)[e];if(l.isOldNew&&l.id===void 0&&(!l.abnormalTimeStart||!l.abnormalTimeEnd)){y.alert(`\u7B2C ${e+1} \u884C\u5F02\u5E38\u65F6\u95F4\u6BB5\u672A\u586B\u5199\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}if(l.abnormalRemark&&l.abnormalRemark.trim()!==""||l.abnormalCountermeasures&&l.abnormalCountermeasures.trim()!==""){if(l.abnormalNum===void 0||l.abnormalNum===null||l.abnormalNum<=0){y.alert(`\u7B2C ${e+1} \u884C\uFF0C\u4EBA\u6570\u5FC5\u987B\u5927\u4E8E0\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}if(l.abnormalReportNum===void 0||l.abnormalReportNum===null||l.abnormalReportNum<=0){y.alert(`\u7B2C ${e+1} \u884C\uFF0C\u5F02\u5E38\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}if(!l.abnormalTimeStart||!l.abnormalTimeEnd){y.alert(`\u7B2C ${e+1} \u884C\u4EBA\u6570\u5927\u4E8E0\uFF0C\u5F02\u5E38\u65F6\u95F4\u6BB5\u672A\u586B\u5199\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}}if(l.abnormalNum!=null&&l.abnormalNum!=null&&l.abnormalNum>0){if(!l.abnormalTimeStart||!l.abnormalTimeEnd){y.alert(`\u7B2C ${e+1} \u884C\u5F02\u5E38\u65F6\u95F4\u6BB5\u672A\u586B\u5199\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}if(!l.abnormalRemark||!l.abnormalCountermeasures){y.alert(`\u7B2C ${e+1} \u884C\u95EE\u9898\u70B9\u548C\u4E34\u65F6\u5BF9\u7B56\u672A\u586B\u5199\uFF0C\u8BF7\u68C0\u67E5\uFF01`),o=!1;break}}}if(o){const e=f.value.filter(m=>(m.isNew||m.isOldNew)&&m.displayAbnormal===0),l=e.filter(m=>m.abnormalRemark&&m.abnormalRemark.trim()!==""||m.abnormalCountermeasures&&m.abnormalCountermeasures.trim()!==""||m.abnormalNum!==void 0&&m.abnormalNum!==null&&m.abnormalNum>0||m.abnormalTimeStart&&m.abnormalTimeEnd||m.abnormalReportNum!==void 0&&m.abnormalReportNum!==null&&m.abnormalReportNum>0);if(e.length>0){if(l.length===0)return void y.alert("\u65B0\u589E\u5F02\u5E38\u5DE5\u65F6\u6570\u636E\u4E0D\u5408\u6CD5\uFF0C\u8BF7\u91CD\u65B0\u63D0\u4EA4\uFF01");l.forEach(s=>{s.dateStr=j.value,s.productionLine=q.value,s.batchesId=M.value,s.workType=1,s.isAbnormal=0,s.productionTime=s.productionTimeStart+"-"+s.productionTimeEnd,s.abnormalTime=s.abnormalTimeStart+"-"+s.abnormalTimeEnd,s.abnormalReportNum=s.abnormalReportNum});const m=await R.createAbnormalDay(l.filter(s=>s.displayAbnormal===0));f.value.forEach(s=>{s.batchesId=m})}else{const m=[{batchesId:M.value,isAbnormal:1}];await R.createAbnormalDay(m),f.value.forEach(s=>{s.batchesId=M.value})}U.value=!1,he("success",f.value),y.success("\u64CD\u4F5C\u6210\u529F")}}finally{E.value=!1}}},Z=(o,e,l)=>{if(o.key==="Enter"&&!o.shiftKey){o.preventDefault();const m=o.target,s=m.selectionStart,g=e[l]||"",p=g.split(`
`);let v=0;for(let b=p.length-1;b>=0;b--){const W=p[b].match(/^(\d+)\.\s*/);if(W){v=parseInt(W[1]);break}}const w=`
${v+1}. `,k=g.substring(0,s),c=g.substring(s),O=k+w+c;e[l]=O,je(()=>{const b=s+w.length;m.setSelectionRange(b,b),m.focus()})}},Q=(o,e)=>{o[e]&&o[e].trim()!==""||(o[e]="1. ")},x=_(null),ye=_(null),ke=(o,e,l)=>{x.value=o,ye.value=e;const m=document.getElementById("contextMenu");m&&(l.preventDefault(),m.style.left=`${l.clientX+10}px`,m.style.top=l.clientY-10+"px",m.style.display="block")},Ne=()=>{const o=document.getElementById("contextMenu");o&&(o.style.display="none")},Ve=()=>{N.value.length!==0?x.value?y.confirm("\u786E\u5B9A\u8981\u6279\u91CF\u8D4B\u503C\u9009\u4E2D\u7684\u884C\u5417\uFF1F").then(()=>{const o={abnormalRemark:x.value.abnormalRemark,abnormalCountermeasures:x.value.abnormalCountermeasures,abnormalNum:x.value.abnormalNum,abnormalWork:x.value.abnormalWork,abnormalTimeStart:x.value.abnormalTimeStart,abnormalTimeEnd:x.value.abnormalTimeEnd};N.value.forEach(l=>{l.abnormalRemark=o.abnormalRemark,l.abnormalCountermeasures=o.abnormalCountermeasures,l.abnormalNum=o.abnormalNum,l.abnormalWork=o.abnormalWork,l.abnormalTimeStart=o.abnormalTimeStart,l.abnormalTimeEnd=o.abnormalTimeEnd}),y.success(`\u5DF2\u5C06\u9009\u4E2D\u7684 ${N.value.length} \u884C\u6279\u91CF\u8D4B\u503C`);const e=document.getElementById("contextMenu");e&&(e.style.display="none")}):y.error("\u8BF7\u5148\u53F3\u952E\u9009\u62E9\u4E00\u4E2A\u5355\u5143\u683C"):y.error("\u8BF7\u5148\u9009\u62E9\u884C")},ee=()=>{N.value.length!==0?y.confirm("\u786E\u5B9A\u8981\u590D\u5236\u9009\u4E2D\u7684\u884C\u5417\uFF1F").then(()=>{const o=[];N.value.forEach(l=>{const m=f.value.findIndex(s=>s===l);if(m>-1){const s=JSON.parse(JSON.stringify(l));s.type=0,s.isOldNew=!0,s.id=void 0,o.push({index:m+1,row:s})}}),o.sort((l,m)=>m.index-l.index).forEach(({index:l,row:m})=>{f.value.splice(l,0,m)}),y.success(`\u5DF2\u590D\u5236 ${N.value.length} \u884C\uFF0C\u5E76\u63D2\u5165\u5230\u5BF9\u5E94\u884C\u4E0B\u65B9`);const e=document.getElementById("contextMenu");e&&(e.style.display="none")}):y.error("\u8BF7\u5148\u9009\u62E9\u884C")},ae=()=>{N.value.length!==0?y.confirm("\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u884C\u5417\uFF1F").then(()=>{if(N.value.filter(l=>l.isRework===1||l.isTrial===1).length>0)return void y.alert("\u9009\u4E2D\u7684\u884C\u4E2D\u5305\u542B\u8FD4\u5DE5/\u8BD5\u4EA7\u7684\u5F02\u5E38\u6570\u636E\uFF0C\u65E0\u6CD5\u5220\u9664\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\uFF01");const o=N.value.filter(l=>l.id!=null&&l.id!=null).map(l=>l.id);o&&R.updateDisplayAbnormal(o),f.value=f.value.filter(l=>!N.value.includes(l)),N.value=[];const e=document.getElementById("contextMenu");e&&(e.style.display="none")}):y.error("\u8BF7\u5148\u9009\u62E9\u884C")};return Ae(()=>{E.value=!1}),(o,e)=>{const l=qe,m=Je,s=Ye,g=na,p=Le,v=Ge,w=He,k=Pe,c=Xe,O=Ze,b=Qe,W=ea,J=ta,we=oa,Ce=aa,Te=le("dialogDrag"),_e=le("resizer");return oe((h(),V("div",ra,[oe((h(),A(we,{title:Y.value,modelValue:U.value,"onUpdate:modelValue":e[3]||(e[3]=a=>U.value=a),width:D.value?"100%":"80%",fullscreen:""},{footer:r(()=>[d("div",cl,[d("span",bl,"\u5F02\u5E38\u5206\u7EC4\u603B\u5DE5\u65F6\uFF1A"+u(L.value),1),d("div",null,[t(l,{onClick:ge,type:"primary",disabled:E.value,loading:E.value},{default:r(()=>[S(u(E.value?"\u63D0\u4EA4\u4E2D...":"\u786E \u5B9A"),1)]),_:1},8,["disabled","loading"]),t(l,{onClick:e[2]||(e[2]=a=>U.value=!1),disabled:E.value},{default:r(()=>e[33]||(e[33]=[S("\u53D6 \u6D88")])),_:1},8,["disabled"])])])]),default:r(()=>[t(O,{ref_key:"formRef",ref:se,model:f.value,rules:Fe(me),"label-width":"120px"},{default:r(()=>[t(J,{class:"mb-4",shadow:"never"},{default:r(()=>[D.value?z("",!0):(h(),V("div",da,[t(l,{onClick:P,type:"primary",size:"small"},{default:r(()=>e[12]||(e[12]=[S(" \u65B0\u589E ")])),_:1}),d("span",ma," \u5DF2\u9009\u62E9 "+u(N.value.length)+" \u9879 ",1)])),D.value?(h(),V("div",sa,[d("div",ua,[t(l,{onClick:ce,size:"small"},{default:r(()=>[S(u(K.value?"\u53D6\u6D88":"\u5168\u9009"),1)]),_:1}),t(l,{onClick:be,type:"primary",size:"small",style:{width:"65px"}},{default:r(()=>e[13]||(e[13]=[S("\u6279\u91CF\u8D4B\u503C")])),_:1}),t(l,{onClick:ee,type:"info",size:"small",style:{width:"65px"}},{default:r(()=>e[14]||(e[14]=[S("\u63D2\u5165\u9009\u4E2D")])),_:1}),t(l,{onClick:ae,type:"danger",size:"small",style:{width:"65px"}},{default:r(()=>e[15]||(e[15]=[S("\u5220\u9664\u9009\u4E2D")])),_:1})])])):z("",!0),D.value?(h(),V("div",ia,[(h(!0),V(Be,null,Me(f.value,(a,i)=>(h(),V("div",{key:i,class:"card-item"},[d("div",pa,[t(m,{modelValue:a._checked,"onUpdate:modelValue":n=>a._checked=n,onClick:e[0]||(e[0]=ze(()=>{},["stop"])),style:{top:"2px",padding:"0",height:"0"},onChange:n=>((C,Oe)=>{Oe?N.value.includes(C)||N.value.push(C):N.value=N.value.filter(Se=>Se!==C)})(a,!!n)},null,8,["modelValue","onUpdate:modelValue","onChange"]),S(" \u7B2C "+u(i+1)+" \u6761 / \u5171 "+u(f.value.length)+" \u6761 ",1),d("span",null,[t(l,{style:{padding:"0",color:"#fff"},link:"",onClick:P},{default:r(()=>[t(s,{icon:"ep:plus"})]),_:1}),a.isEditing?z("",!0):(h(),A(l,{key:0,style:{padding:"0",color:"#fff"},link:"",onClick:n=>(C=>{f.value[C].isEditing=!0})(i)},{default:r(()=>[t(s,{icon:"ep:edit"})]),_:2},1032,["onClick"])),t(l,{style:{padding:"0",color:"#fff"},link:"",onClick:n=>X(i)},{default:r(()=>[t(s,{icon:"ep:delete"})]),_:2},1032,["onClick"])])]),a.isEditing?(h(),A(O,{key:1,class:"mobile-body-form"},{default:r(()=>[t(c,null,{default:r(()=>[a.isNew?z("",!0):(h(),A(v,{key:0,span:24},{default:r(()=>[t(p,{label:"\u751F\u4EA7\u65F6\u95F4\u6BB5"},{default:r(()=>[d("div",Fa,[a.isNew?(h(),V("div",Ba,[t(g,{modelValue:a.productionTimeStart,"onUpdate:modelValue":n=>a.productionTimeStart=n,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),e[28]||(e[28]=d("span",{class:"time-range-separator"},"-",-1)),t(g,{modelValue:a.productionTimeEnd,"onUpdate:modelValue":n=>a.productionTimeEnd=n,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":a.productionTimeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])):(h(),V("div",Ma,u(a.productionTimeStart)+"-"+u(a.productionTimeEnd),1))])]),_:2},1024)]),_:2},1024)),t(v,{span:24},{default:r(()=>[t(p,{label:"\u5F02\u5E38\u65F6\u95F4\u6BB5"},{default:r(()=>[d("div",za,[t(g,{modelValue:a.abnormalTimeStart,"onUpdate:modelValue":n=>a.abnormalTimeStart=n,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),e[29]||(e[29]=d("span",{class:"time-range-separator"},"-",-1)),t(g,{modelValue:a.abnormalTimeEnd,"onUpdate:modelValue":n=>a.abnormalTimeEnd=n,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":a.abnormalTimeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u5DE5\u5355\u53F7"},{default:r(()=>[a.isNew?(h(),V("div",Ka,[t(w,{modelValue:a.productionOrderCode,"onUpdate:modelValue":n=>a.productionOrderCode=n,"fetch-suggestions":(n,C)=>F(n,C,0),placeholder:"\u8F93\u5165\u5DE5\u5355\u53F7",style:{width:"100%"},onSelect:n=>G(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",ja,[d("div",qa,u(n.productionOrderCode)+"-"+u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",Ja,u(a.productionOrderCode),1))]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u9500\u552E\u8BA2\u5355\u53F7"},{default:r(()=>[a.isNew?(h(),V("div",Ya,[t(w,{modelValue:a.salesOrderCode,"onUpdate:modelValue":n=>a.salesOrderCode=n,"fetch-suggestions":(n,C)=>F(n,C,1),placeholder:"\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7",style:{width:"100%"},onSelect:n=>B(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",La,[d("div",Ga,u(n.salesOrderCode)+"-"+u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",Ha,u(a.salesOrderCode),1))]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u54C1\u53F7"},{default:r(()=>[a.isNew?(h(),V("div",Pa,[t(w,{modelValue:a.productNo,"onUpdate:modelValue":n=>a.productNo=n,"fetch-suggestions":(n,C)=>H(n,C,a.productionOrderCode,a.salesOrderCode),placeholder:"\u8F93\u5165\u54C1\u53F7\u67E5\u8BE2",style:{width:"100%"},onSelect:n=>B(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",Xa,[d("div",Za,u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",Qa,u(a.productNo),1))]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u54C1\u540D"},{default:r(()=>[S(u(a.modelsOrColor),1)]),_:2},1024)]),_:2},1024),t(v,{span:12},{default:r(()=>[t(p,{label:"\u5F02\u5E38\u6570\u91CF"},{default:r(()=>[t(k,{modelValue:a.abnormalReportNum,"onUpdate:modelValue":n=>a.abnormalReportNum=n,min:"0",onInput:n=>a.abnormalReportNum=Number(n)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024),t(v,{span:12},{default:r(()=>[t(p,{label:"\u5355\u4F4D"},{default:r(()=>[S(u(a.units),1)]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u95EE\u9898\u70B9"},{default:r(()=>[t(k,{modelValue:a.abnormalRemark,"onUpdate:modelValue":n=>a.abnormalRemark=n},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),t(v,{span:24},{default:r(()=>[t(p,{label:"\u4E34\u65F6\u5BF9\u7B56"},{default:r(()=>[t(k,{modelValue:a.abnormalCountermeasures,"onUpdate:modelValue":n=>a.abnormalCountermeasures=n},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),t(v,{span:12},{default:r(()=>[t(p,{label:"\u4EBA\u6570"},{default:r(()=>[t(k,{modelValue:a.abnormalNum,"onUpdate:modelValue":n=>a.abnormalNum=n,min:"0",onInput:n=>a.abnormalNum=Number(n)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024),t(v,{span:12},{default:r(()=>[t(p,{label:"\u603B\u5DE5\u65F6"},{default:r(()=>[t(k,{modelValue:a.abnormalWork,"onUpdate:modelValue":n=>a.abnormalWork=n,type:"number",min:0,onInput:e[1]||(e[1]=n=>T.value.abnormalWork=Number(n))},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)):(h(),V("div",ca,[d("div",ba,[e[16]||(e[16]=d("span",{class:"info-label"},"\u751F\u4EA7\u65F6\u95F4\u6BB5:",-1)),d("span",fa,u(a.productionTimeStart)+"-"+u(a.productionTimeEnd),1)]),d("div",va,[e[17]||(e[17]=d("span",{class:"info-label"},"\u5F02\u5E38\u65F6\u95F4\u6BB5:",-1)),d("span",ha,u(a.abnormalTimeStart)+"-"+u(a.abnormalTimeEnd),1)]),d("div",ga,[e[18]||(e[18]=d("span",{class:"info-label"},"\u5DE5\u5355\u53F7:",-1)),d("span",ya,u(a.productionOrderCode),1)]),d("div",ka,[e[19]||(e[19]=d("span",{class:"info-label"},"\u9500\u552E\u8BA2\u5355\u53F7:",-1)),d("span",Na,u(a.salesOrderCode),1)]),d("div",Va,[e[20]||(e[20]=d("span",{class:"info-label"},"\u54C1\u53F7:",-1)),d("span",wa,u(a.productNo),1)]),d("div",Ca,[e[21]||(e[21]=d("span",{class:"info-label"},"\u54C1\u540D:",-1)),d("span",Ta,u(a.modelsOrColor),1)]),d("div",_a,[e[22]||(e[22]=d("span",{class:"info-label"},"\u5F02\u5E38\u6570\u91CF:",-1)),d("span",Oa,u(a.abnormalReportNum),1)]),d("div",Sa,[e[23]||(e[23]=d("span",{class:"info-label"},"\u5355\u4F4D:",-1)),d("span",Ea,u(a.units),1)]),d("div",xa,[e[24]||(e[24]=d("span",{class:"info-label"},"\u95EE\u9898\u70B9:",-1)),d("span",Ra,u(a.abnormalRemark),1)]),d("div",Ua,[e[25]||(e[25]=d("span",{class:"info-label"},"\u4E34\u65F6\u5BF9\u7B56:",-1)),d("span",Ia,u(a.abnormalCountermeasures),1)]),d("div",Wa,[e[26]||(e[26]=d("span",{class:"info-label"},"\u4EBA\u6570:",-1)),d("span",$a,u(a.abnormalNum),1)]),d("div",Aa,[e[27]||(e[27]=d("span",{class:"info-label"},"\u603B\u5DE5\u65F6:",-1)),d("span",Da,u(a.abnormalWork),1)])]))]))),128))])):(h(),A(W,{key:3,data:f.value,border:"",stripe:"","scrollbar-always-on":"",ref_key:"tableRef",ref:ie,onSelectionChange:pe,onRowContextmenu:ke,onRowClick:Ne,style:{"min-height":"250px",height:"calc(100vh - 455px)"}},{default:r(()=>[e[32]||(e[32]=S(' "> ')),t(b,{type:"selection",width:"30",align:"center",fixed:"left"}),t(b,{label:"\u5E8F\u53F7",type:"index",width:"40",align:"center",fixed:"left"}),t(b,{label:"\u751F\u4EA7\u65F6\u95F4\u6BB5","min-width":"100"},{default:r(({row:a})=>[d("div",{style:Ke({color:a.isOldNew&&a.id===void 0?"red":""})},u(a.productionTimeStart)+"-"+u(a.productionTimeEnd),5)]),_:1}),t(b,{label:"\u5DE5\u5355\u53F7","min-width":"150",prop:"productionOrderCode"},{default:r(({row:a,$index:i})=>[a.workType===1||a.isNew?(h(),V("div",el,[t(w,{modelValue:a.productionOrderCode,"onUpdate:modelValue":n=>a.productionOrderCode=n,"fetch-suggestions":(n,C)=>F(n,C,0),placeholder:"\u8F93\u5165\u5DE5\u5355\u53F7",style:{width:"100%"},onSelect:n=>G(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",al,[d("div",ll,u(n.productionOrderCode)+"-"+u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",ol,u(a.productionOrderCode),1))]),_:1}),t(b,{label:"\u9500\u552E\u8BA2\u5355\u53F7","min-width":"150",prop:"salesOrderCode"},{default:r(({row:a,$index:i})=>[a.workType===1||a.isNew?(h(),V("div",tl,[t(w,{modelValue:a.salesOrderCode,"onUpdate:modelValue":n=>a.salesOrderCode=n,"fetch-suggestions":(n,C)=>F(n,C,1),placeholder:"\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7",style:{width:"100%"},onSelect:n=>B(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",nl,[d("div",rl,u(n.salesOrderCode)+"-"+u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",dl,u(a.salesOrderCode),1))]),_:1}),t(b,{label:"\u54C1\u53F7","min-width":"100",prop:"productNo"},{default:r(({row:a,$index:i})=>[a.workType===1||a.isNew?(h(),V("div",ml,[t(w,{modelValue:a.productNo,"onUpdate:modelValue":n=>a.productNo=n,"fetch-suggestions":(n,C)=>H(n,C,a.productionOrderCode,a.salesOrderCode),placeholder:"\u8F93\u5165\u54C1\u53F7\u67E5\u8BE2",style:{width:"100%"},onSelect:n=>B(n,i),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:r(({item:n})=>[d("div",sl,[d("div",ul,u(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])])):(h(),V("div",il,u(a.productNo),1))]),_:1}),t(b,{label:"\u673A\u578B/\u989C\u8272\uFF08\u54C1\u540D\uFF09","min-width":"120",prop:"modelsOrColor"},{default:r(({row:a})=>[S(u(a.modelsOrColor),1)]),_:1}),t(b,{label:"\u5F02\u5E38\u6570\u91CF","min-width":"60",prop:"abnormalReportNum",align:"center"},{default:r(({row:a})=>[t(k,{modelValue:a.abnormalReportNum,"onUpdate:modelValue":i=>a.abnormalReportNum=i,type:"number",min:"0",class:"no-spin-input",onInput:i=>a.abnormalReportNum=Number(i)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),t(b,{label:"\u5F02\u5E38\u65F6\u95F4\u6BB5","min-width":"180"},{default:r(({row:a})=>[d("div",pl,[t(g,{modelValue:a.abnormalTimeStart,"onUpdate:modelValue":i=>a.abnormalTimeStart=i,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),e[30]||(e[30]=d("span",{class:"time-range-separator"},"-",-1)),t(g,{modelValue:a.abnormalTimeEnd,"onUpdate:modelValue":i=>a.abnormalTimeEnd=i,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":a.abnormalTimeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:1}),t(b,{label:"\u4EBA\u6570","min-width":"50",prop:"abnormalNum"},{default:r(({row:a})=>[t(k,{modelValue:a.abnormalNum,"onUpdate:modelValue":i=>a.abnormalNum=i,type:"number",min:"0",class:"no-spin-input",onInput:i=>a.abnormalNum=Number(i)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),t(b,{label:"\u603B\u5DE5\u65F6","min-width":"60",prop:"abnormalWork"},{default:r(({row:a})=>[t(k,{modelValue:a.abnormalWork,"onUpdate:modelValue":i=>a.abnormalWork=i,type:"number",class:"no-spin-input",readonly:!0,onInput:i=>a.abnormalWork=Number(i)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),t(b,{label:"\u95EE\u9898\u70B9","min-width":"180",prop:"abnormalRemark"},{default:r(({row:a})=>[t(k,{modelValue:a.abnormalRemark,"onUpdate:modelValue":i=>a.abnormalRemark=i,type:"textarea",placeholder:"\u8BF7\u8F93\u5165\u95EE\u9898\u70B9",onKeydown:te(i=>Z(i,a,"abnormalRemark"),["enter"]),onFocus:i=>Q(a,"abnormalRemark"),style:{width:"100%","max-height":"50px"}},null,8,["modelValue","onUpdate:modelValue","onKeydown","onFocus"])]),_:1}),t(b,{label:"\u4E34\u65F6\u5BF9\u7B56","min-width":"180",prop:"abnormalCountermeasures"},{default:r(({row:a})=>[t(k,{modelValue:a.abnormalCountermeasures,"onUpdate:modelValue":i=>a.abnormalCountermeasures=i,type:"textarea",placeholder:"\u8BF7\u8F93\u5165\u4E34\u65F6\u5BF9\u7B56",onKeydown:te(i=>Z(i,a,"abnormalCountermeasures"),["enter"]),onFocus:i=>Q(a,"abnormalCountermeasures"),style:{width:"100%","max-height":"50px"}},null,8,["modelValue","onUpdate:modelValue","onKeydown","onFocus"])]),_:1}),t(b,{label:"\u64CD\u4F5C","min-width":"50",fixed:"right"},{default:r(({$index:a})=>[t(l,{type:"danger",link:"",onClick:i=>X(a)},{default:r(()=>e[31]||(e[31]=[S(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),d("div",{id:"contextMenu",class:"context-menu",style:{position:"fixed",display:"none","z-index":"1000"}},[d("ul",{class:"menu-list"},[d("li",{onClick:Ve,class:"menu-item"}," \u5217\u6279\u91CF\u8D4B\u503C "),d("li",{onClick:ee,class:"menu-item"}," \u63D2\u5165\u9009\u4E2D\u884C "),d("li",{onClick:ae,class:"menu-item"}," \u6279\u91CF\u5220\u9664 ")])])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","width"])),[[Te]]),t(Ce,{modelValue:$.value,"onUpdate:modelValue":e[11]||(e[11]=a=>$.value=a),title:"\u6279\u91CF\u8D4B\u503C",width:"400px"},{footer:r(()=>[t(l,{onClick:e[10]||(e[10]=a=>$.value=!1)},{default:r(()=>e[35]||(e[35]=[S("\u53D6 \u6D88")])),_:1}),t(l,{type:"primary",onClick:fe},{default:r(()=>e[36]||(e[36]=[S("\u786E \u5B9A")])),_:1})]),default:r(()=>[t(O,{model:T.value,"label-width":"90px"},{default:r(()=>[t(p,{label:"\u5F02\u5E38\u65F6\u95F4\u6BB5"},{default:r(()=>[d("div",fl,[t(g,{modelValue:T.value.abnormalTimeStart,"onUpdate:modelValue":e[4]||(e[4]=a=>T.value.abnormalTimeStart=a),placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue"]),e[34]||(e[34]=d("span",{class:"time-range-separator"},"-",-1)),t(g,{modelValue:T.value.abnormalTimeEnd,"onUpdate:modelValue":e[5]||(e[5]=a=>T.value.abnormalTimeEnd=a),placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":T.value.abnormalTimeStart,placement:"top-start"},null,8,["modelValue","min-time"])])]),_:1}),t(p,{label:"\u95EE\u9898\u70B9"},{default:r(()=>[t(k,{modelValue:T.value.abnormalRemark,"onUpdate:modelValue":e[6]||(e[6]=a=>T.value.abnormalRemark=a)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u4E34\u65F6\u5BF9\u7B56"},{default:r(()=>[t(k,{modelValue:T.value.abnormalCountermeasures,"onUpdate:modelValue":e[7]||(e[7]=a=>T.value.abnormalCountermeasures=a)},null,8,["modelValue"])]),_:1}),t(p,{label:"\u4EBA\u6570"},{default:r(()=>[t(k,{modelValue:T.value.abnormalNum,"onUpdate:modelValue":e[8]||(e[8]=a=>T.value.abnormalNum=a),type:"number",min:0,onInput:e[9]||(e[9]=a=>T.value.abnormalNum=Number(a))},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])),[[De,U.value],[_e]])}}}),[["__scopeId","data-v-be74b445"]]);export{vl as default};
