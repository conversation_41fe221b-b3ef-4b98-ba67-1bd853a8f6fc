import{d as ae,aj as p,j as v,f as re,u as t,Y as R,au as M,x as ce,L as de,k as h,K as pe,ar as G,aT as ue,J as q,_ as me,aO as te,p as ne,ax as ge,ch as he,o as A,c as Q,c0 as fe,g as we,w as O,bf as X,h as B,v as D,t as I,$ as T,aK as ye,F as xe,G as be,H as ve,r as Se,d1 as oe,d2 as se}from"./index-C8b06LRn.js";import{g as N,_ as Pe}from"./Form-CkLzRm65.js";import{E as ke}from"./index-Cl43piKd.js";import{d as Ae}from"./download-D5Lb_h0f.js";const Le=me(ae({name:"Table",props:{pageSize:p.number.def(10),currentPage:p.number.def(1),selection:p.bool.def(!1),showOverflowTooltip:p.bool.def(!0),columns:{type:Array,default:()=>[]},expand:p.bool.def(!1),pagination:{type:Object,default:()=>{}},reserveSelection:p.bool.def(!1),loading:p.bool.def(!1),reserveIndex:p.bool.def(!1),align:p.string.validate(a=>["left","center","right"].includes(a)).def("center"),headerAlign:p.string.validate(a=>["left","center","right"].includes(a)).def("center"),data:{type:Array,default:()=>[]}},emits:["update:pageSize","update:currentPage","register"],setup(a,{attrs:o,slots:m,emit:f,expose:_}){const g=v();re(()=>{const e=t(g);f("register",e==null?void 0:e.$parent,g)});const P=v(a.pageSize),w=v(a.currentPage),n=v({}),i=v({}),r=R(()=>{const e={...a};return Object.assign(e,t(i)),e}),k=(e,s)=>{var L;const{columns:b}=t(r);for(const C of s||b)for(const F of e)C.field===F.field?ue(C,F.path,F.value):(L=C.children)!=null&&L.length&&k(e,C.children)},V=v([]),K=e=>{V.value=e};_({setProps:(e={})=>{i.value=Object.assign(t(i),e),n.value=e},setColumn:k,selections:V});const l=R(()=>Object.assign({small:!1,background:!0,pagerCount:document.body.clientWidth<992?5:7,layout:"total, sizes, prev, pager, next, jumper",pageSizes:[10,20,30,50,100],disabled:!1,hideOnSinglePage:!1,total:10},t(r).pagination));M(()=>t(r).pageSize,e=>{P.value=e}),M(()=>t(r).currentPage,e=>{w.value=e}),M(()=>P.value,e=>{f("update:pageSize",e)}),M(()=>w.value,e=>{f("update:currentPage",e)});const x=R(()=>{const e={...o,...a};return delete e.columns,delete e.data,e}),d=()=>{const{selection:e,reserveSelection:s,align:b,headerAlign:L}=t(r);return e?h(q,{type:"selection",reserveSelection:s,align:b,headerAlign:L,width:"50"},null):void 0},u=()=>{const{align:e,headerAlign:s,expand:b}=t(r);return b?h(q,{type:"expand",align:e,headerAlign:s},{default:L=>N(m,"expand",L)}):void 0},j=e=>{const{columns:s,reserveIndex:b,pageSize:L,currentPage:C,align:F,headerAlign:Z,showOverflowTooltip:ie}=t(r);return[u(),d()].concat((e||s).map(c=>{if(c.type==="index")return h(q,{type:"index",index:c.index?c.index:W=>((z,U,H,J)=>{const E=U+1;return z?H*(J-1)+E:E})(b,W,L,C),align:c.align||F,headerAlign:c.headerAlign||Z,label:c.label,width:"65px"},null);{const W={...c};return W.children&&delete W.children,h(q,G({showOverflowTooltip:ie,align:F,headerAlign:Z},W,{prop:c.field}),{default:z=>{var U;return c.children&&c.children.length?(H=>{const{align:J,headerAlign:E,showOverflowTooltip:le}=t(r);return H.map(y=>{const Y={...y};return Y.children&&delete Y.children,h(q,G({showOverflowTooltip:le,align:J,headerAlign:E},Y,{prop:y.field}),{default:$=>{var ee;return y.children&&y.children.length?j(y.children):N(m,y.field,$)||((ee=y==null?void 0:y.formatter)==null?void 0:ee.call(y,$.row,$.column,$.row[y.field],$.$index))||$.row[y.field]},header:N(m,`${y.field}-header`)})})})(c.children):N(m,c.field,z)||((U=c==null?void 0:c.formatter)==null?void 0:U.call(c,z.row,z.column,z.row[c.field],z.$index))||z.row[c.field]},header:()=>N(m,`${c.field}-header`)||c.label})}}))};return()=>ce(h("div",null,[h(pe,G({ref:g,data:t(r).data,"onSelection-change":K},t(x)),{default:()=>j(),append:()=>N(m,"append")}),t(r).pagination?h(ke,G({pageSize:P.value,"onUpdate:pageSize":e=>P.value=e,currentPage:w.value,"onUpdate:currentPage":e=>w.value=e,class:"float-right mb-15px mt-15px"},t(l)),null):void 0]),[[de,t(r).loading]])}}),[["__scopeId","data-v-1ee0f9ba"]]),ze={key:0},Oe=ae({name:"Search",__name:"Search",props:{schema:{type:Array,default:()=>[]},isCol:p.bool.def(!1),labelWidth:p.oneOfType([String,Number]).def("auto"),layout:p.string.validate(a=>["inline","bottom"].includes(a)).def("inline"),buttomPosition:p.string.validate(a=>["left","center","right"].includes(a)).def("center"),showSearch:p.bool.def(!0),showReset:p.bool.def(!0),expand:p.bool.def(!1),expandField:p.string.def(""),inline:p.bool.def(!0),model:{type:Object,default:()=>({})}},emits:["search","reset"],setup(a,{emit:o}){const{t:m}=ne(),f=a,_=o,g=v(!0),P=R(()=>{let l=ge(f.schema);if(f.expand&&f.expandField&&!t(g)){const x=he(l,d=>d.field===f.expandField);if(x>-1){const d=l.length;l.splice(x+1,d)}}return f.layout==="inline"&&(l=l.concat([{field:"action",formItemProps:{labelWidth:"0px"}}])),l}),{register:w,elFormRef:n,methods:i}=(l=>{const x=v(),d=v(),u=async()=>(await te(),t(x)),j={setProps:async(e={})=>{const s=await u();s==null||s.setProps(e),e.model&&(s==null||s.setValues(e.model))},setValues:async e=>{const s=await u();s==null||s.setValues(e)},setSchema:async e=>{const s=await u();s==null||s.setSchema(e)},addSchema:async(e,s)=>{const b=await u();b==null||b.addSchema(e,s)},delSchema:async e=>{const s=await u();s==null||s.delSchema(e)},getFormData:async()=>{const e=await u();return e==null?void 0:e.formModel}};return l&&j.setProps(l),{register:(e,s)=>{x.value=e,d.value=s},elFormRef:d,methods:j}})({model:f.model||{}}),r=async()=>{var l;await((l=t(n))==null?void 0:l.validate(async x=>{if(x){const{getFormData:d}=i,u=await d();_("search",u)}}))},k=async()=>{var d;(d=t(n))==null||d.resetFields();const{getFormData:l}=i,x=await l();_("reset",x)},V=R(()=>({textAlign:f.buttomPosition})),K=()=>{var l;(l=t(n))==null||l.resetFields(),g.value=!t(g)};return(l,x)=>{const d=be,u=ve,j=Pe;return A(),Q(xe,null,[h(j,{inline:a.inline,"is-col":a.isCol,"is-custom":!1,"label-width":a.labelWidth,schema:t(P),class:"-mb-15px","hide-required-asterisk":"",onRegister:t(w)},fe({action:O(()=>[a.layout==="inline"?(A(),Q("div",ze,[a.showSearch?(A(),B(u,{key:0,onClick:r},{default:O(()=>[h(d,{class:"mr-5px",icon:"ep:search"}),D(" "+I(t(m)("common.query")),1)]),_:1})):T("",!0),a.showReset?(A(),B(u,{key:1,onClick:k},{default:O(()=>[h(d,{class:"mr-5px",icon:"ep:refresh"}),D(" "+I(t(m)("common.reset")),1)]),_:1})):T("",!0),a.expand?(A(),B(u,{key:2,text:"",onClick:K},{default:O(()=>[D(I(t(m)(t(g)?"common.shrink":"common.expand"))+" ",1),h(d,{icon:t(g)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):T("",!0),X(l.$slots,"actionMore")])):T("",!0)]),_:2},[we(Object.keys(l.$slots),e=>({name:e,fn:O(()=>[X(l.$slots,e)])}))]),1032,["inline","is-col","label-width","schema","onRegister"]),a.layout==="bottom"?(A(),Q("div",{key:0,style:ye(t(V))},[a.showSearch?(A(),B(u,{key:0,type:"primary",onClick:r},{default:O(()=>[h(d,{class:"mr-5px",icon:"ep:search"}),D(" "+I(t(m)("common.query")),1)]),_:1})):T("",!0),a.showReset?(A(),B(u,{key:1,onClick:k},{default:O(()=>[h(d,{class:"mr-5px",icon:"ep:refresh-right"}),D(" "+I(t(m)("common.reset")),1)]),_:1})):T("",!0),a.expand?(A(),B(u,{key:2,text:"",onClick:K},{default:O(()=>[D(I(t(m)(t(g)?"common.shrink":"common.expand"))+" ",1),h(d,{icon:t(g)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):T("",!0),X(l.$slots,"actionMore")],4)):T("",!0)],64)}}}),{t:S}=ne(),Te=a=>{const o=Se({pageSize:10,currentPage:1,total:10,tableList:[],params:{...(a==null?void 0:a.defaultParams)||{}},loading:!0,exportLoading:!1,currentRow:null}),m=R(()=>({...o.params,pageSize:o.pageSize,pageNo:o.currentPage}));M(()=>o.currentPage,()=>{w.getList()}),M(()=>o.pageSize,()=>{o.currentPage===1||(o.currentPage=1),w.getList()});const f=v(),_=v(),g=async()=>(await te(),t(f)),P=async n=>{let i=1;n instanceof Array?(i=n.length,await Promise.all(n.map(async r=>{await((a==null?void 0:a.delListApi)&&(a==null?void 0:a.delListApi(r)))}))):await((a==null?void 0:a.delListApi)&&(a==null?void 0:a.delListApi(n))),oe.success(S("common.delSuccess")),o.currentPage=(o.total%o.pageSize===i||o.pageSize===1)&&o.currentPage>1?o.currentPage-1:o.currentPage,await w.getList()},w={getList:async()=>{o.loading=!0;const n=await(a==null?void 0:a.getListApi(t(m)).finally(()=>{o.loading=!1}));n&&(o.tableList=n.list,o.total=n.total??0)},setProps:async(n={})=>{const i=await g();i==null||i.setProps(n)},setColumn:async n=>{const i=await g();i==null||i.setColumn(n)},getSelections:async()=>{const n=await g();return(n==null?void 0:n.selections)||[]},setSearchParams:n=>{o.params=Object.assign(o.params,{pageSize:o.pageSize,pageNo:1,...n}),o.currentPage!==1?o.currentPage=1:w.getList()},delList:async(n,i,r=!0)=>{const k=await g();!i||k!=null&&k.selections.length?r?se.confirm(S("common.delMessage"),S("common.confirmTitle"),{confirmButtonText:S("common.ok"),cancelButtonText:S("common.cancel"),type:"warning"}).then(async()=>{await P(n)}):await P(n):oe.warning(S("common.delNoData"))},exportList:async n=>{o.exportLoading=!0,se.confirm(S("common.exportMessage"),S("common.confirmTitle"),{confirmButtonText:S("common.ok"),cancelButtonText:S("common.cancel"),type:"warning"}).then(async()=>{var r;const i=await((r=a==null?void 0:a.exportListApi)==null?void 0:r.call(a,t(m)));i&&Ae.excel(i,n)}).finally(()=>{o.exportLoading=!1})}};return a!=null&&a.props&&w.setProps(a.props),{register:(n,i)=>{f.value=n,_.value=i},elTableRef:_,tableObject:o,methods:w,tableMethods:w}};export{Oe as _,Le as a,Te as u};
