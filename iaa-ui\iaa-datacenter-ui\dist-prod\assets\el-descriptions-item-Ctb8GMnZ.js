import{d as $,av as F,cQ as J,bd as L,bb as O,x as R,bv as A,cr as K,b6 as j,ba as M,u as i,o,c as f,l as m,F as w,g as I,h as W,k as Q,bg as T,bA as P,bT as U,cx as V,aL as X,Y as Z,n as x,bf as Y,v as q,t as G,$ as ll,cR as tl,bh as el,cS as sl}from"./index-C8b06LRn.js";const B=Symbol("elDescriptions");var _=$({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:F(B,{})}),render(){var N;const t=J(this.cell),s=(((N=this.cell)==null?void 0:N.dirs)||[]).map(p=>{const{dir:b,arg:v,modifiers:g,value:C}=p;return[b,C,v,g]}),{border:D,direction:d}=this.descriptions,n=d==="vertical",E=()=>{var p,b,v;return((v=(b=(p=this.cell)==null?void 0:p.children)==null?void 0:b.label)==null?void 0:v.call(b))||t.label},z=()=>{var p,b,v;return(v=(b=(p=this.cell)==null?void 0:p.children)==null?void 0:b.default)==null?void 0:v.call(b)},l=t.span,c=t.rowspan,a=t.align?`is-${t.align}`:"",r=t.labelAlign?`is-${t.labelAlign}`:a,S=t.className,y=t.labelClassName,u={width:L(t.width),minWidth:L(t.minWidth)},e=O("descriptions");switch(this.type){case"label":return R(A(this.tag,{style:u,class:[e.e("cell"),e.e("label"),e.is("bordered-label",D),e.is("vertical-label",n),r,y],colSpan:n?l:1,rowspan:n?1:c},E()),s);case"content":return R(A(this.tag,{style:u,class:[e.e("cell"),e.e("content"),e.is("bordered-content",D),e.is("vertical-content",n),a,S],colSpan:n?l:2*l-1,rowspan:n?2*c-1:c},z()),s);default:{const p=E();return R(A("td",{style:u,class:[e.e("cell"),a],colSpan:l,rowspan:c},[K(p)?void 0:A("span",{class:[e.e("label"),y]},p),A("span",{class:[e.e("content"),S]},z())]),s)}}}});const al=j({row:{type:M(Array),default:()=>[]}}),nl=$({name:"ElDescriptionsRow"});var rl=T($({...nl,props:al,setup(N){const t=F(B,{});return(s,D)=>i(t).direction==="vertical"?(o(),f(w,{key:0},[m("tr",null,[(o(!0),f(w,null,I(s.row,(d,n)=>(o(),W(i(_),{key:`tr1-${n}`,cell:d,tag:"th",type:"label"},null,8,["cell"]))),128))]),m("tr",null,[(o(!0),f(w,null,I(s.row,(d,n)=>(o(),W(i(_),{key:`tr2-${n}`,cell:d,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(o(),f("tr",{key:1},[(o(!0),f(w,null,I(s.row,(d,n)=>(o(),f(w,{key:`tr3-${n}`},[i(t).border?(o(),f(w,{key:0},[Q(i(_),{cell:d,tag:"td",type:"label"},null,8,["cell"]),Q(i(_),{cell:d,tag:"td",type:"content"},null,8,["cell"])],64)):(o(),W(i(_),{key:1,cell:d,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}}),[["__file","descriptions-row.vue"]]);const il=j({border:Boolean,column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:P,title:{type:String,default:""},extra:{type:String,default:""}}),ol=$({name:"ElDescriptions"});var cl=T($({...ol,props:il,setup(N){const t=N,s=O("descriptions"),D=U(),d=V();X(B,t);const n=Z(()=>[s.b(),s.m(D.value)]),E=(l,c,a,r=!1)=>(l.props||(l.props={}),c>a&&(l.props.span=a),r&&(l.props.span=c),l),z=()=>{if(!d.default)return[];const l=tl(d.default()).filter(u=>{var e;return((e=u==null?void 0:u.type)==null?void 0:e.name)==="ElDescriptionsItem"}),c=[];let a=[],r=t.column,S=0;const y=[];return l.forEach((u,e)=>{var p,b,v;const g=((p=u.props)==null?void 0:p.span)||1,C=((b=u.props)==null?void 0:b.rowspan)||1,h=c.length;if(y[h]||(y[h]=0),C>1)for(let k=1;k<C;k++)y[v=h+k]||(y[v]=0),y[h+k]++,S++;if(y[h]>0&&(r-=y[h],y[h]=0),e<l.length-1&&(S+=g>r?r:g),e===l.length-1){const k=t.column-S%t.column;return a.push(E(u,k,r,!0)),void c.push(a)}g<r?(r-=g,a.push(u)):(a.push(E(u,g,r)),c.push(a),r=t.column,a=[])}),c};return(l,c)=>(o(),f("div",{class:x(i(n))},[l.title||l.extra||l.$slots.title||l.$slots.extra?(o(),f("div",{key:0,class:x(i(s).e("header"))},[m("div",{class:x(i(s).e("title"))},[Y(l.$slots,"title",{},()=>[q(G(l.title),1)])],2),m("div",{class:x(i(s).e("extra"))},[Y(l.$slots,"extra",{},()=>[q(G(l.extra),1)])],2)],2)):ll("v-if",!0),m("div",{class:x(i(s).e("body"))},[m("table",{class:x([i(s).e("table"),i(s).is("bordered",l.border)])},[m("tbody",null,[(o(!0),f(w,null,I(z(),(a,r)=>(o(),W(rl,{key:r,row:a},null,8,["row"]))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);const H=$({name:"ElDescriptionsItem",props:j({label:{type:String,default:""},span:{type:Number,default:1},rowspan:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},align:{type:String,default:"left"},labelAlign:{type:String,default:""},className:{type:String,default:""},labelClassName:{type:String,default:""}})}),pl=el(cl,{DescriptionsItem:H}),dl=sl(H);export{dl as E,pl as a};
