import{aG as a}from"./index-C8b06LRn.js";const r={getList:async t=>await a.post({url:"/report/products/page",data:t}),exportTemplate:()=>a.download({url:"/report/products/export-template"}),getLastChangeLog:async()=>await a.get({url:"/report/products/last-change-log"}),batchDelete:async t=>await a.post({url:"/report/products/batch-delete",data:t}),exportDay:async t=>await a.download({url:"/report/products/export-excel",params:t}),getHistory:async t=>await a.post({url:"/report/products/history",data:t}),update:async t=>await a.post({url:"/report/products/update",data:t})};export{r as P};
