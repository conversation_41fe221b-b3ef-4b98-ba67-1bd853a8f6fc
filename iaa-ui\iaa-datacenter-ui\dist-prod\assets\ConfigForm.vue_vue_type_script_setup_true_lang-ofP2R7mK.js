import{d as R,p as j,y as A,j as i,r as B,o as p,h as f,w as s,k as r,u as l,v as g,x as L,c as O,F as D,g as E,s as G,D as H,t as T,m as z,S as J,B as K,ap as M,aq as P,I as Q,H as W,L as X}from"./index-C8b06LRn.js";import{_ as Y}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{g as Z,c as $,u as ee}from"./index-C78nCjiS.js";const le=R({name:"InfraConfigForm",__name:"ConfigForm",emits:["success"],setup(ae,{expose:k,emit:h}){const{t:v}=j(),y=A(),t=i(!1),b=i(""),d=i(!1),V=i(""),u=i({id:void 0,category:"",name:"",key:"",value:"",visible:!0,remark:""}),U=B({category:[{required:!0,message:"\u53C2\u6570\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u53C2\u6570\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],key:[{required:!0,message:"\u53C2\u6570\u952E\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],value:[{required:!0,message:"\u53C2\u6570\u952E\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],visible:[{required:!0,message:"\u662F\u5426\u53EF\u89C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=i();k({open:async(o,e)=>{if(t.value=!0,b.value=v("action."+o),V.value=o,F(),e){d.value=!0;try{u.value=await Z(e)}finally{d.value=!1}}}});const q=h,w=async()=>{if(c&&await c.value.validate()){d.value=!0;try{const o=u.value;V.value==="create"?(await $(o),y.success(v("common.createSuccess"))):(await ee(o),y.success(v("common.updateSuccess"))),t.value=!1,q("success")}finally{d.value=!1}}},F=()=>{var o;u.value={id:void 0,category:"",name:"",key:"",value:"",visible:!0,remark:""},(o=c.value)==null||o.resetFields()};return(o,e)=>{const n=J,m=K,x=M,C=P,I=Q,_=W,S=Y,N=X;return p(),f(S,{modelValue:l(t),"onUpdate:modelValue":e[7]||(e[7]=a=>z(t)?t.value=a:null),title:l(b)},{footer:s(()=>[r(_,{disabled:l(d),type:"primary",onClick:w},{default:s(()=>e[8]||(e[8]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),r(_,{onClick:e[6]||(e[6]=a=>t.value=!1)},{default:s(()=>e[9]||(e[9]=[g("\u53D6 \u6D88")])),_:1})]),default:s(()=>[L((p(),f(I,{ref_key:"formRef",ref:c,model:l(u),rules:l(U),"label-width":"80px"},{default:s(()=>[r(m,{label:"\u53C2\u6570\u5206\u7C7B",prop:"category"},{default:s(()=>[r(n,{modelValue:l(u).category,"onUpdate:modelValue":e[0]||(e[0]=a=>l(u).category=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u5206\u7C7B"},null,8,["modelValue"])]),_:1}),r(m,{label:"\u53C2\u6570\u540D\u79F0",prop:"name"},{default:s(()=>[r(n,{modelValue:l(u).name,"onUpdate:modelValue":e[1]||(e[1]=a=>l(u).name=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0"},null,8,["modelValue"])]),_:1}),r(m,{label:"\u53C2\u6570\u952E\u540D",prop:"key"},{default:s(()=>[r(n,{modelValue:l(u).key,"onUpdate:modelValue":e[2]||(e[2]=a=>l(u).key=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u952E\u540D"},null,8,["modelValue"])]),_:1}),r(m,{label:"\u53C2\u6570\u952E\u503C",prop:"value"},{default:s(()=>[r(n,{modelValue:l(u).value,"onUpdate:modelValue":e[3]||(e[3]=a=>l(u).value=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u952E\u503C"},null,8,["modelValue"])]),_:1}),r(m,{label:"\u662F\u5426\u53EF\u89C1",prop:"visible"},{default:s(()=>[r(C,{modelValue:l(u).visible,"onUpdate:modelValue":e[4]||(e[4]=a=>l(u).visible=a)},{default:s(()=>[(p(!0),O(D,null,E(l(G)(l(H).INFRA_BOOLEAN_STRING),a=>(p(),f(x,{key:a.value,value:a.value},{default:s(()=>[g(T(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[r(n,{modelValue:l(u).remark,"onUpdate:modelValue":e[5]||(e[5]=a=>l(u).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[N,l(d)]])]),_:1},8,["modelValue","title"])}}});export{le as _};
