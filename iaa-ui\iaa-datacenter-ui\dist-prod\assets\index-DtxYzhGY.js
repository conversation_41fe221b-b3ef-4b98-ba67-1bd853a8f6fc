import{d as U,j as d,y as q,au as z,f as E,o as u,h as m,w as l,k as n,l as h,u as s,v as H,G as I,S as P,cq as R,a8 as $,cH as A,c9 as B,ab as D,_ as F}from"./index-C8b06LRn.js";import{E as J}from"./el-empty-ag1-OZ0J.js";import{_ as K}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{p as M,l as O,s as Q,g as T}from"./permissions-BPTnIY5v.js";import"./el-card-CaOo8U9P.js";const W={class:"head-container"},X={class:"head-container"},Y=F(U({__name:"index",setup(Z){const t=d({pageSize:1e3,pageNo:1,nickname:""}),x=q(),c=d([]),_=d(),p=d([]),i=d({backend_user_id:void 0,xiaoman_user_ids:[]}),v=async()=>{await Q(i.value),x.success("\u8BBE\u5B9A\u6210\u529F")},y={children:"children",label:"nickname",value:"id",isLeaf:"leaf",emitPath:!1},b=(e,a)=>!e||a.nickname.includes(e),g=async e=>{i.value.backend_user_id=e.id;const a=await T(e.id);i.value.xiaoman_user_ids=a==null?void 0:a.xiaoman_user_ids};z(()=>t.value.nickname,e=>{_.value.filter(e)});const w=(e,a)=>a.chinese_name.toLowerCase().includes(e.toLowerCase());return E(()=>{(async()=>{const e=await M(t.value);c.value=e.list})(),(async()=>{const e=await O();p.value=e})()}),(e,a)=>{const V=I,C=P,G=R,f=K,k=$,L=A,j=B,N=J,S=D;return u(),m(S,{gutter:20},{default:l(()=>[n(k,{span:4,xs:24},{default:l(()=>[n(f,null,{default:l(()=>[h("div",W,[n(C,{modelValue:s(t).nickname,"onUpdate:modelValue":a[0]||(a[0]=o=>s(t).nickname=o),class:"mb-20px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0"},{prefix:l(()=>[n(V,{icon:"ep:search"})]),_:1},8,["modelValue"])]),h("div",X,[n(G,{ref_key:"treeRef",ref:_,data:s(c),"expand-on-click-node":!1,"filter-node-method":b,props:y,"default-expanded-keys":[100],"highlight-current":"","node-key":"id",onNodeClick:g},null,8,["data"])])]),_:1})]),_:1}),n(k,{span:20,xs:24},{default:l(()=>[n(f,null,{default:l(()=>{var o;return[n(L,{type:"warning",center:"",description:`\u5F53\u524D\u9009\u62E9\u7528\u6237\uFF1A${((o=s(c).find(r=>r.id===s(i).backend_user_id))==null?void 0:o.nickname)||""}`,closable:!1},null,8,["description"]),s(i).backend_user_id?(u(),m(j,{key:0,modelValue:s(i).xiaoman_user_ids,"onUpdate:modelValue":a[1]||(a[1]=r=>s(i).xiaoman_user_ids=r),data:s(p),props:{key:"user_id",label:"chinese_name"},titles:["\u5C0F\u6EE1\u7528\u6237","\u53EF\u67E5\u770B\u4EE5\u4E0B\u7528\u6237\u4EA7\u751F\u6570\u636E"],onChange:v,class:"mt-20px",filterable:"","filter-method":w},null,8,["modelValue","data"])):(u(),m(N,{key:1},{default:l(()=>a[2]||(a[2]=[H(" \u8BF7\u9009\u62E9\u5DE6\u4FA7\u7528\u6237 ")])),_:1}))]}),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-75930371"]]);export{Y as default};
