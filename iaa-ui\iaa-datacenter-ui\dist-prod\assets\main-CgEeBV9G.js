import{d as qe,y as Le,j as C,r as ce,bU as pe,f as Pe,aO as je,u as o,aF as W,T as Ee,o as i,c as b,k as a,w as t,x as X,h as c,v as _,l as v,F as V,g as h,di as w,$ as ve,m as P,ax as He,H as Qe,C as Ge,aC as Ze,G as Je,S as Ke,z as We,A as Xe,aM as el,B as ll,a8 as al,c3 as ol,ab as tl,I as ul,ap as dl,aq as il,cy as nl,_ as sl}from"./index-C8b06LRn.js";import{_ as ml}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as rl}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import cl from"./DetailForm-BVwfkXTG.js";import{C as S}from"./index-B2m4kQ_G.js";import j from"./DetailDate-DCB2Zfz0.js";import pl from"./PasteParser-ChRqthhH.js";import{c as vl}from"./vxeCustom-D2Re1O-c.js";import{c as gl}from"./permission-DVzqLl93.js";import{d as fl}from"./download-D5Lb_h0f.js";import"./index-Cl43piKd.js";import"./el-infinite-scroll-fE_Jh_bm.js";import"./index-WiqCEeob.js";import"./el-drawer-C5TFtzfV.js";import"./el-timeline-item-BqzIH3Db.js";import"./CardTitle-00NfZwLk.js";import"./dateUtil-D9m5ek6U.js";const bl={class:"h-[calc(100vh-260px)]"},Vl={class:"flex justify-between p-8px rounded-4px bg-#f5f7fa"},hl={class:"dialog-footer"},_l=sl(qe({__name:"main",setup(Cl){const N=Le(),ee=C([]),le=C(0),ae=C(!1),n=C({pageNo:1,pageSize:100,businessDate:void 0,seller:void 0,customerName:void 0,itemCode:void 0,itemName:void 0,spec:void 0,packing:void 0,logo:void 0,instruction:void 0,program:void 0,structure:void 0,remark:void 0,planBomCompleteDate:void 0,planBomCompleteDateChange:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,taskStatus:void 0,showAll:!0,overdue:void 0}),oe=C(),E=C(),te=C(),ue=C(),z=async()=>{ae.value=!0;try{const d=await S.pageTask(n.value);ee.value=d.list,le.value=d.total}finally{ae.value=!1}},f=async()=>{n.value.pageNo=1,z()},A=[{label:"\u5F85\u5904\u7406",value:"98"},{label:"\u6B63\u5E38",value:"97"},{label:"\u9884\u8B66",value:"96"},{label:"\u8D85\u671F",value:"95"},{label:"\u8D85\u671F\u5B8C\u6210",value:"94"}],ge=()=>{n.value={pageNo:1,pageSize:100,businessDate:void 0,seller:void 0,customerName:void 0,itemCode:void 0,itemName:void 0,spec:void 0,packing:void 0,logo:void 0,instruction:void 0,program:void 0,structure:void 0,remark:void 0,planBomCompleteDate:void 0,planBomCompleteDateChange:void 0,planCompleteDate:void 0,actualCompleteDate:void 0,showAll:!0,taskStatus:void 0,overdue:void 0},f()},O=C(!1),x=C(!1),I=C(!1),s=C({id:void 0,businessDate:void 0,seller:void 0,customerCode:void 0,customerName:void 0,itemCode:void 0,itemName:void 0,spec:void 0,packing:void 0,packingDescription:void 0,logo:void 0,logoDescription:void 0,instruction:void 0,instructionDescription:void 0,program:void 0,programDescription:void 0,structure:void 0,structureDescription:void 0,planBomCompleteDate:void 0,remark:void 0}),fe=ce({itemCode:[{required:!0,message:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u7801",trigger:"blur"}]}),k=C([]),be=[{label:"\u54C1\u53F7",value:"itemCode"},{label:"\u54C1\u540D",value:"itemName"},{label:"\u89C4\u683C",value:"spec"}],Ve=["itemCode","itemName","spec"],he=d=>{d.forEach(l=>{const r={...s.value,itemCode:l.itemCode,itemName:l.itemName,spec:l.spec};k.value.push(r)}),I.value=!1,N.success(`\u6210\u529F\u5BFC\u5165 ${d.length} \u6761\u6570\u636E\u5230\u6279\u91CF\u6DFB\u52A0\u5217\u8868`)},_e=ce({body:{options:[[{code:"batch",name:"\u6279\u91CF\u8BBE\u7F6E"}]]}}),Ce=({menu:d,rowIndex:l,column:r})=>{if(d.code=="batch")for(let p=l;p<k.value.length;p++)k.value[p][r.field]=k.value[l][r.field]},ke=({row:d})=>{if(d.actualCompleteDate)return{backgroundColor:"var(--el-color-success-light-3) !important",color:"#ffffff"}},ye=d=>{const l=!d.instruction||d.instruction==="99",r=!d.logo||d.logo==="99",p=!d.packing||d.packing==="99",R=!d.program||d.program==="99",B=!d.structure||d.structure==="99";return l&&r&&p&&R&&B},de=async()=>{var d;x.value=!0;try{if(await((d=te.value)==null?void 0:d.validate()),!(s.value.packing||s.value.program||s.value.structure||s.value.logo||s.value.instruction))return void N.error("\u8BF7\u81F3\u5C11\u9009\u62E9\u4E00\u9879\u5B9A\u5236\u5185\u5BB9\uFF01");k.value.push({...s.value}),ie()}finally{x.value=!1}},we=d=>({...d,type:"main"}),F=C(!1),$=C(""),q=C(""),De=()=>{F.value=!1},Ue=async()=>{x.value=!0;try{k.value.forEach(d=>{var r;const l=(r=G.value.find(p=>p.id==d.id))==null?void 0:r.planBomCompleteDate;d.planBomCompleteDateChange!=l&&(d.modifyInfo=`BOM\u8BA1\u5212\u5B8C\u6210\u53D8\u66F4\u65F6\u95F4\u4ECE${l}\u4FEE\u6539\u4E3A${d.planBomCompleteDateChange}`,d.reason=$,d.category=q)}),await S.batchSaveTask(k.value),F.value=!1,$.value="",q.value="",N.success("\u4FDD\u5B58\u6210\u529F"),Q(),z()}finally{x.value=!1}},Ye=async()=>{x.value=!0;try{k.value.length==0&&await de(),k.value.some(d=>d.id)&&G.value.some(d=>{var r;return((r=k.value.find(p=>p.id==d.id))==null?void 0:r.planBomCompleteDateChange)!=d.planBomCompleteDateChange})?F.value=!0:(await S.batchSaveTask(k.value),N.success("\u4FDD\u5B58\u6210\u529F"),Q(),z())}finally{x.value=!1}},ie=()=>{s.value={id:void 0,businessDate:void 0,seller:void 0,customerCode:void 0,customerName:void 0,itemCode:void 0,itemName:void 0,spec:void 0,packing:void 0,packingDescription:void 0,logo:void 0,logoDescription:void 0,instruction:void 0,instructionDescription:void 0,program:void 0,programDescription:void 0,structure:void 0,structureDescription:void 0,planBomCompleteDate:void 0,remark:void 0}},Q=d=>{ie(),k.value=[],O.value=!1,Z.value=!1,d&&d()},H=pe(async(d,l,r)=>{let p={};p[r]=d;const R=await S.getCustomerList(p);l(r=="code"?R.map(B=>({value:B.code,label:B.name})):R.map(B=>({value:B.name,label:B.code})))},500),ne=(d,l)=>{l=="code"?s.value.customerName=d.label:s.value.customerCode=d.label},se=pe(async(d,l)=>{l((await S.getItemList(d)).map(r=>({value:r.itemCode,name:r.itemName,spec:r.spec})))},500),ze=d=>{s.value.itemName=d.name,s.value.spec=d.spec},G=C([]),Z=C(!1),Ne=()=>{var l;const d=(l=o(E))==null?void 0:l.getCheckboxRecords();d&&d.length!=0?(k.value=d,G.value=He(d),O.value=!0,Z.value=!0):N.error("\u8BF7\u9009\u62E9\u8981\u4FEE\u6539\u7684\u884C\uFF01")},Me=()=>{var l;const d=(l=o(E))==null?void 0:l.getCheckboxRecords();d&&d.length!=0?N.confirm("\u786E\u5B9A\u8981\u5220\u9664\u9009\u62E9\u7684\u5185\u5BB9\u5417\uFF1F").then(()=>{S.batchDelete(d.map(r=>r.id)),N.success("\u5220\u9664\u6210\u529F"),z()}):N.error("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u884C\uFF01")},Be=async()=>{const d=await S.export(n.value);fl.excel(d,"\u8BA2\u5355BOM\u8DDF\u8FDB\u6570\u636E.xlsx")};return Pe(()=>{z(),je(()=>{var d;(d=o(E))==null||d.connect(o(oe))})}),(d,l)=>{const r=Qe,p=Ge,R=Ze,B=Je,xe=W("vxe-toolbar"),m=W("vxe-column"),Y=Ke,g=We,y=Xe,L=el,me=W("vxe-table"),Se=rl,D=ll,U=al,T=ol,Oe=tl,Ie=ul,re=ml,J=dl,Fe=il,Re=nl,K=Ee("hasPermi");return i(),b(V,null,[a(xe,{ref_key:"toolbarRef",ref:oe,custom:"",size:"mini"},{buttons:t(()=>[X((i(),c(r,{type:"success",size:"small",plain:"",onClick:l[0]||(l[0]=e=>O.value=!0)},{default:t(()=>l[49]||(l[49]=[_(" \u5F55\u5165\u9700\u6C42 ")])),_:1})),[[K,["order-bom:task:save"]]]),a(r,{type:"primary",size:"small",plain:"",onClick:f},{default:t(()=>l[50]||(l[50]=[_("\u67E5\u8BE2")])),_:1}),a(r,{type:"warning",size:"small",plain:"",onClick:ge},{default:t(()=>l[51]||(l[51]=[_("\u6E05\u7A7A\u7B5B\u9009")])),_:1}),X((i(),c(r,{type:"info",size:"small",plain:"",onClick:Ne},{default:t(()=>l[52]||(l[52]=[_("\u4FEE\u6539")])),_:1})),[[K,["order-bom:task:save"]]]),X((i(),c(r,{type:"danger",size:"small",plain:"",onClick:Me},{default:t(()=>l[53]||(l[53]=[_("\u5220\u9664")])),_:1})),[[K,["order-bom:task:delete"]]]),a(p,{modelValue:o(n).overdue,"onUpdate:modelValue":l[1]||(l[1]=e=>o(n).overdue=e),class:"ml-10px",size:"small",placeholder:"\u672A\u5B8C\u6210\u6570\u636E",onChange:f},null,8,["modelValue"])]),tools:t(()=>[a(R,{"active-text":"\u9690\u85CF\u5DF2\u5B8C\u6210","inactive-text":"\u663E\u793A\u6240\u6709",size:"small",modelValue:o(n).showAll,"onUpdate:modelValue":l[2]||(l[2]=e=>o(n).showAll=e),onChange:f},null,8,["modelValue"]),a(r,{size:"small",circle:"",onClick:Be},{default:t(()=>[a(B,{icon:"ep:download"})]),_:1})]),_:1},512),v("div",bl,[a(me,{"cell-config":{height:34},"row-config":{height:34,isCurrent:!0,isHover:!0},id:"mainTable",onCellClick:l[20]||(l[20]=e=>(u=>{var M;u.column.field==="planBomCompleteDateChange"&&((M=ue.value)==null||M.openForm(we(u.row)))})(e)),"custom-config":o(vl),"header-cell-style":{padding:0,height:"30px"},"cell-style":{padding:0,height:"30px"},"checkbox-config":{labelField:"check",range:!0},"column-config":{resizable:!0,maxFixedSize:0},"virtual-y-config":{enabled:!0,gt:0},align:"center",border:"","show-overflow":"",height:"100%",data:o(ee),ref_key:"tableRef",ref:E,size:"small","row-style":ke},{default:t(()=>[a(m,{type:"checkbox",width:"50",field:"check",align:"center"}),a(m,{field:"businessDate",width:"200",title:"\u65E5\u671F"},{header:t(()=>[l[54]||(l[54]=v("div",null,"\u65E5\u671F",-1)),a(p,{modelValue:o(n).businessDate,"onUpdate:modelValue":l[3]||(l[3]=e=>o(n).businessDate=e),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"seller",width:"100",title:"\u4E1A\u52A1\u5458"},{header:t(()=>[l[55]||(l[55]=v("div",null,"\u4E1A\u52A1\u5458",-1)),a(Y,{modelValue:o(n).seller,"onUpdate:modelValue":l[4]||(l[4]=e=>o(n).seller=e),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"customerName",width:"120",title:"\u5BA2\u6237"},{header:t(()=>[l[56]||(l[56]=v("div",null,"\u5BA2\u6237",-1)),a(Y,{modelValue:o(n).customerName,"onUpdate:modelValue":l[5]||(l[5]=e=>o(n).customerName=e),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"itemCode",width:"120",title:"\u54C1\u53F7"},{header:t(()=>[l[57]||(l[57]=v("div",null,"\u54C1\u53F7",-1)),a(Y,{modelValue:o(n).itemCode,"onUpdate:modelValue":l[6]||(l[6]=e=>o(n).itemCode=e),clearable:"",placeholder:"\u5DE6\u67E5\u8BE2",size:"small",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"itemName",width:"140",title:"\u54C1\u540D"},{header:t(()=>[l[58]||(l[58]=v("div",null,"\u54C1\u540D",-1)),a(Y,{modelValue:o(n).itemName,"onUpdate:modelValue":l[7]||(l[7]=e=>o(n).itemName=e),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"spec","min-width":"200",title:"\u89C4\u683C"},{header:t(()=>[l[59]||(l[59]=v("div",null,"\u89C4\u683C",-1)),a(Y,{modelValue:o(n).spec,"onUpdate:modelValue":l[8]||(l[8]=e=>o(n).spec=e),clearable:"",placeholder:"\u6A21\u7CCA\u67E5\u8BE2",size:"small",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"lastOrderQty",width:"120",title:"\u6700\u540E\u4E0B\u5355\u6570\u91CF"},{header:t(()=>l[60]||(l[60]=[v("div",null,"\u6700\u540E\u4E0B\u5355\u6570\u91CF",-1)])),_:1}),a(m,{field:"lastOrderDate",width:"120",title:"\u6700\u540E\u4E0B\u5355\u65E5\u671F"},{header:t(()=>l[61]||(l[61]=[v("div",null,"\u6700\u540E\u4E0B\u5355\u65E5\u671F",-1)])),_:1}),a(m,{field:"packing",width:"120",title:"\u5B9A\u5236\u5305\u6750\u7C7B"},{header:t(()=>[l[62]||(l[62]=v("div",null,"\u5B9A\u5236\u5305\u6750\u7C7B",-1)),a(y,{modelValue:o(n).packing,"onUpdate:modelValue":l[9]||(l[9]=e=>o(n).packing=e),multiple:"",clearable:"","collapse-tags":"",size:"small",onChange:f},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_packing_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128)),(i(),b(V,null,h(A,e=>a(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[e.packing&&(e==null?void 0:e.packing)!="99"?(i(),c(j,{key:1,data:e.packingDetail,onSuccess:z},null,8,["data"])):(i(),c(L,{key:0,type:"success"},{default:t(()=>l[63]||(l[63]=[_("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),a(m,{field:"logo",width:"120",title:"\u5B9A\u5236logo\u7C7B"},{header:t(()=>[l[64]||(l[64]=v("div",null,"\u5B9A\u5236logo\u7C7B",-1)),a(y,{modelValue:o(n).logo,"onUpdate:modelValue":l[10]||(l[10]=e=>o(n).logo=e),multiple:"",clearable:"","collapse-tags":"",size:"small",onChange:f},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_logo_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128)),(i(),b(V,null,h(A,e=>a(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[e.logo&&(e==null?void 0:e.logo)!="99"?(i(),c(j,{key:1,data:e.logoDetail,onSuccess:z},null,8,["data"])):(i(),c(L,{key:0,type:"success"},{default:t(()=>l[65]||(l[65]=[_("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),a(m,{field:"instruction",width:"120",title:"\u5B9A\u5236\u8BF4\u660E\u4E66\u7C7B"},{header:t(()=>[l[66]||(l[66]=v("div",null,"\u5B9A\u5236\u8BF4\u660E\u4E66\u7C7B",-1)),a(y,{modelValue:o(n).instruction,"onUpdate:modelValue":l[11]||(l[11]=e=>o(n).instruction=e),multiple:"",clearable:"","collapse-tags":"",size:"small",onChange:f},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_instruction_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128)),(i(),b(V,null,h(A,e=>a(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[e.instruction&&(e==null?void 0:e.instruction)!="99"?(i(),c(j,{key:1,data:e.instructionDetail,onSuccess:z},null,8,["data"])):(i(),c(L,{key:0,type:"success"},{default:t(()=>l[67]||(l[67]=[_("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),a(m,{field:"program",width:"120",title:"\u5B9A\u5236\u7A0B\u5E8F\u7C7B"},{header:t(()=>[l[68]||(l[68]=v("div",null,"\u5B9A\u5236\u7A0B\u5E8F\u7C7B",-1)),a(y,{modelValue:o(n).program,"onUpdate:modelValue":l[12]||(l[12]=e=>o(n).program=e),multiple:"",clearable:"","collapse-tags":"",size:"small",onChange:f},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_program_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128)),(i(),b(V,null,h(A,e=>a(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[e.program&&(e==null?void 0:e.program)!="99"?(i(),c(j,{key:1,data:e.programDetail,onSuccess:z},null,8,["data"])):(i(),c(L,{key:0,type:"success"},{default:t(()=>l[69]||(l[69]=[_("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),a(m,{field:"structure",width:"120",title:"\u5B9A\u5236\u7ED3\u6784\u7C7B"},{header:t(()=>[l[70]||(l[70]=v("div",null,"\u5B9A\u5236\u7ED3\u6784\u7C7B",-1)),a(y,{modelValue:o(n).structure,"onUpdate:modelValue":l[13]||(l[13]=e=>o(n).structure=e),multiple:"",clearable:"","collapse-tags":"",size:"small",onChange:f},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_structure_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128)),(i(),b(V,null,h(A,e=>a(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[e.structure&&(e==null?void 0:e.structure)!="99"?(i(),c(j,{key:1,data:e.structureDetail,onSuccess:z},null,8,["data"])):(i(),c(L,{key:0,type:"success"},{default:t(()=>l[71]||(l[71]=[_("\u65E0\u5B9A\u5236")])),_:1}))]),_:1}),a(m,{field:"remark",width:"120",title:"\u5907\u6CE8"},{header:t(()=>[l[72]||(l[72]=v("div",null,"\u5907\u6CE8",-1)),a(Y,{modelValue:o(n).remark,"onUpdate:modelValue":l[14]||(l[14]=e=>o(n).remark=e),size:"small",clearable:"",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"planBomCompleteDate",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"},{header:t(()=>[l[73]||(l[73]=v("div",null,"BOM\u8BA1\u5212\u5B8C\u6210\u65E5\u671F",-1)),a(p,{modelValue:o(n).planBomCompleteDate,"onUpdate:modelValue":l[15]||(l[15]=e=>o(n).planBomCompleteDate=e),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"planBomCompleteDateChange",width:"200",title:"BOM\u8BA1\u5212\u5B8C\u6210\u53D8\u66F4\u65E5\u671F","class-name":"cursor-pointer"},{header:t(()=>[l[74]||(l[74]=v("div",null,"BOM\u8BA1\u5212\u5B8C\u6210\u53D8\u66F4\u65E5\u671F",-1)),a(p,{modelValue:o(n).planBomCompleteDateChange,"onUpdate:modelValue":l[16]||(l[16]=e=>o(n).planBomCompleteDateChange=e),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"planCompleteDate",width:"200",title:"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F"},{header:t(()=>[l[75]||(l[75]=v("div",null,"\u8BA1\u5212\u5B8C\u6210\u65E5\u671F",-1)),a(p,{modelValue:o(n).planCompleteDate,"onUpdate:modelValue":l[17]||(l[17]=e=>o(n).planCompleteDate=e),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"actualCompleteDate",width:"200",title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F"},{header:t(()=>[l[76]||(l[76]=v("div",null,"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F",-1)),a(p,{modelValue:o(n).actualCompleteDate,"onUpdate:modelValue":l[18]||(l[18]=e=>o(n).actualCompleteDate=e),clearable:"",type:"daterange","value-format":"YYYY-MM-DD",size:"small",class:"!w-100%",onChange:f},null,8,["modelValue"])]),_:1}),a(m,{field:"taskStatus",width:"100",title:"\u4EFB\u52A1\u72B6\u6001"},{header:t(()=>[l[77]||(l[77]=v("div",{class:"w-100%"},"\u4EFB\u52A1\u72B6\u6001",-1)),a(y,{modelValue:o(n).taskStatus,"onUpdate:modelValue":l[19]||(l[19]=e=>o(n).taskStatus=e),clearable:"","collapse-tags":"",size:"small",class:"!w-100%",onChange:z},{default:t(()=>[a(g,{label:"\u672A\u5230\u671F",value:"\u672A\u5230\u671F"}),a(g,{label:"\u8D85\u671F\u672A\u5B8C\u6210",value:"\u8D85\u671F\u672A\u5B8C\u6210"}),a(g,{label:"\u8D85\u671F\u5B8C\u6210",value:"\u8D85\u671F\u5B8C\u6210"}),a(g,{label:"\u6309\u671F\u5B8C\u6210",value:"\u6309\u671F\u5B8C\u6210"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["custom-config","data"])]),a(Se,{limit:o(n).pageSize,"onUpdate:limit":l[21]||(l[21]=e=>o(n).pageSize=e),page:o(n).pageNo,"onUpdate:page":l[22]||(l[22]=e=>o(n).pageNo=e),total:o(le),onPagination:z,size:"small"},null,8,["limit","page","total"]),a(re,{title:"\u5F55\u5165\u8BA2\u5355\u9700\u6C42",modelValue:o(O),"onUpdate:modelValue":l[44]||(l[44]=e=>P(O)?O.value=e:null),width:"80%","before-close":Q},{footer:t(()=>[a(r,{type:"primary",size:"small",onClick:Ye,loading:o(x)},{default:t(()=>l[81]||(l[81]=[_(" \u4FDD\u5B58 ")])),_:1},8,["loading"])]),default:t(()=>[a(Ie,{"label-width":"105",size:"small",model:o(s),rules:o(fe),ref_key:"formRef",ref:te},{default:t(()=>[a(Oe,null,{default:t(()=>[a(U,{span:6},{default:t(()=>[a(D,{label:"\u65E5\u671F"},{default:t(()=>[a(p,{class:"!w-100%",modelValue:o(s).businessDate,"onUpdate:modelValue":l[23]||(l[23]=e=>o(s).businessDate=e),type:"date",placeholder:"\u9009\u62E9\u65E5\u671F","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u4E1A\u52A1\u5458"},{default:t(()=>[a(Y,{modelValue:o(s).seller,"onUpdate:modelValue":l[24]||(l[24]=e=>o(s).seller=e),clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5BA2\u6237\u7F16\u7801"},{default:t(()=>[a(T,{modelValue:o(s).customerCode,"onUpdate:modelValue":l[25]||(l[25]=e=>o(s).customerCode=e),"fetch-suggestions":(e,u)=>o(H)(e,u,"code"),clearable:"",onSelect:l[26]||(l[26]=e=>ne(e,"code")),onClear:l[27]||(l[27]=()=>{o(s).customerName=void 0})},null,8,["modelValue","fetch-suggestions"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5BA2\u6237\u540D\u79F0"},{default:t(()=>[a(T,{modelValue:o(s).customerName,"onUpdate:modelValue":l[28]||(l[28]=e=>o(s).customerName=e),"fetch-suggestions":(e,u)=>o(H)(e,u,"name"),clearable:"",onSelect:l[29]||(l[29]=e=>ne(e,"name")),onClear:l[30]||(l[30]=()=>{o(s).customerCode=void 0})},null,8,["modelValue","fetch-suggestions"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u54C1\u53F7",prop:"itemCode"},{default:t(()=>[a(T,{modelValue:o(s).itemCode,"onUpdate:modelValue":l[31]||(l[31]=e=>o(s).itemCode=e),"fetch-suggestions":o(se),clearable:"",onSelect:ze,onClear:l[32]||(l[32]=()=>{o(s).itemName=void 0,o(s).spec=void 0})},null,8,["modelValue","fetch-suggestions"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u54C1\u540D"},{default:t(()=>[a(Y,{modelValue:o(s).itemName,"onUpdate:modelValue":l[33]||(l[33]=e=>o(s).itemName=e),disabled:!0},null,8,["modelValue"])]),_:1})]),_:1}),a(U,{span:12},{default:t(()=>[a(D,{label:"\u89C4\u683C"},{default:t(()=>[a(Y,{modelValue:o(s).spec,"onUpdate:modelValue":l[34]||(l[34]=e=>o(s).spec=e),disabled:!0},null,8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5B9A\u5236\u5305\u6750"},{default:t(()=>[a(y,{modelValue:o(s).packing,"onUpdate:modelValue":l[35]||(l[35]=e=>o(s).packing=e),clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_packing_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5B9A\u5236logo"},{default:t(()=>[a(y,{modelValue:o(s).logo,"onUpdate:modelValue":l[36]||(l[36]=e=>o(s).logo=e),clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_logo_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5B9A\u5236\u8BF4\u660E\u4E66"},{default:t(()=>[a(y,{modelValue:o(s).instruction,"onUpdate:modelValue":l[37]||(l[37]=e=>o(s).instruction=e),clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_instruction_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5B9A\u5236\u7A0B\u5E8F"},{default:t(()=>[a(y,{modelValue:o(s).program,"onUpdate:modelValue":l[38]||(l[38]=e=>o(s).program=e),clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_program_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"\u5B9A\u5236\u7ED3\u6784"},{default:t(()=>[a(y,{modelValue:o(s).structure,"onUpdate:modelValue":l[39]||(l[39]=e=>o(s).structure=e),clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_structure_dict"),e=>(i(),c(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(U,{span:6},{default:t(()=>[a(D,{label:"BOM\u8BA1\u5212\u5B8C\u6210"},{default:t(()=>[a(p,{modelValue:o(s).planBomCompleteDate,"onUpdate:modelValue":l[40]||(l[40]=e=>o(s).planBomCompleteDate=e),type:"date","value-format":"YYYY-MM-DD",class:"!w-100%",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),a(U,{span:24},{default:t(()=>[a(D,{label:"\u5907\u6CE8"},{default:t(()=>[a(Y,{modelValue:o(s).remark,"onUpdate:modelValue":l[41]||(l[41]=e=>o(s).remark=e),clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]),v("div",Vl,[a(r,{type:"info",plain:"",size:"small",onClick:l[42]||(l[42]=e=>I.value=!0)},{default:t(()=>l[78]||(l[78]=[_(" \u6279\u91CF\u7C98\u8D34\u89E3\u6790 ")])),_:1}),a(r,{type:"primary",plain:"",size:"small",onClick:de,loading:o(x)},{default:t(()=>l[79]||(l[79]=[_(" \u7EE7\u7EED\u6DFB\u52A0 ")])),_:1},8,["loading"])]),a(me,{"header-cell-style":{padding:0,height:"30px"},"cell-style":{padding:0},"cell-config":{height:34},"column-config":{resizable:!0},align:"center",border:"","show-overflow":"",height:"380px",data:o(k),stripe:"","menu-config":o(_e),onMenuClick:Ce},{default:t(()=>[a(m,{title:"\u65E5\u671F",field:"businessDate",width:"120"},{default:t(({row:e})=>[a(p,{modelValue:e.businessDate,"onUpdate:modelValue":u=>e.businessDate=u,type:"date","value-format":"YYYY-MM-DD",placeholder:"\u9009\u62E9\u65E5\u671F",class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u4E1A\u52A1\u5458",field:"seller",width:"120"},{default:t(({row:e})=>[a(Y,{modelValue:e.seller,"onUpdate:modelValue":u=>e.seller=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u5BA2\u6237\u7F16\u7801",field:"customerCode",width:"120"},{default:t(({row:e})=>[a(T,{modelValue:e.customerCode,"onUpdate:modelValue":u=>e.customerCode=u,"fetch-suggestions":(u,M)=>o(H)(u,M,"code"),clearable:"",onSelect:u=>{e.customerName=u.label},onClear:()=>{e.customerName=void 0}},null,8,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect","onClear"])]),_:1}),a(m,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName",width:"120"},{default:t(({row:e})=>[a(T,{modelValue:e.customerName,"onUpdate:modelValue":u=>e.customerName=u,"fetch-suggestions":(u,M)=>o(H)(u,M,"name"),clearable:"",onSelect:u=>{e.customerCode=u.label},onClear:l[43]||(l[43]=()=>{o(s).customerCode=void 0})},null,8,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])]),_:1}),a(m,{title:"\u54C1\u53F7",field:"itemCode",width:"120"},{default:t(({row:e})=>[a(T,{modelValue:e.itemCode,"onUpdate:modelValue":u=>e.itemCode=u,"fetch-suggestions":o(se),clearable:"",onSelect:u=>{e.itemName=u.name,e.spec=u.spec},onClear:()=>{e.itemName=void 0,e.spec=void 0}},null,8,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect","onClear"])]),_:1}),a(m,{title:"\u54C1\u540D",field:"itemName",width:"120"}),a(m,{title:"\u89C4\u683C",field:"spec",width:"120"}),a(m,{title:"\u5B9A\u5236\u5305\u6750",field:"packing",width:"150"},{default:t(({row:e})=>[a(y,{modelValue:e.packing,"onUpdate:modelValue":u=>e.packing=u,clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_packing_dict"),u=>(i(),c(g,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u5B9A\u5236Logo",field:"logo",width:"150"},{default:t(({row:e})=>[a(y,{modelValue:e.logo,"onUpdate:modelValue":u=>e.logo=u,clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_logo_dict"),u=>(i(),c(g,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u5B9A\u5236\u8BF4\u660E\u4E66",field:"instruction",width:"150"},{default:t(({row:e})=>[a(y,{modelValue:e.instruction,"onUpdate:modelValue":u=>e.instruction=u,clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_instruction_dict"),u=>(i(),c(g,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u5B9A\u5236\u7A0B\u5E8F",field:"program",width:"120"},{default:t(({row:e})=>[a(y,{modelValue:e.program,"onUpdate:modelValue":u=>e.program=u,clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_program_dict"),u=>(i(),c(g,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u5B9A\u5236\u7ED3\u6784",field:"structure",width:"120"},{default:t(({row:e})=>[a(y,{modelValue:e.structure,"onUpdate:modelValue":u=>e.structure=u,clearable:""},{default:t(()=>[(i(!0),b(V,null,h(o(w)("eng_structure_dict"),u=>(i(),c(g,{key:u.value,label:u.label,value:u.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"BOM\u8BA1\u5212\u5B8C\u6210",field:"planBomCompleteDate",width:"140"},{default:t(({row:e})=>[a(p,{modelValue:e.planBomCompleteDate,"onUpdate:modelValue":u=>e.planBomCompleteDate=u,type:"date","value-format":"YYYY-MM-DD",class:"!w-100%",clearable:"",disabled:e.id},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),o(Z)?(i(),c(m,{key:0,title:"BOM\u8BA1\u5212\u5B8C\u6210\u53D8\u66F4",field:"planBomCompleteDateChange",width:"140"},{default:t(({row:e})=>[a(p,{modelValue:e.planBomCompleteDateChange,"onUpdate:modelValue":u=>e.planBomCompleteDateChange=u,type:"date","value-format":"YYYY-MM-DD",class:"!w-100%",clearable:"",disabled:e.id&&!o(gl)(["edit:bom-complete:date"])},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})):ve("",!0),k.value.some(e=>{const u=!e.instruction||e.instruction==="99",M=!e.logo||e.logo==="99",Te=!e.packing||e.packing==="99",Ae=!e.program||e.program==="99",$e=!e.structure||e.structure==="99";return u&&M&&Te&&Ae&&$e})?(i(),c(m,{key:1,title:"\u5B9E\u9645\u5B8C\u6210\u65E5\u671F",field:"actualCompleteDateMain",width:"140"},{default:t(({row:e})=>[a(p,{modelValue:e.actualCompleteDateMain,"onUpdate:modelValue":u=>e.actualCompleteDateMain=u,type:"date","value-format":"YYYY-MM-DD",class:"!w-100%",clearable:"",disabled:e.id&&!ye(e)},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})):ve("",!0),a(m,{title:"\u5907\u6CE8",field:"remark",width:"120"},{default:t(({row:e})=>[a(Y,{modelValue:e.remark,"onUpdate:modelValue":u=>e.remark=u},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(m,{title:"\u64CD\u4F5C",field:"opertion",width:"80",fixed:"right"},{default:t(({rowIndex:e})=>[a(r,{type:"danger",link:"",onClick:u=>(async M=>{await N.confirm("\u786E\u5B9A\u8981\u79FB\u9664\u8BE5\u884C\u5417\uFF1F"),k.value.splice(M,1),N.success("\u79FB\u9664\u6210\u529F")})(e)},{default:t(()=>l[80]||(l[80]=[_("\u79FB\u9664")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","menu-config"])]),_:1},8,["modelValue"]),a(re,{title:"\u6570\u636E\u6279\u91CF\u7C98\u8D34\u89E3\u6790",modelValue:o(I),"onUpdate:modelValue":l[45]||(l[45]=e=>P(I)?I.value=e:null),width:"90%","before-close":()=>I.value=!1},{default:t(()=>[a(pl,{"target-fields":be,mappings:Ve,onDataParsed:he})]),_:1},8,["modelValue","before-close"]),a(Re,{modelValue:o(F),"onUpdate:modelValue":l[48]||(l[48]=e=>P(F)?F.value=e:null),title:"\u4FEE\u6539\u539F\u56E0",width:"400","align-center":""},{footer:t(()=>[v("span",hl,[a(r,{onClick:De},{default:t(()=>l[87]||(l[87]=[_("\u53D6\u6D88")])),_:1}),a(r,{type:"primary",onClick:Ue},{default:t(()=>l[88]||(l[88]=[_("\u786E\u8BA4")])),_:1})])]),default:t(()=>[v("div",null,[l[85]||(l[85]=v("p",null,"\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0",-1)),a(Y,{modelValue:o($),"onUpdate:modelValue":l[46]||(l[46]=e=>P($)?$.value=e:null),type:"textarea",style:{width:"380px"},placeholder:"\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0"},null,8,["modelValue"]),l[86]||(l[86]=v("p",null,"\u8BF7\u9009\u62E9\u8D23\u4EFB\u4E3B\u4F53",-1)),a(Fe,{modelValue:o(q),"onUpdate:modelValue":l[47]||(l[47]=e=>P(q)?q.value=e:null),style:{display:"block","margin-bottom":"10px"}},{default:t(()=>[a(J,{label:"\u7814\u53D1"},{default:t(()=>l[82]||(l[82]=[_("\u7814\u53D1")])),_:1}),a(J,{label:"\u5DE5\u7A0B"},{default:t(()=>l[83]||(l[83]=[_("\u5DE5\u7A0B")])),_:1}),a(J,{label:"\u4E1A\u52A1"},{default:t(()=>l[84]||(l[84]=[_("\u4E1A\u52A1")])),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"]),a(cl,{ref_key:"detailFormRef",ref:ue,width:"34%",onSuccess:f},null,512)],64)}}}),[["__scopeId","data-v-af1a8f8d"]]);export{_l as default};
