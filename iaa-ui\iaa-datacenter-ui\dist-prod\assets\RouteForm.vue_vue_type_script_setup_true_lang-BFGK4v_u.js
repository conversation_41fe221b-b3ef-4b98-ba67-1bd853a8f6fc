import{d as T,aj as D,j as b,r as G,y as J,aF as x,o as p,h as c,w as o,u as e,v as y,$ as k,x as K,k as d,c as M,F as N,g as P,m as Q,ax as W,S as X,B as Y,a8 as Z,ab as ee,I as le,z as de,A as ae,aB as oe,H as ie,L as te}from"./index-C8b06LRn.js";import{_ as se}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{R as U}from"./index-BqCcY1XT.js";const ue=T({__name:"RouteForm",props:{defineList:D.oneOfType([]).isRequired},emits:["success"],setup(I,{expose:C,emit:R}){const f=b(!1),q=I,i=b({id:void 0,code:void 0,name:void 0,version:0,description:void 0,body:[]}),F=G({code:[{required:!0,message:"\u8BF7\u8F93\u5165\u7F16\u7801",trigger:"blur"}],name:[{required:!0,message:"\u8BF7\u8F93\u5165\u540D\u79F0",trigger:"blur"}]}),h=b(),u=b(!1),_=J(),B=R,t=b(!0),L=async()=>{var m;await((m=e(h))==null?void 0:m.validate()),u.value=!0;try{i.value.body=i.value.body.filter(n=>!!n.defineId);const l=W(i.value);if(!l.body||l.body.length===0)return void _.error("\u8BF7\u6DFB\u52A0\u5DE5\u827A\u8DEF\u7EBF\u884C");let r=1;l.body.forEach(n=>n.sort=r++),await U.saveRoute(l),_.success("\u4FDD\u5B58\u6210\u529F"),B("success"),w(),f.value=!1}finally{u.value=!1}},w=()=>{i.value={id:void 0,code:void 0,name:void 0,version:0,description:void 0,body:[]}};return C({openForm:async m=>{u.value=!0;try{if(t.value=!0,w(),f.value=!0,m){const l=await U.getRouteById(m);i.value=l,t.value=!1}}finally{u.value=!1}}}),(m,l)=>{const r=X,n=Y,V=Z,j=ee,z=le,v=x("vxe-column"),H=de,O=ae,$=oe,g=ie,A=x("vxe-table"),E=se,S=te;return p(),c(E,{title:"\u5DE5\u827A\u8DEF\u7EBF",modelValue:e(f),"onUpdate:modelValue":l[6]||(l[6]=a=>Q(f)?f.value=a:null),width:"60%"},{footer:o(()=>[e(t)?(p(),c(g,{key:0,type:"primary",onClick:L,loading:e(u)},{default:o(()=>l[9]||(l[9]=[y("\u4FDD\u5B58")])),_:1},8,["loading"])):k("",!0),e(t)?k("",!0):(p(),c(g,{key:1,type:"warning",onClick:l[5]||(l[5]=a=>t.value=!e(t)),loading:e(u)},{default:o(()=>l[10]||(l[10]=[y("\u4FEE\u6539")])),_:1},8,["loading"]))]),default:o(()=>[K((p(),c(z,{model:e(i),rules:e(F),ref_key:"formRef",ref:h},{default:o(()=>[d(j,{gutter:20},{default:o(()=>[d(V,{span:8},{default:o(()=>[d(n,{label:"\u7F16\u7801",prop:"code"},{default:o(()=>[d(r,{modelValue:e(i).code,"onUpdate:modelValue":l[0]||(l[0]=a=>e(i).code=a),maxlength:64,"show-word-limit":"",disabled:e(i).id},null,8,["modelValue","disabled"])]),_:1})]),_:1}),d(V,{span:8},{default:o(()=>[d(n,{label:"\u540D\u79F0",prop:"name"},{default:o(()=>[d(r,{modelValue:e(i).name,"onUpdate:modelValue":l[1]||(l[1]=a=>e(i).name=a),maxlength:255,"show-word-limit":"",disabled:!e(t)},null,8,["modelValue","disabled"])]),_:1})]),_:1}),d(V,{span:8},{default:o(()=>[d(n,{label:"\u7248\u672C"},{default:o(()=>[d(r,{disabled:!0,modelValue:e(i).version,"onUpdate:modelValue":l[2]||(l[2]=a=>e(i).version=a)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(n,{label:"\u63CF\u8FF0"},{default:o(()=>[d(r,{type:"textarea",rows:4,modelValue:e(i).description,"onUpdate:modelValue":l[3]||(l[3]=a=>e(i).description=a),maxlength:500,"show-word-limit":"",disabled:!e(t)},null,8,["modelValue","disabled"])]),_:1})]),_:1},8,["model","rules"])),[[S,e(u)]]),d(A,{height:"300px","header-cell-style":{padding:0,height:"30px"},"cell-style":{padding:0,height:"30px"},data:e(i).body,loading:e(u),align:"center","row-config":{drag:!0,height:30}},{default:o(()=>[d(v,{type:"seq",width:"80"}),d(v,{title:"\u5DE5\u827A\u7EC4",field:"defineId"},{default:o(({row:a})=>[d(O,{modelValue:a.defineId,"onUpdate:modelValue":s=>a.defineId=s,disabled:!e(t)},{default:o(()=>[(p(!0),M(N,null,P(q.defineList,s=>(p(),c(H,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),d(v,{title:"\u6807\u51C6\u5DE5\u65F6(\u5206)"},{default:o(({row:a})=>[d($,{modelValue:a.timeConsuming,"onUpdate:modelValue":s=>a.timeConsuming=s,min:0,precision:2,disabled:!e(t)},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),d(v,{title:"\u5907\u6CE8"},{default:o(({row:a})=>[d(r,{modelValue:a.reamrks,"onUpdate:modelValue":s=>a.reamrks=s,maxlength:255,"show-word-limit":"",disabled:!e(t)},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),d(v,{title:"\u64CD\u4F5C",width:"80",disabled:e(t)},{default:o(({rowIndex:a})=>[d(g,{type:"text",onClick:s=>e(i).body.splice(a,1),disabled:!e(t)},{default:o(()=>l[7]||(l[7]=[y("\u79FB\u9664")])),_:2},1032,["onClick","disabled"])]),_:1},8,["disabled"])]),_:1},8,["data","loading"]),d(g,{type:"primary",plain:"",class:"w-full",size:"small",loading:e(u),onClick:l[4]||(l[4]=a=>e(i).body.push({id:void 0,headId:void 0,defineId:void 0,timeConsuming:void 0,reamrks:void 0})),disabled:!e(t)},{default:o(()=>l[8]||(l[8]=[y(" \u6DFB\u52A0\u884C ")])),_:1},8,["loading","disabled"])]),_:1},8,["modelValue"])}}});export{ue as _};
