import{_ as h}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as g,j as r,o as m,h as E,w as t,k as l,v as d,t as u,u as e,D as n,c as b,$ as I,m as P}from"./index-C8b06LRn.js";import{E as x,a as U}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as k}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as c}from"./formatTime-COZ9Bl52.js";const q={key:0},w={key:1},C=g({name:"ApiAccessLogDetail",__name:"ApiAccessLogDetail",setup(D,{expose:v}){const o=r(!1),p=r(!1),a=r({});return v({open:async _=>{o.value=!0,p.value=!0;try{a.value=_}finally{p.value=!1}}}),(_,f)=>{const s=x,i=k,y=U,A=h;return m(),E(A,{modelValue:e(o),"onUpdate:modelValue":f[0]||(f[0]=T=>P(o)?o.value=T:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5",width:"800"},{default:t(()=>[l(y,{column:1,border:""},{default:t(()=>[l(s,{label:"\u65E5\u5FD7\u4E3B\u952E","min-width":"120"},{default:t(()=>[d(u(e(a).id),1)]),_:1}),l(s,{label:"\u94FE\u8DEF\u8FFD\u8E2A"},{default:t(()=>[d(u(e(a).traceId),1)]),_:1}),l(s,{label:"\u5E94\u7528\u540D"},{default:t(()=>[d(u(e(a).applicationName),1)]),_:1}),l(s,{label:"\u7528\u6237\u4FE1\u606F"},{default:t(()=>[d(u(e(a).userId)+" ",1),l(i,{type:e(n).USER_TYPE,value:e(a).userType},null,8,["type","value"])]),_:1}),l(s,{label:"\u7528\u6237 IP"},{default:t(()=>[d(u(e(a).userIp),1)]),_:1}),l(s,{label:"\u7528\u6237 UA"},{default:t(()=>[d(u(e(a).userAgent),1)]),_:1}),l(s,{label:"\u8BF7\u6C42\u4FE1\u606F"},{default:t(()=>[d(u(e(a).requestMethod)+" "+u(e(a).requestUrl),1)]),_:1}),l(s,{label:"\u8BF7\u6C42\u53C2\u6570"},{default:t(()=>[d(u(e(a).requestParams),1)]),_:1}),l(s,{label:"\u8BF7\u6C42\u7ED3\u679C"},{default:t(()=>[d(u(e(a).responseBody),1)]),_:1}),l(s,{label:"\u8BF7\u6C42\u65F6\u95F4"},{default:t(()=>[d(u(e(c)(e(a).beginTime))+" ~ "+u(e(c)(e(a).endTime)),1)]),_:1}),l(s,{label:"\u8BF7\u6C42\u8017\u65F6"},{default:t(()=>[d(u(e(a).duration)+" ms",1)]),_:1}),l(s,{label:"\u64CD\u4F5C\u7ED3\u679C"},{default:t(()=>[e(a).resultCode===0?(m(),b("div",q,"\u6B63\u5E38")):e(a).resultCode>0?(m(),b("div",w," \u5931\u8D25 | "+u(e(a).resultCode)+" | "+u(e(a).resultMsg),1)):I("",!0)]),_:1}),l(s,{label:"\u64CD\u4F5C\u6A21\u5757"},{default:t(()=>[d(u(e(a).operateModule),1)]),_:1}),l(s,{label:"\u64CD\u4F5C\u540D"},{default:t(()=>[d(u(e(a).operateName),1)]),_:1}),l(s,{label:"\u64CD\u4F5C\u540D"},{default:t(()=>[l(i,{type:e(n).INFRA_OPERATE_TYPE,value:e(a).operateType},null,8,["type","value"])]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{C as _};
