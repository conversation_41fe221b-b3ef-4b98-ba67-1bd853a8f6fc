import{d as ae,j as i,r as le,f as te,aF as r,T as se,o as t,c as k,k as y,l as u,u as o,m as N,w as s,F as ie,g as oe,t as w,b4 as _,h as d,v,$ as U,x as b,aO as ue,df as q,_ as ne}from"./index-C8b06LRn.js";import{C as M}from"./claim-BPjhJe4B.js";import ce from"./recordDialogMobile-BcRVzwqq.js";import{s as B}from"./function-call-DKtMTaHE.js";import"./index-DVzg-3-A.js";import"./index-584CLaw9.js";import"./VDatePicker.vue_vue_type_script_setup_true_lang-fYMpMkPC.js";const re={class:"h-[100%]"},de={class:"fixed top-44px left-0 right-0 z-10 bg-white"},ve={class:"h-[12vh] w-[25vh] p-1"},me={class:"text-xs truncate"},pe={class:"text-xs truncate"},ye={class:"text-xs truncate"},he={class:"text-xs truncate"},fe={class:"text-xs truncate"},ge=["src","onClick"],xe={key:1,class:"van-card__img flex items-center justify-center bg-gray-100",style:{width:"100px",height:"100px","object-fit":"cover"}},ke={class:"flex items-center justify-between w-full mt-1 ml-2.5"},we={class:"flex items-center gap-2"},_e={key:3},be={class:"fixed bottom-10 left-0 right-0 bg-white border-t p-10px flex justify-between items-center"},Ce=ne(ae({__name:"recordMobile",setup(ze){const V=i(""),m=i([]),n=i(!1),C=i(!1),z=i(0),P=i(null),p=i(!1),h=i(void 0),j=i(!1),A=i(!1),$=i(!1),S=i(!1),D=i([]),L=le({pageNo:1,pageSize:10,type:2}),O=()=>{E()},E=()=>{m.value=[],z.value=0,L.pageNo=1,C.value=!1,n.value=!1,ue(()=>{var c;return(c=P.value)==null?void 0:c.scrollTo({top:0})}),I()},I=async()=>{if(!C.value){n.value=!0;try{const c=await M.getClaimPage({...L,global:V.value});z.value=c.total;const e=c.list||[];L.pageNo===1?m.value=e:m.value.push(...e),n.value=!1,e.length<L.pageSize?C.value=!0:L.pageNo++}catch{n.value=!1,C.value=!0}}},G=()=>{h.value=void 0,j.value=!1,A.value=!0,p.value=!0},R=async c=>{B({title:"\u5220\u9664\u5F55\u6B3E",message:"\u662F\u5426\u786E\u8BA4\u5220\u9664\u8BE5\u7B14\u6B3E\u9879\uFF1F"}).then(async()=>{await M.deleteClaim([c.id]),q("\u5220\u9664\u6210\u529F");const e=m.value.findIndex(T=>T.id===c.id);e!==-1&&(m.value.splice(e,1),z.value=Math.max(0,z.value-1))}).catch(()=>{})},H=()=>{p.value=!1,h.value=void 0,E()};return te(()=>{I()}),(c,e)=>{const T=r("van-nav-bar"),J=r("van-search"),K=r("van-icon"),F=r("van-tag"),f=r("van-button"),Q=r("van-card"),W=r("van-list"),X=r("van-pull-refresh"),Y=r("van-image-preview"),g=se("hasPermi");return t(),k("div",re,[y(T,{title:"\u5F55\u6B3E\u767B\u8BB0",fixed:""}),u("div",de,[y(J,{modelValue:o(V),"onUpdate:modelValue":e[0]||(e[0]=a=>N(V)?V.value=a:null),placeholder:"\u8BF7\u8F93\u5165\u641C\u7D22\u5173\u952E\u8BCD",onSearch:O,onCancel:O},null,8,["modelValue"])]),u("div",{ref_key:"scrollEl",ref:P,class:"h-[calc(100vh-160px)] overflow-auto px-10px pt-45px mt-12px"},[y(X,{modelValue:o(n),"onUpdate:modelValue":e[2]||(e[2]=a=>N(n)?n.value=a:null),onRefresh:E},{default:s(()=>[y(W,{loading:o(n),"onUpdate:loading":e[1]||(e[1]=a=>N(n)?n.value=a:null),finished:o(C),"finished-text":"\u6CA1\u6709\u66F4\u591A\u6570\u636E\u4E86",onLoad:I},{default:s(()=>[(t(!0),k(ie,null,oe(o(m),a=>(t(),k("div",{key:a.id,class:"mb-10px"},[y(Q,{tag:"",price:`\u603B\u91D1\u989D\uFF1A${a.totalAmount}`,thumb:a.customerList,currency:"",onClick:x=>{return l=a,h.value=l.id,j.value=!0,A.value=!1,void(p.value=!0);var l},class:"rounded-lg shadow-sm"},{title:s(()=>[u("div",ve,[u("div",me,"\u5BA2\u6237\uFF1A"+w(a.customerName),1),u("div",pe,"\u4E1A\u52A1\uFF1A"+w(a.salesmanName),1),u("div",ye,"\u6536\u6B3E\uFF1A"+w(a.collectionAccount),1),u("div",he,"\u5E01\u79CD\uFF1A"+w(a.currency),1),u("div",fe,"\u65E5\u671F\uFF1A"+w(a.claimDate),1)])]),thumb:s(()=>[Array.isArray(a.customerList)&&a.customerList.length>0?(t(),k("img",{key:0,src:a.customerList[0],alt:"\u6682\u65E0\u6C34\u5355",class:"van-card__img",onClick:_(x=>{return l=a.customerList,void(Array.isArray(l)&&l.length>0&&(D.value=l,S.value=!0));var l},["stop"]),style:{width:"100px",height:"100px","object-fit":"cover"}},null,8,ge)):(t(),k("div",xe,[y(K,{name:"photograph",size:"24",color:"#ccc"})]))]),footer:s(()=>[u("div",ke,[a.status===2?(t(),d(F,{key:0,type:"warning",size:"medium"},{default:s(()=>e[5]||(e[5]=[v("\u6682\u5B58")])),_:1})):a.status===1?(t(),d(F,{key:1,type:"success",size:"medium"},{default:s(()=>e[6]||(e[6]=[v("\u8D22\u52A1\u5DF2\u786E\u8BA4")])),_:1})):a.status===3?(t(),d(F,{key:2,type:"primary",size:"medium"},{default:s(()=>e[7]||(e[7]=[v("\u4E1A\u52A1\u5458\u5DF2\u786E\u8BA4")])),_:1})):U("",!0),u("div",we,[a.status===2?b((t(),d(f,{key:0,size:"small",type:"primary",onClick:_(x=>{return l=a,h.value=l.id,j.value=!1,A.value=!1,void(p.value=!0);var l},["stop"]),class:"min-w-60px"},{default:s(()=>e[8]||(e[8]=[v(" \u7EE7\u7EED ")])),_:2},1032,["onClick"])),[[g,["record:money:create"]]]):a.status===3?b((t(),d(f,{key:1,size:"small",type:"success",onClick:_(x=>(async l=>{B({title:"\u786E\u8BA4\u6536\u6B3E",message:"\u662F\u5426\u786E\u8BA4\u8BE5\u7B14\u6B3E\u9879\u5DF2\u6536\u6B3E\uFF1F"}).then(async()=>{await M.updateClaim([l.id]),q("\u786E\u8BA4\u6536\u6B3E\u6210\u529F");const Z=m.value.findIndex(ee=>ee.id===l.id);Z!==-1&&(m.value[Z].status=1)}).catch(()=>{})})(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[9]||(e[9]=[v(" \u786E\u8BA4 ")])),_:2},1032,["onClick"])),[[g,["record:money:status"]]]):U("",!0),a.status!==1?b((t(),d(f,{key:2,size:"small",type:"danger",onClick:_(x=>R(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[10]||(e[10]=[v(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["record:money:delete"]]]):U("",!0),a.status===1?(t(),k("span",_e,[b((t(),d(f,{size:"small",type:"primary",onClick:_(x=>{return l=a,h.value=l.id,j.value=!1,A.value=!1,p.value=!0,void($.value=!0);var l},["stop"]),class:"min-w-60px"},{default:s(()=>e[11]||(e[11]=[v(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[g,["record:money:admin"]]]),b((t(),d(f,{size:"small",type:"danger",onClick:_(x=>R(a),["stop"]),class:"min-w-60px"},{default:s(()=>e[12]||(e[12]=[v(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["record:money:admin"]]])])):U("",!0)])])]),_:2},1032,["price","thumb","onClick"])]))),128))]),_:1},8,["loading","finished"])]),_:1},8,["modelValue"])],512),u("div",be,[u("div",null,"\u5171 "+w(o(z))+" \u6761",1),b((t(),d(f,{type:"primary",size:"small",icon:"plus",onClick:G},{default:s(()=>e[13]||(e[13]=[v(" \u65B0\u589E ")])),_:1})),[[g,["record:money:create"]]])]),y(ce,{show:o(p),"onUpdate:show":e[3]||(e[3]=a=>N(p)?p.value=a:null),id:o(h),"read-only":o(j),"is-add":o(A),isEdit:o($),onSuccess:H},null,8,["show","id","read-only","is-add","isEdit"]),y(Y,{show:o(S),"onUpdate:show":e[4]||(e[4]=a=>N(S)?S.value=a:null),images:o(D),closeable:!0},null,8,["show","images"])])}}}),[["__scopeId","data-v-75e86721"]]);export{Ce as default};
