import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as z,p as L,y as V,j as i,f as A,aF as U,T as $,o as C,c as O,k as t,w as s,l as f,u as n,v as g,t as H,x as K,$ as G,m as P,F as Q,dX as X,dY as Y,aZ as Z,H as q,cz as W}from"./index-C8b06LRn.js";import{_ as ee}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{u as ae}from"./useFormCreateDesigner-5Md06TW6.js";import{H as v,j as te}from"./java-CzcbBSiR.js";import"./el-card-CaOo8U9P.js";import"./dict.type-Bqd7OQxQ.js";function se(r){const o=["true","false","null"],c={scope:"literal",beginKeywords:o.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:o},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},r.QUOTE_STRING_MODE,c,r.C_NUMBER_MODE,r.C_LINE_COMMENT_MODE,r.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}const ne={class:"h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-2px)]"},oe={key:0,ref:"editor"},le={class:"hljs"},ie=z({name:"InfraBuild",__name:"index",setup(r){const{t:o}=L(),c=V(),w=i({switchType:[],autoActive:!0,useTemplate:!1,formOptions:{},fieldReadonly:!1,hiddenDragMenu:!1,hiddenDragBtn:!1,hiddenMenu:[],hiddenItem:[],hiddenItemConfig:{},disabledItemConfig:{},showSaveBtn:!1,showConfig:!0,showBaseForm:!0,showControl:!0,showPropsForm:!0,showEventForm:!0,showValidateForm:!0,showFormConfig:!0,showInputData:!0,showDevice:!0,appendConfigData:[]}),l=i(),u=i(!1),y=i(""),m=i(-1),p=i("");ae(l);const h=a=>{u.value=!0,y.value=a},N=()=>{h("\u751F\u6210 JSON"),m.value=0,p.value=l.value.getRule()},S=()=>{h("\u751F\u6210 Options"),m.value=1,p.value=l.value.getOption()},E=()=>{h("\u751F\u6210\u7EC4\u4EF6"),m.value=2,p.value=M()},M=()=>{const a=l.value.getRule(),e=l.value.getOption();return`<template>
    <form-create
      v-model:api="fApi"
      :rule="rule"
      :option="option"
      @submit="onSubmit"
    ></form-create>
  </template>
  <script setup lang=ts>
    const faps = ref(null)
    const rule = ref('')
    const option = ref('')
    const init = () => {
      rule.value = formCreate.parseJson('${X.toJson(a).replaceAll("\\","\\\\")}')
      option.value = formCreate.parseJson('${JSON.stringify(e)}')
    }
    const onSubmit = (formData) => {
      //todo \u63D0\u4EA4\u8868\u5355
    }
    init()
  <\/script>`},D=a=>{let e="json";return m.value===2&&(e="xml"),Z(a)||(a=JSON.stringify(a)),v.highlight(e,a,!0).value||"&nbsp;"};return A(async()=>{v.registerLanguage("xml",te),v.registerLanguage("json",se)}),(a,e)=>{const d=q,b=U("fc-designer"),J=ee,k=W,x=R,j=$("dompurify-html");return C(),O(Q,null,[t(J,{"body-style":{padding:"0px"},class:"!mb-0"},{default:s(()=>[f("div",ne,[t(b,{class:"my-designer",ref_key:"designer",ref:l,config:n(w)},{handle:s(()=>[t(d,{size:"small",type:"primary",plain:"",onClick:N},{default:s(()=>e[2]||(e[2]=[g("\u751F\u6210JSON")])),_:1}),t(d,{size:"small",type:"success",plain:"",onClick:S},{default:s(()=>e[3]||(e[3]=[g("\u751F\u6210Options")])),_:1}),t(d,{size:"small",type:"danger",plain:"",onClick:E},{default:s(()=>e[4]||(e[4]=[g("\u751F\u6210\u7EC4\u4EF6")])),_:1})]),_:1},8,["config"])])]),_:1}),t(x,{modelValue:n(u),"onUpdate:modelValue":e[1]||(e[1]=_=>P(u)?u.value=_:null),title:n(y),"max-height":"600"},{default:s(()=>[n(u)?(C(),O("div",oe,[t(d,{style:{float:"right"},onClick:e[0]||(e[0]=_=>(async F=>{const{copy:I,copied:T,isSupported:B}=Y({source:F});B?(await I(),n(T)&&c.success(o("common.copySuccess"))):c.error(o("common.copyError"))})(n(p)))},{default:s(()=>[g(H(n(o)("common.copy")),1)]),_:1}),t(k,{height:"580"},{default:s(()=>[f("div",null,[f("pre",null,[K(f("code",le,null,512),[[j,D(n(p))]])])])]),_:1})],512)):G("",!0)]),_:1},8,["modelValue","title"])],64)}}});export{ie as default};
