import{cD as u}from"./index-C8b06LRn.js";const w=e=>{const o=e.split(","),a=o[0].match(/:(.*?);/)[1],r=window.atob(o[1]);let n=r.length;const t=new Uint8Array(n);for(;n--;)t[n]=r.charCodeAt(n);return new Blob([t],{type:a})},f=({url:e,target:o="_blank",fileName:a})=>{const r=window.navigator.userAgent.toLowerCase().indexOf("chrome")>-1,n=window.navigator.userAgent.toLowerCase().indexOf("safari")>-1;if(/(iP)/g.test(window.navigator.userAgent))return!1;if(r||n){const t=document.createElement("a");if(t.href=e,t.target=o,t.download!==void 0&&(t.download=a||e.substring(e.lastIndexOf("/")+1,e.length)),document.createEvent){const s=document.createEvent("MouseEvents");return s.initEvent("click",!0,!0),t.dispatchEvent(s),!0}}return e.indexOf("?")===-1&&(e+="?download"),((t,s)=>{const{target:i="__blank",noopener:c=!0,noreferrer:l=!0}=s||{},d=[];c&&d.push("noopener=yes"),l&&d.push("noreferrer=yes"),window.open(t,i,d.join(","))})(e,{target:o}),!0},p=async({url:e,fileName:o})=>{const a=await u.get(e,{responseType:"blob"}),r=new Blob([a.data],{type:"application/pdf"}),n=document.createElement("a");n.href=URL.createObjectURL(r),n.download=o||e.substring(e.lastIndexOf("/")+1),document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(n.href)};export{f as a,p as b,w as d};
