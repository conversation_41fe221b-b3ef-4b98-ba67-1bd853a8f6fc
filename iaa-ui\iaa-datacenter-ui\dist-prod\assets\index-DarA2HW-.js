import{d as ee,j as d,r as K,y as ae,au as te,f as le,aF as R,T as ie,o as T,c as q,l as r,k as a,w as h,u as i,m as P,v as de,t as S,cq as oe,x as re,aK as ne,F as se,aJ as ce,G as fe,S as ue,H as me,cy as he,_ as pe}from"./index-C8b06LRn.js";import{_ as ge}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{t as F}from"./Filter-Dzz2caxb.js";import{D as ve,a as w}from"./DrawingsLog-DuGni-5M.js";import we from"./PdfPreview-096QIbby.js";import{b as ye}from"./filt-CBkj7zaY.js";import"./index-Cl43piKd.js";import"./formatTime-COZ9Bl52.js";const be={class:"drawing-control-container"},ke={class:"main-content"},Ce={class:"left-tree"},xe={class:"center-table"},Ne={class:"h-[calc(100vh-180px)]"},Ve={class:"h-[calc(100%-5px)]"},_e={class:"right-detail"},ze={class:"h-[calc(100vh-180px)]"},De={class:"h-[calc(100%-10px)]"},Ue={class:"pdf-dialog-header"},Pe={class:"pdf-controls"},Se={class:"pdf-container"},Fe=pe(ee({__name:"index",setup(Ie){const I=d(),Q={label:"kindName",children:"children",disabled:"disabled"},c=K({kind:[],pageNo:1,pageSize:30}),k=d(""),W=d([{data:""}]),X=d([{data:""}]),$=d([{data:""}]),v=ae(),_=d(!0),L=d([]),O=d(0),y=d(!0),z=d([]),D=d([]),A=(e,t)=>!e||t.kindName.includes(e),B=async e=>{if(e.id==="1")c.kind=[];else{const t=((n,f)=>{let l=null;const m=s=>{for(const u of s){if(u.id===n)return u;if(u.children&&u.children.length>0){const o=m(u.children);if(o)return o}}return null};for(const s of f)if(l=m([s]),l)break;if(!l)return[];const g=[],V=s=>{g.push(s.id),s.children&&s.children.length>0&&s.children.forEach(u=>V(u))};return V(l),g})(e.id,D.value);c.kind=t}C(),z.value=[]},C=async()=>{try{_.value=!0;const e=await w.getPage(c);L.value=e.list,O.value=e.total}catch{}finally{_.value=!1}},p=K({itemCode:"",itemVersion:""}),j=async()=>{try{y.value=!0;const e=await w.getQueryDocumentByItemCode(p);z.value=e}catch{}finally{y.value=!1}},G=e=>{const t=[""],n={};e.filterList.forEach(f=>{const{field:l,values:m,datas:g}=f;t.includes(l)&&m.length>0?n[l]=m:g.length>0&&(n[l]=g[0])}),Object.keys(c).forEach(f=>{["pageNo","pageSize","kind"].includes(f)||e.filterList.some(l=>l.field===f)||(c[f]=void 0)}),Object.assign(c,n),C()},H=async({row:e})=>{p.itemCode=e.itemCode,p.itemVersion=e.itemVersion,j()},U=d(""),x=d(!1),N=d(!1),b=d(""),E=d(""),J=d(""),M=d(0),Y=async({row:e})=>{if(e.format!=="\u4E0D\u5B58\u5728")if(e.isSuance!=="\u672A\u53D1\u653E")if(e.status==="\u53D7\u63A7\u5B8C\u6210")try{y.value=!0;const t=await w.getDocPathOrWx({methodName:"getDocPathForWX",docId:e.documentCode,docVer:e.documentVersion,docName:e.documentName,uuid:"1"});t&&typeof t=="string"?(U.value=t+"#printbar=0",J.value=t.substring(t.lastIndexOf("/")+1),b.value=e.documentName,E.value=e.documentCode,x.value=!0,await w.addDrawingLog({itemCode:p.itemCode,itemVersion:p.itemVersion,wordName:e.documentName,wordCode:e.documentCode,type:0})):v.error("\u83B7\u53D6\u6587\u4EF6\u5730\u5740\u5931\u8D25\uFF01")}catch{v.error("\u9884\u89C8\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}finally{y.value=!1}else v.error("\u5F53\u524D\u9009\u4E2D\u6587\u4EF6\u53D7\u63A7\u672A\u5B8C\u6210\uFF0C\u65E0\u6CD5\u9884\u89C8\uFF01");else v.error("\u5F53\u524D\u9009\u4E2D\u6587\u4EF6\u672A\u53D1\u653E\uFF0C\u65E0\u6CD5\u9884\u89C8\uFF01");else v.error("\u5F53\u524D\u9009\u4E2D\u6587\u4EF6\u4E0D\u5B58\u5728PDF\uFF0C\u65E0\u6CD5\u9884\u89C8\uFF01")};te(k,e=>{I.value.filter(e)});const Z=async()=>{await w.addDrawingLog({itemCode:p.itemCode,itemVersion:p.itemVersion,wordName:b.value,wordCode:E.value,type:1});const e=U.value;ye({url:e,fileName:b.value+".pdf"}),v.success(`\u6587\u6863 "${b.value}" \u5F00\u59CB\u4E0B\u8F7D`)};return le(async()=>{(async()=>{const e=await w.getQueryPartKind(),t=ce(e);D.value=t.filter(n=>n.id=="1")})(),C(),j()}),(e,t)=>{const n=fe,f=ue,l=R("vxe-column"),m=R("vxe-table"),g=ge,V=me,s=he,u=ie("hasPermi");return T(),q(se,null,[r("div",be,[r("div",ke,[r("div",Ce,[a(f,{modelValue:i(k),"onUpdate:modelValue":t[0]||(t[0]=o=>P(k)?k.value=o:null),class:"mb-10px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7C7B\u578B\u540D\u79F0"},{prefix:h(()=>[a(n,{icon:"ep:search"})]),_:1},8,["modelValue"]),a(i(oe),{ref_key:"treeRef",ref:I,data:i(D),"node-key":"id","filter-node-method":A,props:Q,"default-expanded-keys":["1"],accordion:"","highlight-current":"","check-on-click-node":"",onNodeClick:B},{default:h(({data:o})=>[de(S(o.kindName)+","+S(o.kindKey),1)]),_:1},8,["data"])]),r("div",xe,[r("div",Ne,[r("div",Ve,[a(m,{ref:"tableRef",data:i(L),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:i(_),"row-config":{isCurrent:!0,height:25},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},onFilterChange:G,onCellClick:H,tabindex:"0",size:"mini","cell-class-name":"cursor-pointer"},{default:h(()=>[a(l,{field:"itemCode",title:"\u7269\u6599\u7F16\u7801",width:"100",filters:i(W),"filter-render":F},null,8,["filters","filter-render"]),a(l,{field:"itemCode1",title:"\u539F\u4EF6\u53F7",width:"100"}),a(l,{field:"itemVersion",title:"\u7248\u672C",width:"80"}),a(l,{field:"itemName",title:"\u7269\u6599\u540D\u79F0",width:"200",filters:i(X),"filter-render":F},null,8,["filters","filter-render"]),a(l,{field:"attribute",title:"\u5C5E\u6027",width:"80"}),a(l,{field:"spec",title:"\u89C4\u683C","min-width":"200",filters:i($),"filter-render":F},null,8,["filters","filter-render"])]),_:1},8,["data","loading"])]),a(g,{total:i(O),page:i(c).pageNo,"onUpdate:page":t[1]||(t[1]=o=>i(c).pageNo=o),limit:i(c).pageSize,"onUpdate:limit":t[2]||(t[2]=o=>i(c).pageSize=o),onPagination:C,size:"small"},null,8,["total","page","limit"])])]),r("div",_e,[r("div",ze,[r("div",De,[a(m,{ref:"tableRef",data:i(z),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:i(y),"row-config":{isCurrent:!0,height:25},"filter-config":{},"show-footer":"","keep-source":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},onCellClick:Y,tabindex:"0",size:"mini","cell-class-name":"cursor-pointer"},{default:h(()=>[a(l,{field:"status",title:"\u662F\u5426\u53D7\u63A7",width:"100"}),a(l,{field:"isSuance",title:"\u662F\u5426\u53D1\u653E",width:"80"}),a(l,{field:"format",title:"\u662F\u5426\u5B58\u5728pdf",width:"80"}),a(l,{field:"documentCode",title:"\u6587\u6863\u7F16\u7801",width:"120"}),a(l,{field:"documentName",title:"\u540D\u79F0",width:"150"}),a(l,{field:"documentType",title:"\u7C7B\u578B",width:"80"}),a(l,{field:"createName",title:"\u521B\u5EFA\u4EBA",width:"80"}),a(l,{field:"updateName",title:"\u4FEE\u6539\u4EBA",width:"80"}),a(l,{field:"updateTime",title:"\u4FEE\u6539\u65F6\u95F4",width:"80"})]),_:1},8,["data","loading"])])])]),re((T(),q("div",{class:"floating-log-btn",title:"\u67E5\u770B\u65E5\u5FD7",onClick:t[3]||(t[3]=o=>N.value=!0)},[a(n,{icon:"ep:view"})])),[[u,["development:drawings:querylog"]]])])]),a(s,{modelValue:i(x),"onUpdate:modelValue":t[4]||(t[4]=o=>P(x)?x.value=o:null),title:"\u6587\u6863\u9884\u89C8",width:"90%",fullscreen:!0},{header:h(()=>[r("div",Ue,[r("span",null,"\u6587\u6863\u9884\u89C8\uFF1A"+S(i(b)),1),r("div",Pe,[a(V,{circle:"",class:"ml-10px",onClick:Z,title:"\u4E0B\u8F7D"},{default:h(()=>[a(n,{icon:"ep:download"})]),_:1})])])]),default:h(()=>[r("div",Se,[r("div",{class:"pdf-wrapper",style:ne({transform:`rotate(${i(M)}deg)`,transition:"transform 0.3s ease"})},[a(we,{pdfUrl:i(U)},null,8,["pdfUrl"])],4)])]),_:1},8,["modelValue"]),a(s,{modelValue:i(N),"onUpdate:modelValue":t[5]||(t[5]=o=>P(N)?N.value=o:null),title:"\u64CD\u4F5C\u65E5\u5FD7",width:"60%",height:"80%",draggable:"","destroy-on-close":""},{default:h(()=>[a(ve)]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-2615504a"]]);export{Fe as default};
