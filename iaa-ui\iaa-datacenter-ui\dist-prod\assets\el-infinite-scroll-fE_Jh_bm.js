import{bY as N,dL as T,aO as p,dv as w,dM as y,dN as L}from"./index-C8b06LRn.js";const i="ElInfiniteScroll",O={delay:{type:Number,default:200},distance:{type:Number,default:0},disabled:{type:Boolean,default:!1},immediate:{type:Boolean,default:!0}},m=(e,n)=>Object.entries(O).reduce((l,[t,s])=>{var d,c;const{type:u,default:r}=s,a=e.getAttribute(`infinite-scroll-${t}`);let o=(c=(d=n[a])!=null?d:a)!=null?c:r;return o=o!=="false"&&o,o=u(o),l[t]=Number.isNaN(o)?r:o,l},{}),g=e=>{const{observer:n}=e[i];n&&(n.disconnect(),delete e[i].observer)},B=(e,n)=>{const{container:l,containerEl:t,instance:s,observer:d,lastScrollTop:c}=e[i],{disabled:u,distance:r}=m(e,s),{clientHeight:a,scrollHeight:o,scrollTop:b}=t,h=b-c;if(e[i].lastScrollTop=b,d||u||h<0)return;let f=!1;if(l===e)f=o-(a+b)<=r;else{const{clientTop:H,scrollHeight:S}=e;f=b+a>=L(e,t)+H+S-r}f&&n.call(s)};function v(e,n){const{containerEl:l,instance:t}=e[i],{disabled:s}=m(e,t);s||l.clientHeight===0||(l.scrollHeight<=l.clientHeight?n.call(t):g(e))}const E={async mounted(e,n){const{instance:l,value:t}=n;N(t)||T(i,"'v-infinite-scroll' binding value must be a function"),await p();const{delay:s,immediate:d}=m(e,l),c=w(e,!0),u=c===window?document.documentElement:c,r=y(B.bind(null,e,t),s);if(c){if(e[i]={instance:l,container:c,containerEl:u,delay:s,cb:t,onScroll:r,lastScrollTop:u.scrollTop},d){const a=new MutationObserver(y(v.bind(null,e,t),50));e[i].observer=a,a.observe(e,{childList:!0,subtree:!0}),v(e,t)}c.addEventListener("scroll",r)}},unmounted(e){if(!e[i])return;const{container:n,onScroll:l}=e[i];n==null||n.removeEventListener("scroll",l),g(e)},async updated(e){if(e[i]){const{containerEl:n,cb:l,observer:t}=e[i];n.clientHeight&&t&&v(e,l)}else await p()},install:e=>{e.directive("InfiniteScroll",E)}},I=E;export{I as E};
