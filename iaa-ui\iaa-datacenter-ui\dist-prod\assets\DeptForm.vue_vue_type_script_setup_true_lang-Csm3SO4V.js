import{d as j,p as z,y as J,j as u,r as R,o as m,h as c,w as o,k as s,v as k,x as $,u as a,aA as G,c as w,F as x,g as S,N as K,D as P,m as Q,ea as W,aH as X,eb as Y,ec as Z,aI as ee,aJ as le,B as ae,S as te,aB as se,z as oe,A as de,I as ue,H as re,L as ie}from"./index-C8b06LRn.js";import{_ as me}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as ne}from"./el-tree-select-E9FCZb0j.js";import{C as E}from"./constants-D3f7Z3TX.js";const pe=j({name:"SystemDeptForm",__name:"DeptForm",emits:["success"],setup(ce,{expose:F,emit:q}){const{t:v}=z(),b=J(),i=u(!1),g=u(""),n=u(!1),h=u(""),t=u({id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:E.ENABLE}),A=R({parentId:[{required:!0,message:"\u4E0A\u7EA7\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u90E8\u95E8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u663E\u793A\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=u(),f=u(),_=u([]);F({open:async(d,e)=>{if(i.value=!0,g.value=v("action."+d),h.value=d,N(),e){n.value=!0;try{t.value=await W(e)}finally{n.value=!1}}_.value=await X(),await D()}});const B=q,C=async()=>{if(p&&await p.value.validate()){n.value=!0;try{const d=t.value;h.value==="create"?(await Y(d),b.success(v("common.createSuccess"))):(await Z(d),b.success(v("common.updateSuccess"))),i.value=!1,B("success")}finally{n.value=!1}}},N=()=>{var d;t.value={id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:E.ENABLE},(d=p.value)==null||d.resetFields()},D=async()=>{f.value=[];const d=await ee();let e={id:0,name:"\u9876\u7EA7\u90E8\u95E8",children:[]};e.children=le(d),f.value.push(e)};return(d,e)=>{const H=ne,r=ae,V=te,L=se,y=oe,I=de,M=ue,U=re,O=me,T=ie;return m(),c(O,{modelValue:a(i),"onUpdate:modelValue":e[8]||(e[8]=l=>Q(i)?i.value=l:null),title:a(g)},{footer:o(()=>[s(U,{type:"primary",onClick:C},{default:o(()=>e[9]||(e[9]=[k("\u786E \u5B9A")])),_:1}),s(U,{onClick:e[7]||(e[7]=l=>i.value=!1)},{default:o(()=>e[10]||(e[10]=[k("\u53D6 \u6D88")])),_:1})]),default:o(()=>[$((m(),c(M,{ref_key:"formRef",ref:p,model:a(t),rules:a(A),"label-width":"80px"},{default:o(()=>[s(r,{label:"\u4E0A\u7EA7\u90E8\u95E8",prop:"parentId"},{default:o(()=>[s(H,{modelValue:a(t).parentId,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).parentId=l),data:a(f),props:a(G),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u90E8\u95E8","value-key":"deptId"},null,8,["modelValue","data","props"])]),_:1}),s(r,{label:"\u90E8\u95E8\u540D\u79F0",prop:"name"},{default:o(()=>[s(V,{modelValue:a(t).name,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u663E\u793A\u6392\u5E8F",prop:"sort"},{default:o(()=>[s(L,{modelValue:a(t).sort,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).sort=l),min:0,"controls-position":"right"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u8D1F\u8D23\u4EBA",prop:"leaderUserId"},{default:o(()=>[s(I,{modelValue:a(t).leaderUserId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).leaderUserId=l),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8D1F\u8D23\u4EBA"},{default:o(()=>[(m(!0),w(x,null,S(a(_),l=>(m(),c(y,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"\u8054\u7CFB\u7535\u8BDD",prop:"phone"},{default:o(()=>[s(V,{modelValue:a(t).phone,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).phone=l),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u90AE\u7BB1",prop:"email"},{default:o(()=>[s(V,{modelValue:a(t).email,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).email=l),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[s(I,{modelValue:a(t).status,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).status=l),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:o(()=>[(m(!0),w(x,null,S(a(K)(a(P).COMMON_STATUS),l=>(m(),c(y,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[T,a(n)]])]),_:1},8,["modelValue","title"])}}});export{pe as _};
