import{aG as e}from"./index-C8b06LRn.js";const t=a=>e.get({url:"/system/tenant-package/page",params:a}),s=a=>e.get({url:"/system/tenant-package/get?id="+a}),p=a=>e.post({url:"/system/tenant-package/create",data:a}),n=a=>e.put({url:"/system/tenant-package/update",data:a}),g=a=>e.delete({url:"/system/tenant-package/delete?id="+a}),r=()=>e.get({url:"/system/tenant-package/simple-list"});export{s as a,t as b,p as c,g as d,r as g,n as u};
