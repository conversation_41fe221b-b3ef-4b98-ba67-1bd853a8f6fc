import{d as Y,a as z,j as s,r as B,f as F,o as x,c as H,k as e,w as o,u as l,M,v as h,x as j,h as q,F as K,S as P,B as G,C as J,G as L,H as A,I as R,J as E,K as O,L as Q}from"./index-C8b06LRn.js";import{_ as W}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as X}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as Z}from"./index-CkzUfjB7.js";import{d as y}from"./formatTime-COZ9Bl52.js";import{j as $}from"./index-BwETMpJ2.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";const ee=Y({name:"BpmTodoTask",__name:"index",setup(ae){const{push:T}=z(),p=s(!0),w=s(0),b=s([]),t=B({pageNo:1,pageSize:10,name:"",createTime:[]}),_=s(),d=async()=>{p.value=!0;try{const c=await $(t);b.value=c.list,w.value=c.total}finally{p.value=!1}},m=()=>{t.pageNo=1,d()},V=()=>{_.value.resetFields(),m()};return F(()=>{d()}),(c,a)=>{const i=Z,D=P,u=G,I=J,k=L,f=A,C=R,v=X,n=E,U=O,N=W,S=Q;return x(),H(K,null,[e(i,{title:"\u5BA1\u6279\u901A\u8FC7\u3001\u4E0D\u901A\u8FC7\u3001\u9A73\u56DE",url:"https://doc.iocoder.cn/bpm/task-todo-done/"}),e(i,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(i,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),e(i,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(v,null,{default:o(()=>[e(C,{ref_key:"queryFormRef",ref:_,inline:!0,model:l(t),class:"-mb-15px","label-width":"68px"},{default:o(()=>[e(u,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:o(()=>[e(D,{modelValue:l(t).name,"onUpdate:modelValue":a[0]||(a[0]=r=>l(t).name=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:M(m,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[e(I,{modelValue:l(t).createTime,"onUpdate:modelValue":a[1]||(a[1]=r=>l(t).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:o(()=>[e(f,{onClick:m},{default:o(()=>[e(k,{class:"mr-5px",icon:"ep:search"}),a[4]||(a[4]=h(" \u641C\u7D22 "))]),_:1}),e(f,{onClick:V},{default:o(()=>[e(k,{class:"mr-5px",icon:"ep:refresh"}),a[5]||(a[5]=h(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:o(()=>[j((x(),q(U,{data:l(b)},{default:o(()=>[e(n,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(n,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(n,{formatter:l(y),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(n,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(n,{formatter:l(y),align:"center",label:"\u4EFB\u52A1\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(n,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(n,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(n,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:o(r=>[e(f,{link:"",type:"primary",onClick:le=>{return g=r.row,void T({name:"BpmProcessInstanceDetail",query:{id:g.processInstance.id,taskId:g.id}});var g}},{default:o(()=>a[6]||(a[6]=[h("\u529E\u7406")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,l(p)]]),e(N,{limit:l(t).pageSize,"onUpdate:limit":a[2]||(a[2]=r=>l(t).pageSize=r),page:l(t).pageNo,"onUpdate:page":a[3]||(a[3]=r=>l(t).pageNo=r),total:l(w),onPagination:d},null,8,["limit","page","total"])]),_:1})],64)}}});export{ee as default};
