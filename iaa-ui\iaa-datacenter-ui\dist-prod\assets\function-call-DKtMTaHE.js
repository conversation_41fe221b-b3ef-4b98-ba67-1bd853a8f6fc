import{dQ as t,dR as r,dS as i,dT as u,k as d,ar as m,dU as f}from"./index-C8b06LRn.js";let e,B=t({},{title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1});function C(o){return r?new Promise((n,s)=>{e||function(){({instance:e}=i({setup(){const{state:a,toggle:c}=u();return()=>d(f,m(a,{"onUpdate:show":c}),null)}}))}(),e.open(t({},B,o,{callback:l=>{(l==="confirm"?n:s)(l)}}))}):Promise.resolve(void 0)}const v=o=>C(t({showCancelButton:!0},o));export{v as s};
