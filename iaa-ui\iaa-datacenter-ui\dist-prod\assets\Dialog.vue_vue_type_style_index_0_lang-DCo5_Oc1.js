import{d as $,aj as a,cx as O,Y as p,j as x,aS as h,au as _,h as d,c0 as j,u as s,w as c,ar as C,cy as D,o as u,bf as n,l as g,v as S,t as T,$ as N,k as z,aK as B,bi as E,aO as G,G as K,cz as V}from"./index-C8b06LRn.js";const Y={class:"relative h-54px flex items-center justify-between pl-15px pr-15px"},q={class:"absolute right-15px top-[50%] h-54px flex translate-y-[-50%] items-center justify-between"},A=$({name:"Dialog",__name:"Dialog",props:{modelValue:a.bool.def(!1),title:a.string.def("Dialog"),fullscreen:a.bool.def(!0),width:a.oneOfType([String,Number]).def("40%"),scroll:a.bool.def(!1),maxHeight:a.oneOfType([String,Number]).def("400px")},setup(t){const m=O(),o=t,v=p(()=>{const e=["fullscreen","title","maxHeight","appendToBody"],l={...E(),...o};for(const i in l)e.indexOf(i)!==-1&&delete l[i];return l}),r=x(!1),y=()=>{r.value=!s(r)},f=x(h(o.maxHeight)?`${o.maxHeight}px`:o.maxHeight);_(()=>r.value,async e=>{if(await G(),e){const l=document.documentElement.offsetHeight;f.value=l-55-60-(m.footer?63:0)+"px"}else f.value=h(o.maxHeight)?`${o.maxHeight}px`:o.maxHeight},{immediate:!0});const b=p(()=>({height:s(f)}));return(e,l)=>{const i=K,k=V,w=D;return u(),d(w,C(s(v),{"close-on-click-modal":!0,fullscreen:s(r),width:t.width,"destroy-on-close":"","lock-scroll":"",draggable:"",class:"com-dialog","show-close":!1}),j({header:c(({close:H})=>[g("div",Y,[n(e.$slots,"title",{},()=>[S(T(t.title),1)]),g("div",q,[t.fullscreen?(u(),d(i,{key:0,class:"is-hover mr-10px cursor-pointer",icon:s(r)?"radix-icons:exit-full-screen":"radix-icons:enter-full-screen",color:"var(--el-color-info)","hover-color":"var(--el-color-primary)",onClick:y},null,8,["icon"])):N("",!0),z(i,{class:"is-hover cursor-pointer",icon:"ep:close","hover-color":"var(--el-color-primary)",color:"var(--el-color-info)",onClick:H},null,8,["onClick"])])])]),default:c(()=>[t.scroll?(u(),d(k,{key:0,style:B(s(b))},{default:c(()=>[n(e.$slots,"default")]),_:3},8,["style"])):n(e.$slots,"default",{key:1})]),_:2},[s(m).footer?{name:"footer",fn:c(()=>[n(e.$slots,"footer")]),key:"0"}:void 0]),1040,["fullscreen","width"])}}});export{A as _};
