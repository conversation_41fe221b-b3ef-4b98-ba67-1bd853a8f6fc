import{_ as U}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as k,j as _,o as p,h as m,w as l,k as t,v as o,t as s,u as e,D as y,$ as n,m as A,S as E}from"./index-C8b06LRn.js";import{E as R,a as h}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as V}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as v}from"./formatTime-COZ9Bl52.js";const g=k({name:"ApiErrorLogDetail",__name:"ApiErrorLogDetail",setup(P,{expose:T}){const r=_(!1),c=_(!1),a=_({});return T({open:async f=>{r.value=!0,c.value=!0;try{a.value=f}finally{c.value=!1}}}),(f,d)=>{const u=R,b=V,x=E,S=h,I=U;return p(),m(I,{modelValue:e(r),"onUpdate:modelValue":d[1]||(d[1]=i=>A(r)?r.value=i:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5",width:"800"},{default:l(()=>[t(S,{column:1,border:""},{default:l(()=>[t(u,{label:"\u65E5\u5FD7\u4E3B\u952E","min-width":"120"},{default:l(()=>[o(s(e(a).id),1)]),_:1}),t(u,{label:"\u94FE\u8DEF\u8FFD\u8E2A"},{default:l(()=>[o(s(e(a).traceId),1)]),_:1}),t(u,{label:"\u5E94\u7528\u540D"},{default:l(()=>[o(s(e(a).applicationName),1)]),_:1}),t(u,{label:"\u7528\u6237\u7F16\u53F7"},{default:l(()=>[o(s(e(a).userId)+" ",1),t(b,{type:e(y).USER_TYPE,value:e(a).userType},null,8,["type","value"])]),_:1}),t(u,{label:"\u7528\u6237 IP"},{default:l(()=>[o(s(e(a).userIp),1)]),_:1}),t(u,{label:"\u7528\u6237 UA"},{default:l(()=>[o(s(e(a).userAgent),1)]),_:1}),t(u,{label:"\u8BF7\u6C42\u4FE1\u606F"},{default:l(()=>[o(s(e(a).requestMethod)+" "+s(e(a).requestUrl),1)]),_:1}),t(u,{label:"\u8BF7\u6C42\u53C2\u6570"},{default:l(()=>[o(s(e(a).requestParams),1)]),_:1}),t(u,{label:"\u5F02\u5E38\u65F6\u95F4"},{default:l(()=>[o(s(e(v)(e(a).exceptionTime)),1)]),_:1}),t(u,{label:"\u5F02\u5E38\u540D"},{default:l(()=>[o(s(e(a).exceptionName),1)]),_:1}),e(a).exceptionStackTrace?(p(),m(u,{key:0,label:"\u5F02\u5E38\u5806\u6808"},{default:l(()=>[t(x,{modelValue:e(a).exceptionStackTrace,"onUpdate:modelValue":d[0]||(d[0]=i=>e(a).exceptionStackTrace=i),autosize:{maxRows:20},readonly:!0,type:"textarea"},null,8,["modelValue"])]),_:1})):n("",!0),t(u,{label:"\u5904\u7406\u72B6\u6001"},{default:l(()=>[t(b,{type:e(y).INFRA_API_ERROR_LOG_PROCESS_STATUS,value:e(a).processStatus},null,8,["type","value"])]),_:1}),e(a).processUserId?(p(),m(u,{key:1,label:"\u5904\u7406\u4EBA"},{default:l(()=>[o(s(e(a).processUserId),1)]),_:1})):n("",!0),e(a).processTime?(p(),m(u,{key:2,label:"\u5904\u7406\u65F6\u95F4"},{default:l(()=>[o(s(e(v)(e(a).processTime)),1)]),_:1})):n("",!0)]),_:1})]),_:1},8,["modelValue"])}}});export{g as _};
