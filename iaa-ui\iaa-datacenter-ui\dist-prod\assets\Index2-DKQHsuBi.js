import{_ as G}from"./Echart.vue_vue_type_script_setup_true_lang-Cn4V3zug.js";import{d as z,aj as v,r as $,aR as L,f as Q,Y as R,au as U,u as t,o as C,c as I,t as F,n as y,X as O,aS as H,p as P,j as N,k as s,w as l,l as n,F as W,G as X,a8 as Y,ab as B,aT as w,_ as J}from"./index-C8b06LRn.js";import{E as K}from"./el-card-CaOo8U9P.js";import{E as Z}from"./el-skeleton-item-DDp1oSSE.js";import"./echarts-cfVEL83D.js";const aa=z({name:"CountTo",__name:"CountTo",props:{startVal:v.number.def(0),endVal:v.number.def(2021),duration:v.number.def(3e3),autoplay:v.bool.def(!0),decimals:v.number.validate(h=>h>=0).def(0),decimal:v.string.def("."),separator:v.string.def(","),prefix:v.string.def(""),suffix:v.string.def(""),useEasing:v.bool.def(!0),easingFn:{type:Function,default:(h,c,p,V)=>p*(1-Math.pow(2,-10*h/V))*1024/1023+c}},emits:["mounted","callback"],setup(h,{expose:c,emit:p}){const{getPrefixCls:V}=O(),o=V("count-to"),r=h,A=p,k=u=>{const{decimals:f,decimal:g,separator:i,suffix:d,prefix:T}=r;u=Number(u).toFixed(f);const b=(u+="").split(".");let E=b[0];const M=b.length>1?g+b[1]:"",q=/(\d+)(\d{3})/;if(i&&!H(i))for(;q.test(E);)E=E.replace(q,"$1"+i+"$2");return T+E+M+d},a=$({localStartVal:r.startVal,displayValue:k(r.startVal),printVal:null,paused:!1,localDuration:r.duration,startTime:null,timestamp:null,remaining:null,rAF:null}),j=L(a,"displayValue");Q(()=>{r.autoplay&&_(),A("mounted")});const S=R(()=>r.startVal>r.endVal);U([()=>r.startVal,()=>r.endVal],()=>{r.autoplay&&_()});const _=()=>{const{startVal:u,duration:f}=r;a.localStartVal=u,a.startTime=null,a.localDuration=f,a.paused=!1,a.rAF=requestAnimationFrame(m)},D=()=>{cancelAnimationFrame(a.rAF)},x=()=>{a.startTime=null,a.localDuration=+a.remaining,a.localStartVal=+a.printVal,requestAnimationFrame(m)},m=u=>{const{useEasing:f,easingFn:g,endVal:i}=r;a.startTime||(a.startTime=u),a.timestamp=u;const d=u-a.startTime;a.remaining=a.localDuration-d,f?t(S)?a.printVal=a.localStartVal-g(d,0,a.localStartVal-i,a.localDuration):a.printVal=g(d,a.localStartVal,i-a.localStartVal,a.localDuration):t(S)?a.printVal=a.localStartVal-(a.localStartVal-i)*(d/a.localDuration):a.printVal=a.localStartVal+(i-a.localStartVal)*(d/a.localDuration),t(S)?a.printVal=a.printVal<i?i:a.printVal:a.printVal=a.printVal>i?i:a.printVal,a.displayValue=k(a.printVal),d<a.localDuration?a.rAF=requestAnimationFrame(m):A("callback")};return c({pauseResume:()=>{a.paused?(x(),a.paused=!1):(D(),a.paused=!0)},reset:()=>{a.startTime=null,cancelAnimationFrame(a.rAF),a.displayValue=k(r.startVal)},start:_,pause:D}),(u,f)=>(C(),I("span",{class:y(t(o))},F(t(j)),3))}}),{t:e}=P(),ea={title:{text:e("analysis.monthlySales"),left:"center"},xAxis:{data:[e("analysis.january"),e("analysis.february"),e("analysis.march"),e("analysis.april"),e("analysis.may"),e("analysis.june"),e("analysis.july"),e("analysis.august"),e("analysis.september"),e("analysis.october"),e("analysis.november"),e("analysis.december")],boundaryGap:!1,axisTick:{show:!1}},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},yAxis:{axisTick:{show:!1}},legend:{data:[e("analysis.estimate"),e("analysis.actual")],top:50},series:[{name:e("analysis.estimate"),smooth:!0,type:"line",data:[100,120,161,134,105,160,165,114,163,185,118,123],animationDuration:2800,animationEasing:"cubicInOut"},{name:e("analysis.actual"),smooth:!0,type:"line",itemStyle:{},data:[120,82,91,154,162,140,145,250,134,56,99,123],animationDuration:2800,animationEasing:"quadraticOut"}]},ta={title:{text:e("analysis.userAccessSource"),left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:[e("analysis.directAccess"),e("analysis.mailMarketing"),e("analysis.allianceAdvertising"),e("analysis.videoAdvertising"),e("analysis.searchEngines")]},series:[{name:e("analysis.userAccessSource"),type:"pie",radius:"55%",center:["50%","60%"],data:[{value:335,name:e("analysis.directAccess")},{value:310,name:e("analysis.mailMarketing")},{value:234,name:e("analysis.allianceAdvertising")},{value:135,name:e("analysis.videoAdvertising")},{value:1548,name:e("analysis.searchEngines")}]}]},sa={title:{text:e("analysis.weeklyUserActivity"),left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:50,right:20,bottom:20},xAxis:{type:"category",data:[e("analysis.monday"),e("analysis.tuesday"),e("analysis.wednesday"),e("analysis.thursday"),e("analysis.friday"),e("analysis.saturday"),e("analysis.sunday")],axisTick:{alignWithLabel:!0}},yAxis:{type:"value"},series:[{name:e("analysis.activeQuantity"),data:[13253,34235,26321,12340,24643,1322,1324],type:"bar"}]};e("workplace.personal"),e("workplace.team"),e("workplace.quote"),e("workplace.contribution"),e("workplace.hot"),e("workplace.yield"),e("workplace.follow"),e("workplace.index"),e("workplace.personal"),e("workplace.team");const la={class:"flex flex-col justify-between"},ia={class:"flex flex-col justify-between"},na={class:"flex flex-col justify-between"},oa={class:"flex flex-col justify-between"},ra=z({name:"Home2",__name:"Index2",setup(h){const{t:c}=P(),p=N(!0),{getPrefixCls:V}=O(),o=V("panel"),r=$(ta);let A=$({users:0,messages:0,moneys:0,shoppings:0});const k=async()=>{A=Object.assign(A,{users:102400,messages:81212,moneys:9280,shoppings:13600})},a=async()=>{const x=[{value:335,name:"analysis.directAccess"},{value:310,name:"analysis.mailMarketing"},{value:234,name:"analysis.allianceAdvertising"},{value:135,name:"analysis.videoAdvertising"},{value:1548,name:"analysis.searchEngines"}];w(r,"legend.data",x.map(m=>c(m.name))),w(r,"series.data",x)},j=$(sa),S=async()=>{const x=[{value:13253,name:"analysis.monday"},{value:34235,name:"analysis.tuesday"},{value:26321,name:"analysis.wednesday"},{value:12340,name:"analysis.thursday"},{value:24643,name:"analysis.friday"},{value:1322,name:"analysis.saturday"},{value:1324,name:"analysis.sunday"}];w(j,"xAxis.data",x.map(m=>c(m.name))),w(j,"series",[{name:c("analysis.activeQuantity"),data:x.map(m=>m.value),type:"bar"}])},_=$(ea),D=async()=>{const x=[{estimate:100,actual:120,name:"analysis.january"},{estimate:120,actual:82,name:"analysis.february"},{estimate:161,actual:91,name:"analysis.march"},{estimate:134,actual:154,name:"analysis.april"},{estimate:105,actual:162,name:"analysis.may"},{estimate:160,actual:140,name:"analysis.june"},{estimate:165,actual:145,name:"analysis.july"},{estimate:114,actual:250,name:"analysis.august"},{estimate:163,actual:134,name:"analysis.september"},{estimate:185,actual:56,name:"analysis.october"},{estimate:118,actual:99,name:"analysis.november"},{estimate:123,actual:123,name:"analysis.december"}];w(_,"xAxis.data",x.map(m=>c(m.name))),w(_,"series",[{name:c("analysis.estimate"),smooth:!0,type:"line",data:x.map(m=>m.estimate),animationDuration:2800,animationEasing:"cubicInOut"},{name:c("analysis.actual"),smooth:!0,type:"line",itemStyle:{},data:x.map(m=>m.actual),animationDuration:2800,animationEasing:"quadraticOut"}])};return(async()=>(await Promise.all([k(),a(),S(),D()]),p.value=!1))(),(x,m)=>{const u=X,f=aa,g=Z,i=K,d=Y,T=B,b=G;return C(),I(W,null,[s(T,{class:y(t(o)),gutter:20,justify:"space-between"},{default:l(()=>[s(d,{lg:6,md:12,sm:12,xl:6,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),rows:2,animated:""},{default:l(()=>[n("div",{class:y(`${t(o)}__item flex justify-between`)},[n("div",null,[n("div",{class:y(`${t(o)}__item--icon ${t(o)}__item--peoples p-16px inline-block rounded-6px`)},[s(u,{size:40,icon:"svg-icon:peoples"})],2)]),n("div",la,[n("div",{class:y(`${t(o)}__item--text text-16px text-gray-500 text-right`)},F(t(c)("analysis.newUser")),3),s(f,{duration:2600,"end-val":102400,"start-val":0,class:"text-right text-20px font-700"})])],2)]),_:1},8,["loading"])]),_:1})]),_:1}),s(d,{lg:6,md:12,sm:12,xl:6,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),rows:2,animated:""},{default:l(()=>[n("div",{class:y(`${t(o)}__item flex justify-between`)},[n("div",null,[n("div",{class:y(`${t(o)}__item--icon ${t(o)}__item--message p-16px inline-block rounded-6px`)},[s(u,{size:40,icon:"svg-icon:message"})],2)]),n("div",ia,[n("div",{class:y(`${t(o)}__item--text text-16px text-gray-500 text-right`)},F(t(c)("analysis.unreadInformation")),3),s(f,{duration:2600,"end-val":81212,"start-val":0,class:"text-right text-20px font-700"})])],2)]),_:1},8,["loading"])]),_:1})]),_:1}),s(d,{lg:6,md:12,sm:12,xl:6,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),rows:2,animated:""},{default:l(()=>[n("div",{class:y(`${t(o)}__item flex justify-between`)},[n("div",null,[n("div",{class:y(`${t(o)}__item--icon ${t(o)}__item--money p-16px inline-block rounded-6px`)},[s(u,{size:40,icon:"svg-icon:money"})],2)]),n("div",na,[n("div",{class:y(`${t(o)}__item--text text-16px text-gray-500 text-right`)},F(t(c)("analysis.transactionAmount")),3),s(f,{duration:2600,"end-val":9280,"start-val":0,class:"text-right text-20px font-700"})])],2)]),_:1},8,["loading"])]),_:1})]),_:1}),s(d,{lg:6,md:12,sm:12,xl:6,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),rows:2,animated:""},{default:l(()=>[n("div",{class:y(`${t(o)}__item flex justify-between`)},[n("div",null,[n("div",{class:y(`${t(o)}__item--icon ${t(o)}__item--shopping p-16px inline-block rounded-6px`)},[s(u,{size:40,icon:"svg-icon:shopping"})],2)]),n("div",oa,[n("div",{class:y(`${t(o)}__item--text text-16px text-gray-500 text-right`)},F(t(c)("analysis.totalShopping")),3),s(f,{duration:2600,"end-val":13600,"start-val":0,class:"text-right text-20px font-700"})])],2)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["class"]),s(T,{gutter:20,justify:"space-between"},{default:l(()=>[s(d,{lg:10,md:24,sm:24,xl:10,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),animated:""},{default:l(()=>[s(b,{height:300,options:t(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1}),s(d,{lg:14,md:24,sm:24,xl:14,xs:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),animated:""},{default:l(()=>[s(b,{height:300,options:t(j)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1}),s(d,{span:24},{default:l(()=>[s(i,{class:"mb-20px",shadow:"hover"},{default:l(()=>[s(g,{loading:t(p),rows:4,animated:""},{default:l(()=>[s(b,{height:350,options:t(_)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})],64)}}}),ma=J(ra,[["__scopeId","data-v-f87e28ea"]]);export{ma as default};
