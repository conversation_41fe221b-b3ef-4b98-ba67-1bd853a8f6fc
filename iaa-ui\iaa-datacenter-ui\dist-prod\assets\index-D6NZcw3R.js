import{aG as J,d as ve,j as p,e as we,Y as _e,y as ge,r as ye,f as Ve,aO as be,u as t,aF as F,T as ke,o as _,h as H,w as o,c as b,k as e,l as g,v as r,t as s,x as I,F as K,dE as Ce,g as Ne,m as $,ax as Q,aB as xe,S as Ue,H as ze,G as Oe,B as Ee,a8 as Re,ab as Te,aM as Se,I as Me,L as je,_ as Ae}from"./index-C8b06LRn.js";import{E as $e}from"./el-infinite-scroll-fE_Jh_bm.js";import{_ as Be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{E as Fe}from"./el-drawer-C5TFtzfV.js";import{_ as He}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as Ie}from"./CardTitle-00NfZwLk.js";import{_ as Ge}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{f as Pe}from"./dateUtil-D9m5ek6U.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";const W=async j=>await J.post({url:"/report/cust-declaration/page",data:j}),G=async j=>await J.post({url:"/report/cust-declaration/update",data:j}),De={key:0,class:"pc-view"},Le={class:"h-[calc(100vh-260px)]"},Ye={key:1,class:"h-[calc(100vh-110px)]"},Ze={class:"h-40px"},qe=["infinite-scroll-immediate"],Je={class:"position-absolute top-5px right-5px"},Ke={class:"h-30px leading-30px border-t-#a8a8a8"},Qe=Ae(ve({__name:"index",setup(j){const y=p(),P=p(),x=p(!1),U=p([]),z=p(void 0),O=p(void 0),X=p(!1),ee=we(),D=_e(()=>ee.getMobile),d=p({pageNo:1,pageSize:20,orderCode:"",customers:"",workOrderCode:"",salesman:"",line:void 0,all:""}),E=p(0),R=p([]),f=p(!1),A=ge(),le=ye({body:{options:[[{code:"selected",name:"\u6309\u9009\u4E2D\u8BBE\u7F6E\u62A5\u5DE5\u6570\u91CF\u548C\u5DE5\u65F6"}]]}}),ae=({menu:u})=>{const l=y.value;if(l)switch(u.code){case"selected":const h=l.getCheckboxRecords();if(h.length===0)return A.alertError("\u8BF7\u9009\u62E9\u8981\u8BBE\u7F6E\u7684\u6570\u636E"),void(U.value=[]);U.value=h,x.value=!0;break;case"conditions":X.value=!0}},V=()=>{d.value.pageNo=1,D.value?d.value.pageSize=10:d.value.pageSize=20,S()},T=p(!1),m=p({id:void 0,reportNum:void 0,hours:void 0,workOrderCode:"",process:""}),L=p(!1),oe=async()=>{f.value=!0;try{const u=Math.ceil(E.value/d.value.pageSize);if(d.value.pageNo>=u)return void(L.value=!0);d.value.pageNo+=1;let l=Q(d.value);for(let i in l)Array.isArray(l[i])&&(l[i]=`${l[i].join(",")}`);const h=await W(l);R.value=R.value.concat(h.list),E.value=h.total}finally{f.value=!1}},S=async()=>{f.value=!0;try{let u=Q(d.value);for(let h in u)Array.isArray(u[h])&&(u[h]=`${u[h].join(",")}`);const l=await W(u);R.value=l.list,E.value=l.total}finally{f.value=!1}},te=async()=>{f.value=!0;try{let u=[];U.value.forEach(l=>{u.push({id:l.id,reportNum:z.value,hours:O.value})}),await G(u),A.success("\u4FDD\u5B58\u6210\u529F"),S(),x.value=!1,U.value=[],z.value=void 0,O.value=void 0}finally{f.value=!1}},de=u=>{const l=y.value;if(l)return l.isEditByRow(u)};return Ve(()=>{V(),be(()=>{var u;(u=t(y))==null||u.connect(t(P))})}),(u,l)=>{const h=F("vxe-toolbar"),i=F("vxe-column"),k=xe,C=Ue,N=ze,ie=F("vxe-table"),re=Ge,ue=Oe,se=Ie,n=Ee,w=Re,Y=Te,Z=Se,B=Me,ne=He,ce=Fe,pe=Be,q=ke("hasPermi"),me=$e,fe=je;return _(),H(pe,null,{default:o(()=>[t(D)?(_(),b("div",Ye,[g("div",Ze,[e(C,{modelValue:t(d).all,"onUpdate:modelValue":l[8]||(l[8]=a=>t(d).all=a),size:"large","suffix-icon":t(Ce),placeholder:"\u5168\u57DF\u67E5\u8BE2",clearable:"",onChange:V},null,8,["modelValue","suffix-icon"])]),I((_(),b("div",{class:"h-[calc(100%-75px)] overflow-auto","infinite-scroll-distance":20,"infinite-scroll-immediate":t(L)},[(_(!0),b(K,null,Ne(t(R),a=>(_(),b("div",{key:a.id,class:"p-10px data-item position-relative"},[I((_(),b("div",Je,[e(N,{type:"primary",circle:"",plain:"",onClick:v=>{return c=a,m.value.id=c.id,m.value.reportNum=c.reportNum,m.value.hours=c.hours,m.value.workOrderCode=c.workOrderCode,m.value.process=c.process,void(T.value=!0);var c}},{default:o(()=>[e(ue,{icon:"ep:edit"})]),_:2},1032,["onClick"])])),[[q,["technology:declaration:edit"]]]),e(se,{title:`\u5DE5\u5355\uFF1A${a.workOrderCode};\u6570\u91CF\uFF1A${a.workOrderNum}`},null,8,["title"]),e(B,{size:"small"},{default:o(()=>[e(Y,null,{default:o(()=>[e(w,{span:12},{default:o(()=>[e(n,{label:"\u54C1\u53F7"},{default:o(()=>[r(s(a.productNo),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u54C1\u540D"},{default:o(()=>[r(s(a.productName),1)]),_:2},1024)]),_:2},1024)]),_:2},1024),e(n,{label:"\u89C4\u683C"},{default:o(()=>[r(s(a.specifications),1)]),_:2},1024),e(Y,null,{default:o(()=>[e(w,{span:12},{default:o(()=>[e(n,{label:"\u62A5\u5DE5\u6570\u91CF"},{default:o(()=>[r(s(a.reportNum),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u7D2F\u8BA1\u6570\u91CF"},{default:o(()=>[r(s(a.orderTotalReportNum),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u62A5\u5DE5\u5DE5\u65F6"},{default:o(()=>[r(s(a.hours),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u7D2F\u8BA1\u5DE5\u65F6"},{default:o(()=>[r(s(a.orderTotalReportHours),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u5DE5\u5E8F"},{default:o(()=>[e(Z,null,{default:o(()=>[r(s(a.process),1)]),_:2},1024)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u62A5\u5DE5\u4EBA"},{default:o(()=>[r(s(a.reportUser),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u65F6\u95F4"},{default:o(()=>[r(s(a.reportTime),1)]),_:2},1024)]),_:2},1024),e(w,{span:12},{default:o(()=>[e(n,{label:"\u4EA7\u7EBF"},{default:o(()=>[r(s(a.line),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]))),128))],8,qe)),[[me,oe],[fe,t(f)]]),g("div",Ke,"\u5171\u8BA1\uFF1A"+s(t(E))+"\u6761\u8BB0\u5F55",1)])):(_(),b("div",De,[e(h,{custom:"",ref_key:"toolbarRef",ref:P,size:"mini"},null,512),g("div",Le,[e(ie,{"row-config":{height:30},ref_key:"tableRef",ref:y,data:t(R),"header-cell-style":{padding:0},"cell-style":{padding:0,height:"30px",color:"#232323"},"filter-config":{showIcon:!1},border:"",stripe:"","show-overflow":"",align:"center",loading:t(f),height:"100%","menu-config":t(le),onMenuClick:ae,"checkbox-config":{labelField:"name",highlight:!0,range:!0},"edit-config":{trigger:"manual",mode:"row",autoClear:!1}},{default:o(()=>[e(i,{type:"checkbox",width:"60",field:"name"}),e(i,{title:"\u62A5\u5DE5\u6570\u91CF",width:"200",field:"reportNum","edit-render":{}},{default:o(({row:a})=>[r(s(a.reportNum),1)]),edit:o(({row:a})=>[e(k,{modelValue:a.reportNum,"onUpdate:modelValue":v=>a.reportNum=v,min:0,precision:2},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(i,{title:"\u5DE5\u65F6",width:"200",field:"hours","edit-render":{}},{default:o(({row:a})=>[r(s(a.hours),1)]),edit:o(({row:a})=>[e(k,{modelValue:a.hours,"onUpdate:modelValue":v=>a.hours=v,min:0,precision:2},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(i,{field:"orderCode",width:"150"},{header:o(()=>[l[16]||(l[16]=g("div",null,"\u8BA2\u5355\u53F7",-1)),e(C,{modelValue:t(d).orderCode,"onUpdate:modelValue":l[0]||(l[0]=a=>t(d).orderCode=a),onChange:V,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),e(i,{field:"customers",width:"100"},{header:o(()=>[l[17]||(l[17]=g("div",null,"\u5BA2\u6237",-1)),e(C,{modelValue:t(d).customers,"onUpdate:modelValue":l[1]||(l[1]=a=>t(d).customers=a),onChange:V,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),e(i,{field:"salesman",width:"100"},{header:o(()=>[l[18]||(l[18]=g("div",null,"\u4E1A\u52A1\u5458",-1)),e(C,{modelValue:t(d).salesman,"onUpdate:modelValue":l[2]||(l[2]=a=>t(d).salesman=a),onChange:V,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),e(i,{field:"line",width:"100"},{header:o(()=>[l[19]||(l[19]=g("div",null,"\u4EA7\u7EBF",-1)),e(C,{modelValue:t(d).line,"onUpdate:modelValue":l[3]||(l[3]=a=>t(d).line=a),onChange:V,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),e(i,{field:"workOrderCode",width:"200"},{header:o(()=>[l[20]||(l[20]=g("div",null,"\u5DE5\u5355\u53F7",-1)),e(C,{modelValue:t(d).workOrderCode,"onUpdate:modelValue":l[4]||(l[4]=a=>t(d).workOrderCode=a),onChange:V,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),e(i,{title:"\u8BA2\u5355\u6570\u91CF",width:"100",field:"orderNum"}),e(i,{title:"\u5DE5\u5355\u6570\u91CF",width:"100",field:"workOrderNum"}),e(i,{title:"\u5DE5\u5E8F",width:"160",field:"process"}),e(i,{title:"\u54C1\u53F7",width:"160",field:"productNo"}),e(i,{title:"\u54C1\u540D",width:"160",field:"productName"}),e(i,{title:"\u89C4\u683C",width:"160",field:"specifications"}),e(i,{title:"\u8BA2\u5355\u7D2F\u8BA1\u62A5\u5DE5\u6570\u91CF",width:"160",field:"orderTotalReportNum"}),e(i,{title:"\u8BA2\u5355\u7D2F\u8BA1\u62A5\u5DE5\u5DE5\u65F6",width:"160",field:"orderTotalReportHours"}),e(i,{title:"\u62A5\u5DE5\u65F6\u95F4",width:"160",field:"reportTime"},{default:o(({row:a})=>[r(s(a.reportTime&&t(Pe)(a.reportTime)),1)]),_:1}),e(i,{title:"\u62A5\u5DE5\u4EBA",width:"160",field:"reportUser"}),I((_(),H(i,{title:"\u64CD\u4F5C",width:"160",fixed:"right"},{default:o(({row:a})=>[de(a)?(_(),b(K,{key:0},[e(N,{onClick:v=>(c=>{const M=y.value;f.value=!0;try{M&&M.clearEdit().then(async()=>{let he={id:c.id,reportNum:c.reportNum,hours:c.hours};await G([he]),A.success("\u4FDD\u5B58\u6210\u529F"),S()})}finally{f.value=!1}})(a),link:""},{default:o(()=>l[21]||(l[21]=[r("\u4FDD\u5B58")])),_:2},1032,["onClick"]),e(N,{onClick:l[5]||(l[5]=v=>(()=>{const c=y.value;c&&c.clearEdit()})()),link:""},{default:o(()=>l[22]||(l[22]=[r("\u53D6\u6D88")])),_:1})],64)):(_(),H(N,{key:1,onClick:v=>(c=>{const M=y.value;M&&M.setEditRow(c)})(a),link:""},{default:o(()=>l[23]||(l[23]=[r("\u7F16\u8F91")])),_:2},1032,["onClick"]))]),_:1})),[[q,["technology:declaration:edit"]]])]),_:1},8,["data","loading","menu-config"])]),e(re,{total:t(E),page:t(d).pageNo,"onUpdate:page":l[6]||(l[6]=a=>t(d).pageNo=a),limit:t(d).pageSize,"onUpdate:limit":l[7]||(l[7]=a=>t(d).pageSize=a),onPagination:S},null,8,["total","page","limit"])])),e(ne,{title:"\u6309\u9009\u4E2D\u6570\u636E\u8BBE\u7F6E\u62A5\u5DE5\u6570\u91CF\u548C\u5DE5\u65F6",modelValue:t(x),"onUpdate:modelValue":l[11]||(l[11]=a=>$(x)?x.value=a:null)},{footer:o(()=>[e(N,{type:"primary",onClick:te},{default:o(()=>l[24]||(l[24]=[r("\u4FDD\u5B58")])),_:1})]),default:o(()=>[g("div",null,"\u5F53\u524D\u9009\u4E2D\uFF1A"+s(t(U).length)+" \u6761\u6570\u636E",1),e(B,{"label-width":"100px"},{default:o(()=>[e(n,{label:"\u8BBE\u7F6E\u62A5\u5DE5\u6570\u91CF"},{default:o(()=>[e(k,{modelValue:t(z),"onUpdate:modelValue":l[9]||(l[9]=a=>$(z)?z.value=a:null),min:0,precision:2,class:"!w-100%"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u8BBE\u7F6E\u5DE5\u65F6"},{default:o(()=>[e(k,{modelValue:t(O),"onUpdate:modelValue":l[10]||(l[10]=a=>$(O)?O.value=a:null),min:0,precision:2,class:"!w-100%"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(ce,{title:"\u4FEE\u6539\u62A5\u5DE5\u6570\u91CF\u53CA\u5DE5\u65F6",modelValue:t(T),"onUpdate:modelValue":l[15]||(l[15]=a=>$(T)?T.value=a:null),direction:"btt",size:"50%"},{footer:o(()=>[e(N,{type:"primary",class:"!w-100%",onClick:l[14]||(l[14]=a=>(async v=>{f.value=!0;try{let c={id:v.id,reportNum:v.reportNum,hours:v.hours};await G([c]),A.success("\u4FDD\u5B58\u6210\u529F"),S(),T.value=!1,m.value={id:void 0,reportNum:void 0,hours:void 0,workOrderCode:"",process:""}}finally{f.value=!1}})(t(m)))},{default:o(()=>l[25]||(l[25]=[r("\u4FDD\u5B58")])),_:1})]),default:o(()=>[e(B,{"label-width":"90px"},{default:o(()=>[e(n,{label:"\u5DE5\u5355"},{default:o(()=>[r(s(t(m).workOrderCode),1)]),_:1}),e(n,{label:"\u5DE5\u5E8F"},{default:o(()=>[e(Z,null,{default:o(()=>[r(s(t(m).process),1)]),_:1})]),_:1}),e(n,{label:"\u62A5\u5DE5\u6570\u91CF"},{default:o(()=>[e(k,{modelValue:t(m).reportNum,"onUpdate:modelValue":l[12]||(l[12]=a=>t(m).reportNum=a),class:"!w-100%"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u5DE5\u65F6"},{default:o(()=>[e(k,{modelValue:t(m).hours,"onUpdate:modelValue":l[13]||(l[13]=a=>t(m).hours=a),class:"!w-100%"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}}),[["__scopeId","data-v-ec8f681c"]]);export{Qe as default};
