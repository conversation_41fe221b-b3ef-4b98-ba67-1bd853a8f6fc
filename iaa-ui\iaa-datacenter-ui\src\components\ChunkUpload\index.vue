<template>
  <div class="chunk-upload">
    <el-upload
      ref="uploadRef"
      :auto-upload="false"
      :show-file-list="false"
      :on-change="handleFileChange"
      :accept="accept"
      :multiple="false"
      drag
    >
      <div class="upload-area">
        <el-icon class="upload-icon"><upload-filled /></el-icon>
        <div class="upload-text">
          <div>将文件拖到此处，或<em>点击上传</em></div>
          <div class="upload-tip">{{ uploadTip }}</div>
        </div>
      </div>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="uploadState.uploading || uploadState.uploaded" class="upload-progress">
      <div class="file-info">
        <el-icon><document /></el-icon>
        <span class="file-name">{{ uploadState.fileName }}</span>
        <span class="file-size">{{ formatFileSize(uploadState.fileSize) }}</span>
      </div>

      <div class="progress-info">
        <el-progress
          :percentage="uploadState.progress"
          :status="uploadState.status"
          :stroke-width="8"
        />
        <div class="progress-text">
          <span>{{ uploadState.statusText }}</span>
          <span v-if="uploadState.uploading">
            {{ uploadState.uploadedChunks }}/{{ uploadState.totalChunks }} 分片
          </span>
        </div>
      </div>

      <div class="upload-actions">
        <el-button
          v-if="uploadState.uploading"
          type="warning"
          size="small"
          @click="pauseUpload"
        >
          {{ uploadState.paused ? '继续' : '暂停' }}
        </el-button>
        <el-button
          v-if="uploadState.uploading || uploadState.paused"
          type="danger"
          size="small"
          @click="cancelUpload"
        >
          取消
        </el-button>
        <el-button
          v-if="uploadState.uploaded"
          type="success"
          size="small"
          @click="resetUpload"
        >
          重新上传
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Document } from '@element-plus/icons-vue'
import SparkMD5 from 'spark-md5'
import * as ChunkUploadApi from '@/api/infra/chunkUpload'

interface Props {
  accept?: string
  maxSize?: number // MB
  chunkSize?: number // MB
}

interface Emits {
  (e: 'success', file: ChunkUploadApi.ChunkMergeRespVO): void
  (e: 'error', error: any): void
  (e: 'progress', progress: number): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*',
  maxSize: 1024, // 1GB
  chunkSize: 5 // 5MB
})

const emit = defineEmits<Emits>()

const uploadRef = ref()

// 上传状态
const uploadState = reactive({
  uploading: false,
  uploaded: false,
  paused: false,
  fileName: '',
  fileSize: 0,
  fileMd5: '',
  uploadId: '',
  totalChunks: 0,
  uploadedChunks: 0,
  progress: 0,
  status: 'success' as 'success' | 'exception' | 'warning',
  statusText: '准备上传...'
})

// 当前文件和分片信息
let currentFile: File | null = null
let chunks: Blob[] = []
let chunkMd5s: string[] = []

// 上传提示文本
const uploadTip = computed(() => {
  return `支持 ${props.accept}，单个文件不超过 ${props.maxSize}MB`
})

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 文件选择处理
const handleFileChange = async (file: any) => {
  const rawFile = file.raw as File
  
  // 文件大小检查
  if (rawFile.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return
  }

  currentFile = rawFile
  uploadState.fileName = rawFile.name
  uploadState.fileSize = rawFile.size
  uploadState.uploading = false
  uploadState.uploaded = false
  uploadState.paused = false
  uploadState.progress = 0
  uploadState.statusText = '准备上传...'

  try {
    // 开始上传流程
    await startUpload()
  } catch (error) {
    console.error('上传失败:', error)
    uploadState.statusText = '上传失败'
    uploadState.status = 'exception'
    emit('error', error)
  }
}

// 开始上传
const startUpload = async () => {
  if (!currentFile) return

  uploadState.statusText = '计算文件MD5...'
  uploadState.uploading = true

  // 1. 计算文件MD5
  const fileMd5 = await calculateFileMD5(currentFile)
  uploadState.fileMd5 = fileMd5

  // 2. 分片文件
  uploadState.statusText = '准备分片...'
  await createChunks(currentFile)

  // 3. 初始化上传
  uploadState.statusText = '初始化上传...'
  const initResp = await ChunkUploadApi.initChunkUpload({
    fileName: currentFile.name,
    fileSize: currentFile.size,
    fileMd5: fileMd5,
    chunkSize: props.chunkSize * 1024 * 1024
  })

  uploadState.uploadId = initResp.uploadId
  uploadState.totalChunks = initResp.totalChunks

  // 检查是否需要上传（秒传功能）
  if (!initResp.needUpload) {
    uploadState.uploading = false
    uploadState.uploaded = true
    uploadState.progress = 100
    uploadState.statusText = '上传完成（秒传）'
    emit('success', {
      fileId: initResp.fileId!,
      fileUrl: initResp.fileUrl!,
      fileName: currentFile.name,
      fileSize: currentFile.size,
      success: true
    })
    return
  }

  // 4. 上传分片
  uploadState.uploadedChunks = initResp.uploadedChunks.length
  await uploadChunks(initResp.uploadedChunks)
}

// 计算文件MD5
const calculateFileMD5 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const spark = new SparkMD5.ArrayBuffer()
    const fileReader = new FileReader()
    const chunkSize = 2 * 1024 * 1024 // 2MB per chunk for MD5 calculation
    let currentChunk = 0
    const chunks = Math.ceil(file.size / chunkSize)

    fileReader.onload = (e) => {
      spark.append(e.target?.result as ArrayBuffer)
      currentChunk++

      if (currentChunk < chunks) {
        loadNext()
      } else {
        resolve(spark.end())
      }
    }

    fileReader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    const loadNext = () => {
      const start = currentChunk * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      fileReader.readAsArrayBuffer(file.slice(start, end))
    }

    loadNext()
  })
}

// 创建分片
const createChunks = async (file: File) => {
  const chunkSize = props.chunkSize * 1024 * 1024
  const totalChunks = Math.ceil(file.size / chunkSize)
  
  chunks = []
  chunkMd5s = []

  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize
    const end = Math.min(start + chunkSize, file.size)
    const chunk = file.slice(start, end)
    chunks.push(chunk)

    // 计算分片MD5
    const chunkMd5 = await calculateChunkMD5(chunk)
    chunkMd5s.push(chunkMd5)
  }
}

// 计算分片MD5
const calculateChunkMD5 = (chunk: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const spark = new SparkMD5.ArrayBuffer()
    const fileReader = new FileReader()

    fileReader.onload = (e) => {
      spark.append(e.target?.result as ArrayBuffer)
      resolve(spark.end())
    }

    fileReader.onerror = () => {
      reject(new Error('分片读取失败'))
    }

    fileReader.readAsArrayBuffer(chunk)
  })
}

// 上传分片
const uploadChunks = async (uploadedChunks: number[]) => {
  uploadState.statusText = '上传分片中...'
  
  const uploadedSet = new Set(uploadedChunks)
  const concurrency = 3 // 并发上传数量

  // 创建上传队列
  const uploadQueue: number[] = []
  for (let i = 1; i <= uploadState.totalChunks; i++) {
    if (!uploadedSet.has(i)) {
      uploadQueue.push(i)
    }
  }

  // 并发上传分片
  const uploadPromises: Promise<void>[] = []
  for (let i = 0; i < Math.min(concurrency, uploadQueue.length); i++) {
    uploadPromises.push(uploadWorker(uploadQueue))
  }

  await Promise.all(uploadPromises)

  // 所有分片上传完成，开始合并
  await mergeChunks()
}

// 上传工作线程
const uploadWorker = async (queue: number[]) => {
  while (queue.length > 0 && !uploadState.paused) {
    const chunkNumber = queue.shift()
    if (chunkNumber === undefined) break

    try {
      const chunk = chunks[chunkNumber - 1]
      const chunkMd5 = chunkMd5s[chunkNumber - 1]

      await ChunkUploadApi.uploadChunk({
        uploadId: uploadState.uploadId,
        chunkNumber,
        chunkMd5,
        chunkFile: new File([chunk], `chunk_${chunkNumber}`)
      })

      uploadState.uploadedChunks++
      uploadState.progress = Math.round((uploadState.uploadedChunks / uploadState.totalChunks) * 90) // 90% for upload, 10% for merge
      emit('progress', uploadState.progress)

    } catch (error) {
      console.error(`分片 ${chunkNumber} 上传失败:`, error)
      // 重新加入队列重试
      queue.push(chunkNumber)
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒后重试
    }
  }
}

// 合并分片
const mergeChunks = async () => {
  uploadState.statusText = '合并文件中...'
  uploadState.progress = 95

  try {
    const mergeResp = await ChunkUploadApi.mergeChunks({
      uploadId: uploadState.uploadId
    })

    uploadState.uploading = false
    uploadState.uploaded = true
    uploadState.progress = 100
    uploadState.statusText = '上传完成'
    uploadState.status = 'success'

    emit('success', mergeResp)
    ElMessage.success('文件上传成功')

  } catch (error) {
    uploadState.statusText = '合并失败'
    uploadState.status = 'exception'
    throw error
  }
}

// 暂停上传
const pauseUpload = () => {
  uploadState.paused = !uploadState.paused
  uploadState.statusText = uploadState.paused ? '上传已暂停' : '上传中...'
}

// 取消上传
const cancelUpload = async () => {
  try {
    await ElMessageBox.confirm('确定要取消上传吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (uploadState.uploadId) {
      await ChunkUploadApi.cancelUpload(uploadState.uploadId)
    }

    resetUpload()
    ElMessage.info('上传已取消')

  } catch (error) {
    // 用户取消确认
  }
}

// 重置上传状态
const resetUpload = () => {
  uploadState.uploading = false
  uploadState.uploaded = false
  uploadState.paused = false
  uploadState.progress = 0
  uploadState.statusText = '准备上传...'
  uploadState.status = 'success'
  
  currentFile = null
  chunks = []
  chunkMd5s = []
  
  uploadRef.value?.clearFiles()
}

// 暴露方法
defineExpose({
  resetUpload
})
</script>

<style scoped>
.chunk-upload {
  width: 100%;
}

.upload-area {
  padding: 40px;
  text-align: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
}

.upload-text em {
  color: #409eff;
  font-style: normal;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

.upload-progress {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.file-info .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.file-name {
  flex: 1;
  margin-right: 12px;
  font-weight: 500;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.progress-info {
  margin-bottom: 12px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.upload-actions {
  display: flex;
  gap: 8px;
}
</style>
