import{_ as F}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as k,j as n,y as P,aF as g,o as m,h,w as o,k as t,u,c as z,F as L,g as j,m as R,aC as S,z as q,A as B}from"./index-C8b06LRn.js";import{R as v}from"./index-rrqDtE6o.js";const D=k({__name:"PermissionForm",setup(E,{expose:p}){const s=n(!1),d=n(!1),c=n([]),w=n([]),V=P(),f=async a=>{var i;((i=a==null?void 0:a.viewUser)==null?void 0:i.length)>0&&(a.viewAll=!1),await v.savePermission(a),V.success("\u4FDD\u5B58\u6210\u529F")},U=async()=>{d.value=!0;try{c.value=await v.getAllSellerList(),w.value=await v.getPermissionList()}finally{d.value=!1}};return p({openForm:()=>{s.value=!0,U()}}),(a,i)=>{const r=g("vxe-column"),_=S,b=q,y=B,A=g("vxe-table"),x=F;return m(),h(x,{title:"\u6536\u6B3E\u72B6\u51B5\u8868\u6743\u9650\u7BA1\u7406",modelValue:u(s),"onUpdate:modelValue":i[0]||(i[0]=e=>R(s)?s.value=e:null),width:"80%"},{default:o(()=>[t(A,{data:u(w),align:"center","header-cell-config":{height:30},"cell-config":{height:30},height:"400",loading:u(d),border:"",stripe:""},{default:o(()=>[t(r,{title:"\u7528\u6237",field:"nickname",width:"100"}),t(r,{title:"\u67E5\u770B\u6240\u6709",field:"viewAll",width:"120"},{default:o(({row:e})=>[t(_,{modelValue:e.viewAll,"onUpdate:modelValue":l=>e.viewAll=l,onChange:l=>f(e),"active-value":!0,"inactive-value":!1},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(r,{title:"\u53EF\u67E5\u770B\u8303\u56F4",field:"viewUser"},{default:o(({row:e})=>[t(y,{modelValue:e.viewUser,"onUpdate:modelValue":l=>e.viewUser=l,multiple:"",filterable:"",size:"small",onChange:l=>f(e)},{default:o(()=>[(m(!0),z(L,null,j(u(c),(l,C)=>(m(),h(b,{key:C,value:l,label:l},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1})]),_:1},8,["data","loading"])]),_:1},8,["modelValue"])}}});export{D as _};
