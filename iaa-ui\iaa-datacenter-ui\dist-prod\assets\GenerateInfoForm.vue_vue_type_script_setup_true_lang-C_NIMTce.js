import{d as z,j as h,r as B,au as H,f as L,aJ as W,u,o,h as i,w as e,k as a,c as v,g as I,N as U,D as g,F as N,l as p,v as s,$ as P,y as $,z as K,A as Q,B as X,a8 as Z,G as ee,bm as ae,S as le,H as ne,am as ue,an as te,al as oe,ab as de,ap as se,aq as me,I as pe}from"./index-C8b06LRn.js";import{E as re}from"./el-tree-select-E9FCZb0j.js";import{a as fe}from"./index-CAhgYtFg.js";import{g as ce}from"./index-BwMxOnEu.js";import{r}from"./formRules-Upspu04w.js";const ie=z({name:"InfraCodegenGenerateInfoForm",__name:"GenerateInfoForm",props:{table:{type:Object,default:()=>null},columns:{type:Array,default:()=>null}},setup(S,{expose:w}){$();const T=S,E=h(),t=h({templateType:null,frontType:null,scene:null,moduleName:"",businessName:"",className:"",classComment:"",parentMenuId:null,genPath:"",genType:"",masterTableId:void 0,subJoinColumnId:void 0,subJoinMany:void 0,treeParentColumnId:void 0,treeNameColumnId:void 0}),A=B({templateType:[r],frontType:[r],scene:[r],moduleName:[r],businessName:[r],businessPackage:[r],className:[r],classComment:[r],masterTableId:[r],subJoinColumnId:[r],subJoinMany:[r],treeParentColumnId:[r],treeNameColumnId:[r]}),q=h([]),J=h([]),D={label:"name"};return H(()=>T.table,async _=>{_&&(t.value=_,_.dataSourceConfigId>=0&&(q.value=await fe(t.value.dataSourceConfigId)))},{deep:!0,immediate:!0}),L(async()=>{try{const _=await ce();J.value=W(_)}catch{}}),w({validate:async()=>{var _;return(_=u(E))==null?void 0:_.validate()}}),(_,l)=>{const b=K,y=Q,m=X,d=Z,f=ee,c=ae,F=re,C=le,G=ne,O=ue,R=te,x=oe,k=de,M=se,j=me,Y=pe;return o(),i(Y,{ref_key:"formRef",ref:E,model:u(t),rules:u(A),"label-width":"150px"},{default:e(()=>[a(k,null,{default:e(()=>[a(d,{span:12},{default:e(()=>[a(m,{label:"\u751F\u6210\u6A21\u677F",prop:"templateType"},{default:e(()=>[a(y,{modelValue:u(t).templateType,"onUpdate:modelValue":l[0]||(l[0]=n=>u(t).templateType=n)},{default:e(()=>[(o(!0),v(N,null,I(u(U)(u(g).INFRA_CODEGEN_TEMPLATE_TYPE),n=>(o(),i(b,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{label:"\u524D\u7AEF\u7C7B\u578B",prop:"frontType"},{default:e(()=>[a(y,{modelValue:u(t).frontType,"onUpdate:modelValue":l[1]||(l[1]=n=>u(t).frontType=n)},{default:e(()=>[(o(!0),v(N,null,I(u(U)(u(g).INFRA_CODEGEN_FRONT_TYPE),n=>(o(),i(b,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{label:"\u751F\u6210\u573A\u666F",prop:"scene"},{default:e(()=>[a(y,{modelValue:u(t).scene,"onUpdate:modelValue":l[2]||(l[2]=n=>u(t).scene=n)},{default:e(()=>[(o(!0),v(N,null,I(u(U)(u(g).INFRA_CODEGEN_SCENE),n=>(o(),i(b,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,null,{label:e(()=>[p("span",null,[l[15]||(l[15]=s(" \u4E0A\u7EA7\u83DC\u5355 ")),a(c,{content:"\u5206\u914D\u5230\u6307\u5B9A\u83DC\u5355\u4E0B\uFF0C\u4F8B\u5982 \u7CFB\u7EDF\u7BA1\u7406",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(F,{modelValue:u(t).parentMenuId,"onUpdate:modelValue":l[3]||(l[3]=n=>u(t).parentMenuId=n),data:u(J),props:D,"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u7CFB\u7EDF\u83DC\u5355"},null,8,["modelValue","data"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"moduleName"},{label:e(()=>[p("span",null,[l[16]||(l[16]=s(" \u6A21\u5757\u540D ")),a(c,{content:"\u6A21\u5757\u540D\uFF0C\u5373\u4E00\u7EA7\u76EE\u5F55\uFF0C\u4F8B\u5982 system\u3001infra\u3001tool \u7B49\u7B49",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(C,{modelValue:u(t).moduleName,"onUpdate:modelValue":l[4]||(l[4]=n=>u(t).moduleName=n)},null,8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"businessName"},{label:e(()=>[p("span",null,[l[17]||(l[17]=s(" \u4E1A\u52A1\u540D ")),a(c,{content:"\u4E1A\u52A1\u540D\uFF0C\u5373\u4E8C\u7EA7\u76EE\u5F55\uFF0C\u4F8B\u5982 user\u3001permission\u3001dict \u7B49\u7B49",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(C,{modelValue:u(t).businessName,"onUpdate:modelValue":l[5]||(l[5]=n=>u(t).businessName=n)},null,8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"className"},{label:e(()=>[p("span",null,[l[18]||(l[18]=s(" \u7C7B\u540D\u79F0 ")),a(c,{content:"\u7C7B\u540D\u79F0\uFF08\u9996\u5B57\u6BCD\u5927\u5199\uFF09\uFF0C\u4F8B\u5982SysUser\u3001SysMenu\u3001SysDictData \u7B49\u7B49",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(C,{modelValue:u(t).className,"onUpdate:modelValue":l[6]||(l[6]=n=>u(t).className=n)},null,8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"classComment"},{label:e(()=>[p("span",null,[l[19]||(l[19]=s(" \u7C7B\u63CF\u8FF0 ")),a(c,{content:"\u7528\u4F5C\u7C7B\u63CF\u8FF0\uFF0C\u4F8B\u5982 \u7528\u6237",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(C,{modelValue:u(t).classComment,"onUpdate:modelValue":l[7]||(l[7]=n=>u(t).classComment=n)},null,8,["modelValue"])]),_:1})]),_:1}),u(t).genType==="1"?(o(),i(d,{key:0,span:24},{default:e(()=>[a(m,{prop:"genPath"},{label:e(()=>[p("span",null,[l[20]||(l[20]=s(" \u81EA\u5B9A\u4E49\u8DEF\u5F84 ")),a(c,{content:"\u586B\u5199\u78C1\u76D8\u7EDD\u5BF9\u8DEF\u5F84\uFF0C\u82E5\u4E0D\u586B\u5199\uFF0C\u5219\u751F\u6210\u5230\u5F53\u524DWeb\u9879\u76EE\u4E0B",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(C,{modelValue:u(t).genPath,"onUpdate:modelValue":l[9]||(l[9]=n=>u(t).genPath=n)},{append:e(()=>[a(x,null,{dropdown:e(()=>[a(R,null,{default:e(()=>[a(O,{onClick:l[8]||(l[8]=n=>u(t).genPath="/")},{default:e(()=>l[22]||(l[22]=[s(" \u6062\u590D\u9ED8\u8BA4\u7684\u751F\u6210\u57FA\u7840\u8DEF\u5F84 ")])),_:1})]),_:1})]),default:e(()=>[a(G,{type:"primary"},{default:e(()=>l[21]||(l[21]=[s(" \u6700\u8FD1\u8DEF\u5F84\u5FEB\u901F\u9009\u62E9 "),p("i",{class:"el-icon-arrow-down el-icon--right"},null,-1)])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})):P("",!0)]),_:1}),u(t).templateType==2?(o(),i(k,{key:0},{default:e(()=>[a(d,{span:24},{default:e(()=>l[23]||(l[23]=[p("h4",{class:"form-header"},"\u6811\u8868\u4FE1\u606F",-1)])),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"treeParentColumnId"},{label:e(()=>[p("span",null,[l[24]||(l[24]=s(" \u7236\u7F16\u53F7\u5B57\u6BB5 ")),a(c,{content:"\u6811\u663E\u793A\u7684\u7236\u7F16\u7801\u5B57\u6BB5\u540D\uFF0C \u5982\uFF1Aparent_Id",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(y,{modelValue:u(t).treeParentColumnId,"onUpdate:modelValue":l[10]||(l[10]=n=>u(t).treeParentColumnId=n),placeholder:"\u8BF7\u9009\u62E9"},{default:e(()=>[(o(!0),v(N,null,I(T.columns,(n,V)=>(o(),i(b,{key:V,label:n.columnName+"\uFF1A"+n.columnComment,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"treeNameColumnId"},{label:e(()=>[p("span",null,[l[25]||(l[25]=s(" \u6811\u540D\u79F0\u5B57\u6BB5 ")),a(c,{content:"\u6811\u8282\u70B9\u7684\u663E\u793A\u540D\u79F0\u5B57\u6BB5\u540D\uFF0C \u5982\uFF1Adept_name",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(y,{modelValue:u(t).treeNameColumnId,"onUpdate:modelValue":l[11]||(l[11]=n=>u(t).treeNameColumnId=n),placeholder:"\u8BF7\u9009\u62E9"},{default:e(()=>[(o(!0),v(N,null,I(T.columns,(n,V)=>(o(),i(b,{key:V,label:n.columnName+"\uFF1A"+n.columnComment,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):P("",!0),u(t).templateType==15?(o(),i(k,{key:1},{default:e(()=>[a(d,{span:24},{default:e(()=>l[26]||(l[26]=[p("h4",{class:"form-header"},"\u4E3B\u8868\u4FE1\u606F",-1)])),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"masterTableId"},{label:e(()=>[p("span",null,[l[27]||(l[27]=s(" \u5173\u8054\u7684\u4E3B\u8868 ")),a(c,{content:"\u5173\u8054\u4E3B\u8868\uFF08\u7236\u8868\uFF09\u7684\u8868\u540D\uFF0C \u5982\uFF1Asystem_user",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(y,{modelValue:u(t).masterTableId,"onUpdate:modelValue":l[12]||(l[12]=n=>u(t).masterTableId=n),placeholder:"\u8BF7\u9009\u62E9"},{default:e(()=>[(o(!0),v(N,null,I(u(q),(n,V)=>(o(),i(b,{key:V,label:n.tableName+"\uFF1A"+n.tableComment,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"subJoinColumnId"},{label:e(()=>[p("span",null,[l[28]||(l[28]=s(" \u5B50\u8868\u5173\u8054\u7684\u5B57\u6BB5 ")),a(c,{content:"\u5B50\u8868\u5173\u8054\u7684\u5B57\u6BB5\uFF0C \u5982\uFF1Auser_id",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(y,{modelValue:u(t).subJoinColumnId,"onUpdate:modelValue":l[13]||(l[13]=n=>u(t).subJoinColumnId=n),placeholder:"\u8BF7\u9009\u62E9"},{default:e(()=>[(o(!0),v(N,null,I(T.columns,(n,V)=>(o(),i(b,{key:V,label:n.columnName+"\uFF1A"+n.columnComment,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(d,{span:12},{default:e(()=>[a(m,{prop:"subJoinMany"},{label:e(()=>[p("span",null,[l[29]||(l[29]=s(" \u5173\u8054\u5173\u7CFB ")),a(c,{content:"\u4E3B\u8868\u4E0E\u5B50\u8868\u7684\u5173\u8054\u5173\u7CFB",placement:"top"},{default:e(()=>[a(f,{icon:"ep:question-filled"})]),_:1})])]),default:e(()=>[a(j,{modelValue:u(t).subJoinMany,"onUpdate:modelValue":l[14]||(l[14]=n=>u(t).subJoinMany=n),placeholder:"\u8BF7\u9009\u62E9"},{default:e(()=>[a(M,{value:!0},{default:e(()=>l[30]||(l[30]=[s("\u4E00\u5BF9\u591A")])),_:1}),a(M,{value:!1},{default:e(()=>l[31]||(l[31]=[s("\u4E00\u5BF9\u4E00")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):P("",!0)]),_:1},8,["model","rules"])}}});export{ie as _};
