import{_ as x}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as y,j as m,o as h,h as g,w as l,k as t,v as o,t as d,u as a,D as w,m as T}from"./index-C8b06LRn.js";import{E as j,a as D}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as I}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{f as i}from"./formatTime-COZ9Bl52.js";import{a as J}from"./index-GtctjSt3.js";const L=y({name:"JobLogDetail",__name:"JobLogDetail",setup(V,{expose:_}){const s=m(!1),n=m(!1),e=m({});return _({open:async r=>{if(s.value=!0,r){n.value=!0;try{e.value=await J(r)}finally{n.value=!1}}}}),(r,f)=>{const u=j,b=I,p=D,v=x;return h(),g(v,{modelValue:a(s),"onUpdate:modelValue":f[0]||(f[0]=c=>T(s)?s.value=c:null),title:"\u4EFB\u52A1\u8BE6\u7EC6",width:"700px"},{default:l(()=>[t(p,{column:1,border:""},{default:l(()=>[t(u,{label:"\u65E5\u5FD7\u7F16\u53F7","min-width":"60"},{default:l(()=>[o(d(a(e).id),1)]),_:1}),t(u,{label:"\u4EFB\u52A1\u7F16\u53F7"},{default:l(()=>[o(d(a(e).jobId),1)]),_:1}),t(u,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57"},{default:l(()=>[o(d(a(e).handlerName),1)]),_:1}),t(u,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570"},{default:l(()=>[o(d(a(e).handlerParam),1)]),_:1}),t(u,{label:"\u7B2C\u51E0\u6B21\u6267\u884C"},{default:l(()=>[o(d(a(e).executeIndex),1)]),_:1}),t(u,{label:"\u6267\u884C\u65F6\u95F4"},{default:l(()=>[o(d(a(i)(a(e).beginTime)+" ~ "+a(i)(a(e).endTime)),1)]),_:1}),t(u,{label:"\u6267\u884C\u65F6\u957F"},{default:l(()=>[o(d(a(e).duration+" \u6BEB\u79D2"),1)]),_:1}),t(u,{label:"\u4EFB\u52A1\u72B6\u6001"},{default:l(()=>[t(b,{type:a(w).INFRA_JOB_LOG_STATUS,value:a(e).status},null,8,["type","value"])]),_:1}),t(u,{label:"\u6267\u884C\u7ED3\u679C"},{default:l(()=>[o(d(a(e).result),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{L as _};
