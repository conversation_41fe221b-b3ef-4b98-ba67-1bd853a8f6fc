import{cX as f,cY as c,a3 as r,ch as h}from"./index-C8b06LRn.js";const o=f("tagsView",{state:()=>({visitedViews:[],cachedViews:new Set}),getters:{getVisitedViews(){return this.visitedViews},getCachedViews(){return Array.from(this.cachedViews)}},actions:{addView(i){this.addVisitedView(i),this.addCachedView()},addVisitedView(i){var t,a;if(this.visitedViews.some(s=>s.fullPath===i.fullPath)||(t=i.meta)!=null&&t.noTagsView)return;const e=Object.assign({},i,{title:((a=i.meta)==null?void 0:a.title)||"no-name"});if(e.meta){const s=[];if(this.visitedViews.forEach(d=>{var l,V,w;d.path===e.path&&((l=d.meta)==null?void 0:l.title)===((V=e.meta)==null?void 0:V.title)&&s.push(((w=d.meta)==null?void 0:w.titleSuffix)||"1")}),s.length){let d=1;for(;s.includes(`${d}`);)d+=1;e.meta.titleSuffix=d===1?void 0:`${d}`}}this.visitedViews.push(e)},addCachedView(){var e;const i=new Set;for(const t of this.visitedViews){const a=c(t);if((e=a.meta)!=null&&e.noCache)continue;const s=a.name;i.add(s)}Array.from(this.cachedViews).sort().toString()!==Array.from(i).sort().toString()&&(this.cachedViews=i)},delView(i){this.delVisitedView(i),this.delCachedView()},delVisitedView(i){for(const[e,t]of this.visitedViews.entries())if(t.fullPath===i.fullPath){this.visitedViews.splice(e,1);break}},delCachedView(){const i=r.currentRoute.value,e=h(this.getCachedViews,t=>t===i.name);e>-1&&this.cachedViews.delete(this.getCachedViews[e])},delAllViews(){this.delAllVisitedViews(),this.delCachedView()},delAllVisitedViews(){this.visitedViews=[]},delOthersViews(i){this.delOthersVisitedViews(i),this.addCachedView()},delOthersVisitedViews(i){this.visitedViews=this.visitedViews.filter(e=>{var t;return((t=e==null?void 0:e.meta)==null?void 0:t.affix)||e.fullPath===i.fullPath})},delLeftViews(i){const e=h(this.visitedViews,t=>t.fullPath===i.fullPath);e>-1&&(this.visitedViews=this.visitedViews.filter((t,a)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.fullPath===i.fullPath||a>e}),this.addCachedView())},delRightViews(i){const e=h(this.visitedViews,t=>t.fullPath===i.fullPath);e>-1&&(this.visitedViews=this.visitedViews.filter((t,a)=>{var s;return((s=t==null?void 0:t.meta)==null?void 0:s.affix)||t.fullPath===i.fullPath||a<e}),this.addCachedView())},updateVisitedView(i){for(let e of this.visitedViews)if(e.fullPath===i.fullPath){e=Object.assign(e,i);break}}},persist:!1});export{o as u};
