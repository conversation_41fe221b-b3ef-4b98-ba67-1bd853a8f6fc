import{d as x,aj as o,j as c,au as v,o as w,h as g,c0 as k,w as a,bf as n,l as z,k as r,u as V,m as _,aO as $,G as C,H as M,_ as S}from"./index-C8b06LRn.js";import{E as j}from"./el-drawer-C5TFtzfV.js";const q=["id"],E=S(x({__name:"index",props:{modelValue:o.bool.def(!1),title:o.string.def(""),direction:{type:String,default:"rtl"},size:o.string.def("30%"),beforeClose:{type:Function,default:()=>()=>{}},showModal:o.bool.def(!1),id:o.string.def("")},emits:["update:modelValue"],setup(d,{emit:m}){const l=d,s=c(!1),i=c();v(()=>l.modelValue,()=>{var t;if(s.value=l.modelValue,l.modelValue)(async()=>{await $();const e=document.getElementById(`close-btn${l.id}`);if(e)switch(l.direction){case"ltr":e.style.top="calc(50% - 25px)",e.style.left=l.size;break;case"rtl":e.style.top="calc(50% - 80px)",e.style.left="calc(100vw - "+l.size+")";break;case"ttb":e.style.width="50px",e.style.height="30px",e.style.top=l.size,e.style.left="calc(50vw - 25px)";break;case"btt":e.style.width="50px",e.style.height="30px",e.style.top="calc(100vh - "+l.size+" - 30px)",e.style.left="calc(50vw - 25px)"}})();else{const e=document.querySelector(".no-modal-mask-layer");e&&(e.style.display="none"),(t=i.value)==null||t.handleClose()}},{immediate:!0});const f=m,u=()=>{const t=document.querySelector(".no-modal-mask-layer");t&&(t.style.display="none"),i.value.handleClose(),f("update:modelValue",!1)};return(t,e)=>{const p=C,y=M,h=j;return w(),g(h,{modelValue:V(s),"onUpdate:modelValue":e[0]||(e[0]=b=>_(s)?s.value=b:null),title:l.title,modal:d.showModal,direction:l.direction,"show-close":!1,"destroy-on-close":!0,"before-close":l.beforeClose,size:l.size,"modal-class":d.showModal?"":"no-modal-mask-layer",ref_key:"drawerRef",ref:i,class:"no-modal-drawer"},k({default:a(()=>[z("div",{id:`close-btn${l.id}`,class:"position-fixed z-index-999"},[r(y,{type:"danger",style:{width:"20px",height:"80px",padding:"0"},onClick:u},{default:a(()=>[r(p,{icon:"fa-solid:angle-double-right"})]),_:1})],8,q),n(t.$slots,"default",{},void 0,!0)]),_:2},[t.$slots.header?{name:"header",fn:a(()=>[n(t.$slots,"header",{},void 0,!0)]),key:"0"}:void 0,t.$slots.footer?{name:"footer",fn:a(()=>[n(t.$slots,"footer",{},void 0,!0)]),key:"1"}:void 0]),1032,["modelValue","title","modal","direction","before-close","size","modal-class"])}}}),[["__scopeId","data-v-f4c4b160"]]);export{E as N};
