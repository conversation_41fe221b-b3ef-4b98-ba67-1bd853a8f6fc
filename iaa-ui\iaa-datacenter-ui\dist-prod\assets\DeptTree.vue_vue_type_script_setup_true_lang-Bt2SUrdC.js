import{d as k,j as t,au as y,f as v,o as x,c as _,l as d,k as c,w,u as l,m as V,aA as b,cq as g,F as D,aI as j,aJ as S,G as T,S as U}from"./index-C8b06LRn.js";const q={class:"head-container"},A={class:"head-container"},C=k({name:"SystemUserDeptTree",__name:"DeptTree",emits:["node-click"],setup(F,{emit:r}){const a=t(""),n=t([]),o=t(),i=(e,s)=>!e||s.name.includes(e),u=async e=>{p("node-click",e)},p=r;return y(a,e=>{o.value.filter(e)}),v(async()=>{await(async()=>{const e=await j();n.value=[],n.value.push(...S(e))})()}),(e,s)=>{const m=T,f=U;return x(),_(D,null,[d("div",q,[c(f,{modelValue:l(a),"onUpdate:modelValue":s[0]||(s[0]=h=>V(a)?a.value=h:null),class:"mb-20px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},{prefix:w(()=>[c(m,{icon:"ep:search"})]),_:1},8,["modelValue"])]),d("div",A,[c(l(g),{ref_key:"treeRef",ref:o,data:l(n),"expand-on-click-node":!1,"filter-node-method":i,props:l(b),"default-expanded-keys":[100],"highlight-current":"","node-key":"id",onNodeClick:u},null,8,["data","props"])])],64)}}});export{C as _};
