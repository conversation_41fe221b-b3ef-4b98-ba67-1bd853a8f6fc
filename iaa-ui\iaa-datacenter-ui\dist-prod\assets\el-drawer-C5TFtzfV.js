import{b6 as q,d7 as D,d8 as H,d as A,cx as O,cP as j,Y as v,j as w,bb as K,bK as M,d9 as N,bd as U,o as l,h as W,w as r,k as d,W as Y,u as e,x as Z,da as G,db as J,l as F,ar as Q,b4 as V,n as s,c as i,bf as c,t as X,$ as o,be as ee,dc as ae,Z as te,dd as se,bg as le,bh as oe}from"./index-C8b06LRn.js";const re=q({...D,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0},headerAriaLevel:{type:String,default:"2"}}),de=H,ie=A({name:"<PERSON><PERSON><PERSON><PERSON>",inheritAttrs:!1}),ne=oe(le(A({...ie,props:re,emits:de,setup(L,{expose:R}){const n=L,E=O();j({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},v(()=>!!E.title));const f=w(),y=w(),t=K("drawer"),{t:_}=M(),{afterEnter:h,afterLeave:k,beforeLeave:g,visible:u,rendered:x,titleId:p,bodyId:m,zIndex:$,onModalClick:T,onOpenAutoFocus:z,onCloseAutoFocus:B,onFocusoutPrevented:P,onCloseRequested:S,handleClose:b}=N(n,f),I=v(()=>n.direction==="rtl"||n.direction==="ltr"),C=v(()=>U(n.size));return R({handleClose:b,afterEnter:h,afterLeave:k}),(a,ce)=>(l(),W(e(se),{to:a.appendTo,disabled:a.appendTo==="body"&&!a.appendToBody},{default:r(()=>[d(Y,{name:e(t).b("fade"),onAfterEnter:e(h),onAfterLeave:e(k),onBeforeLeave:e(g),persisted:""},{default:r(()=>[Z(d(e(G),{mask:a.modal,"overlay-class":a.modalClass,"z-index":e($),onClick:e(T)},{default:r(()=>[d(e(J),{loop:"",trapped:e(u),"focus-trap-el":f.value,"focus-start-el":y.value,onFocusAfterTrapped:e(z),onFocusAfterReleased:e(B),onFocusoutPrevented:e(P),onReleaseRequested:e(S)},{default:r(()=>[F("div",Q({ref_key:"drawerRef",ref:f,"aria-modal":"true","aria-label":a.title||void 0,"aria-labelledby":a.title?void 0:e(p),"aria-describedby":e(m)},a.$attrs,{class:[e(t).b(),a.direction,e(u)&&"open"],style:e(I)?"width: "+e(C):"height: "+e(C),role:"dialog",onClick:V(()=>{},["stop"])}),[F("span",{ref_key:"focusStartRef",ref:y,class:s(e(t).e("sr-focus")),tabindex:"-1"},null,2),a.withHeader?(l(),i("header",{key:0,class:s(e(t).e("header"))},[a.$slots.title?c(a.$slots,"title",{key:1},()=>[o(" DEPRECATED SLOT ")]):c(a.$slots,"header",{key:0,close:e(b),titleId:e(p),titleClass:e(t).e("title")},()=>[a.$slots.title?o("v-if",!0):(l(),i("span",{key:0,id:e(p),role:"heading","aria-level":a.headerAriaLevel,class:s(e(t).e("title"))},X(a.title),11,["id","aria-level"]))]),a.showClose?(l(),i("button",{key:2,"aria-label":e(_)("el.drawer.close"),class:s(e(t).e("close-btn")),type:"button",onClick:e(b)},[d(e(ee),{class:s(e(t).e("close"))},{default:r(()=>[d(e(ae))]),_:1},8,["class"])],10,["aria-label","onClick"])):o("v-if",!0)],2)):o("v-if",!0),e(x)?(l(),i("div",{key:1,id:e(m),class:s(e(t).e("body"))},[c(a.$slots,"default")],10,["id"])):o("v-if",!0),a.$slots.footer?(l(),i("div",{key:2,class:s(e(t).e("footer"))},[c(a.$slots,"footer")],2)):o("v-if",!0)],16,["aria-label","aria-labelledby","aria-describedby","onClick"])]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])]),_:3},8,["mask","overlay-class","z-index","onClick"]),[[te,e(u)]])]),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])]),_:3},8,["to","disabled"]))}}),[["__file","drawer.vue"]]));export{ne as E};
