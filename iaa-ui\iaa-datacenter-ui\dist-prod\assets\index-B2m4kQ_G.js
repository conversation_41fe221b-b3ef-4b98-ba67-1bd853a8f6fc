import{aG as t}from"./index-C8b06LRn.js";const e={getCustomerList:async a=>await t.get({url:"/butt-joint/erp/project/get-customer",params:a}),getItemList:async a=>await t.get({url:"/butt-joint/erp/project/get-item",params:{itemCode:a}}),batchSaveTask:async a=>await t.post({url:"/eng/bom-task/batch-save",data:a}),pageTask:async a=>await t.post({url:"/eng/bom-task/page",data:a}),getTask:a=>t.get({url:"/eng/bom-task/get/"+a}),pageDetail:async a=>await t.post({url:"/eng/bom-task/page-detail",data:a}),saveDetail:async a=>await t.post({url:"/eng/bom-task/save-detail",data:a}),pageLog:async a=>await t.get({url:"/eng/bom-task/page-log",params:a}),exportDetail:a=>t.downloadPost({url:"/eng/bom-task/export-detail",data:a}),export:a=>t.downloadPost({url:"/eng/bom-task/export",data:a}),getDeliveryRate:async a=>await t.post({url:"/eng/bom-task/monthly-rate",data:a}),getTrendData:async a=>await t.post({url:"/eng/bom-task/trend-data",data:a}),exportDay:async a=>await t.downloadPost({url:"/eng/bom-task/export-excel",data:a}),batchDelete:async a=>await t.post({url:"/eng/bom-task/batch-delete",data:a})};export{e as C};
