import{aG as c,d as E,aj as T,j as p,f as $,x as J,u as n,o as y,h as b,w as l,k as t,m as K,c as k,F as V,g as x,v as s,t as m,ax as W,S as X,z as Y,A as Z,H as ee,B as ae,I as le,at as te,L as re,_ as se}from"./index-C8b06LRn.js";import{_ as ue}from"./index-COdQIXZX.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";const q=async Q=>await c.get({url:"/report/bom/recursive/list-operators/"+Q}),oe=se(E({__name:"DataTable",props:{type:T.oneOf(["forward","reverse","so"]).isRequired,rightMenuItems:T.arrayOf(Array).isRequired},setup(Q,{expose:N}){const d=Q,u=p({pageNo:1,pageSize:30}),C=p([]),I=p([]),S=p([]),U=p([]),j=p([]),A=p(0),z=p(!1),g=p(""),f=p([]),O=async()=>{if(d.type==="forward"){const r=await(async()=>await c.get({url:"/report/bom/recursive/get-forward-table-column"}))();f.value=r}else if(d.type==="reverse"){const r=await(async()=>await c.get({url:"/report/bom/recursive/get-reverse-table-column"}))();f.value=r}else if(d.type==="so"){const r=await(async()=>await c.get({url:"/report/bom/recursive/get-so-table-column"}))();f.value=r}else if(d.type==="material"){const r=await(async()=>await c.get({url:"/report/bom/recursive/get-item-table-column"}))();f.value=r}},R=(r,e)=>{for(let o in r)r[o]?u.value[o]=r[o]:u.value.hasOwnProperty(o)&&delete u.value[o];e||(u.value.pageNo=1),w()},v=()=>{u.value.pageNo=1,w()},D=()=>{u.value.allText=g,v()},G=()=>{u.value.allText="",delete u.value.allText,v()},w=async()=>{z.value=!0;try{let r={},e=W(u.value);for(let o in e)Array.isArray(e[o])&&(e[o]=`${e[o].join(",")}`);d.type==="forward"?r=await(async o=>await c.get({url:"/report/bom/recursive/page-forward",params:o}))(e):d.type==="reverse"?r=await(async o=>await c.get({url:"/report/bom/recursive/page-reverse",params:o}))(e):d.type==="so"?r=await(async o=>await c.get({url:"/report/bom/recursive/page-so",params:o}))(e):d.type==="material"&&(r=await(async o=>await c.get({url:"/report/bom/recursive/page-material",params:o}))(e)),C.value=r.list,A.value=r.total}finally{z.value=!1}},H=async()=>{I.value=await(async()=>await c.get({url:"/report/bom/recursive/list-units"}))(),S.value=await q(0),U.value=await q(1),j.value=await(async()=>await c.get({url:"/report/bom/recursive/list-customer"}))()};return N({onShowItem:(r,e)=>{u.value[r]=e,w()}}),$(()=>{O(),w(),H()}),(r,e)=>{const o=X,_=Y,h=Z,M=ee,i=ae,P=le,B=te,F=ue,L=re;return J((y(),b(F,{data:n(C),columns:n(f),total:n(A),page:n(u),"exclude-query-column":["units","buyer","seller","customer"],"right-menu-items":d.rightMenuItems,stripe:"",onSearch:R,onPagination:e[5]||(e[5]=a=>R(a,!0)),onRefresh:v,onClrear:G,height:"calc(100vh - 400px)"},{"x-table-header":l(()=>[t(o,{modelValue:n(g),"onUpdate:modelValue":e[0]||(e[0]=a=>K(g)?g.value=a:null),size:"small",placeholder:"\u5168\u57DF\u67E5\u8BE2",style:{width:"350px"},onChange:D},null,8,["modelValue"])]),"units-search":l(()=>[t(h,{size:"small",modelValue:n(u).units,"onUpdate:modelValue":e[1]||(e[1]=a=>n(u).units=a),clearable:"",onChange:v,filterable:""},{default:l(()=>[(y(!0),k(V,null,x(n(I),a=>(y(),b(_,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),"buyer-search":l(()=>[t(h,{size:"small",modelValue:n(u).buyer,"onUpdate:modelValue":e[2]||(e[2]=a=>n(u).buyer=a),clearable:"",onChange:v,filterable:""},{default:l(()=>[(y(!0),k(V,null,x(n(S),a=>(y(),b(_,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),"seller-search":l(()=>[t(h,{size:"small",modelValue:n(u).seller,"onUpdate:modelValue":e[3]||(e[3]=a=>n(u).seller=a),clearable:"",onChange:v,filterable:""},{default:l(()=>[(y(!0),k(V,null,x(n(U),a=>(y(),b(_,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),"customer-search":l(()=>[t(h,{size:"small",modelValue:n(u).customer,"onUpdate:modelValue":e[4]||(e[4]=a=>n(u).customer=a),clearable:"",onChange:v,filterable:""},{default:l(()=>[(y(!0),k(V,null,x(n(j),a=>(y(),b(_,{key:a.key,label:a.value,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),actualQty:l(({row:a})=>[t(B,{placement:"top",trigger:"click",width:"400"},{reference:l(()=>[t(M,{type:"primary",link:"",size:"small"},{default:l(()=>[s(m(a.actualQty),1)]),_:2},1024)]),default:l(()=>[t(P,{inline:"","label-position":"top",class:"custom-form",size:"small","label-width":"80px"},{default:l(()=>[t(i,{label:"\u5E93\u5B58"},{default:l(()=>[s(m(a.invQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[6]||(e[6]=[s("+")])),_:1}),t(i,{label:"\u6536\u8D27\u672A\u5BA1"},{default:l(()=>[s(m(a.rcvQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[7]||(e[7]=[s("+")])),_:1}),t(i,{label:"\u6742\u6536\u672A\u5BA1"},{default:l(()=>[s(m(a.miscRcvQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[8]||(e[8]=[s("+")])),_:1}),t(i,{label:"\u8C03\u5165\u5355\u672A\u5BA1"},{default:l(()=>[s(m(a.ferInQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[9]||(e[9]=[s("+")])),_:1}),t(i,{label:"\u8BF7\u8D2D\u672A\u5BA1"},{default:l(()=>[s(m(a.poQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[10]||(e[10]=[s("+")])),_:1}),t(i,{label:"\u5728\u9014"},{default:l(()=>[s(m(a.poQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[11]||(e[11]=[s("-")])),_:1}),t(i,{label:"\u51FA\u8D27\u672A\u5BA1"},{default:l(()=>[s(m(a.shipQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[12]||(e[12]=[s("-")])),_:1}),t(i,{label:"\u6742\u53D1\u672A\u5BA1"},{default:l(()=>[s(m(a.miscShipQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[13]||(e[13]=[s("-")])),_:1}),t(i,{label:"\u5DE5\u5355\u672A\u53D1"},{default:l(()=>[s(m(a.moQty||0),1)]),_:2},1024),t(i,{class:"operator"},{default:l(()=>e[14]||(e[14]=[s("-")])),_:1}),t(i,{label:"\u672A\u7ED3\u8BA2\u5355"},{default:l(()=>[s(m(a.soQty||0),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:1},8,["data","columns","total","page","right-menu-items"])),[[L,n(z)]])}}}),[["__scopeId","data-v-e6ad20f9"]]);export{oe as default};
