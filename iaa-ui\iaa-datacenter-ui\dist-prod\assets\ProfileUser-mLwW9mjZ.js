import{d as j,j as w,f as I,o as p,c as m,l as e,k as r,u as s,v as o,t as i,$ as u,p as P,G as T,_ as U}from"./index-C8b06LRn.js";import{f as G}from"./formatTime-COZ9Bl52.js";import q from"./UserAvatar-BMMTrVpA.js";import{g as A}from"./profile-BAixQBws.js";import"./el-avatar-BVm8aVjJ.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./el-space-CqmKDeRz.js";import"./XButton-BOgar_Ex.js";import"./filt-CBkj7zaY.js";import"./avatar-CPqUN878.js";const B={class:"text-center"},D={class:"list-group list-group-striped"},$={class:"list-group-item"},z={class:"pull-right"},C={class:"list-group-item"},E={class:"pull-right"},F={class:"list-group-item"},H={class:"pull-right"},J={class:"list-group-item"},K={key:0,class:"pull-right"},L={class:"list-group-item"},M={key:0,class:"pull-right"},N={class:"list-group-item"},O={key:0,class:"pull-right"},Q={class:"list-group-item"},R={class:"pull-right"},S=U(j({name:"ProfileUser",__name:"ProfileUser",setup(V){const{t:a}=P(),l=w({});return I(async()=>{await(async()=>{const n=await A();l.value=n})()}),(n,W)=>{var d,g,f,v,x,h,k,y,_,b;const t=T;return p(),m("div",null,[e("div",B,[r(q,{img:(d=s(l))==null?void 0:d.avatar},null,8,["img"])]),e("ul",D,[e("li",$,[r(t,{class:"mr-5px",icon:"ep:user"}),o(" "+i(s(a)("profile.user.username"))+" ",1),e("div",z,i((g=s(l))==null?void 0:g.username),1)]),e("li",C,[r(t,{class:"mr-5px",icon:"ep:phone"}),o(" "+i(s(a)("profile.user.mobile"))+" ",1),e("div",E,i((f=s(l))==null?void 0:f.mobile),1)]),e("li",F,[r(t,{class:"mr-5px",icon:"fontisto:email"}),o(" "+i(s(a)("profile.user.email"))+" ",1),e("div",H,i((v=s(l))==null?void 0:v.email),1)]),e("li",J,[r(t,{class:"mr-5px",icon:"carbon:tree-view-alt"}),o(" "+i(s(a)("profile.user.dept"))+" ",1),(x=s(l))!=null&&x.dept?(p(),m("div",K,i((h=s(l))==null?void 0:h.dept.name),1)):u("",!0)]),e("li",L,[r(t,{class:"mr-5px",icon:"ep:suitcase"}),o(" "+i(s(a)("profile.user.posts"))+" ",1),(k=s(l))!=null&&k.posts?(p(),m("div",M,i((y=s(l))==null?void 0:y.posts.map(c=>c.name).join(",")),1)):u("",!0)]),e("li",N,[r(t,{class:"mr-5px",icon:"icon-park-outline:peoples"}),o(" "+i(s(a)("profile.user.roles"))+" ",1),(_=s(l))!=null&&_.roles?(p(),m("div",O,i((b=s(l))==null?void 0:b.roles.map(c=>c.name).join(",")),1)):u("",!0)]),e("li",Q,[r(t,{class:"mr-5px",icon:"ep:calendar"}),o(" "+i(s(a)("profile.user.createTime"))+" ",1),e("div",R,i(s(G)(s(l).createTime)),1)])])])}}}),[["__scopeId","data-v-e446db2e"]]);export{S as default};
