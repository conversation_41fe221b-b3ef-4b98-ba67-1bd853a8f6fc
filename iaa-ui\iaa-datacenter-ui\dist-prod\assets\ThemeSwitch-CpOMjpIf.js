import{d as h,e as p,j as f,o as _,h as j,u as e,m as b,n as g,X as k,aC as w,_ as C}from"./index-C8b06LRn.js";import{u as t}from"./useIcon-CwemBubV.js";const a="var(--el-color-black)",I=C(h({name:"ThemeSwitch",__name:"ThemeSwitch",setup(T){const{getPrefixCls:i}=k(),l=i("theme-switch"),r=t({icon:"emojione-monotone:sun",color:"#fde047"}),m=t({icon:"emojione-monotone:crescent-moon",color:"#fde047"}),n=p(),o=f(n.getIsDark),u=c=>{n.setIsDark(c)};return(c,s)=>{const d=w;return _(),j(d,{modelValue:e(o),"onUpdate:modelValue":s[0]||(s[0]=v=>b(o)?o.value=v:null),"active-color":a,"active-icon":e(r),"border-color":a,class:g(e(l)),"inactive-color":a,"inactive-icon":e(m),"inline-prompt":"",onChange:u},null,8,["modelValue","active-icon","class","inactive-icon"])}}}),[["__scopeId","data-v-3b3542c2"]]);export{I as T};
