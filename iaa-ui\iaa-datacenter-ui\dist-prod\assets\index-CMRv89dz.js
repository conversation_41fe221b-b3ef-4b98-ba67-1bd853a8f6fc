import{d as ce,y as ue,p as fe,a as ye,dp as we,j as c,r as be,f as ke,aF as ge,T as _e,o,c as V,k as t,w as l,u as s,M as O,F as W,g as he,h as i,v as n,x as _,t as u,l as X,$ as x,m as Ce,S as xe,B as ve,z as Ue,A as De,G as Se,H as Ve,I as Ne,J as qe,dq as Be,bm as Ie,aM as Pe,am as Te,an as Me,al as ze,K as Fe,L as Le}from"./index-C8b06LRn.js";import{_ as je}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as Ee}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{E as Ke}from"./el-image-Dy1AcCSg.js";import{_ as Je}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as Re}from"./index-CkzUfjB7.js";import{f as Ae}from"./formatTime-COZ9Bl52.js";import{b as Ge,d as He,e as Oe,f as We}from"./index-C4DFiyBm.js";import{g as Xe}from"./index-CBsqkafF.js";import{_ as Ye}from"./ModelForm.vue_vue_type_script_setup_true_lang-DsYonCBB.js";import{b as Ze}from"./formCreate-CdPDb26P.js";import{C as $e}from"./index-uSeXqrUH.js";import{B as Qe}from"./constants-D3f7Z3TX.js";import{c as F}from"./permission-DVzqLl93.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";const ea={key:2},aa={key:0},la=ce({name:"BpmModel",__name:"index",setup(ta){const k=ue(),{t:L}=fe(),{push:v}=ye(),Y=we(),N=c(!0),j=c(0),E=c([]),d=be({pageNo:1,pageSize:10,key:void 0,name:void 0,category:void 0}),K=c(),J=c([]),f=async()=>{N.value=!0;try{const r=await Ge(d);E.value=r.list,j.value=r.total}finally{N.value=!1}},U=()=>{d.pageNo=1,f()},Z=()=>{K.value.resetFields(),U()},R=c(),A=(r,a)=>{R.value.open(r,a)},$=async r=>{try{await k.delConfirm(),await He(r.id),k.success(L("common.delSuccess")),await f()}catch{}},Q=async r=>{const a=r.processDefinition.suspensionState,g=a===1?2:1;try{const S=r.id,y=a===1?"\u505C\u7528":"\u542F\u7528",B="\u662F\u5426\u786E\u8BA4"+y+'\u6D41\u7A0B\u540D\u5B57\u4E3A"'+r.name+'"\u7684\u6570\u636E\u9879?';await k.confirm(B),await Oe(S,g),k.success(y+"\u6210\u529F"),await f()}catch{}},ee=r=>{v({name:"BpmProcessDefinition",query:{key:r.key}})},D=c(!1),q=c({rule:[],option:{}}),G=async r=>{if(r.formType==10){const a=await Xe(r.formId);Ze(q,a.conf,a.fields),D.value=!0}else await v({path:r.formCustomCreatePath})},h=r=>{const a=Y.getUser.id;return r.managerUserIds&&r.managerUserIds.includes(a)};return ke(async()=>{await f(),J.value=await $e.getCategorySimpleList()}),(r,a)=>{const g=Re,S=xe,y=ve,B=Ue,ae=De,I=Se,p=Ve,le=Ne,H=Je,w=qe,te=Ke,P=Be,oe=Ie,T=Pe,M=Te,se=Me,re=ze,ie=Fe,ne=Ee,de=ge("form-create"),me=je,C=_e("hasPermi"),pe=Le;return o(),V(W,null,[t(g,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08BPMN\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-dingding/"}),t(g,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08\u9489\u9489\u3001\u98DE\u4E66\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-bpmn/"}),t(g,{title:"\u9009\u62E9\u5BA1\u6279\u4EBA\u3001\u53D1\u8D77\u4EBA\u81EA\u9009",url:"https://doc.iocoder.cn/bpm/assignee/"}),t(g,{title:"\u4F1A\u7B7E\u3001\u6216\u7B7E\u3001\u4F9D\u6B21\u5BA1\u6279",url:"https://doc.iocoder.cn/bpm/multi-instance/"}),t(H,null,{default:l(()=>[t(le,{class:"-mb-15px",model:s(d),ref_key:"queryFormRef",ref:K,inline:!0,"label-width":"68px"},{default:l(()=>[t(y,{label:"\u6D41\u7A0B\u6807\u8BC6",prop:"key"},{default:l(()=>[t(S,{modelValue:s(d).key,"onUpdate:modelValue":a[0]||(a[0]=e=>s(d).key=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u6807\u8BC6",clearable:"",onKeyup:O(U,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),t(y,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:l(()=>[t(S,{modelValue:s(d).name,"onUpdate:modelValue":a[1]||(a[1]=e=>s(d).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:O(U,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),t(y,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:l(()=>[t(ae,{modelValue:s(d).category,"onUpdate:modelValue":a[2]||(a[2]=e=>s(d).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:l(()=>[(o(!0),V(W,null,he(s(J),e=>(o(),i(B,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:l(()=>[t(p,{onClick:U},{default:l(()=>[t(I,{icon:"ep:search",class:"mr-5px"}),a[7]||(a[7]=n(" \u641C\u7D22"))]),_:1}),t(p,{onClick:Z},{default:l(()=>[t(I,{icon:"ep:refresh",class:"mr-5px"}),a[8]||(a[8]=n(" \u91CD\u7F6E"))]),_:1}),_((o(),i(p,{type:"primary",plain:"",onClick:a[3]||(a[3]=e=>A("create"))},{default:l(()=>[t(I,{icon:"ep:plus",class:"mr-5px"}),a[9]||(a[9]=n(" \u65B0\u5EFA "))]),_:1})),[[C,["bpm:model:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),t(H,null,{default:l(()=>[_((o(),i(ie,{data:s(E)},{default:l(()=>[t(w,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name","min-width":"200"}),t(w,{label:"\u6D41\u7A0B\u56FE\u6807",align:"center",prop:"icon","min-width":"100"},{default:l(e=>[t(te,{src:e.row.icon,class:"h-32px w-32px"},null,8,["src"])]),_:1}),t(w,{label:"\u53EF\u89C1\u8303\u56F4",align:"center",prop:"startUserIds","min-width":"100"},{default:l(e=>[e.row.startUsers&&e.row.startUsers.length!==0?e.row.startUsers.length==1?(o(),i(P,{key:1},{default:l(()=>[n(u(e.row.startUsers[0].nickname),1)]),_:2},1024)):(o(),i(P,{key:2},{default:l(()=>[t(oe,{class:"box-item",effect:"dark",placement:"top",content:e.row.startUsers.map(m=>m.nickname).join("\u3001")},{default:l(()=>[n(u(e.row.startUsers[0].nickname)+"\u7B49 "+u(e.row.startUsers.length)+" \u4EBA\u53EF\u89C1 ",1)]),_:2},1032,["content"])]),_:2},1024)):(o(),i(P,{key:0},{default:l(()=>a[10]||(a[10]=[n(" \u5168\u90E8\u53EF\u89C1 ")])),_:1}))]),_:1}),t(w,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName","min-width":"100"}),t(w,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType","min-width":"200"},{default:l(e=>[e.row.formType===10?(o(),i(p,{key:0,type:"primary",link:"",onClick:m=>G(e.row)},{default:l(()=>[X("span",null,u(e.row.formName),1)]),_:2},1032,["onClick"])):e.row.formType===20?(o(),i(p,{key:1,type:"primary",link:"",onClick:m=>G(e.row)},{default:l(()=>[X("span",null,u(e.row.formCustomCreatePath),1)]),_:2},1032,["onClick"])):(o(),V("label",ea,"\u6682\u65E0\u8868\u5355"))]),_:1}),t(w,{label:"\u6700\u540E\u53D1\u5E03",align:"center",prop:"deploymentTime","min-width":"250"},{default:l(e=>{var m;return[e.row.processDefinition?(o(),V("span",aa,u(s(Ae)(e.row.processDefinition.deploymentTime)),1)):x("",!0),e.row.processDefinition?(o(),i(T,{key:1,class:"ml-10px"},{default:l(()=>[n(" v"+u(e.row.processDefinition.version),1)]),_:2},1024)):(o(),i(T,{key:2,type:"warning"},{default:l(()=>a[11]||(a[11]=[n("\u672A\u90E8\u7F72")])),_:1})),((m=e.row.processDefinition)==null?void 0:m.suspensionState)===2?(o(),i(T,{key:3,type:"warning",class:"ml-10px"},{default:l(()=>a[12]||(a[12]=[n(" \u5DF2\u505C\u7528 ")])),_:1})):x("",!0)]}),_:1}),t(w,{label:"\u64CD\u4F5C",align:"center",width:"200",fixed:"right"},{default:l(e=>[_((o(),i(p,{link:"",type:"primary",onClick:m=>A("update",e.row.id),disabled:!h(e.row)},{default:l(()=>a[13]||(a[13]=[n(" \u4FEE\u6539 ")])),_:2},1032,["onClick","disabled"])),[[C,["bpm:model:update"]]]),_((o(),i(p,{link:"",class:"!ml-5px",type:"primary",onClick:m=>{var b;(b=e.row).type==Qe.BPMN?v({name:"BpmModelEditor",query:{modelId:b.id}}):v({name:"SimpleWorkflowDesignEditor",query:{modelId:b.id}})},disabled:!h(e.row)},{default:l(()=>a[14]||(a[14]=[n(" \u8BBE\u8BA1 ")])),_:2},1032,["onClick","disabled"])),[[C,["bpm:model:update"]]]),_((o(),i(p,{link:"",class:"!ml-5px",type:"primary",onClick:m=>(async b=>{try{await k.confirm("\u662F\u5426\u90E8\u7F72\u8BE5\u6D41\u7A0B\uFF01\uFF01"),await We(b.id),k.success(L("\u90E8\u7F72\u6210\u529F")),await f()}catch{}})(e.row),disabled:!h(e.row)},{default:l(()=>a[15]||(a[15]=[n(" \u53D1\u5E03 ")])),_:2},1032,["onClick","disabled"])),[[C,["bpm:model:deploy"]]]),_((o(),i(re,{class:"!align-middle ml-5px",onCommand:m=>((b,z)=>{switch(b){case"handleDefinitionList":ee(z);break;case"handleDelete":$(z);break;case"handleChangeState":Q(z)}})(m,e.row)},{dropdown:l(()=>[t(se,null,{default:l(()=>[s(F)(["bpm:process-definition:query"])?(o(),i(M,{key:0,command:"handleDefinitionList"},{default:l(()=>a[17]||(a[17]=[n(" \u5386\u53F2 ")])),_:1})):x("",!0),s(F)(["bpm:model:update"])&&e.row.processDefinition?(o(),i(M,{key:1,command:"handleChangeState",disabled:!h(e.row)},{default:l(()=>[n(u(e.row.processDefinition.suspensionState===1?"\u505C\u7528":"\u542F\u7528"),1)]),_:2},1032,["disabled"])):x("",!0),s(F)(["bpm:model:delete"])?(o(),i(M,{key:2,type:"danger",command:"handleDelete",disabled:!h(e.row)},{default:l(()=>a[18]||(a[18]=[n(" \u5220\u9664 ")])),_:2},1032,["disabled"])):x("",!0)]),_:2},1024)]),default:l(()=>[t(p,{type:"primary",link:""},{default:l(()=>a[16]||(a[16]=[n("\u66F4\u591A")])),_:1})]),_:2},1032,["onCommand"])),[[C,["bpm:process-definition:query","bpm:model:update","bpm:model:delete"]]])]),_:1})]),_:1},8,["data"])),[[pe,s(N)]]),t(ne,{total:s(j),page:s(d).pageNo,"onUpdate:page":a[4]||(a[4]=e=>s(d).pageNo=e),limit:s(d).pageSize,"onUpdate:limit":a[5]||(a[5]=e=>s(d).pageSize=e),onPagination:f},null,8,["total","page","limit"])]),_:1}),t(Ye,{ref_key:"formRef",ref:R,onSuccess:f},null,512),t(me,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:s(D),"onUpdate:modelValue":a[6]||(a[6]=e=>Ce(D)?D.value=e:null),width:"800"},{default:l(()=>[t(de,{rule:s(q).rule,option:s(q).option},null,8,["rule","option"])]),_:1},8,["modelValue"])],64)}}});export{la as default};
