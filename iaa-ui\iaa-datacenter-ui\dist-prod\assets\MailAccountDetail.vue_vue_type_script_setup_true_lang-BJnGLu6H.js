import{_ as c}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as d}from"./Descriptions-iRMIdIt1.js";import{g as p}from"./useCrudSchemas-HzMMRa-v.js";import{a as f}from"./account.data-D0GCpo8Z.js";import{d as _,j as e,o as v,h,w as y,k as V,u as t,m as w}from"./index-C8b06LRn.js";const x=_({name:"SystemMailAccountDetail",__name:"MailAccountDetail",setup(A,{expose:u}){const a=e(!1),l=e(!1),o=e();return u({open:async s=>{a.value=!0,l.value=!0;try{o.value=await p(s)}finally{l.value=!1}}}),(s,m)=>{const r=d,n=c;return v(),h(n,{modelValue:t(a),"onUpdate:modelValue":m[0]||(m[0]=i=>w(a)?a.value=i:null),title:"\u8BE6\u60C5"},{default:y(()=>[V(r,{data:t(o),schema:t(f).detailSchema},null,8,["data","schema"])]),_:1},8,["modelValue"])}}});export{x as _};
