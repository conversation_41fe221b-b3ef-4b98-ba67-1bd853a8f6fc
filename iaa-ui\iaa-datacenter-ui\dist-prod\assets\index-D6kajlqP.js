import{aG as m,d as B,j as u,y as D,p as F,f as G,o as n,h as c,w as s,x as H,u as r,k as v,c as h,F as _,g as C,v as L,z as O,A as q,H as E,L as I}from"./index-C8b06LRn.js";import{_ as J}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as K}from"./index-COdQIXZX.js";import"./el-card-CaOo8U9P.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";const M=B({__name:"index",setup(Q){const y=u([]),g=u(0),b=u([]),V=u([]),d=u([]),p=u(!1),j=D(),{t:S}=F(),i=u({pageNo:1,pageSize:30}),k=(l,a)=>{for(let t in l)l[t]?i.value[t]=l[t]:i.value.hasOwnProperty(t)&&delete i.value[t];a||(i.value.pageNo=1),w()},w=async()=>{p.value=!0;try{const l=await(async a=>await m.get({url:"/butt-joint/ekuaibao/user/page",params:a}))(i.value);y.value=l.list,g.value=l.total}finally{p.value=!1}},U=async()=>{const l=await(async()=>await m.get({url:"/butt-joint/ekuaibao/user/get-columns"}))();b.value=l;const a=await(async()=>await m.get({url:"/butt-joint/ekuaibao/user/get-erp-all-seller"}))();V.value=a;const t=await(async()=>await m.get({url:"/butt-joint/ekuaibao/user/get-erp-all-employee"}))();d.value=t},x=async l=>{p.value=!0;try{await(async a=>await m.post({url:"/butt-joint/ekuaibao/user/save",data:a}))(l),j.success(S("common.updateSuccess"))}finally{p.value=!1}};return G(()=>{U(),w()}),(l,a)=>{const t=O,f=q,z=E,N=K,P=J,A=I;return n(),c(P,null,{default:s(()=>[H((n(),c(N,{data:r(y),total:r(g),columns:r(b),page:r(i),onSearch:k,onPagination:a[0]||(a[0]=o=>k(o,!0)),stripe:"","highlight-current":"",height:"calc(100vh - 350px)"},{erpUserCode:s(({row:o})=>[v(f,{modelValue:o.erpUserCode,"onUpdate:modelValue":e=>o.erpUserCode=e,filterable:"",size:"small"},{default:s(()=>[(n(!0),h(_,null,C(r(d),e=>(n(),c(t,{key:e.key,label:e.value,value:e.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),erpSellerCode:s(({row:o})=>[v(f,{modelValue:o.erpSellerCode,"onUpdate:modelValue":e=>o.erpSellerCode=e,filterable:"",size:"small","collapse-tags":"",multiple:""},{default:s(()=>[(n(!0),h(_,null,C(r(d),e=>(n(),c(t,{key:e.key,label:e.value,value:e.key},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),operation:s(({row:o})=>[v(z,{type:"primary",size:"small",link:"",onClick:e=>x(o)},{default:s(()=>a[1]||(a[1]=[L(" \u4FDD\u5B58 ")])),_:2},1032,["onClick"])]),_:1},8,["data","total","columns","page"])),[[A,r(p)]])]),_:1})}}});export{M as default};
