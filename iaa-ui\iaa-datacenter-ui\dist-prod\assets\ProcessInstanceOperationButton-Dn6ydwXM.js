import{d as ce,aj as W,aN as pe,y as de,j as m,r as fe,au as me,aF as ve,o as r,c as b,u as a,h as I,w as o,k as l,v as i,t as p,x as X,$ as y,l as w,m as Y,F as T,g as Z,aQ as ye,G as ke,H as be,aM as _e,B as xe,S as Ue,z as Ie,A as ge,I as Ee,at as Re,L as Ve,aE as he,_ as Ce}from"./index-C8b06LRn.js";import{E as Se}from"./el-card-CaOo8U9P.js";import{b as Ne}from"./formCreate-CdPDb26P.js";import{a as we,r as Te}from"./index-BwETMpJ2.js";import{_ as Ae}from"./TaskReturnForm.vue_vue_type_script_name_TaskRollbackDialogForm_setup_true_lang-DQTxc4W4.js";import{_ as Fe}from"./TaskDelegateForm.vue_vue_type_script_setup_true_lang-BfRGgF0r.js";import{_ as Oe}from"./TaskTransferForm.vue_vue_type_script_setup_true_lang-Hv3PPuJ8.js";import{_ as ze}from"./TaskSignCreateForm.vue_vue_type_script_setup_true_lang-BAQTRooy.js";import{s as d,O as De}from"./consts-DWqigJ3H.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const Pe={key:0,class:"h-50px bottom-10 text-14px flex items-center color-#32373c dark:color-#fff font-bold btn-container"},Ge={class:"flex flex-col flex-1 pt-20px px-20px"},Be={class:"el-icon-picture-outline"},je={class:"flex flex-col flex-1 pt-20px px-20px"},qe={class:"el-icon-picture-outline"},Je=Ce(ce({name:"ProcessInstanceBtnConatiner",__name:"ProcessInstanceOperationButton",props:{processInstance:W.any,userOptions:W.any},emits:["success"],setup(k,{expose:ee,emit:ae}){const se=pe().getUser.id,A=de(),{proxy:le}=he(),oe=ae,g=m(!1),C=m(!1),S=m(!1),n=m({}),c=m({}),f=m({}),_=m({}),N=m(),F=fe({reason:[{required:!0,message:"\u5BA1\u6279\u5EFA\u8BAE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]});me(()=>_.value,t=>{var e,u;(e=t==null?void 0:t.btn)==null||e.show(!1),(u=t==null?void 0:t.resetBtn)==null||u.show(!1)},{deep:!0});const O=t=>{n.value={},c.value={},f.value={},_.value={},t.forEach(e=>{if(ye(e.children)||O(e.children),(e.status===1||e.status===6)&&e.assigneeUser&&e.assigneeUser.id===se)if(n.value={...e},c.value={reason:"",copyUserIds:[]},e.formId&&e.formConf){const u={};Ne(u,e.formConf,e.formFields,e.formVariables),f.value=u}else f.value={}})},z=async t=>{var e;g.value=!0;try{const u=le.$refs.formRef,x=a(u);if(!x||!await x.validate())return;const R={id:n.value.id,reason:c.value.reason,copyUserIds:c.value.copyUserIds};if(t){const v=_.value;((e=Object.keys(v))==null?void 0:e.length)>0&&(await v.validate(),R.variables=f.value.value),await we(R),A.success("\u5BA1\u6279\u901A\u8FC7\u6210\u529F")}else await Te(R),A.success("\u5BA1\u6279\u4E0D\u901A\u8FC7\u6210\u529F");V()}finally{g.value=!1}},te=()=>{},D=t=>{C.value=!1,S.value=!1,N.value.resetFields(),t==="1"?C.value=!0:S.value=!0},P=m(),ne=()=>{P.value.open(n.value.id)},G=m(),re=async()=>{G.value.open(n.value.id)},B=m(),ue=async()=>{B.value.open(n.value.id)},j=m(),ie=async()=>{j.value.open(n.value.id)},V=()=>{oe("success")},E=t=>{let e=!0;return n.value.buttonsSetting&&n.value.buttonsSetting[t]&&(e=n.value.buttonsSetting[t].enable),e},U=t=>{let e=De.get(t);return n.value.buttonsSetting&&n.value.buttonsSetting[t]&&(e=n.value.buttonsSetting[t].displayName),e};return ee({loadRunningTask:O}),(t,e)=>{const u=ke,x=be,R=_e,v=xe,q=ve("form-create"),J=Se,L=Ue,K=Ie,$=ge,H=Ee,M=Re,Q=Ve;return r(),b(T,null,[a(n).id?(r(),b("div",Pe,[E(a(d).APPROVE)?(r(),I(M,{key:0,visible:a(C),placement:"top-end",width:500,trigger:"click"},{reference:o(()=>[l(x,{plain:"",type:"success",onClick:e[0]||(e[0]=s=>D("1"))},{default:o(()=>[l(u,{icon:"ep:select"}),i("\xA0 "+p(U(a(d).APPROVE)),1)]),_:1})]),default:o(()=>[X((r(),b("div",Ge,[l(H,{"label-position":"top",class:"mb-auto",ref_key:"formRef",ref:N,model:a(c),rules:a(F),"label-width":"100px"},{default:o(()=>[k.processInstance&&k.processInstance.startUser?(r(),I(v,{key:0,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:o(()=>{var s;return[i(p((s=k.processInstance)==null?void 0:s.startUser.nickname)+" ",1),l(R,{size:"small",type:"info",class:"ml-8px"},{default:o(()=>{var h;return[i(p((h=k.processInstance)==null?void 0:h.startUser.deptName),1)]}),_:1})]}),_:1})):y("",!0),a(n).formId>0?(r(),I(J,{key:1,class:"mb-15px !-mt-10px"},{header:o(()=>{var s;return[w("span",Be," \u586B\u5199\u8868\u5355\u3010"+p((s=a(n))==null?void 0:s.formName)+"\u3011 ",1)]}),default:o(()=>[l(q,{modelValue:a(f).value,"onUpdate:modelValue":e[1]||(e[1]=s=>a(f).value=s),api:a(_),"onUpdate:api":e[2]||(e[2]=s=>Y(_)?_.value=s:null),option:a(f).option,rule:a(f).rule},null,8,["modelValue","api","option","rule"])]),_:1})):y("",!0),l(v,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:o(()=>[l(L,{modelValue:a(c).reason,"onUpdate:modelValue":e[3]||(e[3]=s=>a(c).reason=s),placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue"])]),_:1}),l(v,{label:"\u6284\u9001\u4EBA",prop:"copyUserIds"},{default:o(()=>[l($,{modelValue:a(c).copyUserIds,"onUpdate:modelValue":e[4]||(e[4]=s=>a(c).copyUserIds=s),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6284\u9001\u4EBA"},{default:o(()=>[(r(!0),b(T,null,Z(k.userOptions,s=>(r(),I(K,{key:s.id,label:s.nickname,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(v,null,{default:o(()=>[l(x,{disabled:a(g),type:"success",onClick:e[5]||(e[5]=s=>z(!0))},{default:o(()=>[i(p(U(a(d).APPROVE)),1)]),_:1},8,["disabled"]),l(x,{onClick:e[6]||(e[6]=s=>C.value=!1)},{default:o(()=>e[14]||(e[14]=[i(" \u53D6\u6D88 ")])),_:1})]),_:1})]),_:1},8,["model","rules"])])),[[Q,a(g)]])]),_:1},8,["visible"])):y("",!0),E(a(d).REJECT)?(r(),I(M,{key:1,visible:a(S),placement:"top-end",width:500,trigger:"click"},{reference:o(()=>[l(x,{class:"mr-20px",plain:"",type:"danger",onClick:e[7]||(e[7]=s=>D("2"))},{default:o(()=>[l(u,{icon:"ep:close"}),i("\xA0 "+p(U(a(d).REJECT)),1)]),_:1})]),default:o(()=>[X((r(),b("div",je,[l(H,{"label-position":"top",class:"mb-auto",ref_key:"formRef",ref:N,model:a(c),rules:a(F),"label-width":"100px"},{default:o(()=>[k.processInstance&&k.processInstance.startUser?(r(),I(v,{key:0,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:o(()=>{var s;return[i(p((s=k.processInstance)==null?void 0:s.startUser.nickname)+" ",1),l(R,{size:"small",type:"info",class:"ml-8px"},{default:o(()=>{var h;return[i(p((h=k.processInstance)==null?void 0:h.startUser.deptName),1)]}),_:1})]}),_:1})):y("",!0),a(n).formId>0?(r(),I(J,{key:1,class:"mb-15px !-mt-10px"},{header:o(()=>{var s;return[w("span",qe," \u586B\u5199\u8868\u5355\u3010"+p((s=a(n))==null?void 0:s.formName)+"\u3011 ",1)]}),default:o(()=>[l(q,{modelValue:a(f).value,"onUpdate:modelValue":e[8]||(e[8]=s=>a(f).value=s),api:a(_),"onUpdate:api":e[9]||(e[9]=s=>Y(_)?_.value=s:null),option:a(f).option,rule:a(f).rule},null,8,["modelValue","api","option","rule"])]),_:1})):y("",!0),l(v,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:o(()=>[l(L,{modelValue:a(c).reason,"onUpdate:modelValue":e[10]||(e[10]=s=>a(c).reason=s),placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue"])]),_:1}),l(v,{label:"\u6284\u9001\u4EBA",prop:"copyUserIds"},{default:o(()=>[l($,{modelValue:a(c).copyUserIds,"onUpdate:modelValue":e[11]||(e[11]=s=>a(c).copyUserIds=s),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6284\u9001\u4EBA"},{default:o(()=>[(r(!0),b(T,null,Z(k.userOptions,s=>(r(),I(K,{key:s.id,label:s.nickname,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(v,null,{default:o(()=>[l(x,{disabled:a(g),type:"danger",onClick:e[12]||(e[12]=s=>z(!1))},{default:o(()=>[i(p(U(a(d).REJECT)),1)]),_:1},8,["disabled"]),l(x,{onClick:e[13]||(e[13]=s=>S.value=!1)},{default:o(()=>e[15]||(e[15]=[i(" \u53D6\u6D88 ")])),_:1})]),_:1})]),_:1},8,["model","rules"])])),[[Q,a(g)]])]),_:1},8,["visible"])):y("",!0),w("div",{onClick:te},[l(u,{size:14,icon:"svg-icon:send"}),e[16]||(e[16]=i("\xA0\u6284\u9001 "))]),E(a(d).TRANSFER)?(r(),b("div",{key:2,onClick:ne},[l(u,{size:14,icon:"fa:share-square-o"}),i("\xA0 "+p(U(a(d).TRANSFER)),1)])):y("",!0),E(a(d).DELEGATE)?(r(),b("div",{key:3,onClick:re},[l(u,{size:14,icon:"ep:position"}),i("\xA0 "+p(U(a(d).DELEGATE)),1)])):y("",!0),E(a(d).ADD_SIGN)?(r(),b("div",{key:4,onClick:ie},[l(u,{size:14,icon:"ep:plus"}),i("\xA0 "+p(U(a(d).ADD_SIGN)),1)])):y("",!0),E(a(d).RETURN)?(r(),b("div",{key:5,onClick:ue},[l(u,{size:14,icon:"fa:mail-reply"}),i("\xA0 "+p(U(a(d).RETURN)),1)])):y("",!0)])):y("",!0),l(Oe,{ref_key:"taskTransferFormRef",ref:P,onSuccess:V},null,512),l(Ae,{ref_key:"taskReturnFormRef",ref:B,onSuccess:V},null,512),l(Fe,{ref_key:"taskDelegateForm",ref:G,onSuccess:V},null,512),l(ze,{ref_key:"taskSignCreateFormRef",ref:j,onSuccess:V},null,512)],64)}}}),[["__scopeId","data-v-fcf203a3"]]);export{Je as default};
