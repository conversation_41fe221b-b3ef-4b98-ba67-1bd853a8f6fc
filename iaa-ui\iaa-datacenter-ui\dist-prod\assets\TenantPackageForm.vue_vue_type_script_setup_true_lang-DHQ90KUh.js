import{d as R,p as j,y as D,j as r,r as J,o as h,h as _,w as u,k as t,u as a,v as f,x as z,m as g,cq as G,aA as Q,c as W,F as X,g as Y,N as Z,D as $,t as ee,aJ as ae,S as le,B as se,aC as ue,ap as te,aq as oe,I as re,H as de,L as ne}from"./index-C8b06LRn.js";import{_ as me}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as ce}from"./el-card-CaOo8U9P.js";import{C as q}from"./constants-D3f7Z3TX.js";import{a as ie,c as pe,u as ve}from"./index-AhqHODKl.js";import{g as fe}from"./index-BwMxOnEu.js";const ke=R({name:"SystemTenantPackageForm",__name:"TenantPackageForm",emits:["success"],setup(he,{expose:E,emit:I}){const{t:y}=j(),x=D(),m=r(!1),b=r(""),c=r(!1),C=r(""),o=r({id:null,name:null,remark:null,menuIds:[],status:q.ENABLE}),N=J({name:[{required:!0,message:"\u5957\u9910\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],menuIds:[{required:!0,message:"\u5173\u8054\u7684\u83DC\u5355\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=r(),V=r([]),i=r(!1),n=r(),v=r(!1);E({open:async(s,e)=>{if(m.value=!0,b.value=y("action."+s),C.value=s,F(),V.value=ae(await fe()),e){c.value=!0;try{const d=await ie(e);o.value=d,d.menuIds.forEach(p=>{n.value.setChecked(p,!0,!1)})}finally{c.value=!1}}}});const S=I,A=async()=>{if(k&&await k.value.validate()){c.value=!0;try{const s=o.value;s.menuIds=[...n.value.getCheckedKeys(!1),...n.value.getHalfCheckedKeys()],C.value==="create"?(await pe(s),x.success(y("common.createSuccess"))):(await ve(s),x.success(y("common.updateSuccess"))),m.value=!1,S("success")}finally{c.value=!1}}},F=()=>{var s,e;v.value=!1,i.value=!1,o.value={id:null,name:null,remark:null,menuIds:[],status:q.ENABLE},(s=n.value)==null||s.setCheckedNodes([]),(e=k.value)==null||e.resetFields()},T=()=>{n.value.setCheckedNodes(v.value?V.value:[])},B=()=>{var e;const s=(e=n.value)==null?void 0:e.store.nodesMap;for(let d in s)s[d].expanded!==i.value&&(s[d].expanded=i.value)};return(s,e)=>{const d=le,p=se,w=ue,M=ce,H=te,K=oe,L=re,U=de,O=me,P=ne;return h(),_(O,{modelValue:a(m),"onUpdate:modelValue":e[6]||(e[6]=l=>g(m)?m.value=l:null),title:a(b)},{footer:u(()=>[t(U,{disabled:a(c),type:"primary",onClick:A},{default:u(()=>e[9]||(e[9]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),t(U,{onClick:e[5]||(e[5]=l=>m.value=!1)},{default:u(()=>e[10]||(e[10]=[f("\u53D6 \u6D88")])),_:1})]),default:u(()=>[z((h(),_(L,{ref_key:"formRef",ref:k,model:a(o),rules:a(N),"label-width":"80px"},{default:u(()=>[t(p,{label:"\u5957\u9910\u540D",prop:"name"},{default:u(()=>[t(d,{modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u5957\u9910\u540D"},null,8,["modelValue"])]),_:1}),t(p,{label:"\u83DC\u5355\u6743\u9650"},{default:u(()=>[t(M,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:u(()=>[e[7]||(e[7]=f(" \u5168\u9009/\u5168\u4E0D\u9009: ")),t(w,{modelValue:a(v),"onUpdate:modelValue":e[1]||(e[1]=l=>g(v)?v.value=l:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:T},null,8,["modelValue"]),e[8]||(e[8]=f(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),t(w,{modelValue:a(i),"onUpdate:modelValue":e[2]||(e[2]=l=>g(i)?i.value=l:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:B},null,8,["modelValue"])]),default:u(()=>[t(a(G),{ref_key:"treeRef",ref:n,data:a(V),props:a(Q),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1}),t(p,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[t(K,{modelValue:a(o).status,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).status=l)},{default:u(()=>[(h(!0),W(X,null,Y(a(Z)(a($).COMMON_STATUS),l=>(h(),_(H,{key:l.value,value:l.value},{default:u(()=>[f(ee(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[t(d,{modelValue:a(o).remark,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,a(c)]])]),_:1},8,["modelValue","title"])}}});export{ke as _};
