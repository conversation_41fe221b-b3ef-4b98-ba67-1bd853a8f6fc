import{d as G,y as J,p as L,a as A,j as p,r as E,f as O,T as Q,o as s,c as W,k as e,w as l,u as o,M as R,v as n,x as u,h as f,t as X,F as Z,S as $,B as ee,C as ae,G as te,H as le,I as oe,J as re,K as ie,L as ne}from"./index-C8b06LRn.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as me}from"./index-CkzUfjB7.js";import{d as Y}from"./formatTime-COZ9Bl52.js";import{d as ce}from"./download-D5Lb_h0f.js";import{d as pe,e as ue,s as fe,f as we}from"./index-CAhgYtFg.js";import{g as ge}from"./index-BqFsG9Sv.js";import{_ as ye}from"./ImportTable.vue_vue_type_script_setup_true_lang-CRTbiZgg.js";import{_ as _e}from"./PreviewCode.vue_vue_type_style_index_0_lang-DKblvxbF.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./java-CzcbBSiR.js";const be=G({name:"InfraCodegen",__name:"index",setup(he){const y=J(),{t:N}=L(),{push:F}=A(),k=p(!0),V=p(0),S=p([]),i=E({pageNo:1,pageSize:10,tableName:void 0,tableComment:void 0,createTime:[]}),T=p(),z=p([]),g=async()=>{k.value=!0;try{const C=await pe(i);S.value=C.list,V.value=C.total}finally{k.value=!1}},_=()=>{i.pageNo=1,g()},K=()=>{T.value.resetFields(),_()},U=p(),D=p();return O(async()=>{await g(),z.value=await ge()}),(C,a)=>{const b=me,H=$,h=ee,M=ae,v=te,d=le,P=oe,I=se,m=re,j=ie,q=de,w=Q("hasPermi"),B=ne;return s(),W(Z,null,[e(b,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u5355\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/"}),e(b,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u6811\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/tree/"}),e(b,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u4E3B\u5B50\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/master-sub/"}),e(b,{title:"\u5355\u5143\u6D4B\u8BD5",url:"https://doc.iocoder.cn/unit-test/"}),e(I,null,{default:l(()=>[e(P,{ref_key:"queryFormRef",ref:T,inline:!0,model:o(i),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(h,{label:"\u8868\u540D\u79F0",prop:"tableName"},{default:l(()=>[e(H,{modelValue:o(i).tableName,"onUpdate:modelValue":a[0]||(a[0]=t=>o(i).tableName=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u540D\u79F0",onKeyup:R(_,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"\u8868\u63CF\u8FF0",prop:"tableComment"},{default:l(()=>[e(H,{modelValue:o(i).tableComment,"onUpdate:modelValue":a[1]||(a[1]=t=>o(i).tableComment=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u63CF\u8FF0",onKeyup:R(_,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(M,{modelValue:o(i).createTime,"onUpdate:modelValue":a[2]||(a[2]=t=>o(i).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(h,null,{default:l(()=>[e(d,{onClick:_},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:search"}),a[6]||(a[6]=n(" \u641C\u7D22 "))]),_:1}),e(d,{onClick:K},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:refresh"}),a[7]||(a[7]=n(" \u91CD\u7F6E "))]),_:1}),u((s(),f(d,{type:"primary",onClick:a[3]||(a[3]=t=>{U.value.open()})},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:zoom-in"}),a[8]||(a[8]=n(" \u5BFC\u5165 "))]),_:1})),[[w,["infra:codegen:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(I,null,{default:l(()=>[u((s(),f(j,{data:o(S)},{default:l(()=>[e(m,{align:"center",label:"\u6570\u636E\u6E90"},{default:l(t=>{var c;return[n(X((c=o(z).find(r=>r.id===t.row.dataSourceConfigId))==null?void 0:c.name),1)]}),_:1}),e(m,{align:"center",label:"\u8868\u540D\u79F0",prop:"tableName",width:"200"}),e(m,{"show-overflow-tooltip":!0,align:"center",label:"\u8868\u63CF\u8FF0",prop:"tableComment",width:"200"}),e(m,{align:"center",label:"\u5B9E\u4F53",prop:"className",width:"200"}),e(m,{formatter:o(Y),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(m,{formatter:o(Y),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(m,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"300px"},{default:l(t=>[u((s(),f(d,{link:"",type:"primary",onClick:c=>{return r=t.row,void D.value.open(r.id);var r}},{default:l(()=>a[9]||(a[9]=[n(" \u9884\u89C8 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:preview"]]]),u((s(),f(d,{link:"",type:"primary",onClick:c=>{return r=t.row.id,void F("/codegen/edit?id="+r);var r}},{default:l(()=>a[10]||(a[10]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:update"]]]),u((s(),f(d,{link:"",type:"danger",onClick:c=>(async r=>{try{await y.delConfirm(),await ue(r),y.success(N("common.delSuccess")),await g()}catch{}})(t.row.id)},{default:l(()=>a[11]||(a[11]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:delete"]]]),u((s(),f(d,{link:"",type:"primary",onClick:c=>(async r=>{const x=r.tableName;try{await y.confirm("\u786E\u8BA4\u8981\u5F3A\u5236\u540C\u6B65"+x+"\u8868\u7ED3\u6784\u5417?",N("common.reminder")),await fe(r.id),y.success("\u540C\u6B65\u6210\u529F")}catch{}})(t.row)},{default:l(()=>a[12]||(a[12]=[n(" \u540C\u6B65 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:update"]]]),u((s(),f(d,{link:"",type:"primary",onClick:c=>(async r=>{const x=await we(r.id);ce.zip(x,"codegen-"+r.className+".zip")})(t.row)},{default:l(()=>a[13]||(a[13]=[n(" \u751F\u6210\u4EE3\u7801 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:download"]]])]),_:1})]),_:1},8,["data"])),[[B,o(k)]]),e(q,{limit:o(i).pageSize,"onUpdate:limit":a[4]||(a[4]=t=>o(i).pageSize=t),page:o(i).pageNo,"onUpdate:page":a[5]||(a[5]=t=>o(i).pageNo=t),total:o(V),onPagination:g},null,8,["limit","page","total"])]),_:1}),e(ye,{ref_key:"importRef",ref:U,onSuccess:g},null,512),e(_e,{ref_key:"previewRef",ref:D},null,512)],64)}}});export{be as default};
