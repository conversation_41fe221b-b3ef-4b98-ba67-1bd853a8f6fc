import{aG as w,d as ee,j as r,au as ae,f as le,aF as R,o as p,h as v,w as i,k as d,l as s,u as o,m as oe,aA as te,t as x,v as B,c as T,F as X,g as $,G as ie,S as de,cq as ne,a8 as ue,aa as re,z as pe,A as se,dF as me,ab as ce,_ as ve}from"./index-C8b06LRn.js";import{_ as fe}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{E as he}from"./el-empty-ag1-OZ0J.js";import{_ as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";const be=()=>w.get({url:"/butt-joint/plm/attribute/category"}),Ve=f=>w.get({url:"/butt-joint/plm/attribute/"+f}),ye=f=>w.post({url:"/butt-joint/plm/attribute/dict",data:f}),_e=f=>w.post({url:"/butt-joint/plm/attribute/item-list",data:f}),Ce={class:"head-container"},ke={class:"head-container h-[calc(100vh-120px-52px)] overflow-auto"},we={class:"h-[calc(100vh-130px-45px)]"},xe=ee({__name:"index",setup(f){const U=r([]),_=r(""),j=r(),m=r({}),A=r([]),F=r({}),S=r([]),l=r({pageNo:1,pageSize:100,itemCode:void 0,itemVar:void 0,itemName:void 0,model:void 0}),E=r(0),z=r(!1),V=r(null),N=async()=>{const t=await ye({id:m.value.id,code:m.value.code,...l.value});F.value=t.reduce((e,h)=>{const{key:u,value:k}=h;return e[u]=[...e[u]||[],{label:k,value:k}],e},{})},C=async()=>{z.value=!0;try{const t=await _e({id:m.value.id,code:m.value.code,...l.value});S.value=t.list,E.value=t.total}finally{z.value=!1}},c=()=>{l.value.pageNo=1,C(),N()},I=(t,e)=>{t.type==="dict"?l.value[t.prop]=[e]:l.value[t.prop]=e,c()},q=(t,e)=>!t||e.name.includes(t),J=async t=>{m.value=t,await(async()=>{var h;if(!((h=m.value)!=null&&h.id))return;const e=await Ve(m.value.id);A.value=e,l.value={pageNo:1,pageSize:100,itemCode:void 0,itemVar:void 0,itemName:void 0,model:void 0},V.value=null;for(const u of e)l.value[u.prop]=void 0})(),C(),N()},M=({menu:t})=>{if(t.code==="refresh"){for(const e in l.value)["pageNo","pageSize"].includes(e)||(l.value[e]=void 0);C(),N()}};return ae(_,t=>{j.value.filter(t)}),le(()=>{(async()=>{const t=await be();U.value=t})()}),(t,e)=>{const h=ie,u=de,k=ne,D=ge,G=ue,y=R("vxe-column"),K=re,H=pe,L=se,O=me,Q=R("vxe-table"),W=he,Y=fe,Z=ce;return p(),v(Z,{class:"h-[calc(100vh-120px)]",gutter:10},{default:i(()=>[d(G,{span:5,xs:24,class:"h-100%"},{default:i(()=>[d(D,null,{default:i(()=>[s("div",Ce,[d(u,{modelValue:o(_),"onUpdate:modelValue":e[0]||(e[0]=g=>oe(_)?_.value=g:null),class:"mb-10px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0"},{prefix:i(()=>[d(h,{icon:"ep:search"})]),_:1},8,["modelValue"])]),s("div",ke,[d(k,{ref_key:"treeRef",ref:j,data:o(U),"expand-on-click-node":!1,"filter-node-method":q,props:o(te),"highlight-current":"","node-key":"id",onNodeClick:J},{default:i(({node:g,data:a})=>[s("span",null,x(`${g.label}\u3010${a.code}\u3011`),1)]),_:1},8,["data","props"])])]),_:1})]),_:1}),d(G,{span:19,xs:24},{default:i(()=>[d(D,null,{default:i(()=>{var g;return[s("div",we,[(g=o(m))!=null&&g.id?(p(),v(Q,{key:0,height:"100%",align:"center","show-overflow":"","header-cell-config":{height:30},"cell-config":{height:30},"column-config":{resizable:!0},border:"",stripe:"",data:o(S),loading:o(z),"menu-config":{header:{options:[[{code:"refresh",name:"\u6E05\u7A7A\u7B5B\u9009"}]]},body:{options:[[{code:"refresh",name:"\u6E05\u7A7A\u7B5B\u9009"}]]}},onMenuClick:M},{default:i(()=>[d(y,{title:"\u54C1\u53F7",field:"itemCode",width:"120"},{header:i(()=>[e[7]||(e[7]=s("div",null,"\u54C1\u53F7",-1)),d(u,{size:"small",modelValue:o(l).itemCode,"onUpdate:modelValue":e[1]||(e[1]=a=>o(l).itemCode=a),clearable:"",onChange:c},null,8,["modelValue"])]),_:1}),d(y,{title:"\u7248\u672C",field:"itemVar",width:"80"},{header:i(()=>[e[8]||(e[8]=s("div",null,"\u7248\u672C",-1)),d(u,{size:"small",modelValue:o(l).itemVar,"onUpdate:modelValue":e[2]||(e[2]=a=>o(l).itemVar=a),clearable:"",onChange:c},null,8,["modelValue"])]),_:1}),d(y,{title:"\u54C1\u540D",field:"itemName",width:"160",align:"left"},{header:i(()=>[e[9]||(e[9]=s("div",null,"\u54C1\u540D",-1)),d(u,{size:"small",modelValue:o(l).itemName,"onUpdate:modelValue":e[3]||(e[3]=a=>o(l).itemName=a),clearable:"",onChange:c},null,8,["modelValue"])]),_:1}),d(y,{title:"\u578B\u53F7",field:"model",width:"80"},{header:i(()=>[e[10]||(e[10]=s("div",null,"\u578B\u53F7",-1)),d(u,{size:"small",modelValue:o(l).model,"onUpdate:modelValue":e[4]||(e[4]=a=>o(l).model=a),clearable:"",onChange:c},null,8,["modelValue"])]),default:i(({row:a})=>[d(K,{type:"primary",link:"",onClick:P=>I({prop:"model",type:"input"},a.model)},{default:i(()=>[B(x(a.model),1)]),_:2},1032,["onClick"])]),_:1}),(p(!0),T(X,null,$(o(A),(a,P)=>(p(),v(y,{key:P,title:a.label,field:a.prop,width:a.prop==="PARTFIXEDBACK2"?"auto":"120px",align:a.prop==="PARTFIXEDBACK2"?"left":"center"},{header:i(()=>[s("div",null,x(a.label),1),a.type=="input"?(p(),v(u,{key:0,modelValue:o(l)[a.prop],"onUpdate:modelValue":n=>o(l)[a.prop]=n,size:"small",clearable:"",onChange:c},null,8,["modelValue","onUpdate:modelValue"])):(p(),v(O,{key:1,title:"\u786E\u8BA4\u7B5B\u9009\uFF1F",placement:"top",trigger:"click",visible:o(V)===a.prop,onConfirm:n=>(a.prop,V.value=null,void c()),onCancel:n=>{return b=a.prop,l.value[b]=void 0,void(V.value=null);var b}},{reference:i(()=>[d(L,{modelValue:o(l)[a.prop],"onUpdate:modelValue":n=>o(l)[a.prop]=n,size:"small",clearable:"",multiple:"","collapse-tags":"",onChange:n=>{return b=a.prop,void(V.value=b);var b}},{default:i(()=>[(p(!0),T(X,null,$(o(F)[a.prop],n=>(p(),v(H,{key:n.value,label:n.label,value:n.value=="\u7A7A"?"":n.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["visible","onConfirm","onCancel"]))]),default:i(({row:n})=>[d(K,{type:"primary",link:"",onClick:b=>I(a,n[a.prop])},{default:i(()=>[B(x(n[a.prop]),1)]),_:2},1032,["onClick"])]),_:2},1032,["title","field","width","align"]))),128))]),_:1},8,["data","loading"])):(p(),v(W,{key:1,description:"\u8BF7\u9009\u62E9\u5DE6\u4FA7\u6210\u54C1\u5206\u7C7B"}))]),d(Y,{total:o(E),size:"small",page:o(l).pageNo,"onUpdate:page":e[5]||(e[5]=a=>o(l).pageNo=a),limit:o(l).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>o(l).pageSize=a),onPagination:C},null,8,["total","page","limit"])]}),_:1})]),_:1})]),_:1})}}}),ze=ve(xe,[["__scopeId","data-v-817423e2"]]);export{ze as default};
