import{d as ao,p as oo,y as to,e as ro,Y as $e,j as b,bU as ia,dJ as no,r as uo,b3 as so,f as io,bj as mo,o as g,c as C,x as ma,Z as po,l as i,k as a,w as d,v as U,h as P,u as T,F as j,g as oe,N as ue,D as se,aK as pa,c0 as co,t as f,$ as A,b4 as vo,e5 as ca,ax as B,aN as va,aO as fa,d2 as Ie,z as fo,A as bo,B as ho,a8 as yo,S as go,C as wo,ab as No,H as ko,G as ba,c3 as Vo,bm as Oo,a9 as Co,I as _o,J as To,K as Ro,L as Uo,ag as ha,_ as Eo,__tla as xo}from"./index-C8b06LRn.js";import{_ as So}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as Wo}from"./el-time-select-BrN8x4_E.js";import{E as $o}from"./el-card-CaOo8U9P.js";import{D as W}from"./index-SUTJ2GLg.js";import{d as ya}from"./dateUtil-D9m5ek6U.js";let ga,Io=Promise.all([(()=>{try{return xo}catch{}})()]).then(async()=>{let De,Ae,Le,Pe,Be,qe,Fe,Ye,ze,He,Me,je,Je,Ke,Ze,Ge,Xe,Qe,el,ll,al,ol,tl,rl,dl,nl,ul,sl,il,ml,pl,cl,vl,fl,bl,hl,yl,gl,wl,Nl,kl,Vl,Ol,Cl,_l,Tl,Rl,Ul,El,xl,Sl,Wl,$l,Il,Dl,Al,Ll,Pl,Bl,ql,Fl,Yl;De={class:"dialogContainer"},Ae={style:{display:"flex","justify-content":"space-between","align-items":"center"}},Le={style:{display:"flex",gap:"8px"}},Pe={key:0},Be=["v-loading"],qe={class:"card-title-actions"},Fe={key:0,style:{display:"flex",gap:"4px"}},Ye={class:"time-range-row"},ze={class:"autocomplete-item"},He={class:"main-text"},Me={class:"autocomplete-item"},je={class:"main-text"},Je={class:"autocomplete-item"},Ke={class:"main-text"},Ze={key:1,class:"mobile-body-info"},Ge={class:"info-row"},Xe={class:"info-value"},Qe={class:"info-row"},el={class:"info-value"},ll={class:"info-row"},al={class:"info-value"},ol={class:"info-row"},tl={class:"info-value"},rl={class:"info-row"},dl={class:"info-value"},nl={class:"info-row"},ul={class:"info-value"},sl={class:"info-value"},il={class:"info-row"},ml={class:"info-value"},pl={class:"info-value"},cl={class:"info-row"},vl={class:"info-value"},fl={class:"info-value"},bl={key:0,class:"info-row"},hl={class:"info-value"},yl={key:1,class:"info-row"},gl={class:"info-value"},wl={class:"info-row"},Nl={class:"info-value"},kl={class:"info-value"},Vl={key:2,class:"info-row"},Ol={class:"info-value"},Cl={class:"time-range-row"},_l={class:"autocomplete-item"},Tl={class:"main-text"},Rl={class:"autocomplete-item"},Ul={class:"main-text"},El={class:"autocomplete-item"},xl={class:"main-text"},Sl=["title"],Wl={id:"menu",class:"menuDiv",style:{position:"fixed"}},$l={class:"menuUl"},Il=["onClick"],Dl={class:"quick-entry-container"},Al={class:"mb-4"},Ll={class:"autocomplete-item"},Pl={class:"main-text"},Bl={key:0},ql={class:"mb-2"},Fl={class:"ml-2 text-gray-500"},Yl={key:1,class:"text-center py-8 text-gray-500"},ga=Eo(ao({name:"DayForm",__name:"DayForm",emits:["success"],setup(Do,{expose:wa,emit:Na}){const ka=ca(()=>ha(()=>import("./AbnormalForm-74fZUt7u.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),Va=ca(()=>ha(()=>import("./AttendanceForm-BqRyoilH.js"),__vite__mapDeps([11,3,1,2,4,12,5,6,7,8,13,9,14]))),{t:ge}=oo(),h=to(),Oa=ro(),V=$e(()=>Oa.getMobile),zl=()=>{V.value&&setTimeout(()=>{const o=document.querySelector('meta[name="viewport"]');if(o){const e=o.getAttribute("content");o.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"),setTimeout(()=>{o.setAttribute("content",e||"width=device-width, initial-scale=1.0")},100)}window.dispatchEvent(new Event("resize"))},300)},Ca=b(),_a=[{name:"\u5217\u6279\u91CF\u8D4B\u503C",operType:1,visibleColumns:["hoursReportNum","number","standardWork","type"]},{name:"\u751F\u6210\u5269\u4F59\u751F\u4EA7\u6570",operType:2,visibleColumns:["hoursReportNum"]},{name:"\u590D\u5236",operType:3},{name:"\u7C98\u8D34",operType:4}],we=b(null),J=b(null),Y=b(null),Ta=(o,e,t)=>{J.value=o,Y.value=e;const u=document.getElementById("menu");if(!u)return;t.preventDefault();const s=_a.map(r=>{var m;return r.operType===3||r.operType===4||(m=r.visibleColumns)!=null&&m.includes(e.property)?{...r,hidden:!1}:{...r,hidden:!0}});Ra(s),u.style.left=`${t.clientX+10}px`,u.style.top=t.clientY-310+"px",u.style.display="block",u.style.zIndex="1000"},Ne=b([]),Ra=o=>{Ne.value=o},Ua=(o,e,t)=>{document.getElementById("menu").style.display="none"},Hl=b(),Ml=b(),ie=ia((o,e,t,u)=>{var $,S;if(!o)return void e([]);const s={[t===0?"productionOrderCode":"salesOrderCode"]:"\u52A0\u8F7D\u4E2D...",loading:!0},r={[t===0?"productionOrderCode":"salesOrderCode"]:"\u6682\u65E0\u6570\u636E",noData:!0},m={[t===0?"productionOrderCode":"salesOrderCode"]:"\u67E5\u8BE2\u5931\u8D25",error:!0},y=`${t}-${o}-${u}`,R=Date.now();if(pe.has(y)&&ce.has(y)){const O=ce.get(y);if(R-O<3e5){const E=pe.get(y);return void e(E.length>0?E:[r])}}e([s]);const N={codeList:v.value.filter((O,E)=>O.salesOrderCode!==null&&O.salesOrderCode!==""&&O.salesOrderCode!==void 0&&O.productNo!==null&&O.productNo!==""&&O.productNo!==void 0&&E!==u).map(O=>({salesOrderCode:O.salesOrderCode,productNo:O.productNo})),docNo:o,workSelectCode:($=v.value[u||0])==null?void 0:$.salesOrderCode,workSelectNo:(S=v.value[u||0])==null?void 0:S.productNo,type:t};W.getWorkSalesOptions(N).then(O=>{const E=O||[];pe.set(y,E),ce.set(y,R),e(E.length>0?E:[r])}).catch(O=>{e([m])})},300),jl=(o,e,t,u)=>{if(!o)return void e([]);e([{productNo:"\u52A0\u8F7D\u4E2D...",loading:!0}]);const s={productionOrderCode:t,salesOrderCode:u,productNo:o};W.getProductNo(s).then(r=>{const m=r||[];e(m.length>0?m:[{productNo:"\u6682\u65E0\u6570\u636E",noData:!0}])}).catch(r=>{e([{productNo:"\u67E5\u8BE2\u5931\u8D25",error:!0}])})},Jl=async(o,e)=>{if(o.loading||o.noData||o.error)return;const t=v.value[e];if(t&&o){t.productionOrderCode=o.productionOrderCode,t.salesOrderCode=o.salesOrderCode,t.productNo=o.productNo,t.modelsOrColor=o.modelsOrColor,t.lineNo=o.lineNo,t.demand=o.demand,t.specs=o.specs,t.workOrderNum=o.workOrderNum,t.units=o.units,t.pmcRemark=o.pmcRemark,t.salesman=o.salesman;const u={productionOrderCode:o.productionOrderCode,salesOrderCode:o.salesOrderCode,type:t.type,dateStr:c.value.dateStr},s=await W.getOrderCodeNum(u),r=`${t.productionOrderCode}-${t.type}`;M.value[r]=s||0,t.totalReportNum=s||0,ae.value[r]=t.hoursReportNum||0}},Ea=no({render:()=>null}),me=async(o,e)=>{if(o.loading||o.noData||o.error)return;const t=v.value[e];t&&o&&(t.productionOrderCode=o.productionOrderCode,t.salesOrderCode=o.salesOrderCode,t.productNo=o.productNo,t.modelsOrColor=o.modelsOrColor,t.lineNo=o.lineNo,t.demand=o.demand,t.specs=o.specs,t.workOrderNum=o.workOrderNum,t.units=o.units,t.pmcRemark=o.pmcRemark,t.salesman=o.salesman,ke(t.type,t))},ke=async(o,e)=>{if(!e.productionOrderCode)return;const t={productionOrderCode:e.productionOrderCode,salesOrderCode:e.salesOrderCode,type:o,dateStr:c.value.dateStr},u=await W.getOrderCodeNum(t),s=`${e.productionOrderCode}-${o}`;e.type=o,M.value[s]=u||0,e.totalReportNum=u||0,ae.value[s]=e.hoursReportNum||0},X=b(!1),Kl=b(""),Q=b(!1),ee=b(""),xa=b(0),Ve=b([]),Oe=b([]),Ce=b([]),c=b({productionLine:1,teamLeader:"",dateStr:"",requiredAttendanceNum:void 0,actualAttendanceNum:void 0,assembledNum:void 0,assembledTotal:void 0,standardWork:void 0,remarkHead:void 0,batchesId:void 0,numberWork:void 0,type:1}),v=b([]),x=b({productionOrderCode:"",salesOrderCode:"",productNo:""}),Zl=()=>{x.value={productionOrderCode:"",salesOrderCode:"",productNo:""}},q=$e(()=>{const o=v.value,{productionOrderCode:e,salesOrderCode:t,productNo:u}=x.value;return e||t||u?o.filter(s=>{if(!s)return!1;const r=!e||s.productionOrderCode&&s.productionOrderCode.toString().includes(e),m=!t||s.salesOrderCode&&s.salesOrderCode.toString().includes(t),y=!u||s.productNo&&s.productNo.toString().includes(u);return s.isEditing||r&&m&&y}):o}),Gl=b([]),Sa={productionTimeStart:"",productionTimeEnd:"",type:1,productionOrderCode:void 0,salesOrderCode:void 0,productNo:void 0,modelsOrColor:void 0,specs:void 0,workOrderNum:void 0,pmcRemark:void 0,units:void 0,number:null,hoursReportNum:null,totalReportNum:void 0,standardWork:null,abnormalWork:void 0,abnormalNum:void 0,abnormalRemark:void 0,abnormalCountermeasures:void 0,abnormalTimeStart:void 0,abnormalTimeEnd:void 0,actualWork:void 0,isRework:0,isTrial:0,remark:void 0,isEditing:!1,id:void 0},_e=(o="",e="",t=0,u=0,s=1)=>({...Sa,productionTimeStart:e,productionTimeEnd:o,type:s,number:t,standardWork:u,displayAbnormal:0}),Wa=uo({productionLine:[{required:!0,message:"\u8BF7\u9009\u62E9\u751F\u4EA7\u4EA7\u7EBF",trigger:"blur"}],teamLeader:[{required:!0,message:"\u8BF7\u8F93\u5165\u73ED\u7EC4\u957F",trigger:"blur"}],date:[{required:!0,message:"\u8BF7\u9009\u62E9\u65E5\u671F",trigger:"change"}]}),Te=b(),te=b(!1),Re=b(!1),Ue=b(),K=b(""),z=b([]),L=b([]),re=b(!1),de=b(!1),ne=b(new Map),pe=new Map,ce=new Map,Xl={startTime:0,start(o){this.startTime=performance.now()},end(o){return performance.now()-this.startTime}},le=b([]),F=b(0),$a=()=>{const o=performance.now();let e=0;for(;performance.now()-o<5;)e++;return e<5e3},Ql=async()=>{try{le.value=[...q.value],F.value=q.value.length}catch{}};so(()=>{(()=>{const o=q.value.slice(0,F.value);le.value=[...o]})()});const ve=b(!1),Ia=$e(()=>!ve.value&&F.value<q.value.length),fe=b(!1),ea=async()=>{let o="",e="";v.value.length>0&&(o=v.value[v.value.length-1].productionTimeEnd||"",e=v.value[v.value.length-1].productionTimeStart||"");const t=c.value.assembledNum||0,u=c.value.standardWork||0,s=c.value.type||0,r=_e(o,e,t,u,s);ee.value==="update"&&(r.isEditing=!0),v.value.push(r),v.value=[...v.value]},la=(o,e)=>{e.isRework=o?1:0,oa(o,e)},aa=(o,e)=>{e.isTrial=o?1:0,oa(o,e)},oa=(o,e)=>{if(o){if(!(e.number&&e.productionTimeStart&&e.productionTimeEnd&&e.hoursReportNum))return h.error("\u8BF7\u5148\u586B\u5199\u65F6\u95F4\u6BB5\u3001\u4EBA\u6570\u3001\u751F\u4EA7\u6570\uFF01"),e.isTrial=0,void(e.isRework=0);e.abnormalTimeStart=e.productionTimeStart,e.abnormalTimeEnd=e.productionTimeEnd,e.abnormalReportNum=e.hoursReportNum,e.abnormalNum=e.number,e.displayAbnormal=0,Da(e)}else e.isRework===0&&e.isTrial===0&&(e.abnormalTimeStart="",e.abnormalTimeEnd="",e.abnormalNum=0,e.abnormalReportNum=0,e.abnormalWork=0)},Da=o=>{if(o.abnormalTimeStart&&o.abnormalTimeEnd&&o.abnormalNum!==void 0){const e=La(o.abnormalTimeStart,o.abnormalTimeEnd);o.abnormalWork=parseFloat((e/60*(o.abnormalNum||0)).toFixed(3))}else o.abnormalWork=0},be=o=>{const e=new Date,[t,u]=o.split(":").map(Number);return new Date(e.getFullYear(),e.getMonth(),e.getDate(),t,u)},Aa=o=>{const e=[{start:"12:00",end:"13:00"},{start:"17:30",end:"18:00"}];for(const t of e){const u=be(t.start),s=be(t.end);if(o>=u&&o<s)return!0}return!1},La=(o,e)=>{const t=be(o),u=be(e);if(u<=t)return 0;let s=0,r=new Date(t);for(;r<u;){const m=new Date(r.getTime()+6e4),y=m>u?u:m;Aa(r)||(s+=(y.getTime()-r.getTime())/6e4),r=y}return s},Pa=(o,e)=>{const t=ue(o).find(u=>u.value===e);return t?t.label:""},ta=o=>{h.confirm("\u786E\u5B9A\u8981\u7B2C"+(o+1)+"\u884C\u5220\u9664\u5417\uFF1F").then(()=>{if(v.value.length===1)return void h.error("\u6700\u540E\u4E00\u884C\u4E0D\u5141\u8BB8\u5220\u9664\uFF01");const e=v.value[o];ne.value.has(e)&&(ne.value.get(e)(),ne.value.delete(e)),v.value.splice(o,1),v.value=[...v.value]})};wa({open:async(o,e)=>{if(Xl.start("Form Opening"),Zl(),X.value=!0,Kl.value=ge("action."+o),ee.value=o,Qa(),e){Q.value=!0;try{const t=await W.getDay(e);c.value={productionLine:t[0].productionLine,teamLeader:t[0].teamLeader,dateStr:t[0].dateStr,requiredAttendanceNum:t[0].requiredAttendanceNum,actualAttendanceNum:t[0].actualAttendanceNum,assembledNum:t[0].assembledNum,assembledTotal:t[0].assembledTotal,standardWork:t[0].standardWork,remarkHead:t[0].remarkHead,batchesId:t[0].batchesId,numberWork:t[0].numberWork,type:t[0].type};const u=t.map(r=>({id:r.id,productionTimeStart:r.productionTime.split("-")[0],productionTimeEnd:r.productionTime.split("-")[1],productionTime:r.productionTime,productionOrderCode:r.productionOrderCode,salesOrderCode:r.salesOrderCode,productNo:r.productNo,modelsOrColor:r.modelsOrColor,workOrderNum:r.workOrderNum,units:r.units,number:r.number,hoursReportNum:r.hoursReportNum,totalReportNum:r.totalReportNum,lineNo:r.lineNo,demand:r.demand,standardWork:r.standardWork,actualWork:r.actualWork,abnormalWork:r.abnormalWork,abnormalNum:r.abnormalNum,abnormalReportNum:r.abnormalReportNum,abnormalRemark:r.abnormalRemark,abnormalCountermeasures:r.abnormalCountermeasures,abnormalTimeStart:(r.abnormalTime||"").split("-")[0],abnormalTimeEnd:(r.abnormalTime||"").split("-")[1]||"",type:r.type,audit:r.audit,isRework:r.isRework,isTrial:r.isTrial,remark:r.remark,pmcRemark:r.pmcRemark,displayAbnormal:r.displayAbnormal}));v.value=u,v.value.forEach(r=>{const m=`${r.productionOrderCode}-${r.type}`;ae.value[m]=r.hoursReportNum||0,M.value[m]=r.totalReportNum||0});const s=await W.getAttendanceDay(e);Ve.value=s.tabData.filter(r=>r.type==0),Oe.value=s.tabData2,Ce.value=s.tabData5}finally{Q.value=!1}}else{v.value=[_e()];const t=await W.getPrevious();t?(c.value.productionLine=t.productionLine,c.value.dateStr=ya(new Date).format("YYYY-MM-DD"),c.value.requiredAttendanceNum=t.requiredAttendanceNum,c.value.actualAttendanceNum=t.actualAttendanceNum,c.value.assembledNum=t.assembledNum,c.value.remarkHead=t.remarkHead,c.value.type=t.type,Ee()):(c.value.teamLeader=va().user.nickname,c.value.dateStr=ya(new Date).format("YYYY-MM-DD"),c.value.type=1,c.value.productionLine=1),c.value.teamLeader=va().user.nickname,xa.value=0,c.value.batchesId=void 0}fa(()=>{Xl.end("Form Opening")})}});const Ba=o=>{const e=B(o);if(!e||e.length===0)return void v.value.forEach(u=>{u.abnormalWork=0,u.abnormalRemark="",u.abnormalCountermeasures="",u.abnormalTimeStart="",u.abnormalTimeEnd="",u.abnormalReportNum=0,u.displayAbnormal=1});const t=new Set;e.forEach(u=>{if(!u.isNew){let s=-1;if(s=u.id?v.value.findIndex(r=>r.id===u.id):v.value.findIndex(r=>r.productionTimeStart===u.productionTimeStart&&r.productionTimeEnd===u.productionTimeEnd&&r.productionOrderCode===u.productionOrderCode&&r.salesOrderCode===u.salesOrderCode&&r.productNo===u.productNo&&r.type===u.type),s!==-1){t.add(s);const r=v.value[s];r.abnormalReportNum=B(u.abnormalReportNum),r.abnormalNum=B(u.abnormalNum),r.abnormalWork=B(u.abnormalWork),r.abnormalRemark=B(u.abnormalRemark),r.abnormalCountermeasures=B(u.abnormalCountermeasures),r.abnormalTimeStart=B(u.abnormalTimeStart),r.abnormalTimeEnd=B(u.abnormalTimeEnd),r.displayAbnormal=B(u.displayAbnormal)}}});for(let u=0;u<v.value.length;u++)t.has(u)||(v.value[u].abnormalNum=0,v.value[u].abnormalWork=0,v.value[u].abnormalRemark="",v.value[u].abnormalCountermeasures="",v.value[u].abnormalTimeStart="",v.value[u].abnormalTimeEnd="",v.value[u].displayAbnormal=1);c.value.batchesId=e[0].batchesId},qa=(o,e)=>{c.value.batchesId=o.batchesId,c.value.numberWork=o.attendanceWorkingHours,c.value.assembledTotal=o.assembledTotal,Ve.value=e.officially,Oe.value=e.temporary,Ce.value=e.borrow},Fa=o=>{Ie.confirm("\u5185\u5BB9\u8FD8\u672A\u4FDD\u5B58\uFF0C\u786E\u5B9A\u5173\u95ED\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}).then(()=>{o()}).catch(()=>{})},Ya=Na,he=b([]),za=async()=>{Ie.confirm("\u5185\u5BB9\u8FD8\u672A\u4FDD\u5B58\uFF0C\u786E\u5B9A\u5173\u95ED\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}).then(()=>{X.value=!1}).catch(()=>{})},Ha=async()=>{if(await Te.value.validate(),v.value.length===0)return void h.error("\u8BF7\u81F3\u5C11\u6DFB\u52A0\u4E00\u884C\u8868\u8EAB\u6570\u636E");let o=0;if(v.value.forEach(e=>{o+=e.number}),Ma()&&(()=>{if(!c.value.numberWork||c.value.numberWork<=0)return h.error("\u8BF7\u586B\u5199\u6B63\u786E\u7684\u51FA\u52E4\u5DE5\u65F6\uFF01"),!1;for(let e=0;e<v.value.length;e++){const t=v.value[e];if(!t.productionTimeStart||!t.productionTimeEnd)return h.error(`\u7B2C${e+1}\u884C\uFF1A\u8BF7\u9009\u62E9\u65F6\u95F4\u6BB5`),!1;if(!t.workOrderNum)return h.error(`\u7B2C${e+1}\u884C\uFF1A\u5DE5\u5355\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u9009\u62E9\u751F\u4EA7\u5DE5\u5355\u53F7\u6216\u9500\u552E\u8BA2\u5355\u53F7`),!1;if(!t.number)return h.error(`\u7B2C${e+1}\u884C\uFF1A\u4EBA\u6570\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8F93\u5165\u51FA\u52E4\u4EBA\u6570`),!1;if(!t.hoursReportNum)return h.error(`\u7B2C${e+1}\u884C\uFF1A\u8BF7\u8F93\u5165\u751F\u4EA7\u6570`),!1}return!0})()){he.value=[];for(let e=0;e<v.value.length;e++){let t=0;const u=v.value[e].productionTimeStart,s=v.value[e].productionTimeEnd,r=H(u),m=H(s);Ve.value.forEach(y=>{const[R,N]=y.officiallyTimeRange.split("-"),$=H(R),S=H(N);r>=$&&m<=S&&(t+=y.officiallyNumber||0)}),Oe.value.forEach(y=>{const[R,N]=y.temporaryTimeRange.split("-"),$=H(R),S=H(N);r>=$&&m<=S&&(t+=y.temporaryNumber||0)}),Ce.value.forEach(y=>{const[R,N]=y.borrowTimeRange.split("-"),$=H(R),S=H(N);r>=$&&m<=S&&(t+=y.borrowNumber||0)}),t!==v.value[e].number&&he.value.push(`\u7B2C${e+1}\u884C\uFF1A\u51FA\u52E4\u4EBA\u6570\u4E0E\u884C\u751F\u4EA7\u4EBA\u6570\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u68C0\u67E5\uFF01`)}Q.value=!0;try{if(ee.value==="create"){if(he.value.length>0){const t=he.value.map(u=>`<p>${u}</p>`).join("");if(await Ie.confirm(`<div style="text-align: left;">\u4EE5\u4E0B\u9519\u8BEF\u9700\u8981\u68C0\u67E5\uFF1A<br>${t}</div>`,"\u8B66\u544A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning",dangerouslyUseHTMLString:!0}).catch(()=>"cancel")==="cancel")return}const e=v.value.filter(t=>!t.isAdd).map(t=>({...c.value,...t,productionTime:`${t.productionTimeStart||""}-${t.productionTimeEnd||""}`,abnormalTime:`${t.abnormalTimeStart||""}-${t.abnormalTimeEnd||""}`,type:t.type}));e.length>0&&await W.createDay(e),h.success(ge("common.createSuccess"))}else await W.batchUpdateDays(v.value.map(e=>({...c.value,...e,type:e.type,productionTime:`${e.productionTimeStart||""}-${e.productionTimeEnd||""}`,abnormalTime:`${e.abnormalTimeStart||""}-${e.abnormalTimeEnd||""}`}))),h.success(ge("common.updateSuccess"));X.value=!1,Ya("success")}finally{Q.value=!1}}};function H(o){const[e,t]=o.split(":").map(Number);return 60*e+t}const Ee=()=>{v.value.forEach(o=>{o.number=c.value.assembledNum,o.standardWork=c.value.standardWork})},Ma=()=>{const o=new Map;for(let e=0;e<v.value.length;e++){const t=v.value[e];if(t.isRework===1)continue;const u=`${t.type}-${t.productionOrderCode}-${t.salesOrderCode}-${t.productNo}-${t.lineNo}`;o.has(u)||o.set(u,{totalHoursReportNum:0,workOrderNum:t.workOrderNum||0});const s=o.get(u);s.totalHoursReportNum+=Number(t.hoursReportNum)||0,s.firstRow===void 0&&(s.firstRow=e,s.workOrderNum=t.workOrderNum||0)}for(const[e,t]of o.entries())if(t.totalHoursReportNum>t.workOrderNum)return h.error(`\u7B2C${t.firstRow+1}\u884C\uFF1A\u540C\u8BA2\u5355/\u5DE5\u5355/\u5DE5\u5E8F/\u884C\u53F7\uFF0C\u7D2F\u8BA1\u751F\u4EA7\u6570\uFF08${t.totalHoursReportNum}\uFF09\u4E0D\u80FD\u8D85\u8FC7\u5DE5\u5355\u6570\u91CF\uFF08${t.workOrderNum}\uFF09`),!1;return!0},ra=()=>{te.value=!0,K.value="",z.value=[],L.value=[],re.value=!1,de.value=!1},ja=(o,e)=>{if(!o)return void e([]);const t={salesOrderCode:"\u6682\u65E0\u6570\u636E",noData:!0},u={salesOrderCode:"\u67E5\u8BE2\u5931\u8D25",error:!0};e([{salesOrderCode:"\u52A0\u8F7D\u4E2D...",loading:!0}]);const s={codeList:[],docNo:o,workSelectCode:o,type:1};W.getWorkSalesOptions(s).then(r=>{const m=r||[];e(m.length>0?m:[t])}).catch(r=>{e([u])})},Ja=async o=>{o.loading||o.noData||o.error||(K.value=o.salesOrderCode,await Ka())},Ka=async()=>{if(K.value.trim()){Re.value=!0;try{const o=await W.getSalesOrderInfo(K.value.trim());o&&o.length>0?(z.value=o,await fa(),L.value=[...o],re.value=!0,de.value=!1,Ue.value&&o.forEach(e=>{Ue.value.toggleRowSelection(e,!0)})):(z.value=[],L.value=[],re.value=!1,de.value=!1,h.info("\u672A\u627E\u5230\u76F8\u5173\u6570\u636E"))}catch{h.error("\u67E5\u8BE2\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5"),z.value=[],L.value=[],re.value=!1,de.value=!1}finally{Re.value=!1}}else h.warning("\u8BF7\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7")},Za=o=>{L.value=o;const e=z.value.length,t=o.length;re.value=t===e,de.value=t>0&&t<e},Ga=()=>{if(L.value.length===0)return void h.warning("\u8BF7\u9009\u62E9\u8981\u6DFB\u52A0\u7684\u6570\u636E");let o="",e="";if(v.value.length>0){const m=v.value[v.value.length-1];o=m.productionTimeEnd||"",e=m.productionTimeStart||""}const t=c.value.assembledNum||0,u=c.value.standardWork||0,s=c.value.type||0,r=[];L.value.forEach(m=>{const y=_e(o,e,t,u,s);y.salesOrderCode=m.salesOrderCode,y.productNo=m.productNo,y.modelsOrColor=m.modelsOrColor,y.specs=m.specs||"",y.workOrderNum=m.workOrderNum,y.units=m.units,y.pmcRemark=m.pmcRemark,y.lineNo=m.lineNo,y.demand=m.demand,r.push(y)}),v.value=[...v.value,...r],te.value=!1,h.success(`\u6210\u529F\u6DFB\u52A0 ${L.value.length} \u6761\u6570\u636E`)},Xa=o=>{const e=new Date().getFullYear(),t=new Date(e,0,1),u=new Date(e,11,31);return o<t||o>u},Qa=()=>{var o;ne.value.forEach(e=>{e()}),ne.value.clear(),pe.clear(),ce.clear(),c.value={teamLeader:"",dateStr:"",requiredAttendanceNum:void 0,actualAttendanceNum:void 0,assembledNum:void 0,assembledTotal:void 0,remarkHead:void 0,standardWork:void 0,batchesId:void 0,numberWork:void 0,type:1,productionLine:1},v.value=[],ae.value={},M.value={},le.value=[],F.value=0,ve.value=!1,(o=Te.value)==null||o.resetFields()},ae=b({}),M=b({}),da=(o,e)=>{const t=Number(o);if(t>e.workOrderNum)e.hoursReportNum=e.workOrderNum,h.warning("\u5C0F\u65F6\u5B8C\u6210\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7\u5DE5\u5355\u6570\u91CF");else{const u=`${e.productionOrderCode}-${e.type}`,s=t-(ae.value[u]||0);e.isRework===1?e.totalReportNum=M.value[u]||0:(M.value[u]=(M.value[u]||0)+s,e.totalReportNum=M.value[u]),ae.value[u]=t}},na=ia(()=>{if(!V.value||fe.value)return;const o=document.querySelector(".el-card__body");if(o){const{scrollTop:e,scrollHeight:t,clientHeight:u}=o;(e+u)/t>.8&&Ia.value&&(async()=>{if(!fe.value)if(ve.value)await Ql();else try{if(q.value.length-F.value<=10)return ve.value=!0,void await Ql();const s=$a()?10:5,r=q.value.slice(F.value,F.value+s);if(r.length===0)return;le.value=[...le.value,...r],F.value+=r.length}catch{}finally{fe.value=!1}})()}},200);return io(()=>{if(V.value){window.addEventListener("orientationchange",zl);const o=document.querySelector(".el-card__body");o&&o.addEventListener("scroll",na)}}),mo(()=>{if(V.value){window.removeEventListener("orientationchange",zl);const o=document.querySelector(".el-card__body");o&&o.removeEventListener("scroll",na)}le.value=[],F.value=0}),(o,e)=>{const t=fo,u=bo,s=ho,r=yo,m=go,y=wo,R=No,N=ko,$=$o,S=ba,O=ba,E=Wo,Z=Vo,eo=Oo,ye=Co,xe=_o,w=To,ua=Ro,sa=So,lo=Uo;return g(),C(j,null,[ma(i("div",De,[a(sa,{title:Kl.value,modelValue:X.value,"onUpdate:modelValue":e[17]||(e[17]=l=>X.value=l),width:"100%",height:"100%",draggable:!0,overflow:!0,fullscreen:!0,"before-close":Fa},{footer:d(()=>[a(N,{onClick:Ha,type:"primary",disabled:Q.value},{default:d(()=>e[48]||(e[48]=[U("\u786E \u5B9A")])),_:1},8,["disabled"]),a(N,{onClick:za},{default:d(()=>e[49]||(e[49]=[U("\u53D6 \u6D88")])),_:1})]),default:d(()=>[ma((g(),P(xe,{ref_key:"formRef",ref:Te,model:c.value,rules:T(Wa),"label-width":"80px"},{default:d(()=>[a($,{class:"mb-4",shadow:"never"},{default:d(()=>[a(R,null,{default:d(()=>[a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u4EA7\u7EBF","label-width":V.value?"":"70px",prop:"productionLine"},{default:d(()=>[a(u,{modelValue:c.value.productionLine,"onUpdate:modelValue":e[0]||(e[0]=l=>c.value.productionLine=l),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u7EBF"},{default:d(()=>[(g(!0),C(j,null,oe(T(ue)(T(se).PRODUCTION_REPORT_LINE),l=>(g(),P(t,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u73ED\u7EC4\u957F","label-width":V.value?"":"58px",prop:"teamLeader"},{default:d(()=>[a(m,{modelValue:c.value.teamLeader,"onUpdate:modelValue":e[1]||(e[1]=l=>c.value.teamLeader=l),placeholder:"\u8BF7\u8F93\u5165\u73ED\u7EC4\u957F",readonly:""},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u65E5\u671F","label-width":V.value?"":"72px",prop:"dateStr"},{default:d(()=>[a(y,{modelValue:c.value.dateStr,"onUpdate:modelValue":e[2]||(e[2]=l=>c.value.dateStr=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u65E5\u671F",style:{width:"100%"},"prefix-icon":Ea.value,class:"custom-date-picker","disabled-date":Xa},null,8,["modelValue","prefix-icon"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u5E94\u51FA\u52E4","label-width":V.value?"":"84px",prop:"requiredAttendanceNum"},{default:d(()=>[a(m,{modelValue:c.value.requiredAttendanceNum,"onUpdate:modelValue":e[3]||(e[3]=l=>c.value.requiredAttendanceNum=l),style:{width:"100%"},type:"number",min:"0",class:"no-spin-input"},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u5B9E\u9645\u51FA\u52E4","label-width":V.value?"":"84px",prop:"actualAttendanceNum"},{default:d(()=>[a(m,{modelValue:c.value.actualAttendanceNum,"onUpdate:modelValue":e[4]||(e[4]=l=>c.value.actualAttendanceNum=l),style:{width:"100%"},type:"number",min:"0",class:"no-spin-input"},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u7EC4\u88C5\u4EBA\u6570","label-width":V.value?"":"72px",prop:"assembledNum"},{default:d(()=>[a(m,{modelValue:c.value.assembledNum,"onUpdate:modelValue":e[5]||(e[5]=l=>c.value.assembledNum=l),style:{width:"100%"},type:"number",min:"0",onChange:Ee,class:"no-spin-input"},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1})]),_:1}),a(R,null,{default:d(()=>[a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u5DE5\u5E8F","label-width":V.value?"":"70px",prop:"type"},{default:d(()=>[a(u,{modelValue:c.value.type,"onUpdate:modelValue":e[6]||(e[6]=l=>c.value.type=l),placeholder:"\u5DE5\u5E8F",style:{width:"100%"}},{default:d(()=>[(g(!0),C(j,null,oe(T(ue)(T(se).PRODUCTION_REPORT_TYPE),l=>(g(),P(t,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u6807\u51C6\u5DE5\u65F6","label-width":V.value?"":"60px",prop:"standard_work"},{default:d(()=>[a(m,{modelValue:c.value.standardWork,"onUpdate:modelValue":e[7]||(e[7]=l=>c.value.standardWork=l),type:"number",min:0,style:{width:"100%"},onChange:Ee,class:"no-spin-input"},{append:d(()=>e[21]||(e[21]=[U("\u5206")])),_:1},8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u7EC4\u88C5\u5DE5\u65F6","label-width":V.value?"":"72px"},{default:d(()=>[a(m,{modelValue:c.value.assembledTotal,"onUpdate:modelValue":e[8]||(e[8]=l=>c.value.assembledTotal=l),type:"number",placeholder:"\u7CFB\u7EDF\u81EA\u52A8\u8BA1\u7B97",min:0,style:{width:"100%"},readonly:!0},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:4,xs:12},{default:d(()=>[a(s,{label:"\u51FA\u52E4\u5DE5\u65F6","label-width":V.value?"":"84px"},{default:d(()=>[a(m,{modelValue:c.value.numberWork,"onUpdate:modelValue":e[9]||(e[9]=l=>c.value.numberWork=l),type:"number",placeholder:"\u7CFB\u7EDF\u81EA\u52A8\u8BA1\u7B97",min:0,style:{width:"100%"},readonly:!0},null,8,["modelValue"])]),_:1},8,["label-width"])]),_:1}),a(r,{span:2,xs:12},{default:d(()=>[a(N,{onClick:e[10]||(e[10]=l=>{return p=c.value.productionLine,n=c.value.batchesId,k="\u51FA\u52E4\u5DE5\u65F6\u5F55\u5165",void Ml.value.open(p,n,k,c.value.dateStr);var p,n,k}),class:"w-full",plain:"",style:pa(V.value?{"margin-top":"5px"}:null)},{default:d(()=>e[22]||(e[22]=[U(" \u51FA\u52E4\u5DE5\u65F6\u5F55\u5165 ")])),_:1},8,["style"])]),_:1}),a(r,{span:2,xs:12},{default:d(()=>[a(N,{onClick:e[11]||(e[11]=l=>{return p="\u5F02\u5E38\u5DE5\u65F6\u5F55\u5165",Gl.value=B(v.value.filter(n=>n.displayAbnormal===0)),void Hl.value.openForm(Gl,p,c.value.batchesId,c.value.dateStr,c.value.productionLine);var p}),class:"w-full",plain:"",style:pa(V.value?{"margin-top":"5px"}:null)},{default:d(()=>e[23]||(e[23]=[U(" \u5F02\u5E38\u5DE5\u65F6\u5F55\u5165 ")])),_:1},8,["style"])]),_:1})]),_:1})]),_:1}),a($,{shadow:"never",class:"custom-card"},co({default:d(()=>[V.value?(g(),C("div",Pe,[a(R,null,{default:d(()=>[a(r,{span:12},{default:d(()=>[a(m,{modelValue:x.value.productionOrderCode,"onUpdate:modelValue":e[15]||(e[15]=l=>x.value.productionOrderCode=l),size:"small",placeholder:"\u5DE5\u5355\u53F7\u7B5B\u9009"},null,8,["modelValue"])]),_:1}),a(r,{span:12},{default:d(()=>[a(m,{modelValue:x.value.salesOrderCode,"onUpdate:modelValue":e[16]||(e[16]=l=>x.value.salesOrderCode=l),size:"small",placeholder:"\u9500\u552E\u8BA2\u5355\u53F7\u7B5B\u9009"},null,8,["modelValue"])]),_:1})]),_:1}),(g(!0),C(j,null,oe(q.value,(l,p)=>(g(),C("div",{key:l.id||`row-${p}`,class:"card-item"},[i("div",{class:"card-title","v-loading":fe.value},[U("\u7B2C "+f(p+1)+" \u6761 / \u5171 "+f(q.value.length)+" \u6761 ",1),i("div",qe,[a(N,{style:{padding:"0",color:"#fff"},link:"",onClick:ea},{default:d(()=>[a(O,{icon:"ep:plus"})]),_:1}),ee.value==="update"?(g(),C("div",Fe,[l.isEditing?A("",!0):(g(),P(N,{key:0,style:{padding:"0",color:"#fff"},link:"",onClick:n=>(k=>{v.value[k].isEditing=!0})(p)},{default:d(()=>[a(O,{icon:"ep:edit"})]),_:2},1032,["onClick"]))])):A("",!0),a(N,{style:{padding:"0",color:"#fff"},link:"",onClick:n=>ta(p)},{default:d(()=>[a(O,{icon:"ep:delete"})]),_:2},1032,["onClick"])])],8,Be),l.isEditing||ee.value==="create"?(g(),P(xe,{key:0,class:"mobile-body-form"},{default:d(()=>[a(R,null,{default:d(()=>[a(r,{span:24},{default:d(()=>[a(s,{label:"\u65F6\u95F4\u6BB5"},{default:d(()=>[i("div",Ye,[a(E,{modelValue:l.productionTimeStart,"onUpdate:modelValue":n=>l.productionTimeStart=n,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),e[27]||(e[27]=i("span",{class:"time-range-separator"},"-",-1)),a(E,{modelValue:l.productionTimeEnd,"onUpdate:modelValue":n=>l.productionTimeEnd=n,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":l.productionTimeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:2},1024)]),_:2},1024),a(r,{span:24},{default:d(()=>[a(s,{label:"\u5DE5\u5355\u53F7"},{default:d(()=>[a(Z,{modelValue:l.productionOrderCode,"onUpdate:modelValue":n=>l.productionOrderCode=n,"fetch-suggestions":(n,k)=>T(ie)(n,k,0,p),placeholder:"\u8F93\u5165\u5DE5\u5355\u53F7",style:{width:"100%"},onSelect:n=>Jl(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",ze,[i("div",He,f(n.productionOrderCode)+"-"+f(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])]),_:2},1024)]),_:2},1024)]),_:2},1024),a(R,null,{default:d(()=>[a(r,{span:24},{default:d(()=>[a(s,{label:"\u8BA2\u5355\u53F7"},{default:d(()=>[a(Z,{modelValue:l.salesOrderCode,"onUpdate:modelValue":n=>l.salesOrderCode=n,"fetch-suggestions":(n,k)=>T(ie)(n,k,1,p),placeholder:"\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7",style:{width:"100%"},onSelect:n=>me(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",Me,[i("div",je,f(n.salesOrderCode)+"-"+f(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])]),_:2},1024)]),_:2},1024),a(r,{span:24},{default:d(()=>[a(s,{label:"\u54C1\u53F7"},{default:d(()=>[a(Z,{modelValue:l.productNo,"onUpdate:modelValue":n=>l.productNo=n,"fetch-suggestions":(n,k)=>jl(n,k,l.productionOrderCode,l.salesOrderCode),placeholder:"\u8F93\u5165\u54C1\u53F7\u67E5\u8BE2",style:{width:"100%"},onSelect:n=>me(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",Je,[i("div",Ke,f(n.productNo)+"-"+f(n.workOrderNum)+"-"+f(n.lineNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"]),a(m,{modelValue:l.productNo,"onUpdate:modelValue":n=>l.productNo=n,placeholder:"\u54C1\u53F7"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),a(r,{span:24},{default:d(()=>[a(s,{label:"\u54C1\u540D"},{default:d(()=>[a(m,{modelValue:l.modelsOrColor,"onUpdate:modelValue":n=>l.modelsOrColor=n,placeholder:"\u673A\u578B/\u989C\u8272",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),a(r,{span:24},{default:d(()=>[A("",!0)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u6570\u91CF"},{default:d(()=>[a(m,{modelValue:l.workOrderNum,"onUpdate:modelValue":n=>l.workOrderNum=n,placeholder:"\u6570\u91CF",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u4EBA\u6570"},{default:d(()=>[a(m,{modelValue:l.number,"onUpdate:modelValue":n=>l.number=n,placeholder:"\u4EBA\u6570",type:"number",onInput:n=>l.number=Number(n)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u751F\u4EA7\u6570"},{default:d(()=>[a(m,{modelValue:l.hoursReportNum,"onUpdate:modelValue":n=>l.hoursReportNum=n,placeholder:"\u751F\u4EA7\u6570\u91CF",type:"number",max:l.workOrderNum,onInput:n=>da(n,l)},null,8,["modelValue","onUpdate:modelValue","max","onInput"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u7D2F\u8BA1\u6570"},{default:d(()=>[a(m,{modelValue:l.totalReportNum,"onUpdate:modelValue":n=>l.totalReportNum=n,placeholder:"\u7D2F\u8BA1\u6570",readonly:!0,type:"number",min:0},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u5DE5\u5E8F"},{default:d(()=>[a(u,{modelValue:l.type,"onUpdate:modelValue":n=>l.type=n,placeholder:"\u8BF7\u9009\u62E9\u5DE5\u5E8F",style:{width:"100%"},onChange:n=>ke(n,l)},{default:d(()=>[(g(!0),C(j,null,oe(T(ue)(T(se).PRODUCTION_REPORT_TYPE),n=>(g(),P(t,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u6807\u51C6\u5DE5\u65F6"},{default:d(()=>[a(m,{modelValue:l.standardWork,"onUpdate:modelValue":n=>l.standardWork=n,type:"number",min:"0",onInput:n=>l.standardWork=Number(n)},{append:d(()=>e[28]||(e[28]=[U("\u5206")])),_:2},1032,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024),a(r,{span:24},{default:d(()=>[a(s,{label:"\u5907\u6CE8"},{default:d(()=>[a(m,{modelValue:l.remark,"onUpdate:modelValue":n=>l.remark=n,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),l.pmcRemark?(g(),P(r,{key:0,span:24},{default:d(()=>[a(eo,{effect:"dark",content:l.pmcRemark,placement:"top"},{default:d(()=>[a(s,{label:"PMC\u5907\u6CE8"},{default:d(()=>[U(f(l.pmcRemark),1)]),_:2},1024)]),_:2},1032,["content"])]),_:2},1024)):A("",!0),a(r,{span:12},{default:d(()=>[a(s,{label:"\u8FD4\u5DE5"},{default:d(()=>[a(ye,{"model-value":l.isRework===1,"onUpdate:modelValue":n=>la(n,l),align:"center"},null,8,["model-value","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),a(r,{span:12},{default:d(()=>[a(s,{label:"\u8BD5\u4EA7"},{default:d(()=>[a(ye,{"model-value":l.isTrial===1,"onUpdate:modelValue":n=>aa(n,l),align:"center"},null,8,["model-value","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),l.abnormalWork?(g(),P(r,{key:1,span:12},{default:d(()=>[a(s,{label:"\u5F02\u5E38\u5DE5\u65F6"},{default:d(()=>[a(m,{modelValue:l.abnormalWork,"onUpdate:modelValue":n=>l.abnormalWork=n,readonly:!0},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)):A("",!0)]),_:2},1024),a(R,{gutter:10},{default:d(()=>[a(r,{span:24},{default:d(()=>[a(N,{type:"success",plain:"",onClick:ra,size:"small",class:"w-full"},{default:d(()=>e[29]||(e[29]=[U(" \u5FEB\u901F\u5F55\u5165 ")])),_:1})]),_:1})]),_:1})]),_:2},1024)):(g(),C("div",Ze,[i("div",Ge,[e[30]||(e[30]=i("span",{class:"info-label"},"\u65F6\u95F4\u6BB5:",-1)),i("span",Xe,f(l.productionTimeStart)+" - "+f(l.productionTimeEnd),1)]),i("div",Qe,[e[31]||(e[31]=i("span",{class:"info-label"},"\u5DE5\u5355\u53F7:",-1)),i("span",el,f(l.productionOrderCode),1)]),i("div",ll,[e[32]||(e[32]=i("span",{class:"info-label"},"\u8BA2\u5355\u53F7:",-1)),i("span",al,f(l.salesOrderCode),1)]),i("div",ol,[e[33]||(e[33]=i("span",{class:"info-label"},"\u54C1\u53F7:",-1)),i("span",tl,f(l.productNo),1)]),i("div",rl,[e[34]||(e[34]=i("span",{class:"info-label"},"\u54C1\u540D:",-1)),i("span",dl,f(l.modelsOrColor),1)]),i("div",nl,[e[35]||(e[35]=i("span",{class:"info-label"},"\u6570\u91CF:",-1)),i("span",ul,f(l.workOrderNum),1),e[36]||(e[36]=i("span",{class:"info-label"},"\u4EBA\u6570:",-1)),i("span",sl,f(l.number),1)]),i("div",il,[e[37]||(e[37]=i("span",{class:"info-label"},"\u751F\u4EA7\u6570:",-1)),i("span",ml,f(l.hoursReportNum),1),e[38]||(e[38]=i("span",{class:"info-label"},"\u7D2F\u8BA1\u6570:",-1)),i("span",pl,f(l.totalReportNum),1)]),i("div",cl,[e[39]||(e[39]=i("span",{class:"info-label"},"\u5DE5\u5E8F:",-1)),i("span",vl,f(Pa(T(se).PRODUCTION_REPORT_TYPE,l.type)),1),e[40]||(e[40]=i("span",{class:"info-label"},"\u6807\u51C6\u5DE5\u65F6:",-1)),i("span",fl,f(l.standardWork||0)+" \u5206",1)]),l.remark?(g(),C("div",bl,[e[41]||(e[41]=i("span",{class:"info-label"},"\u5907\u6CE8:",-1)),i("span",hl,f(l.remark),1)])):A("",!0),l.pmcRemark?(g(),C("div",yl,[e[42]||(e[42]=i("span",{class:"info-label"},"PMC\u5907\u6CE8:",-1)),i("span",gl,f(l.pmcRemark),1)])):A("",!0),i("div",wl,[e[43]||(e[43]=i("span",{class:"info-label"},"\u8FD4\u5DE5:",-1)),i("span",Nl,f(l.isRework===1?"\u662F":"\u5426"),1),e[44]||(e[44]=i("span",{class:"info-label"},"\u8BD5\u4EA7:",-1)),i("span",kl,f(l.isTrial===1?"\u662F":"\u5426"),1)]),l.abnormalWork?(g(),C("div",Vl,[e[45]||(e[45]=i("span",{class:"info-label"},"\u5F02\u5E38\u5DE5\u65F6:",-1)),i("span",Ol,f(l.abnormalWork),1)])):A("",!0)]))]))),128))])):(g(),P(ua,{key:1,data:q.value,border:"",stripe:"","scrollbar-always-on":"",onRowContextmenu:Ta,onRowClick:Ua,ref_key:"tableRef",ref:Ca,style:{"min-height":"250px",height:"calc(100vh - 455px)"},"virtual-scrolling":!0,height:"calc(100vh - 455px)","row-key":"id",lazy:""},{default:d(()=>[a(w,{label:"\u5E8F\u53F7",type:"index",width:"60",align:"center"}),a(w,{label:"\u65F6\u95F4\u6BB5","min-width":"180",align:"center"},{default:d(({row:l})=>[i("div",Cl,[a(E,{modelValue:l.productionTimeStart,"onUpdate:modelValue":p=>l.productionTimeStart=p,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),e[46]||(e[46]=i("span",{class:"time-range-separator"},"-",-1)),a(E,{modelValue:l.productionTimeEnd,"onUpdate:modelValue":p=>l.productionTimeEnd=p,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:15","min-time":l.productionTimeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:1}),a(w,{label:"\u5DE5\u5E8F","min-width":"80",prop:"type",align:"center"},{default:d(({row:l})=>[a(u,{modelValue:l.type,"onUpdate:modelValue":p=>l.type=p,placeholder:"\u8BF7\u9009\u62E9\u5DE5\u5E8F",style:{width:"100%"},onChange:p=>ke(p,l)},{default:d(()=>[(g(!0),C(j,null,oe(T(ue)(T(se).PRODUCTION_REPORT_TYPE),p=>(g(),P(t,{key:p.value,label:p.label,value:p.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(w,{label:"\u8FD4\u5DE5","min-width":"30",prop:"isRework",align:"center"},{default:d(({row:l})=>[a(ye,{"model-value":l.isRework===1,"onUpdate:modelValue":p=>la(p,l),align:"center"},null,8,["model-value","onUpdate:modelValue"])]),_:1}),a(w,{label:"\u8BD5\u4EA7","min-width":"30",prop:"isTrial",align:"center"},{default:d(({row:l})=>[a(ye,{"model-value":l.isTrial===1,"onUpdate:modelValue":p=>aa(p,l),align:"center"},null,8,["model-value","onUpdate:modelValue"])]),_:1}),a(w,{label:"\u5DE5\u5355\u53F7","min-width":"160",prop:"productionOrderCode",align:"center"},{default:d(({row:l,$index:p})=>[a(Z,{modelValue:l.productionOrderCode,"onUpdate:modelValue":n=>l.productionOrderCode=n,onFocus:n=>l.isEditing=!0,onBlur:n=>l.isEditing=!1,"fetch-suggestions":(n,k)=>T(ie)(n,k,0,p),placeholder:"\u8F93\u5165\u5DE5\u5355\u53F7",style:{width:"100%"},onSelect:n=>Jl(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",_l,[i("div",Tl,f(n.productionOrderCode)+"-"+f(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","onFocus","onBlur","fetch-suggestions","onSelect"])]),_:1}),a(w,{label:"\u9500\u552E\u8BA2\u5355\u53F7","min-width":"160",prop:"salesOrderCode",align:"center"},{default:d(({row:l,$index:p})=>[a(Z,{modelValue:l.salesOrderCode,"onUpdate:modelValue":n=>l.salesOrderCode=n,onFocus:n=>l.isEditing=!0,onBlur:n=>l.isEditing=!1,"fetch-suggestions":(n,k)=>T(ie)(n,k,1,p),placeholder:"\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7",style:{width:"100%"},onSelect:n=>me(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",Rl,[i("div",Ul,f(n.salesOrderCode)+"-"+f(n.productNo),1)])]),_:2},1032,["modelValue","onUpdate:modelValue","onFocus","onBlur","fetch-suggestions","onSelect"])]),_:1}),a(w,{label:"\u54C1\u53F7","min-width":"110",prop:"productNo",align:"center"},{default:d(({row:l,$index:p})=>[a(Z,{onFocus:n=>l.isEditing=!0,onBlur:n=>l.isEditing=!1,modelValue:l.productNo,"onUpdate:modelValue":n=>l.productNo=n,"fetch-suggestions":(n,k)=>jl(n,k,l.productionOrderCode,l.salesOrderCode),placeholder:"\u8F93\u5165\u54C1\u53F7\u67E5\u8BE2",style:{width:"100%"},onSelect:n=>me(n,p),"trigger-on-focus":!1,debounce:300,"popper-class":"production-order-autocomplete",placement:"top-start"},{default:d(({item:n})=>[i("div",El,[i("div",xl,f(n.productNo)+"-"+f(n.workOrderNum)+"-"+f(n.lineNo),1)])]),_:2},1032,["onFocus","onBlur","modelValue","onUpdate:modelValue","fetch-suggestions","onSelect"])]),_:1}),a(w,{label:"\u673A\u578B/\u989C\u8272\uFF08\u54C1\u540D\uFF09","min-width":"120",prop:"modelsOrColor",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.modelsOrColor,"onUpdate:modelValue":p=>l.modelsOrColor=p,placeholder:"\u673A\u578B/\u989C\u8272",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),A("",!0),a(w,{label:"\u5DE5\u5355\u6570\u91CF","min-width":"70",prop:"workOrderNum",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.workOrderNum,"onUpdate:modelValue":p=>l.workOrderNum=p,placeholder:"\u5DE5\u5355\u6570\u91CF",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(w,{label:"\u5355\u4F4D","min-width":"40",prop:"units",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.units,"onUpdate:modelValue":p=>l.units=p,placeholder:"\u5355\u4F4D",readonly:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(w,{label:"\u6807\u51C6\u5DE5\u65F6","min-width":"70",prop:"standardWork",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.standardWork,"onUpdate:modelValue":p=>l.standardWork=p,placeholder:"\u6807\u51C6\u5DE5\u65F6",type:"number",min:"0",onInput:p=>l.standardWork=Number(p),class:"no-spin-input"},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),a(w,{label:"\u4EBA\u6570",width:"50",prop:"number",align:"center"},{default:d(({row:l})=>[a(m,{oninput:"value=value.replace(/^\\.+|[^\\d.]/g,'')",modelValue:l.number,"onUpdate:modelValue":p=>l.number=p,title:"\u4EBA\u6570",placeholder:"\u4EBA\u6570",type:"number",min:"0",onInput:p=>l.number=Number(p),class:"no-spin-input"},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),a(w,{label:"\u751F\u4EA7\u6570","min-width":"70",prop:"hoursReportNum",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.hoursReportNum,"onUpdate:modelValue":p=>l.hoursReportNum=p,placeholder:"\u751F\u4EA7\u6570",type:"number",min:0,max:l.workOrderNum,class:"no-spin-input",onInput:p=>da(p,l)},null,8,["modelValue","onUpdate:modelValue","max","onInput"])]),_:1}),a(w,{label:"\u7D2F\u8BA1\u6570","min-width":"70",prop:"totalReportNum",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.totalReportNum,"onUpdate:modelValue":p=>l.totalReportNum=p,placeholder:"\u7D2F\u8BA1\u6570",readonly:!0,type:"number",min:0},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(w,{label:"\u5907\u6CE8","min-width":"50",prop:"remark",align:"center"},{default:d(({row:l})=>[a(m,{modelValue:l.remark,"onUpdate:modelValue":p=>l.remark=p,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(w,{label:"PMC\u5907\u6CE8","min-width":"70",prop:"pmcRemark",align:"center"},{default:d(({row:l})=>[i("span",{style:{"white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis","max-height":"200px"},title:l.pmcRemark},f(l.pmcRemark),9,Sl)]),_:1}),a(w,{label:"\u64CD\u4F5C",width:"80",fixed:"right"},{default:d(({$index:l})=>[a(N,{type:"danger",link:"",onClick:p=>ta(l)},{default:d(()=>e[47]||(e[47]=[U(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),i("div",Wl,[i("ul",$l,[(g(!0),C(j,null,oe(Ne.value,(l,p)=>(g(),C(j,{key:p},[l.hidden?A("",!0):(g(),C("li",{key:0,onClick:vo(n=>(async k=>{const G=document.getElementById("menu");if(k===0){if(!J.value||!Y.value)return void h.error("\u8BF7\u5148\u53F3\u952E\u9009\u62E9\u4E00\u4E2A\u5355\u5143\u683C");const _=Y.value.property,I=Y.value.label;if(!_||_==="productionOrderCode"||_==="salesOrderCode"||_==="modelsOrColor"||_==="productNo"||_==="workOrderNum"||_==="units")return h.error("\u8BE5\u5217\u65E0\u6CD5\u6279\u91CF\u586B\u5199"),void(document.getElementById("menu").style.display="none");const D=J.value[_];h.confirm(`\u662F\u5426\u8981\u5C06 '${I}' \u5217\u5168\u90E8\u8D4B\u503C '${D}' \u5417\uFF1F`).then(()=>{v.value.forEach(We=>{We[_]=Number(D)}),h.success("\u64CD\u4F5C\u6210\u529F")}).catch(()=>{h.info("\u64CD\u4F5C\u5DF2\u53D6\u6D88")})}else if(k===1)if(ee.value==="update")h.error("\u8BE5\u64CD\u4F5C\u4EC5\u9650\u65B0\u589E\u6570\u636E\u65F6\u4F7F\u7528"),G.style.display="none";else{for(let I=0;I<v.value.length;I++){const D=v.value[I];if(!D.productionOrderCode&&!D.salesOrderCode)return h.error(`\u7B2C${I+1}\u884C\uFF1A\u5DE5\u5355\u53F7\u6216\u9500\u552E\u8BA2\u5355\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8865\u5145\uFF01`),document.getElementById("menu").style.display="none",!1;if(!D.productNo)return h.error(`\u7B2C${I+1}\u884C\uFF1A\u54C1\u53F7\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u8865\u5145\uFF01`),document.getElementById("menu").style.display="none",!1}const _=await W.generated(v.value);v.value.map((I,D)=>{I.hoursReportNum=_[D]})}const Se=Ne.value[k];if(Se&&!Se.hidden){switch(Se.operType){case 3:if(!J.value||!Y.value)return h.error("\u8BF7\u5148\u53F3\u952E\u9009\u62E9\u4E00\u4E2A\u5355\u5143\u683C"),void(G.style.display="none");const _=Y.value.property;if(!_||["modelsOrColor","workOrderNum","units"].includes(_))return h.error("\u8BE5\u5217\u65E0\u6CD5\u590D\u5236"),void(G.style.display="none");const I=J.value[_];we.value={field:_,value:I},h.success("\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F");break;case 4:if(!we.value)return h.warning("\u8BF7\u5148\u590D\u5236\u4E00\u4E2A\u5355\u5143\u683C\u5185\u5BB9"),void(G.style.display="none");if(!J.value||!Y.value)return h.error("\u8BF7\u5148\u53F3\u952E\u9009\u62E9\u4E00\u4E2A\u76EE\u6807\u5355\u5143\u683C"),void(G.style.display="none");const D=Y.value.property,We=J.value;if(["modelsOrColor","workOrderNum","units"].includes(D))return h.error("\u6B64\u5217\u4E0D\u5141\u8BB8\u7C98\u8D34\u5185\u5BB9"),void(G.style.display="none");We[D]=we.value.value,h.success("\u7C98\u8D34\u6210\u529F")}G.style.display="none"}})(p),["stop"])},f(l.name),9,Il))],64))),128))])])]),_:2},[V.value?void 0:{name:"header",fn:d(()=>[i("div",Ae,[i("div",Le,[a(N,{type:"primary",onClick:ea,size:"small",plain:""},{default:d(()=>[a(S,{icon:"ep:plus",style:{"margin-right":"4px"}}),e[24]||(e[24]=U(" \u65B0\u589E\u884C "))]),_:1}),a(N,{type:"success",onClick:ra,size:"small",plain:""},{default:d(()=>[a(S,{icon:"ep:lightning",style:{"margin-right":"4px"}}),e[25]||(e[25]=U(" \u5FEB\u901F\u5F55\u5165 "))]),_:1}),a(m,{modelValue:x.value.productionOrderCode,"onUpdate:modelValue":e[12]||(e[12]=l=>x.value.productionOrderCode=l),size:"small",placeholder:"\u5DE5\u5355\u53F7\u7B5B\u9009"},null,8,["modelValue"]),a(m,{modelValue:x.value.salesOrderCode,"onUpdate:modelValue":e[13]||(e[13]=l=>x.value.salesOrderCode=l),size:"small",placeholder:"\u9500\u552E\u8BA2\u5355\u53F7\u7B5B\u9009"},null,8,["modelValue"]),a(m,{modelValue:x.value.productNo,"onUpdate:modelValue":e[14]||(e[14]=l=>x.value.productNo=l),size:"small",placeholder:"\u54C1\u53F7\u7B5B\u9009"},null,8,["modelValue"]),a(N,{type:"primary",onClick:Zl,size:"small"},{default:d(()=>e[26]||(e[26]=[U(" \u91CD\u7F6E ")])),_:1})])])]),key:"0"}]),1024)]),_:1},8,["model","rules"])),[[lo,Q.value]])]),_:1},8,["title","modelValue"])],512),[[po,X.value]]),a(T(ka),{ref_key:"formRefAbnormal",ref:Hl,onSuccess:Ba},null,512),a(T(Va),{ref_key:"formRefAttendance",ref:Ml,onSuccess:qa},null,512),a(sa,{title:"\u5FEB\u901F\u5F55\u5165",modelValue:te.value,"onUpdate:modelValue":e[20]||(e[20]=l=>te.value=l),width:V.value?"100%":"800px",draggable:!0},{footer:d(()=>[a(N,{onClick:e[19]||(e[19]=l=>te.value=!1)},{default:d(()=>e[51]||(e[51]=[U("\u53D6\u6D88")])),_:1}),a(N,{type:"primary",onClick:Ga,disabled:L.value.length===0},{default:d(()=>[U(" \u6DFB\u52A0\u9009\u4E2D\u9879 ("+f(L.value.length)+") ",1)]),_:1},8,["disabled"])]),default:d(()=>[i("div",Dl,[i("div",Al,[a(xe,{inline:!0},{default:d(()=>[a(s,{label:"\u9500\u552E\u8BA2\u5355\u53F7:"},{default:d(()=>[a(Z,{modelValue:K.value,"onUpdate:modelValue":e[18]||(e[18]=l=>K.value=l),"fetch-suggestions":ja,placeholder:"\u8BF7\u8F93\u5165\u9500\u552E\u8BA2\u5355\u53F7",style:{"min-width":"300px"},onSelect:Ja,"trigger-on-focus":!1,debounce:300,"popper-class":"sales-order-autocomplete",placement:"bottom-start",clearable:""},{default:d(({item:l})=>[i("div",Ll,[i("div",Pl,f(l.salesOrderCode),1)])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),z.value.length>0?(g(),C("div",Bl,[i("div",ql,[i("span",Fl,"\u5171 "+f(z.value.length)+" \u6761\u6570\u636E",1)]),a(ua,{ref_key:"quickEntryTableRef",ref:Ue,data:z.value,border:"",size:"small","min-height":"400",onSelectionChange:Za},{default:d(()=>[a(w,{type:"selection","min-width":"20",align:"center"}),a(w,{prop:"salesOrderCode",label:"\u9500\u552E\u8BA2\u5355\u53F7","min-width":"120"}),a(w,{prop:"productNo",label:"\u54C1\u53F7","min-width":"80"}),a(w,{prop:"modelsOrColor",label:"\u578B\u53F7/\u989C\u8272","min-width":"120"}),a(w,{prop:"workOrderNum",label:"\u6570\u91CF","min-width":"40",align:"center"}),a(w,{prop:"lineNo",label:"\u884C\u53F7","min-width":"40",align:"center"}),a(w,{prop:"units",label:"\u5355\u4F4D","min-width":"50",align:"center"})]),_:1},8,["data"])])):!Re.value&&K.value?(g(),C("div",Yl,[a(S,{icon:"ep:document",size:"48",class:"mb-2"}),e[50]||(e[50]=i("div",null,"\u6682\u65E0\u6570\u636E",-1))])):A("",!0)])]),_:1},8,["modelValue","width"])],64)}}}),[["__scopeId","data-v-2a0932a0"]])});export{Io as __tla,ga as default};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/AbnormalForm-74fZUt7u.js","assets/index-C8b06LRn.js","assets/index-CU8_V1HE.css","assets/Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js","assets/Dialog-BdewL7YE.css","assets/el-card-CaOo8U9P.js","assets/el-card-BJ3sbP9B.css","assets/el-time-select-BrN8x4_E.js","assets/el-time-select-r2HVE9zR.css","assets/index-SUTJ2GLg.js","assets/AbnormalForm-DoYJ4SNT.css","assets/AttendanceForm-BqRyoilH.js","assets/TimeRangeWorkHours-CNSnLb0u.js","assets/TimeRangeWorkHours-DNhUzFEd.css","assets/AttendanceForm-vs6Fh4Eb.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
