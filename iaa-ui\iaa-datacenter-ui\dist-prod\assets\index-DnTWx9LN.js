import{u as R,_ as A,a as M}from"./useTable-5F7-nNM-.js";import{_ as j}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as w,j as P,f as T,T as U,o as n,c as q,k as o,w as r,u as e,x as p,h as c,v as u,F as D,G as F,H as G}from"./index-C8b06LRn.js";import{_ as H}from"./index-CkzUfjB7.js";import{a as v}from"./account.data-D0GCpo8Z.js";import{b as O,d as B}from"./useCrudSchemas-HzMMRa-v.js";import{_ as E}from"./MailAccountForm.vue_vue_type_script_setup_true_lang-B2d925wQ.js";import{_ as I}from"./MailAccountDetail.vue_vue_type_script_setup_true_lang-BJnGLu6H.js";import"./Form-CkLzRm65.js";import"./el-virtual-list-BIjfPDZX.js";import"./el-tree-select-E9FCZb0j.js";import"./el-time-select-BrN8x4_E.js";import"./InputPassword-CcRd7dRE.js";import"./index-Cl43piKd.js";import"./download-D5Lb_h0f.js";import"./el-card-CaOo8U9P.js";import"./formatTime-COZ9Bl52.js";import"./formRules-Upspu04w.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./Descriptions-iRMIdIt1.js";import"./el-descriptions-item-Ctb8GMnZ.js";const J=w({name:"SystemMailAccount",__name:"index",setup(K){const{tableObject:i,tableMethods:d}=R({getListApi:O,delListApi:B}),{getList:f,setSearchParams:g}=d,_=P(),y=(S,a)=>{_.value.open(S,a)},k=P();return T(()=>{f()}),(S,a)=>{const b=H,z=F,l=G,L=A,h=j,x=M,m=U("hasPermi");return n(),q(D,null,[o(b,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),o(h,null,{default:r(()=>[o(L,{schema:e(v).searchSchema,onSearch:e(g),onReset:e(g)},{actionMore:r(()=>[p((n(),c(l,{type:"primary",plain:"",onClick:a[0]||(a[0]=t=>y("create"))},{default:r(()=>[o(z,{icon:"ep:plus",class:"mr-5px"}),a[3]||(a[3]=u(" \u65B0\u589E "))]),_:1})),[[m,["system:mail-account:create"]]])]),_:1},8,["schema","onSearch","onReset"])]),_:1}),o(h,null,{default:r(()=>[o(x,{columns:e(v).tableColumns,data:e(i).tableList,loading:e(i).loading,pagination:{total:e(i).total},pageSize:e(i).pageSize,"onUpdate:pageSize":a[1]||(a[1]=t=>e(i).pageSize=t),currentPage:e(i).currentPage,"onUpdate:currentPage":a[2]||(a[2]=t=>e(i).currentPage=t)},{action:r(({row:t})=>[p((n(),c(l,{link:"",type:"primary",onClick:C=>y("update",t.id)},{default:r(()=>a[4]||(a[4]=[u(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[m,["system:mail-account:update"]]]),p((n(),c(l,{link:"",type:"primary",onClick:C=>{return s=t.id,void k.value.open(s);var s}},{default:r(()=>a[5]||(a[5]=[u(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])),[[m,["system:mail-account:query"]]]),p((n(),c(l,{link:"",type:"danger",onClick:C=>{return s=t.id,void d.delList(s,!1);var s}},{default:r(()=>a[6]||(a[6]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[m,["system:mail-account:delete"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),o(E,{ref_key:"formRef",ref:_,onSuccess:e(f)},null,8,["onSuccess"]),o(I,{ref_key:"detailRef",ref:k},null,512)],64)}}});export{J as default};
