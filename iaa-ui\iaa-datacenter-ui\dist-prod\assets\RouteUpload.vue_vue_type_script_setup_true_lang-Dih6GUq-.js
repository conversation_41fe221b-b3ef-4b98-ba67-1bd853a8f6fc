import{_ as I}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as A,y as B,j as u,o as F,h as G,w as t,k as n,u as d,v as p,m as y,l as m,dP as D,aV as E,aO as H,G as L,aa as O,bn as P,H as T}from"./index-C8b06LRn.js";import{d as q}from"./download-D5Lb_h0f.js";import{R as J}from"./index-BqCcY1XT.js";const K={class:"el-upload__tip text-center"},M=A({__name:"RouteUpload",emits:["success"],setup(N,{expose:h,emit:b}){const o=B(),l=u(!1),r=u([]),s=u(),x=u(),g=b,v=u();h({open:()=>{l.value=!0,r.value=[],U()}});const k=()=>{o.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),s.value=!1},V=()=>{o.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},w=async()=>{r.value.length!=0?(x.value={Authorization:"Bearer "+D(),"tenant-id":E()},s.value=!0,v.value.submit()):o.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6")},C=a=>{if(a.code!==0)return o.error(a.msg),void(s.value=!1);const e=a.data;let i="\u4E0A\u4F20\u6210\u529F\u6570\u91CF\uFF1A"+e.success+";";i+="\u66F4\u65B0\u5931\u8D25\u6570\u91CF\uFF1A"+e.errorItem.length+";";for(const f of e.errorItem)i+="["+f+`]
`;o.alert(i),s.value=!1,l.value=!1,g("success")},R=async()=>{const a=await J.exportTemplate();q.excel(a,"\u5DE5\u827A\u8DEF\u7EBF\u7ED1\u5B9A\u5BFC\u5165\u6A21\u677F.xlsx")},U=async()=>{var a;s.value=!1,await H(),(a=v.value)==null||a.clearFiles()};return(a,e)=>{const i=L,f=O,j=P,_=T,z=I;return F(),G(z,{modelValue:d(l),"onUpdate:modelValue":e[2]||(e[2]=c=>y(l)?l.value=c:null),title:"\u5BFC\u5165\u5DE5\u827A\u8DEF\u7EBF"},{footer:t(()=>[n(_,{disabled:d(s),type:"primary",onClick:w},{default:t(()=>e[6]||(e[6]=[p("\u786E \u5B9A")])),_:1},8,["disabled"]),n(_,{onClick:e[1]||(e[1]=c=>l.value=!1)},{default:t(()=>e[7]||(e[7]=[p("\u53D6 \u6D88")])),_:1})]),default:t(()=>[n(j,{ref_key:"uploadRef",ref:v,"file-list":d(r),"onUpdate:fileList":e[0]||(e[0]=c=>y(r)?r.value=c:null),action:"https://sj.iaa360.cn:13141/admin-api/report/technology-route/import","auto-upload":!1,disabled:d(s),headers:d(x),limit:1,"on-error":k,"on-exceed":V,"on-success":C,accept:".xlsx, .xls",drag:""},{tip:t(()=>[m("div",K,[e[4]||(e[4]=m("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),n(f,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:R},{default:t(()=>e[3]||(e[3]=[p(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:t(()=>[n(i,{icon:"ep:upload"}),e[5]||(e[5]=m("div",{class:"el-upload__text"},[p("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),m("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","disabled","headers"])]),_:1},8,["modelValue"])}}});export{M as _};
