import{aG as u,p as P,r as D,ef as w,eg as E,eh as Y,cG as S,ei as T,ch as F,N as I,s as A,bv as G}from"./index-C8b06LRn.js";import{_ as H}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";const M=async s=>await u.get({url:"/system/mail-account/page",params:s}),N=async s=>await u.get({url:"/system/mail-account/get?id="+s}),x=async s=>await u.post({url:"/system/mail-account/create",data:s}),_=async s=>await u.put({url:"/system/mail-account/update",data:s}),j=async s=>await u.delete({url:"/system/mail-account/delete?id="+s}),k=async()=>u.get({url:"/system/mail-account/simple-list"}),{t:v}=P(),q=s=>{const o=D({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),t=z(s,o);o.searchSchema=t||[];const l=B(s);o.tableColumns=l||[];const a=J(s,o);o.formSchema=a;const i=K(s);return o.detailSchema=i,{allSchemas:o}},z=(s,o)=>{const t=[],l=[];w(s,a=>{var i,m,d,e;if(a!=null&&a.isSearch||(i=a.search)!=null&&i.show){let b=((m=a==null?void 0:a.search)==null?void 0:m.component)||"Input";const f=[];let h={};if(a.dictType){const n={label:"\u5168\u90E8",value:""};f.push(n),S(a.dictType).forEach(c=>{f.push(c)}),h={options:f},(d=a.search)!=null&&d.component||(b="Select")}const p=T({component:b,...a.search,field:a.field,label:((e=a.search)==null?void 0:e.label)||a.label},{componentProps:h});p.api&&l.push(async()=>{var c;const n=await p.api();if(n){const r=F(o.searchSchema,y=>y.field===p.field);r!==-1&&(o.searchSchema[r].componentProps.options=g(n,(c=p.componentProps.optionsAlias)==null?void 0:c.labelField))}}),delete p.show,t.push(p)}});for(const a of l)a();return t},B=s=>{const o=E(s,{conversion:t=>{var l;if((t==null?void 0:t.isTable)!==!1&&((l=t==null?void 0:t.table)==null?void 0:l.show)!==!1)return!t.formatter&&t.dictType&&(t.formatter=(a,i,m)=>G(H,{type:t.dictType,value:m})),{...t.table,...t}}});return Y(o,t=>(t.children===void 0&&delete t.children,!!t.field))},J=(s,o)=>{const t=[],l=[];w(s,a=>{var i,m,d,e,b;if((a==null?void 0:a.isForm)!==!1&&((i=a==null?void 0:a.form)==null?void 0:i.show)!==!1){let f=((m=a==null?void 0:a.form)==null?void 0:m.component)||"Input",h="";(d=a.form)!=null&&d.value?h=(e=a.form)==null?void 0:e.value:f==="InputNumber"&&(h=0);let p={};if(a.dictType){const c=[];a.dictClass&&a.dictClass==="number"?I(a.dictType).forEach(r=>{c.push(r)}):a.dictClass&&a.dictClass==="boolean"?A(a.dictType).forEach(r=>{c.push(r)}):S(a.dictType).forEach(r=>{c.push(r)}),p={options:c},a.form&&a.form.component||(f="Select")}const n=T({component:f,value:h,...a.form,field:a.field,label:((b=a.form)==null?void 0:b.label)||a.label},{componentProps:p});n.api&&l.push(async()=>{var r;const c=await n.api();if(c){const y=F(o.formSchema,C=>C.field===n.field);y!==-1&&(o.formSchema[y].componentProps.options=g(c,(r=n.componentProps.optionsAlias)==null?void 0:r.labelField))}}),delete n.show,t.push(n)}});for(const a of l)a();return t},K=s=>{const o=[];return w(s,t=>{var l,a,i,m,d;if((t==null?void 0:t.isDetail)!==!1&&((l=t.detail)==null?void 0:l.show)!==!1){const e={...t.detail,field:t.field,label:((a=t.detail)==null?void 0:a.label)||t.label};t.dictType&&(e.dictType=t.dictType),((i=t.detail)!=null&&i.dateFormat||t.formatter=="formatDate")&&(e.dateFormat=(m=t==null?void 0:t.detail)!=null&&m.dateFormat?(d=t==null?void 0:t.detail)==null?void 0:d.dateFormat:"YYYY-MM-DD HH:mm:ss"),delete e.show,o.push(e)}}),o},g=(s,o)=>s==null?void 0:s.map(t=>(o?t.labelField=v(t.labelField):t.label=v(t.label),t));export{_ as a,M as b,x as c,j as d,k as e,N as g,q as u};
