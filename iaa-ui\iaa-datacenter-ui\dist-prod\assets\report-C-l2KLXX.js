import{d as J,aj as p,cj as a,j as f,au as V,o as O,h as _,w as h,l as N,n as P,u as s,t as S,k as b,m as U,c as B,F as T,g as q,x as F,Z as G,v as z,ap as H,aq as I,C as Z,H as E,at as K,_ as L,aG as i}from"./index-C8b06LRn.js";const t="YYYY-MM-DD",Q=L(J({__name:"index",props:{modelValue:p.oneOfType([Array]).def([]),size:p.oneOf(["default","small"]).def("default"),selected:p.oneOf(["today","yesterday","thisWeek","lastWeek","thisMonth","lastMonth","last3Month","after3Month","last6Month","last1Year","thisYear","lastYear"]).def("today"),type:p.oneOf(["date","month"]).def("date"),hideRadio:p.oneOfType([Array]).def([])},emits:["update:modelValue","change"],setup(r,{expose:j,emit:C}){a.locale("zh-cn");const d=r,v=C,m=f(!1),n=f(),e=f([]),w=f(),g=f(),Y=u=>{u==2&&a(e.value[1]).isBefore(a(e.value[0]))?(w.value.focus(),e.value[0]=void 0):u==1&&a(e.value[0]).isAfter(a(e.value[1]))?(g.value.focus(),e.value[1]=void 0):n.value=""},k=u=>{const l=y.value.find(x=>x.value===u);e.value=[l.date.start,l.date.end]},R=u=>{k(u),m.value=!1,v("update:modelValue",e.value),v("change",e.value)},D=()=>{e.value[0]&&e.value[1]&&(a(e.value[0]).isBefore(e.value[1])||e[0]==e[1])&&(v("update:modelValue",e.value),v("change",e.value),m.value=!1)},y=f([{label:"\u4ECA\u5929",value:"today",date:{start:a().format(t),end:a().format(t)}},{label:"\u6628\u5929",value:"yesterday",date:{start:a().subtract(1,"days").format(t),end:a().subtract(1,"days").format(t)}},{label:"\u672C\u5468",value:"thisWeek",date:{start:a().startOf("weeks").format(t),end:a().endOf("weeks").format(t)}},{label:"\u4E0A\u5468",value:"lastWeek",date:{start:a().subtract(1,"weeks").startOf("weeks").format(t),end:a().subtract(1,"weeks").endOf("weeks").format(t)}},{label:"\u672C\u6708",value:"thisMonth",date:{start:a().startOf("months").format(t),end:a().endOf("months").format(t)}},{label:"\u4E0A\u6708",value:"lastMonth",date:{start:a().subtract(1,"months").startOf("months").format(t),end:a().subtract(1,"months").endOf("months").format(t)}},{label:"\u524D\u4E09\u6708",value:"last3Month",date:{start:a().subtract(3,"months").format(t),end:a().format(t)}},{label:"\u540E\u4E09\u6708",value:"after3Month",date:{start:a().format(t),end:a().add(3,"months").format(t)}},{label:"\u524D\u534A\u5E74",value:"last6Month",date:{start:a().subtract(6,"months").format(t),end:a().format(t)}},{label:"\u524D\u4E00\u5E74",value:"last1Year",date:{start:a().subtract(12,"months").format(t),end:a().format(t)}},{label:"\u672C\u5E74",value:"thisYear",date:{start:a().startOf("years").format(t),end:a().endOf("years").format(t)}},{label:"\u53BB\u5E74",value:"lastYear",date:{start:a().subtract(1,"years").startOf("years").format(t),end:a().subtract(1,"years").endOf("years").format(t)}}]);return j({hide:()=>{m.value=!1}}),V(()=>d.modelValue,u=>{e.value=u}),V(()=>d.selected,u=>{n.value=u,k(u)},{immediate:!0}),(u,l)=>{const x=H,W=I,M=Z,$=E,A=K;return O(),_(A,{placement:"bottom",trigger:"click",width:"300",visible:s(m)},{reference:h(()=>{var o;return[N("div",{class:P({"date-value":!0,"w-180px":d.size=="default","w-160px":d.size=="small","h-30px leading-30px":d.size=="default","h-22px leading-22px":d.size=="small","px-10px":!0,"text-12px":d.size=="small"}),onClick:l[0]||(l[0]=c=>m.value=!s(m))},S(s(n)?(o=s(y).find(c=>c.value===s(n)))==null?void 0:o.label:d.type==="date"?`${s(e)[0]}\u81F3${s(e)[1]}`:`${s(a)(s(e)[0]).format("YYYY-MM")}\u81F3${s(a)(s(e)[1]).format("YYYY-MM")}`),3)]}),default:h(()=>[b(W,{size:"small",modelValue:s(n),"onUpdate:modelValue":l[1]||(l[1]=o=>U(n)?n.value=o:null),onChange:R},{default:h(()=>[(O(!0),B(T,null,q(s(y),(o,c)=>F((O(),_(x,{key:c,label:o.label,value:o.value,class:"w-33% !mr-0"},null,8,["label","value"])),[[G,!d.hideRadio||!d.hideRadio.includes(o.value)]])),128))]),_:1},8,["modelValue"]),b(M,{type:d.type,ref_key:"startDateRef",ref:w,size:"small",class:"!w-120px",modelValue:s(e)[0],"onUpdate:modelValue":l[2]||(l[2]=o=>s(e)[0]=o),clearable:!1,"value-format":t,onChange:l[3]||(l[3]=o=>Y(1))},null,8,["type","modelValue"]),l[7]||(l[7]=z(" -- ")),b(M,{type:d.type,ref_key:"endDateRef",ref:g,size:"small",class:"!w-120px",modelValue:s(e)[1],"onUpdate:modelValue":l[4]||(l[4]=o=>s(e)[1]=o),clearable:!1,"value-format":t,onChange:l[5]||(l[5]=o=>Y(2))},null,8,["type","modelValue"]),b($,{class:"!mt-5px float-right",size:"small",type:"primary",onClick:D},{default:h(()=>l[6]||(l[6]=[z(" \u786E\u5B9A ")])),_:1})]),_:1},8,["visible"])}}}),[["__scopeId","data-v-1df0b52e"]]),X=async r=>await i.post({url:"/butt-joint/xiaoman/report/page-opportunity",data:r}),aa=r=>i.downloadPost({url:"/butt-joint/xiaoman/report/export-opportunity",data:JSON.stringify(r)}),ea=async r=>await i.post({url:"/butt-joint/xiaoman/report/page-customer-development",data:r}),ta=r=>i.downloadPost({url:"/butt-joint/xiaoman/report/export-customer-development",data:JSON.stringify(r)}),la=async r=>await i.post({url:"/butt-joint/xiaoman/report/page-order",data:r}),sa=r=>i.downloadPost({url:"/butt-joint/xiaoman/report/export-order",data:JSON.stringify(r)});export{Q as _,X as a,aa as b,la as c,sa as d,ta as e,ea as p};
