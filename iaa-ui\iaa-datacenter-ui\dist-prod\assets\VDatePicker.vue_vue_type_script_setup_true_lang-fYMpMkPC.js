import{d as k,j as S,Y as p,o as m,c as w,k as c,u,h as C,w as _,m as $,$ as g,cj as j,aF as r}from"./index-C8b06LRn.js";const x={class:"p-10px"},B=k({__name:"VDatePicker",props:{modelValue:{default:""},label:{default:"\u6536\u6B3E\u65E5\u671F"},placeholder:{default:"\u9009\u62E9\u65E5\u671F"},readonly:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(i,{emit:v}){const o=i,s=v,a=S(!1),f=p(()=>o.modelValue||""),h=p(()=>{if(!o.modelValue)return[];const e=j(o.modelValue);return[e.year().toString(),(e.month()+1).toString().padStart(2,"0"),e.date().toString().padStart(2,"0")]}),V=({selectedValues:e})=>{const[l,t,d]=e;s("update:modelValue",`${l}-${t}-${d}`),a.value=!1},y=()=>s("update:modelValue","");return(e,l)=>{const t=r("van-field"),d=r("van-date-picker"),b=r("van-popup");return m(),w("div",x,[c(t,{"model-value":u(f),label:e.label,"is-link":"",readonly:"",placeholder:e.placeholder,onClick:l[0]||(l[0]=n=>!e.readonly&&(a.value=!0)),clearable:!e.readonly,onClear:y},null,8,["model-value","label","placeholder","clearable"]),e.readonly?g("",!0):(m(),C(b,{key:0,show:u(a),"onUpdate:show":l[2]||(l[2]=n=>$(a)?a.value=n:null),round:"",position:"bottom"},{default:_(()=>[c(d,{"model-value":u(h),title:"\u9009\u62E9\u65E5\u671F",onConfirm:V,onCancel:l[1]||(l[1]=n=>a.value=!1)},null,8,["model-value"])]),_:1},8,["show"]))])}}});export{B as _};
