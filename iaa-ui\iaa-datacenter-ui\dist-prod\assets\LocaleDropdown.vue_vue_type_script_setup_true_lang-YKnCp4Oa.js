import{ae as o,af as v,ag as u,ah as x,ai as P,d as j,aj as O,ak as T,Y as m,h as p,w as e,n as L,u as g,X as z,al as A,o as _,k as f,c as F,g as I,v as M,t as R,F as V,G as $,am as G,an as N,__tla as X}from"./index-C8b06LRn.js";let h,w,Y=Promise.all([(()=>{try{return X}catch{}})()]).then(async()=>{let i;h="/assets/login-box-bg-CL6i7T2F.svg",i=()=>({changeLocale:async l=>{const t=o.global,n=await v(Object.assign({"../../locales/en.ts":()=>u(()=>import("./en-CXAHQ3pU.js"),__vite__mapDeps([])),"../../locales/zh-CN.ts":()=>u(()=>import("./zh-CN-C3N19O0m.js"),__vite__mapDeps([]))}),`../../locales/${l}.ts`);t.setLocaleMessage(l,n.default),(a=>{const c=x();o.mode==="legacy"?o.global.locale=a:o.global.locale.value=a,c.setCurrentLocale({lang:a}),P(a)})(l)}}),w=j({name:"LocaleDropdown",__name:"LocaleDropdown",props:{color:O.string.def("")},setup(l){const{getPrefixCls:t}=z(),n=t("locale-dropdown"),a=T(),c=m(()=>a.getLocaleMap),C=m(()=>a.getCurrentLocale),b=s=>{if(s===g(C).lang)return;window.location.reload(),a.setCurrentLocale({lang:s});const{changeLocale:d}=i();d(s)};return(s,d)=>{const k=$,y=G,D=N,E=A;return _(),p(E,{class:L(g(n)),trigger:"click",onCommand:b},{dropdown:e(()=>[f(D,null,{default:e(()=>[(_(!0),F(V,null,I(g(c),r=>(_(),p(y,{key:r.lang,command:r.lang},{default:e(()=>[M(R(r.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:e(()=>[f(k,{class:L([s.$attrs.class,"cursor-pointer !p-0"]),color:l.color,size:18,icon:"ion:language-sharp"},null,8,["class","color"])]),_:1},8,["class"])}}})});export{h as _,Y as __tla,w as a};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
