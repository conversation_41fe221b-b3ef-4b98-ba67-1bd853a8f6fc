import{d as b,e as z,Y as o,j as f,b3 as x,x as y,Z as h,h as C,u as N,o as S}from"./index-C8b06LRn.js";import{E as _}from"./index-Cl43piKd.js";const j=b({name:"Pagination",__name:"index",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pagerCount:{type:Number,default:document.body.clientWidth<992?5:7}},emits:["update:page","update:limit","pagination"],setup(t,{emit:g}){const m=z(),r=o(()=>m.currentSize),s=f(r.value==="small");x(()=>{s.value=r.value==="small"});const p=t,l=g,e=o({get:()=>p.page,set(a){l("update:page",a)}}),n=o({get:()=>p.limit,set(a){l("update:limit",a)}}),d=a=>{e.value*a>p.total&&(e.value=1),l("pagination",{page:e.value,limit:a})},c=a=>{l("pagination",{page:a,limit:n.value})};return(a,u)=>{const v=_;return y((S(),C(v,{"current-page":e.value,"onUpdate:currentPage":u[0]||(u[0]=i=>e.value=i),"page-size":n.value,"onUpdate:pageSize":u[1]||(u[1]=i=>n.value=i),background:!0,"page-sizes":[10,20,30,50,100,200,500],"pager-count":t.pagerCount,total:t.total,small:N(s),class:"float-right mb-15px mt-15px",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:c},null,8,["current-page","page-size","pager-count","total","small"])),[[h,t.total>0]])}}});export{j as _};
