<template>
  <div class="pdf-preview">
    <div 
      class="pdf-content"
      ref="contentRef"
      @wheel="handleWheel"
    >
      <div 
        class="pdf-wrapper"
        :style="pdfWrapperStyle"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      >
        <!-- 使用固定高分辨率渲染，通过CSS缩放控制显示 -->
        <div class="pdf-container" :style="pdfContainerStyle">
          <vue-pdf-embed
            v-if="state.source"
            annotation-layer
            text-layer
            :source="{
              cMapUrl: 'https://unpkg.com/pdfjs-dist/cmaps/',
              url: state.source,
            }"
            :page="state.pageNum"
            :rotation="state.rotation"
            :scale="state.fixedRenderScale"
            :disable-font-face="fontRendering.disableFontFace"
            class="vue-pdf-embed"
            @loaded="handlePdfLoaded"
          />
        </div>
      </div>
    </div>
    <div class="page_tool">
      <div class="page_tool-item" @click="lastPage">上一页</div>
      <div class="page_tool-item" @click="nextPage">下一页</div>
      <div class="page_tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page_tool-item" @click="pageZoomIn">放大</div>
      <div class="page_tool-item" @click="pageZoomOut">缩小</div>
      <div class="page_tool-item" @click="pageRotation">旋转</div>
      <div class="page_tool-item" @click="resetView">重置</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import axios from 'axios'
import VuePdfEmbed from 'vue-pdf-embed'
import 'vue-pdf-embed/dist/styles/annotationLayer.css'
import 'vue-pdf-embed/dist/styles/textLayer.css'

const props = defineProps({
  pdfUrl: String
})

const contentRef = ref<HTMLElement | null>(null)

// 获取设备像素比，用于高清渲染
const devicePixelRatio = window.devicePixelRatio || 1

const state = ref({
  source: undefined as any,
  pageNum: 1,
  displayScale: 1.5,  // 用户看到的缩放比例
  fixedRenderScale: 3 * devicePixelRatio,  // 固定的高分辨率渲染比例，不会频繁变化
  numPages: 0,
  rotation: 0,
  disableFontFace: false,  // 控制是否禁用字体
  enableWebGL: false       // 控制是否启用 WebGL 渲染
})

// 字体渲染配置
const fontRendering = ref({
  disableFontFace: true,    // 禁用字体面，强制使用 canvas 渲染
  enableWebGL: false,       // WebGL 可能有助于字体渲染
  noNativeText: false        // 不使用原生文本渲染
})

// 拖拽和位置状态
const viewState = ref({
  isDragging: false,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
  startOffsetX: 0,
  startOffsetY: 0
})

// PDF容器样式 - 控制高分辨率PDF的显示缩放
const pdfContainerStyle = computed(() => {
  // 计算从固定渲染比例到显示比例的缩放因子
  const baseScale = state.value.fixedRenderScale / devicePixelRatio
  const scaleRatio = state.value.displayScale / baseScale
  return {
    transform: `scale(${scaleRatio})`,
    transformOrigin: 'center center',
    transition: 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
    display: 'inline-block'
  }
})

// PDF包装器样式 - 控制位置
const pdfWrapperStyle = computed(() => ({
  transform: `translate(${viewState.value.offsetX}px, ${viewState.value.offsetY}px)`,
  transformOrigin: 'center center',
  transition: viewState.value.isDragging ? 'none' : 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
  display: 'inline-block',
  cursor: viewState.value.isDragging ? 'grabbing' : 'grab',
  willChange: 'transform'
}))

// 鼠标滚轮缩放（优化版）
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  const zoomSpeed = state.value.displayScale > 2 ? 0.05 : 0.1
  const delta = e.deltaY > 0 ? -zoomSpeed : zoomSpeed
  const newScale = Math.max(0.5, Math.min(5, state.value.displayScale + delta))
  state.value.displayScale = newScale
}

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键
  
  viewState.value.isDragging = true
  viewState.value.startX = e.clientX
  viewState.value.startY = e.clientY
  viewState.value.startOffsetX = viewState.value.offsetX
  viewState.value.startOffsetY = viewState.value.offsetY
  
  e.preventDefault()
}

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!viewState.value.isDragging) return

  e.preventDefault()

  const deltaX = e.clientX - viewState.value.startX
  const deltaY = e.clientY - viewState.value.startY

  // 直接使用像素偏移，不需要除以缩放比例
  viewState.value.offsetX = viewState.value.startOffsetX + deltaX
  viewState.value.offsetY = viewState.value.startOffsetY + deltaY
}

// 鼠标释放事件
const handleMouseUp = () => {
  viewState.value.isDragging = false
}

// 重置视图
const resetView = () => {
  state.value.displayScale = 1.5
  viewState.value.offsetX = 0
  viewState.value.offsetY = 0
}

const lastPage = () => {
  if (state.value.pageNum > 1) {
    state.value.pageNum -= 1
    resetView()
  }
}

const nextPage = () => {
  if (state.value.pageNum < state.value.numPages) {
    state.value.pageNum += 1
    resetView()
  }
}

const pageZoomOut = () => {
  state.value.displayScale = Math.max(0.5, state.value.displayScale - 0.2)
}

const pageZoomIn = () => {
  state.value.displayScale = Math.min(5, state.value.displayScale + 0.2)
}

const pageRotation = () => {
  if (state.value.rotation == 270) {
    state.value.rotation = 0
  } else {
    state.value.rotation += 90
  }
  resetView()
}

const handlePdfLoaded = (pdf: { numPages: number }) => {
  state.value.numPages = pdf.numPages
  resetView()
}

watch(
  () => props.pdfUrl,
  async () => {
    if (!props.pdfUrl) return
    try {
      const response = await axios.get(props.pdfUrl, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })
      state.value.source = URL.createObjectURL(blob)
    } catch (error) {
      console.error('加载PDF失败:', error)
    }
  },
  { immediate: true }
)

onUnmounted(() => {
  if (state.value.source) {
    URL.revokeObjectURL(state.value.source)
  }
})
</script>

<style lang="scss" scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background-color: #e9e9e9;
  overflow: hidden;
}

.pdf-content {
  height: calc(100% - 60px);
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}

.pdf-content:active {
  cursor: grabbing;
}

.pdf-wrapper {
  display: inline-block;
  will-change: transform;
}

.pdf-container {
  display: inline-block;
  will-change: transform;
  transform-origin: center center;
}

.vue-pdf-embed {
  text-align: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  transform-origin: center center;
  /* 优化渲染性能 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* 优化图像渲染质量 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  image-rendering: crisp-edges;
}

.page_tool {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 15px;
  display: flex;
  align-items: center;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  border-radius: 25px;
  z-index: 100;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.page_tool-item {
  padding: 8px 15px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.page_tool-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>