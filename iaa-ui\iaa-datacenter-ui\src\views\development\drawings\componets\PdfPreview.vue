<template>
  <div class="pdf-preview">
    <div
      class="pdf-content"
      ref="contentRef"
      @wheel="handleWheel"
    >
      <div
        class="pdf-wrapper"
        :style="pdfWrapperStyle"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      >
        <!-- 加载状态遮罩 -->
        <div v-if="state.isLoading" class="loading-overlay">
          <div class="loading-spinner"></div>
        </div>

        <!-- PDF渲染容器 -->
        <div class="pdf-render-container" :style="renderContainerStyle">
          <vue-pdf-embed
            v-if="state.source && !state.isLoading"
            annotation-layer
            text-layer
            :source="state.source"
            :page="state.pageNum"
            :rotation="state.rotation"
            :scale="state.currentRenderScale"
            class="vue-pdf-embed"
            @loaded="handlePdfLoaded"
            @rendering="handlePdfRendering"
            @rendered="handlePdfRendered"
          />
        </div>
      </div>
    </div>
    <div class="page_tool">
      <div class="page_tool-item" @click="lastPage">上一页</div>
      <div class="page_tool-item" @click="nextPage">下一页</div>
      <div class="page_tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page_tool-item" @click="pageZoomOut">放大</div>
      <div class="page_tool-item" @click="pageZoomIn">缩小</div>
      <div class="page_tool-item" @click="pageRotation">旋转</div>
      <div class="page_tool-item" @click="resetView">重置</div>
      <div class="page_tool-item" @click="toggleHighQuality">
        {{ state.highQuality ? '标准质量' : '高质量' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import axios from 'axios'
import VuePdfEmbed from 'vue-pdf-embed'
import 'vue-pdf-embed/dist/styles/annotationLayer.css'
import 'vue-pdf-embed/dist/styles/textLayer.css'

const props = defineProps({
  pdfUrl: String,
  // 允许父组件设置初始缩放比例
  initialScale: {
    type: Number,
    default: 3
  }
})

const contentRef = ref<HTMLElement | null>(null)

// 获取设备像素比，用于高清渲染
const devicePixelRatio = window.devicePixelRatio || 1

const state = ref({
  source: undefined as any,
  pageNum: 1,
  displayScale: props.initialScale,  // 显示缩放比例
  renderScale: props.initialScale * devicePixelRatio,  // 渲染缩放比例（考虑设备像素比）
  currentRenderScale: props.initialScale * devicePixelRatio,  // 当前实际渲染的缩放比例
  numPages: 0,
  rotation: 0,
  highQuality: false,  // 高质量渲染模式开关
  isLoading: false,    // PDF加载状态
  isRendering: false   // PDF渲染状态
})

// 拖拽和位置状态
const viewState = ref({
  isDragging: false,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
  startOffsetX: 0,
  startOffsetY: 0
})

// PDF渲染容器样式 - 控制高清渲染的缩放
const renderContainerStyle = computed(() => {
  const scaleRatio = state.value.currentRenderScale / state.value.displayScale
  return {
    transform: `scale(${1 / scaleRatio})`,
    transformOrigin: 'center center',
    display: 'inline-block',
    transition: state.value.isRendering ? 'none' : 'transform 0.1s ease-out'
  }
})

// PDF包装器样式 - 控制位置和显示缩放
const pdfWrapperStyle = computed(() => ({
  transform: `scale(${state.value.displayScale}) translate(${viewState.value.offsetX}px, ${viewState.value.offsetY}px)`,
  transformOrigin: 'center center',
  transition: viewState.value.isDragging ? 'none' : 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
  display: 'inline-block',
  cursor: viewState.value.isDragging ? 'grabbing' : 'grab',
  willChange: 'transform'
}))

// 防抖定时器
let renderScaleUpdateTimer: NodeJS.Timeout | null = null

// 更新渲染缩放比例（防抖）
const updateRenderScale = () => {
  if (renderScaleUpdateTimer) {
    clearTimeout(renderScaleUpdateTimer)
  }

  renderScaleUpdateTimer = setTimeout(() => {
    const newRenderScale = state.value.highQuality
      ? state.value.displayScale * devicePixelRatio * 1.5  // 高质量模式额外提升50%渲染分辨率
      : state.value.displayScale * devicePixelRatio

    // 只有当缩放比例变化超过阈值时才更新渲染
    const scaleThreshold = 0.1
    if (Math.abs(newRenderScale - state.value.currentRenderScale) > scaleThreshold) {
      state.value.isRendering = true
      state.value.renderScale = newRenderScale
      state.value.currentRenderScale = newRenderScale
    }
  }, 150) // 150ms防抖延迟
}

// 鼠标滚轮缩放（优化版）
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()

  const zoomSpeed = state.value.displayScale > 1.5 ? 0.05 : 0.1
  const delta = e.deltaY > 0 ? -zoomSpeed : zoomSpeed

  const newDisplayScale = Math.max(0.5, Math.min(5, state.value.displayScale + delta))

  // 立即更新显示缩放（视觉反馈）
  state.value.displayScale = newDisplayScale

  // 延迟更新渲染缩放（避免频繁重渲染）
  updateRenderScale()
}

// PDF渲染状态处理
const handlePdfRendering = () => {
  state.value.isRendering = true
}

const handlePdfRendered = () => {
  state.value.isRendering = false
}

// 切换高质量模式（优化版）
const toggleHighQuality = () => {
  state.value.highQuality = !state.value.highQuality
  state.value.isRendering = true

  // 立即更新渲染缩放比例
  const newRenderScale = state.value.highQuality
    ? state.value.displayScale * devicePixelRatio * 1.5
    : state.value.displayScale * devicePixelRatio

  state.value.renderScale = newRenderScale
  state.value.currentRenderScale = newRenderScale
}

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键
  
  viewState.value.isDragging = true
  viewState.value.startX = e.clientX
  viewState.value.startY = e.clientY
  viewState.value.startOffsetX = viewState.value.offsetX
  viewState.value.startOffsetY = viewState.value.offsetY
  
  e.preventDefault()
}

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!viewState.value.isDragging) return
  
  e.preventDefault()
  
  const deltaX = e.clientX - viewState.value.startX
  const deltaY = e.clientY - viewState.value.startY
  
  viewState.value.offsetX = viewState.value.startOffsetX + deltaX / state.value.displayScale
  viewState.value.offsetY = viewState.value.startOffsetY + deltaY / state.value.displayScale
}

// 鼠标释放事件
const handleMouseUp = () => {
  viewState.value.isDragging = false
}

// 重置视图（优化版）
const resetView = () => {
  state.value.displayScale = props.initialScale
  const newRenderScale = props.initialScale * devicePixelRatio
  state.value.renderScale = newRenderScale
  state.value.currentRenderScale = newRenderScale
  viewState.value.offsetX = 0
  viewState.value.offsetY = 0
  state.value.isRendering = true
}

const lastPage = () => {
  if (state.value.pageNum > 1) {
    state.value.pageNum -= 1
    state.value.isLoading = true
    resetView()
  }
}

const nextPage = () => {
  if (state.value.pageNum < state.value.numPages) {
    state.value.pageNum += 1
    state.value.isLoading = true
    resetView()
  }
}

const pageZoomOut = () => {
  const newDisplayScale = Math.max(0.5, state.value.displayScale - 0.1)
  state.value.displayScale = newDisplayScale
  updateRenderScale()
}

const pageZoomIn = () => {
  const newDisplayScale = Math.min(5, state.value.displayScale + 0.1)
  state.value.displayScale = newDisplayScale
  updateRenderScale()
}

const pageRotation = () => {
  state.value.rotation = (state.value.rotation + 90) % 360
  resetView()
}

const handlePdfLoaded = (pdf: { numPages: number }) => {
  state.value.numPages = pdf.numPages
  state.value.isLoading = false
  state.value.isRendering = false
  resetView()
}

watch(
  () => props.pdfUrl,
  async () => {
    if (!props.pdfUrl) return

    state.value.isLoading = true

    try {
      const response = await axios.get(props.pdfUrl, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })

      // 清理之前的URL对象
      if (state.value.source) {
        URL.revokeObjectURL(state.value.source)
      }

      state.value.source = URL.createObjectURL(blob)
    } catch (error) {
      console.error('加载PDF失败:', error)
      state.value.isLoading = false
    }
  },
  { immediate: true }
)

onUnmounted(() => {
  // 清理定时器
  if (renderScaleUpdateTimer) {
    clearTimeout(renderScaleUpdateTimer)
  }

  // 清理URL对象
  if (state.value.source) {
    URL.revokeObjectURL(state.value.source)
  }
})
</script>

<style lang="scss" scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background-color: #e9e9e9;
  overflow: hidden;
}

.pdf-content {
  height: calc(100% - 60px);
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  background-image: 
    linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pdf-content:active {
  cursor: grabbing;
}

.pdf-wrapper {
  position: relative;
  display: inline-block;
  will-change: transform;
}

.pdf-render-container {
  display: inline-block;
  will-change: transform;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vue-pdf-embed {
  text-align: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  /* 优化图像渲染 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  image-rendering: crisp-edges;
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.page_tool {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 15px;
  display: flex;
  align-items: center;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  border-radius: 25px;
  z-index: 100;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.page_tool-item {
  padding: 8px 15px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.page_tool-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
