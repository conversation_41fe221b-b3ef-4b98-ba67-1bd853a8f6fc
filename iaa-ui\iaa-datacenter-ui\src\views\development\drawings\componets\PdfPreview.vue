<template>
  <div class="pdf-preview">
    <div 
      class="pdf-content"
      ref="contentRef"
      @wheel="handleWheel"
    >
      <div 
        class="pdf-wrapper"
        :style="pdfWrapperStyle"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
      >
        <!-- 使用div包裹，通过调整像素比提升清晰度 -->
        <div :style="renderWrapperStyle">
          <vue-pdf-embed
            v-if="state.source"
            annotation-layer
            text-layer
            :source="state.source"
            :page="state.pageNum"
            :rotation="state.rotation"
            :scale="state.renderScale" 
            class="vue-pdf-embed"
            @loaded="handlePdfLoaded"
          />
        </div>
      </div>
    </div>
    <div class="page_tool">
      <div class="page_tool-item" @click="lastPage">上一页</div>
      <div class="page_tool-item" @click="nextPage">下一页</div>
      <div class="page_tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
      <div class="page_tool-item" @click="pageZoomOut">放大</div>
      <div class="page_tool-item" @click="pageZoomIn">缩小</div>
      <div class="page_tool-item" @click="pageRotation">旋转</div>
      <div class="page_tool-item" @click="resetView">重置</div>
      <div class="page_tool-item" @click="toggleHighQuality">
        {{ state.highQuality ? '标准质量' : '高质量' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import axios from 'axios'
import VuePdfEmbed from 'vue-pdf-embed'
import 'vue-pdf-embed/dist/styles/annotationLayer.css'
import 'vue-pdf-embed/dist/styles/textLayer.css'

const props = defineProps({
  pdfUrl: String,
  // 允许父组件设置初始缩放比例
  initialScale: {
    type: Number,
    default: 10
  }
})

const contentRef = ref<HTMLElement | null>(null)

// 获取设备像素比，用于高清渲染
const devicePixelRatio = window.devicePixelRatio || 1

const state = ref({
  source: undefined as any,
  pageNum: 1,
  displayScale: props.initialScale,  // 显示缩放比例
  renderScale: props.initialScale * devicePixelRatio,  // 渲染缩放比例（考虑设备像素比）
  numPages: 0,
  rotation: 0,
  highQuality: false  // 高质量渲染模式开关
})

// 拖拽和位置状态
const viewState = ref({
  isDragging: false,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
  startOffsetX: 0,
  startOffsetY: 0
})

// 渲染容器样式 - 控制实际显示大小
const renderWrapperStyle = computed(() => ({
  transform: `scale(${1 / devicePixelRatio})`,
  transformOrigin: 'center center',
  display: 'inline-block'
}))

// PDF包装器样式 - 控制位置和显示缩放
const pdfWrapperStyle = computed(() => ({
  transform: `scale(${state.value.displayScale}) translate(${viewState.value.offsetX}px, ${viewState.value.offsetY}px)`,
  transformOrigin: 'center center',
  transition: viewState.value.isDragging ? 'none' : 'transform 0.2s ease',
  display: 'inline-block',
  cursor: viewState.value.isDragging ? 'grabbing' : 'grab'
}))

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  const zoomSpeed = state.value.displayScale > 1.5 ? 0.05 : 0.1
  const delta = e.deltaY > 0 ? -zoomSpeed : zoomSpeed
  
  const newDisplayScale = Math.max(0.5, Math.min(5, state.value.displayScale + delta))
  const newRenderScale = state.value.highQuality 
    ? newDisplayScale * devicePixelRatio * 1.5  // 高质量模式额外提升50%渲染分辨率
    : newDisplayScale * devicePixelRatio
  
  state.value.displayScale = newDisplayScale
  state.value.renderScale = newRenderScale
}

// 切换高质量模式
const toggleHighQuality = () => {
  state.value.highQuality = !state.value.highQuality
  // 更新渲染缩放比例
  state.value.renderScale = state.value.highQuality
    ? state.value.displayScale * devicePixelRatio * 1.5
    : state.value.displayScale * devicePixelRatio
}

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent) => {
  if (e.button !== 0) return // 只处理左键
  
  viewState.value.isDragging = true
  viewState.value.startX = e.clientX
  viewState.value.startY = e.clientY
  viewState.value.startOffsetX = viewState.value.offsetX
  viewState.value.startOffsetY = viewState.value.offsetY
  
  e.preventDefault()
}

// 鼠标移动事件
const handleMouseMove = (e: MouseEvent) => {
  if (!viewState.value.isDragging) return
  
  e.preventDefault()
  
  const deltaX = e.clientX - viewState.value.startX
  const deltaY = e.clientY - viewState.value.startY
  
  viewState.value.offsetX = viewState.value.startOffsetX + deltaX / state.value.displayScale
  viewState.value.offsetY = viewState.value.startOffsetY + deltaY / state.value.displayScale
}

// 鼠标释放事件
const handleMouseUp = () => {
  viewState.value.isDragging = false
}

// 重置视图
const resetView = () => {
  state.value.displayScale = props.initialScale
  state.value.renderScale = props.initialScale * devicePixelRatio
  viewState.value.offsetX = 0
  viewState.value.offsetY = 0
}

const lastPage = () => {
  if (state.value.pageNum > 1) {
    state.value.pageNum -= 1
    resetView()
  }
}

const nextPage = () => {
  if (state.value.pageNum < state.value.numPages) {
    state.value.pageNum += 1
    resetView()
  }
}

const pageZoomOut = () => {
  const newDisplayScale = Math.max(0.5, state.value.displayScale - 0.1)
  const newRenderScale = state.value.highQuality
    ? newDisplayScale * devicePixelRatio * 1.5
    : newDisplayScale * devicePixelRatio
    
  state.value.displayScale = newDisplayScale
  state.value.renderScale = newRenderScale
}

const pageZoomIn = () => {
  const newDisplayScale = Math.min(5, state.value.displayScale + 0.1)
  const newRenderScale = state.value.highQuality
    ? newDisplayScale * devicePixelRatio * 1.5
    : newDisplayScale * devicePixelRatio
    
  state.value.displayScale = newDisplayScale
  state.value.renderScale = newRenderScale
}

const pageRotation = () => {
  state.value.rotation = (state.value.rotation + 90) % 360
  resetView()
}

const handlePdfLoaded = (pdf: { numPages: number }) => {
  state.value.numPages = pdf.numPages
  resetView()
}

watch(
  () => props.pdfUrl,
  async () => {
    if (!props.pdfUrl) return
    try {
      const response = await axios.get(props.pdfUrl, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })
      state.value.source = URL.createObjectURL(blob)
    } catch (error) {
      console.error('加载PDF失败:', error)
    }
  },
  { immediate: true }
)

onUnmounted(() => {
  if (state.value.source) {
    URL.revokeObjectURL(state.value.source)
  }
})
</script>

<style lang="scss" scoped>
.pdf-preview {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background-color: #e9e9e9;
  overflow: hidden;
}

.pdf-content {
  height: calc(100% - 60px);
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  background-image: 
    linear-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pdf-content:active {
  cursor: grabbing;
}

.pdf-wrapper {
  display: inline-block;
  will-change: transform;
}

.vue-pdf-embed {
  text-align: center;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  /* 启用图像平滑 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.page_tool {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 15px;
  display: flex;
  align-items: center;
  background: rgba(66, 66, 66, 0.9);
  color: white;
  border-radius: 25px;
  z-index: 100;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.page_tool-item {
  padding: 8px 15px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.page_tool-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
