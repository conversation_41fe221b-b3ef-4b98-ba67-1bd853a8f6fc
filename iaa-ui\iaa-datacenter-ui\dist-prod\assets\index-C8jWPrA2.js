import{d as N,a as Y,j as i,r as z,f as F,o as T,c as H,k as e,w as n,u as a,M as K,v as s,x as P,h as q,D as A,t as j,F as G,S as J,B as L,C as R,G as E,H as O,I as Q,J as W,K as X,L as Z}from"./index-C8b06LRn.js";import{_ as $}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ee}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as le}from"./index-CkzUfjB7.js";import{d as g,a as te}from"./formatTime-COZ9Bl52.js";import{i as re}from"./index-BwETMpJ2.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";const ne=N({name:"BpmManagerTask",__name:"index",setup(oe){const{push:k}=Y(),p=i(!0),w=i(0),h=i([]),o=z({pageNo:1,pageSize:10,name:"",createTime:[]}),_=i(),m=async()=>{p.value=!0;try{const u=await re(o);h.value=u.list,w.value=u.total}finally{p.value=!1}},d=()=>{o.pageNo=1,m()},x=()=>{_.value.resetFields(),d()};return F(()=>{m()}),(u,l)=>{const I=le,M=J,c=L,S=R,b=E,f=O,U=Q,v=ae,t=W,V=ee,D=X,B=$,C=Z;return T(),H(G,null,[e(I,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),e(v,null,{default:n(()=>[e(U,{ref_key:"queryFormRef",ref:_,inline:!0,model:a(o),class:"-mb-15px","label-width":"68px"},{default:n(()=>[e(c,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:n(()=>[e(M,{modelValue:a(o).name,"onUpdate:modelValue":l[0]||(l[0]=r=>a(o).name=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:K(d,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:n(()=>[e(S,{modelValue:a(o).createTime,"onUpdate:modelValue":l[1]||(l[1]=r=>a(o).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(c,null,{default:n(()=>[e(f,{onClick:d},{default:n(()=>[e(b,{class:"mr-5px",icon:"ep:search"}),l[4]||(l[4]=s(" \u641C\u7D22 "))]),_:1}),e(f,{onClick:x},{default:n(()=>[e(b,{class:"mr-5px",icon:"ep:refresh"}),l[5]||(l[5]=s(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:n(()=>[P((T(),q(D,{data:a(h)},{default:n(()=>[e(t,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(t,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(t,{formatter:a(g),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(t,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(t,{formatter:a(g),align:"center",label:"\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(t,{formatter:a(g),align:"center",label:"\u4EFB\u52A1\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),e(t,{align:"center",label:"\u5BA1\u6279\u4EBA",prop:"assigneeUser.nickname",width:"100"}),e(t,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:n(r=>[e(V,{type:a(A).BPM_TASK_STATUS,value:r.row.status},null,8,["type","value"])]),_:1}),e(t,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"180"}),e(t,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:n(r=>[s(j(a(te)(r.row.durationInMillis)),1)]),_:1}),e(t,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(t,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(t,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:n(r=>[e(f,{link:"",type:"primary",onClick:ie=>{return y=r.row,void k({name:"BpmProcessInstanceDetail",query:{id:y.processInstance.id}});var y}},{default:n(()=>l[6]||(l[6]=[s("\u5386\u53F2")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[C,a(p)]]),e(B,{limit:a(o).pageSize,"onUpdate:limit":l[2]||(l[2]=r=>a(o).pageSize=r),page:a(o).pageNo,"onUpdate:page":l[3]||(l[3]=r=>a(o).pageNo=r),total:a(w),onPagination:m},null,8,["limit","page","total"])]),_:1})],64)}}});export{ne as default};
