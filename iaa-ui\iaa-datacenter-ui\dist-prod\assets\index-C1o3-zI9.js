import{_ as v}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as V,e as b,Y as k,j as u,u as o,o as d,c as m,k as e,w as i,m as n,E as y,q as x}from"./index-C8b06LRn.js";import{_ as g}from"./Total.vue_vue_type_script_setup_true_lang-DA_JwG0H.js";import{_ as j}from"./OrderReceivable.vue_vue_type_script_setup_true_lang-BiYqFbBe.js";import{_ as M}from"./ExpenseReceivable.vue_vue_type_script_setup_true_lang-DXUTxwRm.js";import U from"./MobileReconciliation-BcGtzgyE.js";import"./el-card-CaOo8U9P.js";import"./index-CSCiSrUr.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./Filter-Dzz2caxb.js";const q={key:0},w={key:0},E={key:1},Y={key:1},h=V({__name:"index",setup(z){const p=b(),f=k(()=>p.getMobile),a=u("order"),r=u([]);return(A,l)=>{const t=y,c=x,_=v;return o(f)?(d(),m("div",Y,[e(U)])):(d(),m("div",q,[e(_,null,{default:i(()=>[e(g,{modelValue:o(r),"onUpdate:modelValue":l[0]||(l[0]=s=>n(r)?r.value=s:null)},null,8,["modelValue"]),e(c,{modelValue:o(a),"onUpdate:modelValue":l[1]||(l[1]=s=>n(a)?a.value=s:null)},{default:i(()=>[e(t,{label:"\u8BA2\u5355\u5E94\u6536",name:"order"}),e(t,{label:"\u8D39\u7528\u5E94\u6536",name:"fee"})]),_:1},8,["modelValue"]),o(a)==="order"?(d(),m("div",w,[e(j,{"order-codes":o(r)},null,8,["order-codes"])])):(d(),m("div",E,[e(M,{"order-codes":o(r)},null,8,["order-codes"])]))]),_:1})]))}}});export{h as default};
