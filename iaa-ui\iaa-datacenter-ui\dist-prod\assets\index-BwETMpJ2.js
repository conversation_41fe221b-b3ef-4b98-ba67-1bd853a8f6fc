import{aG as t}from"./index-C8b06LRn.js";var s=(a=>(a[a.NOT_START=-1]="NOT_START",a[a.WAIT=0]="WAIT",a[a.RUNNING=1]="RUNNING",a[a.APPROVE=2]="APPROVE",a[a.REJECT=3]="REJECT",a[a.CANCEL=4]="CANCEL",a[a.RETURN=5]="RETURN",a[a.DELEGATE=6]="DELEGATE",a[a.APPROVING=7]="APPROVING",a))(s||{});const e=async a=>await t.get({url:"/bpm/task/todo-page",params:a}),r=async a=>await t.get({url:"/bpm/task/done-page",params:a}),p=async a=>await t.get({url:"/bpm/task/manager-page",params:a}),n=async a=>await t.put({url:"/bpm/task/approve",data:a}),i=async a=>await t.put({url:"/bpm/task/reject",data:a}),c=async a=>await t.get({url:"/bpm/task/list-by-process-instance-id?processInstanceId="+a}),u=async a=>await t.get({url:"/bpm/task/list-by-return",params:{id:a}}),m=async a=>await t.put({url:"/bpm/task/return",data:a}),l=async a=>await t.put({url:"/bpm/task/delegate",data:a}),d=async a=>await t.put({url:"/bpm/task/transfer",data:a}),b=async a=>await t.put({url:"/bpm/task/create-sign",data:a}),y=async a=>await t.delete({url:"/bpm/task/delete-sign",data:a}),E=async a=>await t.get({url:"/bpm/task/list-by-parent-task-id?parentTaskId="+a});export{s as T,n as a,u as b,m as c,l as d,E as e,y as f,c as g,r as h,p as i,e as j,i as r,b as s,d as t};
