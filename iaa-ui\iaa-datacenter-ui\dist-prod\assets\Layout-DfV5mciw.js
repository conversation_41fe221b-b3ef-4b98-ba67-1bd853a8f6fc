import{dJ as Il,j as y,br as st,f as ve,dL as rt,dx as it,d as U,bb as Ze,Y as b,o as $,h as G,w as _,u as e,c as H,aK as Ml,n as M,b4 as Ll,bf as Re,k as l,be as Sl,ey as ct,$ as me,W as el,bg as ll,bh as Pl,b6 as Bl,b9 as ut,bK as dt,aL as Rl,ba as mt,av as pt,l as u,i as zl,t as w,aE as vt,cS as ft,X as W,aj as ze,au as ue,F as oe,g as Me,G as te,_ as ne,ez as ht,e as ae,m as A,p as se,eA as Le,aC as gt,S as tl,v as Y,eB as Ul,eC as xt,dY as bt,d1 as jl,dm as al,dn as fe,az as wt,H as Ue,eD as yt,eE as je,eF as he,eG as _t,eH as Ct,a as ge,b as He,cz as xe,eI as kt,ci as Hl,r as ol,ax as nl,am as sl,an as rl,al as il,eJ as Ol,aF as cl,aO as ul,y as Al,eK as El,d2 as Fl,eL as Vt,dp as ql,eM as Tt,E as $t,q as It,at as Mt,cX as Lt,aN as dl,B as St,I as Pt,eN as Bt,eO as Rt,b0 as zt,x as be,Z as Oe,eP as Ut,eQ as jt,eR as Ht,eS as Ot,V as At,eh as Et,eT as Ft,T as Ae}from"./index-C8b06LRn.js";import{E as qt}from"./el-drawer-C5TFtzfV.js";import{c as Dl,l as Nl,h as Wl}from"./color-DXkOL5Tu.js";import{T as Dt}from"./ThemeSwitch-CpOMjpIf.js";import{u as Ee}from"./tagsView-D-HCnpxr.js";import{_ as Nt}from"./logo-BB9UjfGS.js";import{e as Wt}from"./index-BwMxOnEu.js";import{_ as Gt}from"./XButton-BOgar_Ex.js";import{a as Fe}from"./avatar-CPqUN878.js";import{f as Kt}from"./formatTime-COZ9Bl52.js";import{c as Jt,d as Yt}from"./index-DLROJAVs.js";import{_ as Gl}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as Qt}from"./el-avatar-BVm8aVjJ.js";import{_ as Xt}from"./ResetPwd.vue_vue_type_script_setup_true_lang-BM2XWWt2.js";import{d as Zt}from"./dateUtil-D9m5ek6U.js";import"./useIcon-CwemBubV.js";import"./InputPassword-CcRd7dRE.js";import"./profile-BAixQBws.js";const ea={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},la={click:p=>p instanceof MouseEvent},Kl="ElBacktop",ta=U({name:Kl}),aa=Pl(ll(U({...ta,props:ea,emits:la,setup(p,{emit:r}){const t=p,n=Ze("backtop"),{handleClick:o,visible:a}=((c,g,h)=>{const m=Il(),f=Il(),C=y(!1),L=()=>{m.value&&(C.value=m.value.scrollTop>=c.visibilityHeight)},T=it(L,300,!0);return st(f,"scroll",T),ve(()=>{var i;f.value=document,m.value=document.documentElement,c.target&&(m.value=(i=document.querySelector(c.target))!=null?i:void 0,m.value||rt(h,`target does not exist: ${c.target}`),f.value=m.value),L()}),{visible:C,handleClick:i=>{var v;(v=m.value)==null||v.scrollTo({top:0,behavior:"smooth"}),g("click",i)}}})(t,r,Kl),s=b(()=>({right:`${t.right}px`,bottom:`${t.bottom}px`}));return(c,g)=>($(),G(el,{name:`${e(n).namespace.value}-fade-in`},{default:_(()=>[e(a)?($(),H("div",{key:0,style:Ml(e(s)),class:M(e(n).b()),onClick:Ll(e(o),["stop"])},[Re(c.$slots,"default",{},()=>[l(e(Sl),{class:M(e(n).e("icon"))},{default:_(()=>[l(e(ct))]),_:1},8,["class"])])],14,["onClick"])):me("v-if",!0)]),_:3},8,["name"]))}}),[["__file","backtop.vue"]])),Jl=Symbol("breadcrumbKey"),oa=Bl({separator:{type:String,default:"/"},separatorIcon:{type:ut}}),na=U({name:"ElBreadcrumb"}),sa=U({...na,props:oa,setup(p){const r=p,{t}=dt(),n=Ze("breadcrumb"),o=y();return Rl(Jl,r),ve(()=>{const a=o.value.querySelectorAll(`.${n.e("item")}`);a.length&&a[a.length-1].setAttribute("aria-current","page")}),(a,s)=>($(),H("div",{ref_key:"breadcrumb",ref:o,class:M(e(n).b()),"aria-label":e(t)("el.breadcrumb.label"),role:"navigation"},[Re(a.$slots,"default")],10,["aria-label"]))}});var ra=ll(sa,[["__file","breadcrumb.vue"]]);const ia=Bl({to:{type:mt([String,Object]),default:""},replace:Boolean}),ca=U({name:"ElBreadcrumbItem"});var Yl=ll(U({...ca,props:ia,setup(p){const r=p,t=vt(),n=pt(Jl,void 0),o=Ze("breadcrumb"),a=t.appContext.config.globalProperties.$router,s=y(),c=()=>{r.to&&a&&(r.replace?a.replace(r.to):a.push(r.to))};return(g,h)=>{var m,f;return $(),H("span",{class:M(e(o).e("item"))},[u("span",{ref_key:"link",ref:s,class:M([e(o).e("inner"),e(o).is("link",!!g.to)]),role:"link",onClick:c},[Re(g.$slots,"default")],2),(m=e(n))!=null&&m.separatorIcon?($(),G(e(Sl),{key:0,class:M(e(o).e("separator"))},{default:_(()=>[($(),G(zl(e(n).separatorIcon)))]),_:1},8,["class"])):($(),H("span",{key:1,class:M(e(o).e("separator")),role:"presentation"},w((f=e(n))==null?void 0:f.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const ua=Pl(ra,{BreadcrumbItem:Yl}),da=ft(Yl),ma=U({name:"BackTop",__name:"Backtop",setup(p){const{getPrefixCls:r,variables:t}=W(),n=r("backtop");return(o,a)=>($(),G(e(aa),{class:M(`${e(n)}-backtop`),target:`.${e(t).namespace}-layout-content-scrollbar .${e(t).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}),pa=["onClick"],va=U({name:"ColorRadioPicker",__name:"ColorRadioPicker",props:{schema:{type:Array,default:()=>[]},modelValue:ze.string.def("")},emits:["update:modelValue","change"],setup(p,{emit:r}){const{getPrefixCls:t}=W(),n=t("color-radio-picker"),o=p,a=r,s=y(o.modelValue);return ue(()=>o.modelValue,c=>{c!==e(s)&&(s.value=c)}),ue(()=>s.value,c=>{a("update:modelValue",c),a("change",c)}),(c,g)=>{const h=te;return $(),H("div",{class:M([e(n),"flex flex-wrap space-x-14px"])},[($(!0),H(oe,null,Me(p.schema,(m,f)=>($(),H("span",{key:`radio-${f}`,class:M([{"is-active":e(s)===m},"mb-5px h-20px w-20px cursor-pointer border-2px border-gray-300 rounded-2px border-solid text-center leading-20px"]),style:Ml({background:m}),onClick:C=>s.value=m},[e(s)===m?($(),G(h,{key:0,size:16,color:"#fff",icon:"ep:check"})):me("",!0)],14,pa))),128))],2)}}}),ml=ne(va,[["__scopeId","data-v-61582eab"]]),fa={class:"flex items-center justify-between"},ha={class:"text-14px"},ga={class:"flex items-center justify-between"},xa={class:"text-14px"},ba={class:"flex items-center justify-between"},wa={class:"text-14px"},ya={class:"flex items-center justify-between"},_a={class:"text-14px"},Ca={class:"flex items-center justify-between"},ka={class:"text-14px"},Va={class:"flex items-center justify-between"},Ta={class:"text-14px"},$a={class:"flex items-center justify-between"},Ia={class:"text-14px"},Ma={class:"flex items-center justify-between"},La={class:"text-14px"},Sa={class:"flex items-center justify-between"},Pa={class:"text-14px"},Ba={class:"flex items-center justify-between"},Ra={class:"text-14px"},za={class:"flex items-center justify-between"},Ua={class:"text-14px"},ja={class:"flex items-center justify-between"},Ha={class:"text-14px"},Oa={class:"flex items-center justify-between"},Aa={class:"text-14px"},Ea={class:"flex items-center justify-between"},Fa={class:"text-14px"},qa={class:"flex items-center justify-between"},Da={class:"text-14px"},Na={class:"flex items-center justify-between"},Wa={class:"text-14px"},Ga={class:"flex items-center justify-between"},Ka={class:"text-14px"},Ja=U({name:"InterfaceDisplay",__name:"InterfaceDisplay",setup(p){const{t:r}=se(),{getPrefixCls:t}=W(),{setWatermark:n}=ht(),o=t("interface-display"),a=ae(),s=y(),c=y(a.getBreadcrumb),g=V=>{a.setBreadcrumb(V)},h=y(a.getBreadcrumbIcon),m=V=>{a.setBreadcrumbIcon(V)},f=y(a.getHamburger),C=V=>{a.setHamburger(V)},L=y(a.getScreenfull),T=V=>{a.setScreenfull(V)},i=y(a.getSize),v=V=>{a.setSize(V)},k=y(a.getLocale),x=V=>{a.setLocale(V)},j=y(a.getMessage),F=V=>{a.setMessage(V)},P=y(a.getTagsView),K=V=>{Le("--tags-view-height",V?"35px":"0px"),a.setTagsView(V)},z=y(a.getTagsViewImmerse),O=V=>{a.setTagsViewImmerse(V)},B=y(a.getTagsViewIcon),ee=V=>{a.setTagsViewIcon(V)},Z=y(a.getLogo),le=V=>{a.setLogo(V)},D=y(a.getUniqueOpened),de=V=>{a.setUniqueOpened(V)},re=y(a.getFixedHeader),$e=V=>{a.setFixedHeader(V)},Te=y(a.getFooter),S=V=>{a.setFooter(V)},R=y(a.getGreyMode),N=V=>{a.setGreyMode(V)},J=y(a.getFixedMenu),ie=V=>{a.setFixedMenu(V)},ke=b(()=>a.getLayout);return ue(()=>ke.value,V=>{V==="top"&&a.setCollapse(!1)}),(V,I)=>{const q=gt,Ve=tl;return $(),H("div",{class:M(e(o))},[u("div",fa,[u("span",ha,w(e(r)("setting.breadcrumb")),1),l(q,{modelValue:e(c),"onUpdate:modelValue":I[0]||(I[0]=d=>A(c)?c.value=d:null),onChange:g},null,8,["modelValue"])]),u("div",ga,[u("span",xa,w(e(r)("setting.breadcrumbIcon")),1),l(q,{modelValue:e(h),"onUpdate:modelValue":I[1]||(I[1]=d=>A(h)?h.value=d:null),onChange:m},null,8,["modelValue"])]),u("div",ba,[u("span",wa,w(e(r)("setting.hamburgerIcon")),1),l(q,{modelValue:e(f),"onUpdate:modelValue":I[2]||(I[2]=d=>A(f)?f.value=d:null),onChange:C},null,8,["modelValue"])]),u("div",ya,[u("span",_a,w(e(r)("setting.screenfullIcon")),1),l(q,{modelValue:e(L),"onUpdate:modelValue":I[3]||(I[3]=d=>A(L)?L.value=d:null),onChange:T},null,8,["modelValue"])]),u("div",Ca,[u("span",ka,w(e(r)("setting.sizeIcon")),1),l(q,{modelValue:e(i),"onUpdate:modelValue":I[4]||(I[4]=d=>A(i)?i.value=d:null),onChange:v},null,8,["modelValue"])]),u("div",Va,[u("span",Ta,w(e(r)("setting.localeIcon")),1),l(q,{modelValue:e(k),"onUpdate:modelValue":I[5]||(I[5]=d=>A(k)?k.value=d:null),onChange:x},null,8,["modelValue"])]),u("div",$a,[u("span",Ia,w(e(r)("setting.messageIcon")),1),l(q,{modelValue:e(j),"onUpdate:modelValue":I[6]||(I[6]=d=>A(j)?j.value=d:null),onChange:F},null,8,["modelValue"])]),u("div",Ma,[u("span",La,w(e(r)("setting.tagsView")),1),l(q,{modelValue:e(P),"onUpdate:modelValue":I[7]||(I[7]=d=>A(P)?P.value=d:null),onChange:K},null,8,["modelValue"])]),u("div",Sa,[u("span",Pa,w(e(r)("setting.tagsViewImmerse")),1),l(q,{modelValue:e(z),"onUpdate:modelValue":I[8]||(I[8]=d=>A(z)?z.value=d:null),onChange:O},null,8,["modelValue"])]),u("div",Ba,[u("span",Ra,w(e(r)("setting.tagsViewIcon")),1),l(q,{modelValue:e(B),"onUpdate:modelValue":I[9]||(I[9]=d=>A(B)?B.value=d:null),onChange:ee},null,8,["modelValue"])]),u("div",za,[u("span",Ua,w(e(r)("setting.logo")),1),l(q,{modelValue:e(Z),"onUpdate:modelValue":I[10]||(I[10]=d=>A(Z)?Z.value=d:null),onChange:le},null,8,["modelValue"])]),u("div",ja,[u("span",Ha,w(e(r)("setting.uniqueOpened")),1),l(q,{modelValue:e(D),"onUpdate:modelValue":I[11]||(I[11]=d=>A(D)?D.value=d:null),onChange:de},null,8,["modelValue"])]),u("div",Oa,[u("span",Aa,w(e(r)("setting.fixedHeader")),1),l(q,{modelValue:e(re),"onUpdate:modelValue":I[12]||(I[12]=d=>A(re)?re.value=d:null),onChange:$e},null,8,["modelValue"])]),u("div",Ea,[u("span",Fa,w(e(r)("setting.footer")),1),l(q,{modelValue:e(Te),"onUpdate:modelValue":I[13]||(I[13]=d=>A(Te)?Te.value=d:null),onChange:S},null,8,["modelValue"])]),u("div",qa,[u("span",Da,w(e(r)("setting.greyMode")),1),l(q,{modelValue:e(R),"onUpdate:modelValue":I[14]||(I[14]=d=>A(R)?R.value=d:null),onChange:N},null,8,["modelValue"])]),u("div",Na,[u("span",Wa,w(e(r)("setting.fixedMenu")),1),l(q,{modelValue:e(J),"onUpdate:modelValue":I[15]||(I[15]=d=>A(J)?J.value=d:null),onChange:ie},null,8,["modelValue"])]),u("div",Ga,[u("span",Ka,w(e(r)("watermark.watermark")),1),l(Ve,{modelValue:e(s),"onUpdate:modelValue":I[16]||(I[16]=d=>A(s)?s.value=d:null),class:"right-1 w-20",onChange:I[17]||(I[17]=d=>{n(s.value)})},null,8,["modelValue"])])],2)}}}),Ya=U({name:"LayoutRadioPicker",__name:"LayoutRadioPicker",setup(p){const{getPrefixCls:r}=W(),t=r("layout-radio-picker"),n=ae(),o=b(()=>n.getLayout);return(a,s)=>($(),H("div",{class:M([e(t),"flex flex-wrap space-x-14px"])},[u("div",{class:M([`${e(t)}__classic`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(o)==="classic"}]),onClick:s[0]||(s[0]=c=>e(n).setLayout("classic"))},null,2),u("div",{class:M([`${e(t)}__top-left`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(o)==="topLeft"}]),onClick:s[1]||(s[1]=c=>e(n).setLayout("topLeft"))},null,2),u("div",{class:M([`${e(t)}__top`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(o)==="top"}]),onClick:s[2]||(s[2]=c=>e(n).setLayout("top"))},null,2),u("div",{class:M([`${e(t)}__cut-menu`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(o)==="cutMenu"}]),onClick:s[3]||(s[3]=c=>e(n).setLayout("cutMenu"))},s[4]||(s[4]=[u("div",{class:"absolute left-[10%] top-0 h-full w-[33%] bg-gray-200"},null,-1)]),2)],2))}}),Qa=ne(Ya,[["__scopeId","data-v-d0ae4a8f"]]),Xa={class:"text-16px font-700"},Za={class:"text-center"},eo={class:"mt-5px"},lo=U({name:"Setting",__name:"Setting",setup(p){const{t:r}=se(),t=ae(),{getPrefixCls:n}=W(),o=n("setting"),a=b(()=>t.getLayout),s=y(!1),c=y(t.getTheme.elColorPrimary),g=i=>{Le("--el-color-primary",i),t.setTheme({elColorPrimary:i});const v=Ul("--left-menu-bg-color",document.documentElement);C(xt(e(v)))},h=y(t.getTheme.topHeaderBgColor||""),m=i=>{const v=Dl(i),k=v?"#fff":"inherit",x=v?Nl(i,6):"#f6f6f6",j=v?i:"#eee";Le("--top-header-bg-color",i),Le("--top-header-text-color",k),Le("--top-header-hover-color",x),t.setTheme({topHeaderBgColor:i,topHeaderTextColor:k,topHeaderHoverColor:x,topToolBorderColor:j}),e(a)==="top"&&C(i)},f=y(t.getTheme.leftMenuBgColor||""),C=i=>{const v=Ul("--el-color-primary",document.documentElement),k=Dl(i),x={leftMenuBorderColor:k?"inherit":"#eee",leftMenuBgColor:i,leftMenuBgLightColor:k?Nl(i,6):i,leftMenuBgActiveColor:k?"var(--el-color-primary)":Wl(e(v),.1),leftMenuCollapseBgActiveColor:k?"var(--el-color-primary)":Wl(e(v),.1),leftMenuTextColor:k?"#bfcbd9":"#333",leftMenuTextActiveColor:k?"#fff":"var(--el-color-primary)",logoTitleTextColor:k?"#fff":"inherit",logoBorderColor:k?i:"#eee"};t.setTheme(x),t.setCssVarTheme()};g("#409eff"),C("#354e6b"),a.value!=="top"||t.getIsDark||(h.value="#fff",m("#fff")),ue(()=>a.value,i=>{i!=="top"||t.getIsDark?C(e(f)):(h.value="#fff",m("#fff"))});const L=async()=>{const{copy:i,copied:v,isSupported:k}=bt({source:`
      // \u9762\u5305\u5C51
      breadcrumb: ${t.getBreadcrumb},
      // \u9762\u5305\u5C51\u56FE\u6807
      breadcrumbIcon: ${t.getBreadcrumbIcon},
      // \u6298\u53E0\u56FE\u6807
      hamburger: ${t.getHamburger},
      // \u5168\u5C4F\u56FE\u6807
      screenfull: ${t.getScreenfull},
      // \u5C3A\u5BF8\u56FE\u6807
      size: ${t.getSize},
      // \u591A\u8BED\u8A00\u56FE\u6807
      locale: ${t.getLocale},
      // \u6D88\u606F\u56FE\u6807
      message: ${t.getMessage},
      // \u6807\u7B7E\u9875
      tagsView: ${t.getTagsView},
      // \u6807\u7B7E\u9875
      tagsViewImmerse: ${t.getTagsViewImmerse},
      // \u6807\u7B7E\u9875\u56FE\u6807
      tagsViewIcon: ${t.getTagsViewIcon},
      // logo
      logo: ${t.getLogo},
      // \u83DC\u5355\u624B\u98CE\u7434
      uniqueOpened: ${t.getUniqueOpened},
      // \u56FA\u5B9Aheader
      fixedHeader: ${t.getFixedHeader},
      // \u9875\u811A
      footer: ${t.getFooter},
      // \u7070\u8272\u6A21\u5F0F
      greyMode: ${t.getGreyMode},
      // layout\u5E03\u5C40
      layout: '${t.getLayout}',
      // \u6697\u9ED1\u6A21\u5F0F
      isDark: ${t.getIsDark},
      // \u7EC4\u4EF6\u5C3A\u5BF8
      currentSize: '${t.getCurrentSize}',
      // \u4E3B\u9898\u76F8\u5173
      theme: {
        // \u4E3B\u9898\u8272
        elColorPrimary: '${t.getTheme.elColorPrimary}',
        // \u5DE6\u4FA7\u83DC\u5355\u8FB9\u6846\u989C\u8272
        leftMenuBorderColor: '${t.getTheme.leftMenuBorderColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u80CC\u666F\u989C\u8272
        leftMenuBgColor: '${t.getTheme.leftMenuBgColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6D45\u8272\u80CC\u666F\u989C\u8272
        leftMenuBgLightColor: '${t.getTheme.leftMenuBgLightColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuBgActiveColor: '${t.getTheme.leftMenuBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6536\u8D77\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuCollapseBgActiveColor: '${t.getTheme.leftMenuCollapseBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u5B57\u4F53\u989C\u8272
        leftMenuTextColor: '${t.getTheme.leftMenuTextColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u5B57\u4F53\u989C\u8272
        leftMenuTextActiveColor: '${t.getTheme.leftMenuTextActiveColor}',
        // logo\u5B57\u4F53\u989C\u8272
        logoTitleTextColor: '${t.getTheme.logoTitleTextColor}',
        // logo\u8FB9\u6846\u989C\u8272
        logoBorderColor: '${t.getTheme.logoBorderColor}',
        // \u5934\u90E8\u80CC\u666F\u989C\u8272
        topHeaderBgColor: '${t.getTheme.topHeaderBgColor}',
        // \u5934\u90E8\u5B57\u4F53\u989C\u8272
        topHeaderTextColor: '${t.getTheme.topHeaderTextColor}',
        // \u5934\u90E8\u60AC\u505C\u989C\u8272
        topHeaderHoverColor: '${t.getTheme.topHeaderHoverColor}',
        // \u5934\u90E8\u8FB9\u6846\u989C\u8272
        topToolBorderColor: '${t.getTheme.topToolBorderColor}'
      }
    `});k?(await i(),e(v)&&jl.success(r("setting.copySuccess"))):jl.error(r("setting.copyFailed"))},T=()=>{const{wsCache:i}=al();i.delete(fe.LAYOUT),i.delete(fe.THEME),i.delete(fe.IS_DARK),window.location.reload()};return(i,v)=>{const k=te,x=wt,j=Ue,F=qt;return $(),H(oe,null,[u("div",{class:M([e(o),"fixed right-0 top-[45%] h-40px w-40px cursor-pointer bg-[var(--el-color-primary)] text-center leading-40px hide"]),onClick:v[0]||(v[0]=P=>s.value=!0)},[l(k,{color:"#fff",icon:"ep:setting"})],2),l(F,{modelValue:e(s),"onUpdate:modelValue":v[4]||(v[4]=P=>A(s)?s.value=P:null),"z-index":4e3,direction:"rtl",size:"350px"},{header:_(()=>[u("span",Xa,w(e(r)("setting.projectSetting")),1)]),default:_(()=>[u("div",Za,[l(x,null,{default:_(()=>[Y(w(e(r)("setting.theme")),1)]),_:1}),l(e(Dt)),l(x,null,{default:_(()=>[Y(w(e(r)("setting.layout")),1)]),_:1}),l(Qa),l(x,null,{default:_(()=>[Y(w(e(r)("setting.systemTheme")),1)]),_:1}),l(ml,{modelValue:e(c),"onUpdate:modelValue":v[1]||(v[1]=P=>A(c)?c.value=P:null),schema:["#409eff","#009688","#536dfe","#ff5c93","#ee4f12","#0096c7","#9c27b0","#ff9800"],onChange:g},null,8,["modelValue"]),l(x,null,{default:_(()=>[Y(w(e(r)("setting.headerTheme")),1)]),_:1}),l(ml,{modelValue:e(h),"onUpdate:modelValue":v[2]||(v[2]=P=>A(h)?h.value=P:null),schema:["#fff","#151515","#5172dc","#e74c3c","#24292e","#394664","#009688","#383f45"],onChange:m},null,8,["modelValue"]),e(a)!=="top"?($(),H(oe,{key:0},[l(x,null,{default:_(()=>[Y(w(e(r)("setting.menuTheme")),1)]),_:1}),l(ml,{modelValue:e(f),"onUpdate:modelValue":v[3]||(v[3]=P=>A(f)?f.value=P:null),schema:["#fff","#001529","#212121","#273352","#191b24","#383f45","#001628","#344058"],onChange:C},null,8,["modelValue"])],64)):me("",!0)]),l(x,null,{default:_(()=>[Y(w(e(r)("setting.interfaceDisplay")),1)]),_:1}),l(Ja),l(x),u("div",null,[l(j,{class:"w-full",type:"primary",onClick:L},{default:_(()=>[Y(w(e(r)("setting.copy")),1)]),_:1})]),u("div",eo,[l(j,{class:"w-full",type:"danger",onClick:T},{default:_(()=>[Y(w(e(r)("setting.clearAndReset")),1)]),_:1})])]),_:1},8,["modelValue"])],64)}}}),to=ne(lo,[["__scopeId","data-v-481aef5d"]]),ao=(p,r)=>(yt(p,t=>t.path===r)||[]).map(t=>t.path),{renderMenuTitle:Ql}={renderMenuTitle:p=>{const{t:r}=se(),{title:t="Please set title",icon:n}=p;return n?l(oe,null,[l(te,{icon:p.icon},null),l("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[r(t)])]):l("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[r(t)])}},oo=()=>{const p=(r,t="/")=>r.filter(n=>{var o;return!((o=n.meta)!=null&&o.hidden)}).map(n=>{const o=n.meta??{},{oneShowingChild:a,onlyOneChild:s}=((g=[],h)=>{const m=y(),f=g.filter(C=>!(C.meta??{}).hidden&&(m.value=C,!0));return f.length===1?{oneShowingChild:!0,onlyOneChild:e(m)}:f.length?{oneShowingChild:!1,onlyOneChild:e(m)}:(m.value={...h,path:"",noShowingChildren:!0},{oneShowingChild:!0,onlyOneChild:e(m)})})(n.children,n),c=je(n.path)?n.path:he(t,n.path);return!a||s!=null&&s.children&&!(s!=null&&s.noShowingChildren)||o!=null&&o.alwaysShow?l(Ct,{index:c},{title:()=>Ql(o),default:()=>p(n.children,c)}):l(_t,{index:s?he(c,s.path):c},{default:()=>Ql(s?s==null?void 0:s.meta:o)})});return{renderMenuItem:p}},{getPrefixCls:no}=W(),Se=no("menu"),so=U({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(p){const r=ae(),t=b(()=>r.getLayout),{push:n,currentRoute:o}=ge(),a=He(),s=b(()=>["classic","topLeft","cutMenu"].includes(e(t))?"vertical":"horizontal"),c=b(()=>e(t)==="cutMenu"?a.getMenuTabRouters:a.getRouters),g=b(()=>r.getCollapse),h=b(()=>r.getUniqueOpened),m=b(()=>{const{meta:T,path:i}=e(o);return T.activeMenu?T.activeMenu:i}),f=T=>{p.menuSelect&&p.menuSelect(T),je(T)?window.open(T):n(T)},C=()=>{if(e(t)==="top")return L();{let i;return l(xe,null,typeof(T=i=L())=="function"||Object.prototype.toString.call(T)==="[object Object]"&&!Hl(T)?i:{default:()=>[i]})}var T},L=()=>l(kt,{defaultActive:e(m),mode:e(s),collapse:e(t)!=="top"&&e(t)!=="cutMenu"&&e(g),uniqueOpened:e(t)!=="top"&&e(h),backgroundColor:"var(--left-menu-bg-color)",textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",popperClass:e(s)==="vertical"?`${Se}-popper--vertical`:`${Se}-popper--horizontal`,onSelect:f},{default:()=>{const{renderMenuItem:T}=oo(e(s));return T(e(c))}});return()=>l("div",{id:Se,class:[`${Se} ${Se}__${e(s)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":e(g)&&e(t)!=="cutMenu","w-[var(--left-menu-max-width)]":!e(g)&&e(t)!=="cutMenu"}]},[C()])}}),qe=ne(so,[["__scopeId","data-v-d886b223"]]),De=ol({}),Xl=(p,r)=>{const t=[];for(const n of p){let o=null;const a=n.meta??{};if(!a.hidden||a.canTo){const s=ao(r,n.path),c=je(n.path)?n.path:s.join("/");o=nl(n),o.path=c,n.children&&o&&(o.children=Xl(n.children,r)),o&&t.push(o),s.length&&Reflect.has(De,s[0])&&De[s[0]].push(c)}}return t},{getPrefixCls:ro,variables:io}=W(),pl=ro("tab-menu"),co=U({name:"TabMenu",setup(){const{push:p,currentRoute:r}=ge(),{t}=se(),n=ae(),o=b(()=>n.getCollapse),a=b(()=>n.getFixedMenu),s=He(),c=b(()=>s.getRouters),g=b(()=>e(c).filter(i=>{var v;return!((v=i==null?void 0:i.meta)!=null&&v.hidden)})),h=()=>{n.setCollapse(!e(o))};ve(()=>{var i;if(e(a)){const v=`/${e(r).path.split("/")[1]}`,k=(i=e(g).find(x=>{var j,F,P;return(((j=x.meta)==null?void 0:j.alwaysShow)||((F=x==null?void 0:x.children)==null?void 0:F.length)&&((P=x==null?void 0:x.children)==null?void 0:P.length)>1)&&x.path===v}))==null?void 0:i.children;C.value=v,k&&s.setMenuTabRouters(nl(k).map(x=>(x.path=he(e(C),x.path),x)))}}),ue(()=>c.value,i=>{(v=>{for(const k of v){const x=k.meta??{};x!=null&&x.hidden||(De[k.path]=[])}})(i),Xl(i,i)},{immediate:!0,deep:!0});const m=y(!0);ue(()=>o.value,i=>{i?m.value=!i:setTimeout(()=>{m.value=!i},200)});const f=y(!!e(a)),C=y(""),L=i=>{const{path:v}=e(r);return!!De[i].includes(v)},T=()=>{e(f)&&!e(a)&&(f.value=!1)};return()=>l("div",{id:`${io.namespace}-menu`,class:[pl,"relative bg-[var(--left-menu-bg-color)] layout-border__right",{"w-[var(--tab-menu-max-width)]":!e(o),"w-[var(--tab-menu-min-width)]":e(o)}],onMouseleave:T},[l(xe,{class:"!h-[calc(100%-var(--tab-menu-collapse-height))]"},{default:()=>[l("div",null,{default:()=>e(g).map(i=>{var k,x,j,F,P,K;const v=(k=i.meta)!=null&&k.alwaysShow||(x=i==null?void 0:i.children)!=null&&x.length&&((j=i==null?void 0:i.children)==null?void 0:j.length)>1?i:{...(i==null?void 0:i.children)&&(i==null?void 0:i.children[0]),path:he(i.path,(F=(i==null?void 0:i.children)&&(i==null?void 0:i.children[0]))==null?void 0:F.path)};return l("div",{class:[`${pl}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":L(i.path)}],onClick:()=>{(z=>{if(je(z.path))return void window.open(z.path);const O=z.children?z.path:z.path.split("/")[0],B=e(C);C.value=z.children?z.path:z.path.split("/")[0],z.children?(O!==B&&e(f)||(f.value=!!e(a)||!e(f)),e(f)&&s.setMenuTabRouters(nl(z.children).map(ee=>(ee.path=he(e(C),ee.path),ee)))):(p(z.path),s.setMenuTabRouters([]),f.value=!1)})(v)}},[l("div",null,[l(te,{icon:(P=v==null?void 0:v.meta)==null?void 0:P.icon},null)]),e(m)?l("p",{class:"mt-5px break-words px-2px"},[t((K=v.meta)==null?void 0:K.title)]):void 0])})})]}),l("div",{class:[`${pl}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:h},[l(te,{icon:e(o)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),l(qe,{class:["!absolute top-0 z-11",{"!left-[var(--tab-menu-min-width)]":e(o),"!left-[var(--tab-menu-max-width)]":!e(o),"!w-[var(--left-menu-max-width)]":e(f)||e(a),"!w-0":!e(f)&&!e(a)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)])}}),uo=ne(co,[["__scopeId","data-v-73123a16"]]),Zl=(p,r="")=>{let t=[];return p.forEach(n=>{const o=n.meta,a=he(r,n.path);if(o!=null&&o.affix&&t.push({...n,path:a,fullPath:a}),n.children){const s=Zl(n.children,a);s.length>=1&&(t=[...t,...s])}}),t},et=U({name:"ContextMenu",__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})}},emits:["visibleChange"],setup(p,{expose:r,emit:t}){const{getPrefixCls:n}=W(),o=n("context-menu"),{t:a}=se(),s=t,c=p,g=f=>{f.command&&f.command(f)},h=f=>{s("visibleChange",f,c.tagItem)},m=y();return r({elDropdownMenuRef:m,tagItem:c.tagItem}),(f,C)=>{const L=te,T=sl,i=rl,v=il;return $(),G(v,{ref_key:"elDropdownMenuRef",ref:m,class:M(e(o)),trigger:p.trigger,placement:"bottom-start","popper-class":"v-context-menu-popper",onCommand:g,onVisibleChange:h},{dropdown:_(()=>[l(i,null,{default:_(()=>[($(!0),H(oe,null,Me(p.schema,(k,x)=>($(),G(T,{key:`dropdown${x}`,command:k,disabled:k.disabled,divided:k.divided},{default:_(()=>[l(L,{icon:k.icon},null,8,["icon"]),Y(" "+w(e(a)(k.label)),1)]),_:2},1032,["command","disabled","divided"]))),128))]),_:1})]),default:_(()=>[Re(f.$slots,"default")]),_:3},8,["class","trigger"])}}});function Pe({el:p,position:r="scrollLeft",to:t,duration:n=500,callback:o}){const a=y(!1),s=p[r],c=t-s;let g=0;function h(){if(!e(a))return;g+=20;const m=((f,C,L,T)=>(f/=T/2)<1?L/2*f*f+C:-L/2*(--f*(f-2)-1)+C)(g,s,c,n);((f,C,L)=>{f[C]=L})(p,r,m),g<n&&e(a)?requestAnimationFrame(h):o&&o()}return{start:function(){a.value=!0,h()},stop:function(){a.value=!1}}}const mo=["id"],po={class:"flex-1 overflow-hidden"},vo={class:"h-[var(--tags-view-height)] flex"},fo=["onClick"],ho=U({__name:"TagsView",setup(p){const{getPrefixCls:r}=W(),t=r("tags-view"),{t:n}=se(),{currentRoute:o,push:a,replace:s}=ge(),c=He(),g=b(()=>c.getRouters),h=Ee(),m=b(()=>h.getVisitedViews),f=y([]),C=ae(),L=b(()=>C.getTagsViewImmerse),T=b(()=>C.getTagsViewIcon),i=b(()=>C.getIsDark),v=y(),k=()=>{const{name:S}=e(o);return S&&(v.value=e(o),h.addView(e(o))),!1},x=S=>{var R;(R=S==null?void 0:S.meta)!=null&&R.affix||(h.delView(S),Z(S)&&O())},j=()=>{h.delAllViews(),O()},F=()=>{h.delOthersViews(e(v))},P=async S=>{if(!S)return;h.delCachedView();const{path:R,query:N}=S;await ul(),s({path:"/redirect"+R,query:N})},K=()=>{h.delLeftViews(e(v))},z=()=>{h.delRightViews(e(v))},O=()=>{const S=h.getVisitedViews.slice(-1)[0];if(S)a(S);else{if(e(o).path===c.getAddRouters[0].path||e(o).path===c.getAddRouters[0].redirect)return void k();a("/")}},B=Ol(),ee=S=>{var ke;const R=(ke=e(de))==null?void 0:ke.wrapRef;let N=null,J=null;const ie=e(B);if(ie.length>0&&(N=ie[0],J=ie[ie.length-1]),(N==null?void 0:N.to).fullPath===S.fullPath){const{start:V}=Pe({el:R,position:"scrollLeft",to:0,duration:500});V()}else if((J==null?void 0:J.to).fullPath===S.fullPath){const{start:V}=Pe({el:R,position:"scrollLeft",to:R.scrollWidth-R.offsetWidth,duration:500});V()}else{const V=ie.findIndex(ce=>(ce==null?void 0:ce.to).fullPath===S.fullPath),I=document.getElementsByClassName(`${t}__item`),q=I[V-1],Ve=I[V+1],d=Ve.offsetLeft+Ve.offsetWidth+4,Ie=q.offsetLeft-4;if(d>e(re)+R.offsetWidth){const{start:ce}=Pe({el:R,position:"scrollLeft",to:d-R.offsetWidth,duration:500});ce()}else if(Ie<e(re)){const{start:ce}=Pe({el:R,position:"scrollLeft",to:Ie,duration:500});ce()}}},Z=S=>S.fullPath===e(o).fullPath,le=Ol(),D=(S,R)=>{if(S)for(const N of e(le)){const J=N.elDropdownMenuRef;R.fullPath!==N.tagItem.fullPath&&(J==null||J.handleClose())}},de=y(),re=y(0),$e=({scrollLeft:S})=>{re.value=S},Te=S=>{var J;const R=(J=e(de))==null?void 0:J.wrapRef,{start:N}=Pe({el:R,position:"scrollLeft",to:e(re)+S,duration:500});N()};return ve(()=>{(()=>{f.value=Zl(e(g));for(const S of e(f))S.name&&h.addVisitedView(S)})(),k()}),ue(()=>o.value,()=>{k(),(async()=>{await ul();for(const S of e(m))if(S.fullPath===e(o).fullPath){ee(S);break}})()}),(S,R)=>{var ie,ke,V,I,q,Ve;const N=te,J=cl("router-link");return $(),H("div",{id:e(t),class:M([e(t),"relative w-full flex bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[u("span",{class:M([L.value?"":`${e(t)}__tool ${e(t)}__tool--first`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:R[0]||(R[0]=d=>Te(-200))},[l(N,{"hover-color":i.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:d-arrow-left"},null,8,["hover-color"])],2),u("div",po,[l(e(xe),{ref_key:"scrollbarRef",ref:de,class:"h-full",onScroll:$e},{default:_(()=>[u("div",vo,[($(!0),H(oe,null,Me(m.value,d=>{var Ie,ce,fl,hl,gl,xl,bl,wl,yl;return $(),G(e(et),{key:d.fullPath,ref_for:!0,ref:e(le).set,class:M([`${e(t)}__item`,L.value?`${e(t)}__item--immerse`:"",T.value?`${e(t)}__item--icon`:"",L.value&&T.value?`${e(t)}__item--immerse--icon`:"",(Ie=d==null?void 0:d.meta)!=null&&Ie.affix?`${e(t)}__item--affix`:"",{"is-active":Z(d)}]),schema:[{icon:"ep:refresh",label:e(n)("common.reload"),disabled:((ce=v.value)==null?void 0:ce.fullPath)!==d.fullPath,command:()=>{P(d)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((fl=m.value)!=null&&fl.length)&&((hl=v.value)==null?void 0:hl.meta.affix),command:()=>{x(d)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((gl=m.value)!=null&&gl.length)&&(d.fullPath===m.value[0].fullPath||((xl=v.value)==null?void 0:xl.fullPath)!==d.fullPath),command:()=>{K()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((bl=m.value)!=null&&bl.length)&&(d.fullPath===m.value[m.value.length-1].fullPath||((wl=v.value)==null?void 0:wl.fullPath)!==d.fullPath),command:()=>{z()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),disabled:((yl=v.value)==null?void 0:yl.fullPath)!==d.fullPath,command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{j()}}],"tag-item":d,onVisibleChange:D},{default:_(()=>[u("div",null,[l(J,{ref_for:!0,ref:e(B).set,to:{...d},custom:""},{default:_(({navigate:nt})=>{var _l,Cl,kl,Vl,Tl,$l;return[u("div",{class:M(`h-full flex items-center justify-center whitespace-nowrap pl-15px ${e(t)}__item--label`),onClick:nt},[T.value&&((_l=d==null?void 0:d.meta)!=null&&_l.icon||d!=null&&d.matched&&d.matched[0]&&((Cl=d.matched[d.matched.length-1].meta)!=null&&Cl.icon))?($(),G(N,{key:0,icon:((kl=d==null?void 0:d.meta)==null?void 0:kl.icon)||d.matched[d.matched.length-1].meta.icon,size:12,class:"mr-5px"},null,8,["icon"])):me("",!0),Y(" "+w(e(n)((Vl=d==null?void 0:d.meta)==null?void 0:Vl.title)+((Tl=d==null?void 0:d.meta)!=null&&Tl.titleSuffix?` (${($l=d==null?void 0:d.meta)==null?void 0:$l.titleSuffix})`:""))+" ",1),l(N,{class:M(`${e(t)}__item--close`),size:12,color:"#333",icon:"ep:close",onClick:Ll(hn=>x(d),["prevent","stop"])},null,8,["class","onClick"])],10,fo)]}),_:2},1032,["to"])])]),_:2},1032,["class","schema","tag-item"])}),128))])]),_:1},512)]),u("span",{class:M([L.value?"":`${e(t)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:R[1]||(R[1]=d=>Te(200))},[l(N,{"hover-color":i.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:d-arrow-right"},null,8,["hover-color"])],2),u("span",{class:M([L.value?"":`${e(t)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:R[2]||(R[2]=d=>P(v.value))},[l(N,{"hover-color":i.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:refresh-right"},null,8,["hover-color"])],2),l(e(et),{schema:[{icon:"ep:refresh",label:e(n)("common.reload"),command:()=>{P(v.value)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((ie=m.value)!=null&&ie.length)&&((ke=v.value)==null?void 0:ke.meta.affix),command:()=>{x(v.value)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((V=m.value)!=null&&V.length)&&((I=v.value)==null?void 0:I.fullPath)===m.value[0].fullPath,command:()=>{K()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((q=m.value)!=null&&q.length)&&((Ve=v.value)==null?void 0:Ve.fullPath)===m.value[m.value.length-1].fullPath,command:()=>{z()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{j()}}],trigger:"click"},{default:_(()=>[u("span",{class:M([L.value?"":`${e(t)}__tool`,"block h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"])},[l(N,{"hover-color":i.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:menu"},null,8,["hover-color"])],2)]),_:1},8,["schema"])],10,mo)}}}),Ne=ne(ho,[["__scopeId","data-v-faadbf1e"]]),We=U({name:"Logo",__name:"Logo",setup(p){const{getPrefixCls:r}=W(),t=r("logo"),n=ae(),o=y(!0),a=b(()=>n.getTitle),s=b(()=>n.getLayout),c=b(()=>n.getCollapse);return ve(()=>{e(c)&&(o.value=!1)}),ue(()=>c.value,g=>{e(s)!=="topLeft"&&e(s)!=="cutMenu"?g?o.value=!g:setTimeout(()=>{o.value=!g},400):o.value=!0}),ue(()=>s.value,g=>{g==="top"||g==="cutMenu"?o.value=!0:e(c)?o.value=!1:o.value=!0}),(g,h)=>{const m=cl("router-link");return $(),H("div",null,[l(m,{class:M([e(t),s.value!=="classic"?`${e(t)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:_(()=>[h[0]||(h[0]=u("img",{class:"h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]",src:Nt},null,-1)),o.value?($(),H("div",{key:0,class:M(["ml-10px text-16px font-700 overflow-hidden",{"text-[var(--logo-title-text-color)]":s.value==="classic","text-[var(--top-header-text-color)]":s.value==="topLeft"||s.value==="top"||s.value==="cutMenu"}])},w(a.value),3)):me("",!0)]),_:1},8,["class"])])}}}),Ge=U({name:"AppView",__name:"AppView",setup(p){const{wsCache:r}=al();Al();const t=ae();b(()=>t.getLayout),b(()=>t.getFixedHeader);const n=b(()=>t.getFooter),o=Ee(),a=b(()=>o.getCachedViews);b(()=>t.getTagsView);const s=y(!0);return Rl("reload",()=>{s.value=!1,ul(()=>s.value=!0)}),ve(async()=>{var f,C;const c=r.get(fe.USER);if(((f=c==null?void 0:c.user)==null?void 0:f.id)===1)return;const g=await Wt(),h=r.get(fe.ROLE_ROUTERS),m=El(h);(g==null?void 0:g.menu)==m.length&&(g==null?void 0:g.permission)==((C=c.permissions)==null?void 0:C.length)||(await Fl.confirm("\u68C0\u6D4B\u5230\u672C\u5730\u6743\u9650\u4E0E\u670D\u52A1\u5668\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u786E\u8BA4\u91CD\u65B0\u83B7\u53D6","\u6743\u9650\u5237\u65B0",{confirmButtonText:"\u786E\u8BA4",cancelButtonText:"\u53D6\u6D88",type:"warning",showCancelButton:!1}),r.delete(fe.USER),r.delete(fe.ROLE_ROUTERS),location.reload())}),(c,g)=>{const h=cl("router-view");return $(),H("section",{class:M(["p-[var(--app-content-padding)] w-full bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100vh-var(--logo-height)-var(--tags-view-height)-var(--app-footer-height))] pb-0":e(n)}])},[e(s)?($(),G(h,{key:0},{default:_(({Component:m,route:f})=>[($(),G(Vt,{include:e(a)},[($(),G(zl(m),{key:f.fullPath}))],1032,["include"]))]),_:1})):me("",!0)],2)}}}),go={class:"message"},xo={class:"message-content"},bo={class:"message-title"},wo={class:"message-date"},yo={style:{"margin-top":"10px","text-align":"right"}},_o=ne(U({name:"Message",__name:"Message",setup(p){const{push:r}=ge(),t=ql(),n=y("notice"),o=y(0),a=y([]),s=async()=>{a.value=await Jt(),o.value=0},c=async()=>{Yt().then(h=>{o.value=h})},g=()=>{r({name:"MyNotifyMessage"})};return ve(()=>{c(),setInterval(()=>{t.getIsSetUser?c():o.value=0},12e4)}),(h,m)=>{const f=te,C=Tt,L=xe,T=$t,i=It,v=Gt,k=Mt;return $(),H("div",go,[l(k,{width:400,placement:"bottom",trigger:"click"},{reference:_(()=>[l(C,{"is-dot":e(o)>0,class:"item"},{default:_(()=>[l(f,{size:18,class:"cursor-pointer",icon:"ep:bell",onClick:s})]),_:1},8,["is-dot"])]),default:_(()=>[l(i,{modelValue:e(n),"onUpdate:modelValue":m[0]||(m[0]=x=>A(n)?n.value=x:null)},{default:_(()=>[l(T,{label:"\u6211\u7684\u7AD9\u5185\u4FE1",name:"notice"},{default:_(()=>[l(L,{class:"message-list"},{default:_(()=>[($(!0),H(oe,null,Me(e(a),x=>($(),H("div",{key:x.id,class:"message-item"},[m[1]||(m[1]=u("img",{alt:"",class:"message-icon",src:Fe},null,-1)),u("div",xo,[u("span",bo,w(x.templateNickname)+"\uFF1A"+w(x.templateContent),1),u("span",wo,w(e(Kt)(x.createTime)),1)])]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"]),u("div",yo,[l(v,{preIcon:"ep:view",title:"\u67E5\u770B\u5168\u90E8",type:"primary",onClick:g})])]),_:1})])}}}),[["__scopeId","data-v-d283f204"]]),Co=U({name:"Collapse",__name:"Collapse",props:{color:ze.string.def("")},setup(p){const{getPrefixCls:r}=W(),t=r("collapse"),n=ae(),o=b(()=>n.getCollapse),a=()=>{const s=e(o);n.setCollapse(!s)};return(s,c)=>{const g=te;return $(),H("div",{class:M(e(t)),onClick:a},[l(g,{color:p.color,icon:e(o)?"ep:expand":"ep:fold",size:18,class:"cursor-pointer"},null,8,["color","icon"])],2)}}}),{t:Ke}=se(),ko=()=>({required:p=>({required:!0,message:p||Ke("common.required")}),lengthRange:p=>{const{min:r,max:t,message:n}=p;return{min:r,max:t,message:n||Ke("common.lengthRange",{min:r,max:t})}},notSpace:p=>({validator:(r,t,n)=>{(t==null?void 0:t.indexOf(" "))!==-1?n(new Error(p||Ke("common.notSpace"))):n()}}),notSpecialCharacters:p=>({validator:(r,t,n)=>{/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi.test(t)?n(new Error(p||Ke("common.notSpecialCharacters"))):n()}})}),vl=Lt("lock",{state:()=>({lockInfo:{}}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(p){this.lockInfo=p},resetLockInfo(){this.lockInfo={}},unLock(p){var r;return((r=this.lockInfo)==null?void 0:r.password)===p&&(this.resetLockInfo(),!0)}},persist:!0}),Vo={class:"flex flex-col items-center"},To=["src"],$o={class:"text-14px my-10px text-[var(--top-header-text-color)]"},Io=U({__name:"LockDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(p,{emit:r}){const{getPrefixCls:t}=W(),n=t("lock-dialog"),{required:o}=ko(),{t:a}=se(),s=vl(),c=p,g=dl(),h=b(()=>g.user.avatar||Fe),m=b(()=>g.user.nickname??"Admin"),f=r,C=b({get:()=>c.modelValue,set:x=>{f("update:modelValue",x)}}),L=y(a("lock.lockScreen")),T=y({password:void 0}),i=ol({password:[o()]}),v=y(),k=async()=>{v&&await v.value.validate()&&(C.value=!1,s.setLockInfo({...T.value,isLock:!0}))};return(x,j)=>{const F=tl,P=St,K=Pt,z=Ue,O=Gl;return $(),G(O,{modelValue:e(C),"onUpdate:modelValue":j[1]||(j[1]=B=>A(C)?C.value=B:null),width:"500px","max-height":"170px",class:M(e(n)),title:e(L)},{footer:_(()=>[l(z,{type:"primary",onClick:k},{default:_(()=>[Y(w(e(a)("lock.lock")),1)]),_:1})]),default:_(()=>[u("div",Vo,[u("img",{src:e(h),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,To),u("span",$o,w(e(m)),1)]),l(K,{ref_key:"formRef",ref:v,model:e(T),rules:e(i),"label-width":"80px"},{default:_(()=>[l(P,{label:e(a)("lock.lockPassword"),prop:"password"},{default:_(()=>[l(F,{type:"password",modelValue:e(T).password,"onUpdate:modelValue":j[0]||(j[0]=B=>e(T).password=B),placeholder:"\u8BF7\u8F93\u5165"+e(a)("lock.lockPassword"),clearable:"","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","class","title"])}}}),Mo=ne(Io,[["__scopeId","data-v-e7f7cd6a"]]),Lo={class:"flex w-screen h-screen justify-center items-center"},So={class:"flex flex-col items-center"},Po=["src"],Bo={class:"text-14px my-10px text-[var(--logo-title-text-color)]"},Ro={class:"absolute bottom-5 w-full text-gray-300 xl:text-xl 2xl:text-3xl text-center enter-y"},zo={class:"text-5xl mb-4 enter-x"},Uo={class:"text-3xl"},jo={class:"text-2xl"},Ho=U({__name:"LockPage",setup(p){const r=Ee(),{replace:t}=ge(),n=dl(),o=y(""),a=y(!1),s=y(!1),c=y(!0),{getPrefixCls:g}=W(),h=g("lock-page"),m=b(()=>n.user.avatar||Fe),f=b(()=>n.user.nickname??"Admin"),C=vl(),{hour:L,month:T,minute:i,meridiem:v,year:k,day:x,week:j}=((z=!0)=>{let O;const B=ol({year:0,month:0,week:"",day:0,hour:"",minute:"",second:0,meridiem:""}),ee=()=>{const D=Zt(),de=D.format("HH"),re=D.format("mm"),$e=D.get("s");B.year=D.get("y"),B.month=D.get("M")+1,B.week="\u661F\u671F"+["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"][D.day()],B.day=D.get("date"),B.hour=de,B.minute=re,B.second=$e,B.meridiem=D.format("A")};function Z(){ee(),clearInterval(O),O=setInterval(()=>ee(),1e3)}function le(){clearInterval(O)}return Bt(()=>{z&&Z()}),Rt(()=>{le()}),{...zt(B),start:Z,stop:le}})(!0),{t:F}=se();async function P(){await n.loginOut().catch(()=>{}),Ut(),r.delAllViews(),C.resetLockInfo(),t("/login")}function K(z=!1){c.value=z}return(z,O)=>{const B=te,ee=tl,Z=Ue;return $(),H("div",{class:M([e(h),"fixed inset-0 flex h-screen w-screen bg-black items-center justify-center"])},[be(u("div",{class:M([`${e(h)}__unlock`,"absolute top-0 left-1/2 flex pt-5 h-16 items-center justify-center sm:text-md xl:text-xl text-white flex-col cursor-pointer transform translate-x-1/2"]),onClick:O[0]||(O[0]=le=>K(!1))},[l(B,{icon:"ep:lock"}),u("span",null,w(e(F)("lock.unlock")),1)],2),[[Oe,e(c)]]),u("div",Lo,[u("div",{class:M([`${e(h)}__hour`,"relative mr-5 md:mr-20 w-2/5 h-2/5 md:h-4/5"])},[u("span",null,w(e(L)),1),be(u("span",{class:"meridiem absolute left-5 top-5 text-md xl:text-xl"},w(e(v)),513),[[Oe,e(c)]])],2),u("div",{class:M(`${e(h)}__minute w-2/5 h-2/5 md:h-4/5 `)},[u("span",null,w(e(i)),1)],2)]),l(el,{name:"fade-slide"},{default:_(()=>[be(u("div",{class:M(`${e(h)}-entry`)},[u("div",{class:M(`${e(h)}-entry-content`)},[u("div",So,[u("img",{src:e(m),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Po),u("span",Bo,w(e(f)),1)]),l(ee,{type:"password",placeholder:e(F)("lock.placeholder"),class:"enter-x",modelValue:e(o),"onUpdate:modelValue":O[1]||(O[1]=le=>A(o)?o.value=le:null)},null,8,["placeholder","modelValue"]),e(s)?($(),H("span",{key:0,class:M(`text-14px ${e(h)}-entry__err-msg enter-x`)},w(e(F)("lock.message")),3)):me("",!0),u("div",{class:M(`${e(h)}-entry__footer enter-x`)},[l(Z,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(a),onClick:O[2]||(O[2]=le=>K(!0))},{default:_(()=>[Y(w(e(F)("common.back")),1)]),_:1},8,["disabled"]),l(Z,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(a),onClick:P},{default:_(()=>[Y(w(e(F)("lock.backToLogin")),1)]),_:1},8,["disabled"]),l(Z,{type:"primary",class:"mt-2",size:"small",link:"",onClick:O[3]||(O[3]=le=>async function(){if(!o.value)return;let D=o.value;try{a.value=!0;const de=await C.unLock(D);s.value=!de}finally{a.value=!1}}()),disabled:e(a)},{default:_(()=>[Y(w(e(F)("lock.entrySystem")),1)]),_:1},8,["disabled"])],2)],2)],2),[[Oe,!e(c)]])]),_:1}),u("div",Ro,[be(u("div",zo,[Y(w(e(L))+":"+w(e(i))+" ",1),u("span",Uo,w(e(v)),1)],512),[[Oe,!e(c)]]),u("div",jo,w(e(k))+"/"+w(e(T))+"/"+w(e(x))+" "+w(e(j)),1)])],2)}}}),Oo=ne(Ho,[["__scopeId","data-v-3e502faf"]]),Ao={class:"flex items-center"},Eo={class:"pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden"},Fo=U({name:"UserInfo",__name:"UserInfo",setup(p){const{t:r}=se(),{push:t,replace:n}=ge(),o=dl(),a=Al(),{wsCache:s}=al(),c=Ee(),{getPrefixCls:g}=W(),h=g("user-info"),m=b(()=>o.user.avatar||Fe),f=b(()=>o.user.nickname??"Admin"),C=vl(),L=b(()=>{var P;return((P=C.getLockInfo)==null?void 0:P.isLock)??!1}),T=y(!1),i=()=>{T.value=!0},v=async()=>{try{await Fl.confirm(r("common.loginOutMessage"),r("common.reminder"),{confirmButtonText:r("common.ok"),cancelButtonText:r("common.cancel"),type:"warning"}),await o.loginOut(),c.delAllViews(),n("/login?redirect=/index")}catch{}},k=async()=>{t("/user/profile")},x=y({visiable:!1}),j=P=>{a.alert("\u60A8\u5F53\u524D\u4E3A\u9996\u6B21\u767B\u5F55\uFF0C\u5FC5\u987B\u4FEE\u6539\u5BC6\u7801")},F=async()=>{o.user.loginCount+=1,x.value.visiable=!1;const P=await Ht();s.set(fe.USER,P)};return ve(()=>{o.user.loginCount===1&&(x.value.visiable=!0)}),(P,K)=>{const z=Qt,O=te,B=sl,ee=rl,Z=il,le=Gl;return $(),H(oe,null,[l(Z,{class:M(["custom-hover",e(h)]),trigger:"click"},{dropdown:_(()=>[l(ee,null,{default:_(()=>[l(B,null,{default:_(()=>[l(O,{icon:"ep:tools"}),u("div",{onClick:k},w(e(r)("common.profile")),1)]),_:1}),l(B,{divided:""},{default:_(()=>[l(O,{icon:"ep:lock"}),u("div",{onClick:i},w(e(r)("lock.lockScreen")),1)]),_:1}),l(B,{divided:"",onClick:v},{default:_(()=>[l(O,{icon:"ep:switch-button"}),u("div",null,w(e(r)("common.loginOut")),1)]),_:1})]),_:1})]),default:_(()=>[u("div",Ao,[l(z,{src:e(m),alt:"",class:"!w-[calc(var(--logo-height)-5px)] rounded-[50%] !h-[calc(var(--logo-height)-5px)]"},null,8,["src"]),u("span",Eo,w(e(f)),1)])]),_:1},8,["class"]),e(T)?($(),G(Mo,{key:0,modelValue:e(T),"onUpdate:modelValue":K[0]||(K[0]=D=>A(T)?T.value=D:null)},null,8,["modelValue"])):me("",!0),($(),G(jt,{to:"body"},[l(el,{name:"fade-bottom",mode:"out-in"},{default:_(()=>[e(L)?($(),G(Oo,{key:0})):me("",!0)]),_:1})])),l(le,{modelValue:e(x).visiable,"onUpdate:modelValue":K[1]||(K[1]=D=>e(x).visiable=D),title:"\u9996\u6B21\u767B\u5F55\uFF0C\u8BF7\u4FEE\u6539\u4F60\u7684\u5BC6\u7801","before-close":j},{default:_(()=>[l(Xt,{"old-pws":"123456",onSuccess:F})]),_:1},8,["modelValue"])],64)}}}),qo=ne(Fo,[["__scopeId","data-v-45859784"]]),Do=U({name:"ScreenFull",__name:"Screenfull",props:{color:ze.string.def("")},setup(p){const{getPrefixCls:r}=W(),t=r("screenfull"),{toggle:n,isFullscreen:o}=Ot(),a=()=>{n()};return(s,c)=>($(),H("div",{class:M(e(t)),onClick:a},[l(e(te),{color:p.color,icon:e(o)?"zmdi:fullscreen-exit":"zmdi:fullscreen",size:18},null,8,["color","icon"])],2))}}),lt=(p,r="")=>{var n;const t=[];for(const o of p){const a=o==null?void 0:o.meta;if(a.hidden&&!a.canTo)continue;const s=a.alwaysShow||((n=o.children)==null?void 0:n.length)!==1?{...o}:{...o.children[0],path:he(o.path,o.children[0].path)};s.path=he(r,s.path),s.children&&(s.children=lt(s.children,s.path)),s&&t.push(s)}return t},{getPrefixCls:No}=W(),Wo=No("breadcrumb"),Go=ae(),Ko=b(()=>Go.getBreadcrumbIcon),Jo=U({name:"Breadcrumb",setup(){const{currentRoute:p}=ge(),{t:r}=se(),t=y([]),n=He(),o=b(()=>{const a=n.getRouters;return lt(a)});return ue(()=>p.value,a=>{a.path.startsWith("/redirect/")||(()=>{const s=p.value.matched.slice(-1)[0].path;t.value=Et(e(o),c=>c.path===s)})()},{immediate:!0}),()=>{let a;return l(ua,{separator:"/",class:`${Wo} flex items-center h-full ml-[10px]`},{default:()=>{return[l(At,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},(s=a=El(e(t)).map(c=>{const g=!c.redirect||c.redirect==="noredirect",h=c.meta;return l(da,{to:{path:g?"":c.path},key:c.name},{default:()=>{var m,f;return[h!=null&&h.icon&&Ko.value?l("div",{class:"flex items-center"},[l(te,{icon:h.icon,class:"mr-[2px]",svgClass:"inline-block"},null),r((m=c==null?void 0:c.meta)==null?void 0:m.title)]):r((f=c==null?void 0:c.meta)==null?void 0:f.title)]}})}),typeof s=="function"||Object.prototype.toString.call(s)==="[object Object]"&&!Hl(s)?a:{default:()=>[a]}))];var s}})}}}),Yo=ne(Jo,[["__scopeId","data-v-4b03c1c3"]]),Qo=U({name:"SizeDropdown",__name:"SizeDropdown",props:{color:ze.string.def("")},setup(p){const{getPrefixCls:r}=W(),t=r("size-dropdown"),{t:n}=se(),o=ae(),a=b(()=>o.sizeMap),s=c=>{o.setCurrentSize(c)};return(c,g)=>{const h=te,m=sl,f=rl,C=il;return $(),G(C,{class:M(e(t)),trigger:"click",onCommand:s},{dropdown:_(()=>[l(f,null,{default:_(()=>[($(!0),H(oe,null,Me(e(a),L=>($(),G(m,{key:L,command:L},{default:_(()=>[Y(w(e(n)(`size.${L}`)),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:_(()=>[l(h,{color:p.color,size:18,class:"cursor-pointer",icon:"mdi:format-size"},null,8,["color"])]),_:1},8,["class"])}}}),{getPrefixCls:Xo,variables:Zo}=W(),en=Xo("tool-header"),we=ae(),ln=b(()=>we.getBreadcrumb),tn=b(()=>we.getHamburger),an=b(()=>we.getScreenfull),on=b(()=>we.search),nn=b(()=>we.getSize),tt=b(()=>we.getLayout);b(()=>we.getLocale);const sn=b(()=>we.getMessage),{getIsIntranet:rn}=ql(),Je=ne(U({name:"ToolHeader",setup:()=>()=>l("div",{id:`${Zo.namespace}-tool-header`,class:[en,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},[tt.value!=="top"?l("div",{class:"h-full flex items-center"},[tn.value&&tt.value!=="cutMenu"?l(Co,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,ln.value?l(Yo,{class:"lt-md:hidden"},null):void 0]):void 0,l("div",{class:"h-full flex items-center"},[l("div",{class:"text-[var(--el-color-primary)]"},[rn?"\u5185\u7F51\u73AF\u5883":"\u5916\u7F51\u73AF\u5883"]),an.value?l(Do,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,on.value?l(Ft,{isModal:!1},null):void 0,nn.value?l(Qo,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,sn.value?l(_o,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,l(qo,null,null)])])}),[["__scopeId","data-v-353d0176"]]),{getPrefixCls:cn}=W(),ye=cn("layout"),pe=ae(),Ye=b(()=>pe.getPageLoading),_e=b(()=>pe.getTagsView),Q=b(()=>pe.getCollapse),Be=b(()=>pe.logo),X=b(()=>pe.getFixedHeader),E=b(()=>pe.getMobile),Ce=b(()=>pe.getFixedMenu),Qe=()=>({renderClassic:()=>l(oe,null,[l("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":E.value}]},[Be.value?l(We,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":E.value&&Q.value,"w-[var(--left-menu-min-width)]":pe.getCollapse,"w-[var(--left-menu-max-width)]":!pe.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,l(qe,{class:[{"!h-[calc(100%-var(--logo-height))]":Be.value}]},null)]),l("div",{class:[`${ye}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Q.value&&!E.value&&!E.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Q.value&&!E.value&&!E.value,"fixed !w-full !left-0":E.value}],style:"transition: all var(--transition-time-02);"},[be(l(xe,{class:[`${ye}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":X.value}]},{default:()=>[l("div",{class:[{"fixed top-0 left-0 z-10":X.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":Q.value&&X.value&&!E.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!Q.value&&X.value&&!E.value,"!w-full !left-0":E.value}],style:"transition: all var(--transition-time-02);"},[l(Je,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!_e.value}]},null),_e.value?l(Ne,{class:"layout-border__top layout-border__bottom"},null):void 0]),l(Ge,null,null)]}),[[Ae("loading"),Ye.value]])])]),renderTopLeft:()=>l(oe,null,[l("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom dark:bg-[var(--el-bg-color)]"},[Be.value?l(We,{class:"custom-hover"},null):void 0,l(Je,{class:"flex-1"},null)]),l("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-full flex"},[l(qe,{class:"relative layout-border__right !h-full"},null),l("div",{class:[`${ye}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Q.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Q.value}],style:"transition: all var(--transition-time-02);"},[be(l(xe,{class:[`${ye}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":X.value&&_e.value}]},{default:()=>[_e.value?l(Ne,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":X.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[var(--logo-height)]":Q.value&&X.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[var(--logo-height)]":!Q.value&&X.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,l(Ge,null,null)]}),[[Ae("loading"),Ye.value]])])])]),renderTop:()=>l(oe,null,[l("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!_e.value}]},[Be.value?l(We,{class:"custom-hover"},null):void 0,l(qe,{class:"h-[var(--top-tool-height)] flex-1 px-10px"},null),l(Je,null,null)]),l("div",{class:[`${ye}-content`,"w-full h-[calc(100%-var(--top-tool-height))]"]},[be(l(xe,{class:[`${ye}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":X.value}]},{default:()=>[_e.value?l(Ne,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[var(--top-tool-height)] left-0":X.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,l(Ge,null,null)]}),[[Ae("loading"),Ye.value]])])]),renderCutMenu:()=>{const p=ge();return l(oe,null,[l("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border bottom"},[Be.value?l(We,{class:"custom-hover !pr-15px"},null):void 0,l(Je,{class:"flex-1"},null)]),l("div",{class:["absolute","left-0","top-[var(--logo-height)]","h-[calc(100%-var(--logo-height))]","w-full",{flex:!E.value},"right-0"]},[l(uo,{class:["hidden",{"!block":!E.value}]},null),l("div",{class:[`${ye}-content`,"h-[100%]",{"!h-[calc(100%-50px)]":E.value},"w-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":Q.value&&!Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!Q.value&&!Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":Q.value&&Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!Q.value&&Ce.value&&!E.value}],style:"transition: all var(--transition-time-02);"},[be(l(xe,{class:[`${ye}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":X.value&&_e.value&&!E.value}]},{default:()=>[_e.value?l(Ne,{class:["relative layout-border__bottom",{"!fixed top-0 left-0 z-10":X.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":Q.value&&X.value&&!Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!Q.value&&X.value&&!Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[calc(var(--tab-menu-min-width)+var(--left-menu-max-width))] mt-[var(--logo-height)]":Q.value&&X.value&&Ce.value&&!E.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[calc(var(--tab-menu-max-width)+var(--left-menu-max-width))] mt-[var(--logo-height)]":!Q.value&&X.value&&Ce.value&&!E.value},"!mt-[calc(var(--logo-height)+2px)]",{"!hidden":E.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,l(Ge,null,null)]}),[[Ae("loading"),Ye.value]])]),l(Ue,{type:"primary",class:["!h-[var(--logo-height)]","text-center","mt-10px","bg-#fff","text-[var(--el-color-primary)]",{"!hidden":!E.value},"!w-full"],onClick:()=>p.push("/")},{default:()=>[Y("\u5DE5\u4F5C\u53F0")]})])])}}),{getPrefixCls:un}=W(),at=un("layout"),Xe=ae(),dn=b(()=>Xe.getMobile),mn=b(()=>Xe.getCollapse),ot=b(()=>Xe.getLayout),pn=()=>{Xe.setCollapse(!0)},vn=()=>{switch(e(ot)){case"classic":const{renderClassic:p}=Qe();return p();case"topLeft":const{renderTopLeft:r}=Qe();return r();case"top":const{renderTop:t}=Qe();return t();case"cutMenu":const{renderCutMenu:n}=Qe();return n()}},fn=ne(U({name:"Layout",setup:()=>()=>l("section",{class:[at,`${at}__${ot.value}`,"w-[100%] h-[100%] relative"]},[dn.value&&!mn.value?l("div",{class:"absolute left-0 top-0 z-99 h-full w-full bg-[var(--el-color-black)] opacity-30",onClick:pn},null):void 0,vn(),l(ma,null,null),l(to,null,null)])}),[["__scopeId","data-v-928b1933"]]);export{fn as default};
