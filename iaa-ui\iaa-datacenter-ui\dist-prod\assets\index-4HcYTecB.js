import{d as X,y as Z,p as ee,j as u,r as ae,f as le,T as te,o as r,c as k,k as e,w as o,u as t,M as S,F as v,g as K,N as oe,D as O,h as i,v as p,x as g,t as A,$ as ne,S as re,B as se,z as pe,A as ie,C as ce,G as ue,H as de,I as me,J as fe,aM as ye,K as _e,L as ge}from"./index-C8b06LRn.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as we}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{_ as ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ve}from"./index-CkzUfjB7.js";import{d as F}from"./formatTime-COZ9Bl52.js";import{d as xe}from"./download-D5Lb_h0f.js";import{_ as he,g as Ce,d as Ve,e as Me}from"./TenantForm.vue_vue_type_script_setup_true_lang-D9reDrrZ.js";import{g as Se}from"./index-AhqHODKl.js";import"./index-Cl43piKd.js";import"./color-DXkOL5Tu.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./constants-D3f7Z3TX.js";const Ne=X({name:"SystemTenant",__name:"index",setup(Te){const x=Z(),{t:H}=ee(),h=u(!0),N=u(0),T=u([]),n=ae({pageNo:1,pageSize:10,name:void 0,contactName:void 0,contactMobile:void 0,status:void 0,createTime:[]}),U=u(),C=u(!1),D=u([]),f=async()=>{h.value=!0;try{const c=await Ce(n);T.value=c.list,N.value=c.total}finally{h.value=!1}},y=()=>{n.pageNo=1,f()},R=()=>{U.value.resetFields(),y()},Y=u(),z=(c,a)=>{Y.value.open(c,a)},q=async()=>{try{await x.exportConfirm(),C.value=!0;const c=await Me(n);xe.excel(c,"\u79DF\u6237\u5217\u8868.xls")}catch{}finally{C.value=!1}};return le(async()=>{await f(),D.value=await Se()}),(c,a)=>{const B=ve,V=re,d=se,G=pe,P=ie,j=ce,b=ue,m=de,J=me,I=ke,s=fe,M=ye,L=we,$=_e,E=be,w=te("hasPermi"),Q=ge;return r(),k(v,null,[e(B,{title:"SaaS \u591A\u79DF\u6237",url:"https://doc.iocoder.cn/saas-tenant/"}),e(I,null,{default:o(()=>[e(J,{class:"-mb-15px",model:t(n),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:o(()=>[e(d,{label:"\u79DF\u6237\u540D",prop:"name"},{default:o(()=>[e(V,{modelValue:t(n).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(n).name=l),placeholder:"\u8BF7\u8F93\u5165\u79DF\u6237\u540D",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u8054\u7CFB\u4EBA",prop:"contactName"},{default:o(()=>[e(V,{modelValue:t(n).contactName,"onUpdate:modelValue":a[1]||(a[1]=l=>t(n).contactName=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u8054\u7CFB\u624B\u673A",prop:"contactMobile"},{default:o(()=>[e(V,{modelValue:t(n).contactMobile,"onUpdate:modelValue":a[2]||(a[2]=l=>t(n).contactMobile=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u624B\u673A",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u79DF\u6237\u72B6\u6001",prop:"status"},{default:o(()=>[e(P,{modelValue:t(n).status,"onUpdate:modelValue":a[3]||(a[3]=l=>t(n).status=l),placeholder:"\u8BF7\u9009\u62E9\u79DF\u6237\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(r(!0),k(v,null,K(t(oe)(t(O).COMMON_STATUS),l=>(r(),i(G,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[e(j,{modelValue:t(n).createTime,"onUpdate:modelValue":a[4]||(a[4]=l=>t(n).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(d,null,{default:o(()=>[e(m,{onClick:y},{default:o(()=>[e(b,{icon:"ep:search",class:"mr-5px"}),a[8]||(a[8]=p(" \u641C\u7D22 "))]),_:1}),e(m,{onClick:R},{default:o(()=>[e(b,{icon:"ep:refresh",class:"mr-5px"}),a[9]||(a[9]=p(" \u91CD\u7F6E "))]),_:1}),g((r(),i(m,{type:"primary",plain:"",onClick:a[5]||(a[5]=l=>z("create"))},{default:o(()=>[e(b,{icon:"ep:plus",class:"mr-5px"}),a[10]||(a[10]=p(" \u65B0\u589E "))]),_:1})),[[w,["system:tenant:create"]]]),g((r(),i(m,{type:"success",plain:"",onClick:q,loading:t(C)},{default:o(()=>[e(b,{icon:"ep:download",class:"mr-5px"}),a[11]||(a[11]=p(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[w,["system:tenant:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(I,null,{default:o(()=>[g((r(),i($,{data:t(T)},{default:o(()=>[e(s,{label:"\u79DF\u6237\u7F16\u53F7",align:"center",prop:"id"}),e(s,{label:"\u79DF\u6237\u540D",align:"center",prop:"name"}),e(s,{label:"\u79DF\u6237\u5957\u9910",align:"center",prop:"packageId"},{default:o(l=>[l.row.packageId===0?(r(),i(M,{key:0,type:"danger"},{default:o(()=>a[12]||(a[12]=[p("\u7CFB\u7EDF\u79DF\u6237")])),_:1})):(r(!0),k(v,{key:1},K(t(D),_=>(r(),k(v,null,[_.id===l.row.packageId?(r(),i(M,{type:"success",key:_.id},{default:o(()=>[p(A(_.name),1)]),_:2},1024)):ne("",!0)],64))),256))]),_:1}),e(s,{label:"\u8054\u7CFB\u4EBA",align:"center",prop:"contactName"}),e(s,{label:"\u8054\u7CFB\u624B\u673A",align:"center",prop:"contactMobile"}),e(s,{label:"\u8D26\u53F7\u989D\u5EA6",align:"center",prop:"accountCount"},{default:o(l=>[e(M,null,{default:o(()=>[p(A(l.row.accountCount),1)]),_:2},1024)]),_:1}),e(s,{label:"\u8FC7\u671F\u65F6\u95F4",align:"center",prop:"expireTime",width:"180",formatter:t(F)},null,8,["formatter"]),e(s,{label:"\u7ED1\u5B9A\u57DF\u540D",align:"center",prop:"website",width:"180"}),e(s,{label:"\u79DF\u6237\u72B6\u6001",align:"center",prop:"status"},{default:o(l=>[e(L,{type:t(O).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(F)},null,8,["formatter"]),e(s,{label:"\u64CD\u4F5C",align:"center","min-width":"110",fixed:"right"},{default:o(l=>[g((r(),i(m,{link:"",type:"primary",onClick:_=>z("update",l.row.id)},{default:o(()=>a[13]||(a[13]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["system:tenant:update"]]]),g((r(),i(m,{link:"",type:"danger",onClick:_=>(async W=>{try{await x.delConfirm(),await Ve(W),x.success(H("common.delSuccess")),await f()}catch{}})(l.row.id)},{default:o(()=>a[14]||(a[14]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["system:tenant:delete"]]])]),_:1})]),_:1},8,["data"])),[[Q,t(h)]]),e(E,{total:t(N),page:t(n).pageNo,"onUpdate:page":a[6]||(a[6]=l=>t(n).pageNo=l),limit:t(n).pageSize,"onUpdate:limit":a[7]||(a[7]=l=>t(n).pageSize=l),onPagination:f},null,8,["total","page","limit"])]),_:1}),e(he,{ref_key:"formRef",ref:Y,onSuccess:f},null,512)],64)}}});export{Ne as default};
