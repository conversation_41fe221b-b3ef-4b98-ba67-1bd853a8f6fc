import{aG as e}from"./index-C8b06LRn.js";const l={getCollectionDetailsPage:t=>e.post({url:"/collection/detail/page",data:t}),getRateAndTotal:t=>e.post({url:"/collection/detail/getTotal",data:t}),exportCollectionDetail:t=>e.download({url:"/collection/detail/export-excel",params:t}),getDate:()=>e.get({url:"/collection/detail/getDate"}),updateDate:t=>e.get({url:`/collection/detail/updateDate?isDate=${t}`})};export{l as C};
