import{_ as O}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{i as S,a as $,g as G,b as s,c as N,d as X,e as U,f as V,h as q,P as I,_ as z,j as H}from"./bpmn-embedded-DWy7HXvQ.js";import{g as J,u as B}from"./index-C4DFiyBm.js";import{d as K,j as T,f as Q,o as j,h as R,w as W,u as f,ar as Y,m as Z,$ as ee,k as te,a as ne,O as ae,y as oe}from"./index-C8b06LRn.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./XTextButton-BSf0iZhI.js";import"./XButton-BOgar_Ex.js";import"./el-collapse-item-JANV_ocl.js";import"./index-CBsqkafF.js";import"./el-tree-select-E9FCZb0j.js";import"./index-C0LhU1J1.js";import"./index-Dz9lR_me.js";import"./index-Cgv48ZKs.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./constants-D3f7Z3TX.js";import"./index-SMELiJYy.js";import"./el-drawer-C5TFtzfV.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./index-B9cOKMOY.js";import"./formatTime-COZ9Bl52.js";function M(o,d,c,r,p,g,u,a,b,y,n,e){o=o||{},r.registerProvider(this),this._contextPad=r,this._modeling=p,this._elementFactory=g,this._connect=u,this._create=a,this._popupMenu=b,this._canvas=y,this._rules=n,this._translate=e,o.autoPlace!==!1&&(this._autoPlace=d.get("autoPlace",!1)),c.on("create.end",250,function(t){const l=t.context.shape;if(!q(t)||!r.isOpen(l))return;const i=r.getEntries(l);i.replace&&i.replace.action.click(t,l)})}function F(o,d,c){const r=o.$instanceOf(d);let p=!1;const g=o.eventDefinitions||[];return V(g,function(u){u.$type===c&&(p=!0)}),r&&p}M.$inject=["config.contextPad","injector","eventBus","contextPad","modeling","elementFactory","connect","create","popupMenu","canvas","rules","translate","elementRegistry"],M.prototype.getContextPadEntries=function(o){const d=this._contextPad,c=this._modeling,r=this._elementFactory,p=this._connect,g=this._create,u=this._popupMenu,a=this._canvas,b=this._rules,y=this._autoPlace,n=this._translate,e={};if(o.type==="label")return e;const t=o.businessObject;function l(v,h){p.start(v,h)}function i(v,h,m,x){function D(_,E){const k=r.createShape(s({type:v},x));g.start(_,k,{source:E})}return typeof m!="string"&&(x=m,m=n("Append {type}",{type:v.replace(/^bpmn:/,"")})),{group:"model",className:h,title:m,action:{dragstart:D,click:y?function(_,E){const k=r.createShape(s({type:v},x));y.append(E,k)}:D}}}function P(v){return function(h,m){c.splitLane(m,v),d.open(m,!0)}}if(S(t,["bpmn:Lane","bpmn:Participant"])&&$(t)){const v=G(o);s(e,{"lane-insert-above":{group:"lane-insert-above",className:"bpmn-icon-lane-insert-above",title:n("Add Lane above"),action:{click:function(h,m){c.addLane(m,"top")}}}}),v.length<2&&(o.height>=120&&s(e,{"lane-divide-two":{group:"lane-divide",className:"bpmn-icon-lane-divide-two",title:n("Divide into two Lanes"),action:{click:P(2)}}}),o.height>=180&&s(e,{"lane-divide-three":{group:"lane-divide",className:"bpmn-icon-lane-divide-three",title:n("Divide into three Lanes"),action:{click:P(3)}}})),s(e,{"lane-insert-below":{group:"lane-insert-below",className:"bpmn-icon-lane-insert-below",title:n("Add Lane below"),action:{click:function(h,m){c.addLane(m,"bottom")}}}})}N(t,"bpmn:FlowNode")&&(N(t,"bpmn:EventBasedGateway")?s(e,{"append.receive-task":i("bpmn:ReceiveTask","bpmn-icon-receive-task",n("Append ReceiveTask")),"append.message-intermediate-event":i("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-message",n("Append MessageIntermediateCatchEvent"),{eventDefinitionType:"bpmn:MessageEventDefinition"}),"append.timer-intermediate-event":i("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-timer",n("Append TimerIntermediateCatchEvent"),{eventDefinitionType:"bpmn:TimerEventDefinition"}),"append.condition-intermediate-event":i("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-condition",n("Append ConditionIntermediateCatchEvent"),{eventDefinitionType:"bpmn:ConditionalEventDefinition"}),"append.signal-intermediate-event":i("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-signal",n("Append SignalIntermediateCatchEvent"),{eventDefinitionType:"bpmn:SignalEventDefinition"})}):F(t,"bpmn:BoundaryEvent","bpmn:CompensateEventDefinition")?s(e,{"append.compensation-activity":i("bpmn:Task","bpmn-icon-task",n("Append compensation activity"),{isForCompensation:!0})}):N(t,"bpmn:EndEvent")||t.isForCompensation||F(t,"bpmn:IntermediateThrowEvent","bpmn:LinkEventDefinition")||X(t)||s(e,{"append.end-event":i("bpmn:EndEvent","bpmn-icon-end-event-none",n("Append EndEvent")),"append.gateway":i("bpmn:ExclusiveGateway","bpmn-icon-gateway-none",n("Append Gateway")),"append.append-task":i("bpmn:UserTask","bpmn-icon-user-task",n("Append Task")),"append.intermediate-event":i("bpmn:IntermediateThrowEvent","bpmn-icon-intermediate-event-none",n("Append Intermediate/Boundary Event"))})),u.isEmpty(o,"bpmn-replace")||s(e,{replace:{group:"edit",className:"bpmn-icon-screw-wrench",title:"\u4FEE\u6539\u7C7B\u578B",action:{click:function(v,h){const m=s(function(x){const D=a.getContainer(),A=d.getPad(x).html,_=D.getBoundingClientRect(),E=A.getBoundingClientRect(),k=E.top-_.top;return{x:E.left-_.left,y:k+E.height+5}}(h),{cursor:{x:v.x,y:v.y}});u.open(h,"bpmn-replace",m)}}}}),S(t,["bpmn:FlowNode","bpmn:InteractionNode","bpmn:DataObjectReference","bpmn:DataStoreReference"])&&s(e,{"append.text-annotation":i("bpmn:TextAnnotation","bpmn-icon-text-annotation"),connect:{group:"connect",className:"bpmn-icon-connection-multi",title:n("Connect using "+(t.isForCompensation?"":"Sequence/MessageFlow or ")+"Association"),action:{click:l,dragstart:l}}}),S(t,["bpmn:DataObjectReference","bpmn:DataStoreReference"])&&s(e,{connect:{group:"connect",className:"bpmn-icon-connection-multi",title:n("Connect using DataInputAssociation"),action:{click:l,dragstart:l}}}),N(t,"bpmn:Group")&&s(e,{"append.text-annotation":i("bpmn:TextAnnotation","bpmn-icon-text-annotation")});let w=b.allowed("elements.delete",{elements:[o]});return U(w)&&(w=w[0]===o),w&&s(e,{delete:{group:"edit",className:"bpmn-icon-trash",title:n("Remove"),action:{click:function(){c.removeElements([o])}}}}),e};const ie={__init__:["contextPadProvider"],contextPadProvider:["type",M]};function C(o,d,c,r,p,g,u,a){I.call(this,o,d,c,r,p,g,u,a,2e3)}const L=function(){};(L.prototype=I.prototype).getPaletteEntries=function(){const o={},d=this._create,c=this._elementFactory,r=this._spaceTool,p=this._lassoTool,g=this._handTool,u=this._globalConnect,a=this._translate;function b(e,t,l,i,P){function w(h){const m=c.createShape(s({type:e},P));d.start(h,m)}const v=e.replace(/^bpmn:/,"");return{group:t,className:l,title:i||a("Create {type}",{type:v}),action:{dragstart:w,click:w}}}function y(e){const t=c.createShape({type:"bpmn:SubProcess",x:0,y:0,isExpanded:!0}),l=c.createShape({type:"bpmn:StartEvent",x:40,y:82,parent:t});d.start(e,[t,l],{hints:{autoSelect:[l]}})}function n(e){d.start(e,c.createParticipantShape())}return s(o,{"hand-tool":{group:"tools",className:"bpmn-icon-hand-tool",title:"\u6FC0\u6D3B\u6293\u624B\u5DE5\u5177",action:{click:function(e){g.activateHand(e)}}},"lasso-tool":{group:"tools",className:"bpmn-icon-lasso-tool",title:a("Activate the lasso tool"),action:{click:function(e){p.activateSelection(e)}}},"space-tool":{group:"tools",className:"bpmn-icon-space-tool",title:a("Activate the create/remove space tool"),action:{click:function(e){r.activateSelection(e)}}},"global-connect-tool":{group:"tools",className:"bpmn-icon-connection-multi",title:a("Activate the global connect tool"),action:{click:function(e){u.toggle(e)}}},"tool-separator":{group:"tools",separator:!0},"create.start-event":b("bpmn:StartEvent","event","bpmn-icon-start-event-none",a("Create StartEvent")),"create.intermediate-event":b("bpmn:IntermediateThrowEvent","event","bpmn-icon-intermediate-event-none",a("Create Intermediate/Boundary Event")),"create.end-event":b("bpmn:EndEvent","event","bpmn-icon-end-event-none",a("Create EndEvent")),"create.exclusive-gateway":b("bpmn:ExclusiveGateway","gateway","bpmn-icon-gateway-none",a("Create Gateway")),"create.user-task":b("bpmn:UserTask","activity","bpmn-icon-user-task",a("Create User Task")),"create.data-object":b("bpmn:DataObjectReference","data-object","bpmn-icon-data-object",a("Create DataObjectReference")),"create.data-store":b("bpmn:DataStoreReference","data-store","bpmn-icon-data-store",a("Create DataStoreReference")),"create.subprocess-expanded":{group:"activity",className:"bpmn-icon-subprocess-expanded",title:a("Create expanded SubProcess"),action:{dragstart:y,click:y}},"create.participant-expanded":{group:"collaboration",className:"bpmn-icon-participant",title:a("Create Pool/Participant"),action:{dragstart:n,click:n}},"create.group":b("bpmn:Group","artifact","bpmn-icon-group",a("Create Group"))}),o},C.$inject=["palette","create","elementFactory","spaceTool","lassoTool","handTool","globalConnect","translate"],C.prototype=new L,C.prototype.constructor=C;const ce={__init__:["paletteProvider"],paletteProvider:["type",C]},re=K({name:"BpmModelEditor",__name:"index",setup(o){const d=ne(),{query:c}=ae(),r=oe(),p=T(void 0),g=T(null),u=T({simulation:!0,labelEditing:!1,labelVisible:!1,prefix:"flowable",headerButtonSize:"mini",additionalModel:[ie,ce]}),a=T(),b=e=>{setTimeout(()=>{g.value=e},10)},y=async e=>{const t={...a.value,bpmnXml:e};t.id?(await B(t),r.success("\u4FEE\u6539\u6210\u529F")):(await B(t),r.success("\u65B0\u589E\u6210\u529F")),n()},n=()=>{d.push({path:"/bpm/manager/model"})};return Q(async()=>{const e=c.modelId;if(!e)return void r.error("\u7F3A\u5C11\u6A21\u578B modelId \u7F16\u53F7");const t=await J(e);t.bpmnXml||(t.bpmnXml=` <?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.activiti.org/processdef">
  <process id="${t.key}" name="${t.name}" isExecutable="true" />
  <bpmndi:BPMNDiagram id="BPMNDiagram">
    <bpmndi:BPMNPlane id="${t.key}_di" bpmnElement="${t.key}" />
  </bpmndi:BPMNDiagram>
</definitions>`),a.value={...t,bpmnXml:void 0},p.value=t.bpmnXml}),(e,t)=>{const l=O;return j(),R(l,null,{default:W(()=>[f(p)!==void 0?(j(),R(f(z),Y({key:"designer",modelValue:f(p),"onUpdate:modelValue":t[0]||(t[0]=i=>Z(p)?p.value=i:null),value:f(p)},f(u),{keyboard:"",ref:"processDesigner",onInitFinished:b,additionalModel:f(u).additionalModel,onSave:y}),null,16,["modelValue","value","additionalModel"])):ee("",!0),te(f(H),{key:"penal",bpmnModeler:f(g),prefix:f(u).prefix,class:"process-panel",model:f(a)},null,8,["bpmnModeler","prefix","model"])]),_:1})}}});export{re as default};
