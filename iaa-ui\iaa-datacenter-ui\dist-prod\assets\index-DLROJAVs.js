import{aG as a,cI as e}from"./index-C8b06LRn.js";const t=async s=>await a.get({url:"/system/notify-message/page",params:s}),y=async s=>await a.get({url:"/system/notify-message/my-page",params:s}),i=async s=>await a.put({url:"/system/notify-message/update-read?"+e.stringify({ids:s},{indices:!1})}),n=async()=>await a.put({url:"/system/notify-message/update-all-read"}),m=async()=>await a.get({url:"/system/notify-message/get-unread-list"}),g=async()=>await a.get({url:"/system/notify-message/get-unread-count"});export{n as a,t as b,m as c,g as d,y as g,i as u};
