import{r as F,D as H,d as V,p as Y,y as j,j as m,o as A,h as E,w as d,k as f,u as i,v as P,x as I,m as O,H as R,L as U,__tla as L}from"./index-C8b06LRn.js";import{_ as N}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as q}from"./Form-CkLzRm65.js";import{g as z,c as B,u as G}from"./index-S60VZmQA.js";import{d as J}from"./formatTime-COZ9Bl52.js";import{e as K,u as Q}from"./useCrudSchemas-HzMMRa-v.js";import{r as a}from"./formRules-Upspu04w.js";let T,h,W=Promise.all([(()=>{try{return L}catch{}})()]).then(async()=>{let c,v,y;c=await K(),v=F({name:[a],code:[a],accountId:[a],label:[a],content:[a],params:[a],status:[a]}),y=F([{label:"\u6A21\u677F\u7F16\u7801",field:"code",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u6A21\u677F\u540D\u79F0",field:"name",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u6A21\u677F\u6807\u9898",field:"title"},{label:"\u6A21\u677F\u5185\u5BB9",field:"content",form:{component:"Editor",componentProps:{valueHtml:"",height:200}}},{label:"\u90AE\u7BB1\u8D26\u53F7",field:"accountId",width:"200px",formatter:(k,b,u)=>{var l;return(l=c.find(n=>n.id===u))==null?void 0:l.mail},search:{show:!0,component:"Select",api:()=>c,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"},style:{width:"240px"}}},form:{component:"Select",api:()=>c,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}}},{label:"\u53D1\u9001\u4EBA\u540D\u79F0",field:"nickname"},{label:"\u5F00\u542F\u72B6\u6001",field:"status",isSearch:!0,dictType:H.COMMON_STATUS,dictClass:"number",search:{componentProps:{style:{width:"240px"}}}},{label:"\u5907\u6CE8",field:"remark",isTable:!1},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isForm:!1,formatter:J,search:{show:!0,component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss",type:"daterange",defaultTime:[new Date("1 00:00:00"),new Date("1 23:59:59")],style:{width:"240px"}}}},{label:"\u64CD\u4F5C",field:"action",isForm:!1}]),{allSchemas:h}=Q(y),T=V({name:"SystemMailTemplateForm",__name:"MailTemplateForm",emits:["success"],setup(k,{expose:b,emit:u}){const{t:l}=Y(),n=j(),t=m(!1),w=m(""),s=m(!1),_=m(""),r=m();b({open:async(o,e)=>{if(t.value=!0,w.value=l("action."+o),_.value=o,e){s.value=!0;try{const p=await z(e);r.value.setValues(p)}finally{s.value=!1}}}});const D=u,M=async()=>{if(r&&await r.value.getElFormRef().validate()){s.value=!0;try{const o=r.value.formModel;_.value==="create"?(await B(o),n.success(l("common.createSuccess"))):(await G(o),n.success(l("common.updateSuccess"))),t.value=!1,D("success")}finally{s.value=!1}}};return(o,e)=>{const p=q,S=R,g=N,C=U;return A(),E(g,{modelValue:i(t),"onUpdate:modelValue":e[1]||(e[1]=x=>O(t)?t.value=x:null),"max-height":500,scroll:!0,title:i(w),width:800},{footer:d(()=>[f(S,{disabled:i(s),type:"primary",onClick:M},{default:d(()=>e[2]||(e[2]=[P("\u786E \u5B9A")])),_:1},8,["disabled"]),f(S,{onClick:e[0]||(e[0]=x=>t.value=!1)},{default:d(()=>e[3]||(e[3]=[P("\u53D6 \u6D88")])),_:1})]),default:d(()=>[I(f(p,{ref_key:"formRef",ref:r,rules:i(v),schema:i(h).formSchema},null,8,["rules","schema"]),[[C,i(s)]])]),_:1},8,["modelValue","title"])}}})});export{T as _,W as __tla,h as a};
