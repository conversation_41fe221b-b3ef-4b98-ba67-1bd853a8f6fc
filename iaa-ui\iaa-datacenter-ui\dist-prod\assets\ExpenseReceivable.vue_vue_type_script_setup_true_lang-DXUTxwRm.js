import{B as C}from"./index-CSCiSrUr.js";import{d as D,j as a,au as j,aF as x,o as d,c as s,l as b,k as o,w as h,F as R,g as S,t as i,u as r}from"./index-C8b06LRn.js";const z={class:"mt-20px"},E={class:"mt-8px text-right text-6 text-gray-500"},B=D({__name:"ExpenseReceivable",props:{orderCodes:{}},setup(y){const u=y,c=a(!1),l=a([]),m=a(0),w=a(),f=a(0),v=a(0),p=a(0),A=a([{dateStr:"\u5408\u8BA1\u603B\u548C",cost:f,collectionAmount:v,receivableAmount:p}]);return j(()=>u.orderCodes,()=>{(async()=>{c.value=!0;try{const g=await C.getExpenseReceivablePage({orderCodes:u.orderCodes||[]})||[];l.value=(g||[]).map(e=>({dateStr:e.dateStr,project:e.label,cost:e.cost,collectionAmount:e.collectionAmount,receivableAmount:e.receivableAmount,currency:e.currency,currencyAmount:e.currencyAmount,rate:e.rate,expenseDetails:e.expenseDetails})),m.value=l.value.length,f.value=l.value.reduce((e,t)=>e+t.cost,0).toFixed(3),v.value=l.value.reduce((e,t)=>e+t.collectionAmount,0).toFixed(3),p.value=l.value.reduce((e,t)=>e+t.receivableAmount,0).toFixed(3)}finally{c.value=!1}})()},{immediate:!0}),(g,e)=>{const t=x("vxe-column"),k=x("vxe-table");return d(),s("div",null,[b("div",z,[o(k,{"row-config":{height:25,keyField:"id"},ref_key:"tableRef",ref:w,data:r(l),"header-cell-style":{padding:0},border:"",stripe:"",align:"center","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:r(c),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-data":r(A),"footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},tabindex:"0",size:"mini"},{default:h(()=>[o(t,{field:"project",title:"\u9879\u76EE",width:"120"}),o(t,{field:"cost",title:"\u8D39\u7528",width:"120"}),o(t,{field:"currency",title:"\u8D39\u7528\u5E01\u79CD",width:"120"}),o(t,{field:"collectionAmount",title:"\u6536\u6B3E\u91D1\u989D",width:"120"}),o(t,{field:"expenseDetails",title:"\u6536\u6B3E\u660E\u7EC6","min-width":"200"},{default:h(({row:F})=>[(d(!0),s(R,null,S(F.expenseDetails,(n,_)=>(d(),s("span",{key:_},i(n.currency)+"\uFF1A"+i(n.amount)+"\uFF0C\u6C47\u7387\uFF1A"+i(n.rate||"-")+"\uFF1B ",1))),128))]),_:1}),o(t,{field:"receivableAmount",title:"\u5E94\u6536\u91D1\u989D\uFF08\u672C\u5E01\uFF09","min-width":"120"})]),_:1},8,["data","loading","footer-data"])]),b("div",E,"\u5171 "+i(r(m))+" \u6761",1)])}}});export{B as _};
