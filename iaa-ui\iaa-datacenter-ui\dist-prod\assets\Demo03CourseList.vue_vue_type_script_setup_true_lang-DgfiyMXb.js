import{d as c,p as w,y as v,j as o,f as _,o as l,h as n,w as p,x as g,u as r,k as e,J as b,K as y,L as h}from"./index-C8b06LRn.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{d as j}from"./formatTime-COZ9Bl52.js";import{a as I}from"./index-ANJNvVVB.js";const L=c({__name:"Demo03CourseList",props:{studentId:{}},setup(i){w(),v();const m=i,t=o(!1),s=o([]);return _(()=>{(async()=>{t.value=!0;try{s.value=await I(m.studentId)}finally{t.value=!1}})()}),(k,C)=>{const a=b,u=y,d=x,f=h;return l(),n(d,null,{default:p(()=>[g((l(),n(u,{data:r(s),stripe:!0,"show-overflow-tooltip":!0},{default:p(()=>[e(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),e(a,{label:"\u5206\u6570",align:"center",prop:"score"}),e(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:r(j),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[f,r(t)]])]),_:1})}}});export{L as _};
