import{_ as re}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ne}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{d as T,aj as g,j as x,au as W,o,c as m,l as U,F as _,g as L,t as O,_ as D,Y as K,f as ie,d0 as se,bf as E,k as w,w as f,u as n,dA as ue,dC as de,dD as ce,m as pe,h as p,v as N,$ as A,di as he,aK as ge,dE as me,b4 as fe,H as ve,bm as ye,a9 as we,b5 as be,at as ke,J as _e,z as Ce,A as xe,C as Se,S as $e,K as Ve}from"./index-C8b06LRn.js";const Re={class:"table-right-menu"},ze=["onClick"],qe=D(T({__name:"RightMenu",props:{items:g.arrayOf(Array).isRequired,clickXY:g.any.isRequired},setup(Y){const R=Y,z=x(null),t=C=>{C.button===0&&(z.value.style.display="none",document.removeEventListener("mouseup",t))};return W(()=>R.clickXY.position,C=>{let S=C.x,b=C.y,k=window.innerWidth,r=window.innerHeight,h=30*R.items.length,u=z.value;u.style.display="block",u.style.top=(b+h>r?r-h-10:b)+"px",u.style.left=(S+100>k?k-100-10:S)+"px",document.addEventListener("mouseup",t,!1)}),(C,S)=>(o(),m("div",{id:"right-menu",ref_key:"rightMenuRef",ref:z},[U("ul",Re,[(o(!0),m(_,null,L(R.items,b=>(o(),m("li",{key:b.text,onClick:k=>(r=>{var h,u;r.fn((h=r==null?void 0:r.params)==null?void 0:h.row,(u=r==null?void 0:r.params)==null?void 0:u.column)})(b)},O(b.text),9,ze))),128))])],512))}}),[["__scopeId","data-v-ca22afd8"]]),Le={class:"header-bar"},Ae={class:"visible-column"},Ue=D(T({__name:"index",props:{data:g.oneOfType([Array]).isRequired,columns:g.oneOfType([Array]).isRequired,page:g.any.isRequired,total:g.number.isRequired,height:g.oneOfType([String,Number]).def(500),excludeQueryColumn:g.array.def([]),stripe:g.bool.def(!1),highlightCurrent:g.bool.def(!1),rightMenuItems:g.arrayOf(Array).def([]),operationWidth:g.number.def(100)},emits:["pagination","search","refresh","current","clrear"],setup(Y,{expose:R,emit:z}){const t=Y,C=x({}),S=x([]),b=x(0),k=K(()=>{if(r.value&&r.value.length>0)for(let l in t.page)r.value[0].hasOwnProperty(l)&&(r.value[0][l]=t.page[l]);return t.page}),r=K(()=>[t.columns.reduce((l,a)=>(l[a.prop]="",l),{})]),h=z,u=x(),y=x(),$=x([]),q=x([]),H=l=>{q.value=[];for(let a of t.columns)l.includes(a.prop)&&q.value.push(a)},P=(l,a,i)=>{const d=q.value.find(v=>v.prop===i.rawColumnKey);d&&(d.width=l)},M=l=>{let a={};for(let i in l)t.excludeQueryColumn.includes(i)||(a[i]=l[i]);h("search",a)},X=()=>{if(r.value&&r.value.length>0){for(let l in r.value[0])r.value[0][l]="";h("search",r.value[0]),h("clrear"),b.value+=1}},j=l=>{var i,d,v;const a=(i=y.value)==null?void 0:i.getSelectionRows();l.length>0&&a.length!=t.data.length?(d=y.value)==null||d.toggleAllSelection():l.length==0&&((v=y.value)==null||v.clearSelection())},Q=l=>{var i,d,v;const a=(i=u.value)==null?void 0:i.getSelectionRows();a.length==0&&l.length==t.data.length?(d=u.value)==null||d.toggleAllSelection():a.length==1&&l.length!=t.data.length&&((v=y.value)==null||v.clearSelection())};R({getSelectionRows:()=>{var l;return(l=y.value)==null?void 0:l.getSelectionRows()}});const F=(l,a,i,d)=>{C.value={position:{x:d.clientX,y:d.clientY}},S.value=[],t.rightMenuItems.forEach(v=>{S.value.push({...v,params:{row:l,column:a}})})};return ie(()=>{if(u.value&&y.value){let l=u.value.$el.querySelector(".el-scrollbar__wrap"),a=y.value.$el.querySelector(".el-scrollbar__wrap");l&&a&&(a.addEventListener("scroll",()=>{l.scrollLeft=a.scrollLeft}),l.addEventListener("scroll",()=>{a.scrollLeft=l.scrollLeft}))}}),se(()=>{if(u.value&&y.value){let l=u.value.$el.querySelector(".el-scrollbar__wrap"),a=y.value.$el.querySelector(".el-scrollbar__wrap");l.removeEventListener("scroll"),a.removeEventListener("scroll")}}),W(()=>t.columns,l=>{l&&($.value=l.map(a=>a.prop),H($.value))},{immediate:!0}),(l,a)=>{const i=ve,d=ye,v=we,J=be,B=ke,V=_e,G=Ce,Z=xe,ee=Se,le=$e,I=Ve,ae=ne,oe=re;return o(),m("div",null,[U("div",Le,[U("div",null,[E(l.$slots,"x-table-header",{},void 0,!0)]),U("div",Ae,[w(d,{content:"\u6E05\u7A7A\u7B5B\u9009"},{default:f(()=>[w(i,{icon:n(ue),circle:"",size:"small",onClick:X},null,8,["icon"])]),_:1}),w(d,{content:"\u5237\u65B0"},{default:f(()=>[w(i,{icon:n(de),circle:"",size:"small",onClick:a[0]||(a[0]=e=>h("refresh"))},null,8,["icon"])]),_:1}),w(B,{trigger:"click",placement:"bottom-start"},{reference:f(()=>[w(i,{icon:n(ce),circle:"",size:"small"},null,8,["icon"])]),default:f(()=>[w(J,{modelValue:n($),"onUpdate:modelValue":a[1]||(a[1]=e=>pe($)?$.value=e:null),size:"small",onChange:a[2]||(a[2]=e=>H(n($)))},{default:f(()=>[(o(!0),m(_,null,L(t.columns,e=>(o(),p(v,{key:e.prop,label:e.prop,value:e.prop},{default:f(()=>[N(O(e.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),(o(),p(I,{data:n(r),"header-cell-style":{fontSize:"12px",padding:"4px",backgroundColor:"#fcfcfc",borderColor:"#ccc",color:"#333"},"cell-style":{padding:0,borderColor:"#ccc"},border:"",ref_key:"queryTableRef",ref:u,key:n(b),class:"query-table",onHeaderDragend:P,onSelectAll:j},{default:f(()=>[l.$slots.selection?(o(),p(V,{key:0,width:"55",fixed:"left",type:"selection",align:"center"})):A("",!0),(o(!0),m(_,null,L(n(q),e=>(o(),p(V,{key:e.prop,label:e.label,width:e.width==-1?"auto":e.width,align:e.align},{default:f(({row:c})=>[e.noSearch?A("",!0):(o(),m(_,{key:0},[e.dict?(o(),p(Z,{key:0,modelValue:c[e.prop],"onUpdate:modelValue":s=>c[e.prop]=s,onChange:s=>M(c),size:"small",clearable:""},{default:f(()=>[(o(!0),m(_,null,L(n(he)(e.dict),(s,te)=>(o(),p(G,{key:te,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(o(),m(_,{key:1},[l.$slots[e.prop+"-search"]?E(l.$slots,`${e.prop}-search`,{key:0,row:c,column:e},void 0,!0):e.date?(o(),p(ee,{key:1,modelValue:c[e.prop],"onUpdate:modelValue":s=>c[e.prop]=s,"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F",onChange:s=>M(c),clearable:"",size:"small",style:ge(`width:${e.width==-1?240:e.width}px`)},null,8,["modelValue","onUpdate:modelValue","onChange","style"])):(o(),p(le,{key:2,modelValue:c[e.prop],"onUpdate:modelValue":s=>c[e.prop]=s,onChange:s=>M(c),size:"small","suffix-icon":n(me)},null,8,["modelValue","onUpdate:modelValue","onChange","suffix-icon"]))],64))],64))]),_:2},1032,["label","width","align"]))),128)),l.$slots.operation?(o(),p(V,{key:1,label:"\u64CD\u4F5C",align:"center",width:t.operationWidth,fixed:"right"},null,8,["width"])):A("",!0)]),_:3},8,["data"])),w(I,{data:t.data,"show-header":!1,"cell-style":{fontSize:"12px",padding:0,color:"rgb(51,51,51)"},"show-overflow-tooltip":"",border:"","scrollbar-always-on":"",ref_key:"scrollTableRef",ref:y,height:t.height,stripe:t.stripe,"highlight-current-row":t.highlightCurrent,onCurrentChange:a[3]||(a[3]=(e,c)=>h("current",e)),onContextmenu:a[4]||(a[4]=fe(()=>{},["prevent"])),onCellContextmenu:F,onSelectionChange:Q},{default:f(()=>[l.$slots.selection?(o(),p(V,{key:0,width:"55",fixed:"left",type:"selection",align:"center"})):A("",!0),(o(!0),m(_,null,L(n(q),(e,c)=>(o(),p(V,{key:c,label:e.label,prop:e.prop,width:e.width==-1?"auto":e.width,align:e.align},{default:f(({row:s})=>[e.dict?(o(),p(ae,{key:0,type:e.dict,value:s[e.prop]},null,8,["type","value"])):(o(),m(_,{key:1},[l.$slots[e.prop]?E(l.$slots,e.prop,{key:0,row:s,column:e},void 0,!0):(o(),m(_,{key:1},[N(O(s[e.prop]),1)],64))],64))]),_:2},1032,["label","prop","width","align"]))),128)),l.$slots.operation?(o(),p(V,{key:1,label:"\u64CD\u4F5C",align:"center",width:t.operationWidth,fixed:"right"},{default:f(({row:e})=>[E(l.$slots,"operation",{row:e},void 0,!0)]),_:3},8,["width"])):A("",!0)]),_:3},8,["data","height","stripe","highlight-current-row"]),w(oe,{limit:n(k).pageSize,"onUpdate:limit":a[5]||(a[5]=e=>n(k).pageSize=e),page:n(k).pageNo,"onUpdate:page":a[6]||(a[6]=e=>n(k).pageNo=e),total:t.total,onPagination:a[7]||(a[7]=e=>h("pagination",n(k))),size:"small"},null,8,["limit","page","total"]),w(qe,{items:n(S),"click-x-y":n(C)},null,8,["items","click-x-y"])])}}}),[["__scopeId","data-v-29190a78"]]);export{Ue as _};
