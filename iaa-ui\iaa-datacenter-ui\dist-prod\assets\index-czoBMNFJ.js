import{aG as s}from"./index-C8b06LRn.js";const n=async a=>await s.get({url:"/bpm/process-instance/my-page",params:a}),r=async a=>await s.get({url:"/bpm/process-instance/manager-page",params:a}),c=async a=>await s.post({url:"/bpm/process-instance/create",data:a}),p=async(a,e)=>{const t={id:a,reason:e};return await s.delete({url:"/bpm/process-instance/cancel-by-start-user",data:t})},i=async(a,e)=>{const t={id:a,reason:e};return await s.delete({url:"/bpm/process-instance/cancel-by-admin",data:t})},o=async a=>await s.get({url:"/bpm/process-instance/get?id="+a}),m=async a=>await s.get({url:"/bpm/process-instance/copy/page",params:a}),l=async(a,e)=>{const t=a?"?processInstanceId="+a:"?processDefinitionId="+e;return await s.get({url:"bpm/process-instance/get-approval-detail"+t})},d=async a=>await s.get({url:"/bpm/process-instance/get-form-fields-permission",params:a});export{c as a,l as b,p as c,d,n as e,r as f,o as g,i as h,m as i};
