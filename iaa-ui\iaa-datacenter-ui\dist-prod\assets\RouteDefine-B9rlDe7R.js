import{_ as I}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as A,r as w,j as r,f as E,u as l,aF as h,o as z,c as N,k as o,w as i,v as p,l as v,F as R,$ as M,aD as $,aq as B,aC as G,H as J,S as K,_ as L}from"./index-C8b06LRn.js";import{D as O}from"./index-a4vw0wEg.js";import{R as Q}from"./index-BqCcY1XT.js";import{_ as W}from"./RouteForm.vue_vue_type_script_setup_true_lang-BFGK4v_u.js";import"./index-Cl43piKd.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const X={class:"h-[calc(100%-80px)]"},Y=L(A({__name:"RouteDefine",emits:["search"],setup(Z,{emit:U}){const t=w({type:1,showTop:!0,pageNo:1,pageSize:30,routeCode:"",routeName:""}),y=r(0),C=r(),c=r(),_=r([]),f=r(),g=r(!1),V=r([]),m=()=>{t.pageNo=1,d()},d=async()=>{g.value=!0;try{const s=await Q.getRoutePage(t);V.value=s.list,y.value=s.total}finally{g.value=!1}},D=w({body:{options:[[{code:"selected",name:"\u5F53\u524D\u5DE5\u827A\u8DEF\u7EBF\u7684\u7269\u6599"}]]}}),F=({menu:s,row:e})=>{c.value&&s.code==="selected"&&S("search",e.id)},S=U;return E(()=>{var s;(s=l(c))==null||s.connect(l(C)),(async()=>{const e=await O.listDefine();_.value=e})(),d()}),(s,e)=>{const b=$,T=B,q=G,x=J,H=h("vxe-toolbar"),k=K,n=h("vxe-column"),P=h("vxe-table"),j=I;return z(),N(R,null,[o(H,{class:"h-40px",ref_key:"toolbarRef",ref:C,custom:""},{buttons:i(()=>[o(T,{size:"small",modelValue:l(t).type,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).type=a),onChange:d},{default:i(()=>[o(b,{label:"\u4EC5\u663E\u793A\u5355\u5934",value:1}),o(b,{label:"\u660E\u7EC6",value:2})]),_:1},8,["modelValue"]),o(q,{modelValue:l(t).showTop,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).showTop=a),style:{"--el-switch-on-color":"#13ce66","--el-switch-off-color":"#ff4949"},"inline-prompt":"","active-text":"\u53EA\u770B\u6700\u65B0\u7248\u672C","inactive-text":"\u663E\u793A\u6240\u6709\u7248\u672C",size:"small",onChange:m},{open:i(()=>e[8]||(e[8]=[p("\u542F\u7528")])),close:i(()=>e[9]||(e[9]=[p("\u7981\u7528")])),_:1},8,["modelValue"]),o(x,{type:"success",size:"small",onClick:m},{default:i(()=>e[10]||(e[10]=[p("\u5237\u65B0")])),_:1}),o(x,{type:"primary",size:"small",onClick:e[2]||(e[2]=a=>{var u;return(u=l(f))==null?void 0:u.openForm()})},{default:i(()=>e[11]||(e[11]=[p("\u65B0\u589E")])),_:1})]),_:1},512),v("div",X,[o(P,{height:"100%","header-cell-style":{padding:0,height:"30px"},"cell-style":{padding:0,height:"30px",cursor:"pointer",color:"#232323"},"row-config":{isHover:!0,isCurrent:!0,height:30},align:"center",ref_key:"tableRef",ref:c,border:"",stripe:"",loading:l(g),data:l(V),onCellClick:e[5]||(e[5]=({row:a})=>{var u;return(u=l(f))==null?void 0:u.openForm(a.id)}),"menu-config":l(D),onMenuClick:F},{default:i(()=>[o(n,{title:"\u8DEF\u7EBF\u7F16\u7801",field:"routeCode"},{header:i(()=>[e[12]||(e[12]=v("div",null,"\u8DEF\u7EBF\u7F16\u7801",-1)),o(k,{modelValue:l(t).routeCode,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).routeCode=a),size:"small",onChange:m},null,8,["modelValue"])]),_:1}),o(n,{title:"\u8DEF\u7EBF\u540D\u79F0",field:"routeName"},{header:i(()=>[e[13]||(e[13]=v("div",null,"\u8DEF\u7EBF\u540D\u79F0",-1)),o(k,{modelValue:l(t).routeName,"onUpdate:modelValue":e[4]||(e[4]=a=>l(t).routeName=a),size:"small",onChange:m},null,8,["modelValue"])]),_:1}),o(n,{title:"\u8DEF\u7EBF\u7248\u672C",field:"routeVersion"}),o(n,{title:"\u8DEF\u7EBF\u63CF\u8FF0",field:"routeDescription"}),l(t).type===2?(z(),N(R,{key:0},[o(n,{title:"\u5DE5\u827A\u7F16\u7801",field:"defineCode"}),o(n,{title:"\u5DE5\u827A\u540D\u79F0",field:"defineName"}),o(n,{title:"\u6807\u51C6\u5DE5\u65F6\uFF08\u5206\uFF09",field:"timeConsuming"}),o(n,{title:"\u5DE5\u827A\u5907\u6CE8",field:"remarks"})],64)):M("",!0)]),_:1},8,["loading","data","menu-config"])]),o(j,{class:"h-40px !m-b-0",size:"small",total:l(y),page:l(t).pageNo,"onUpdate:page":e[6]||(e[6]=a=>l(t).pageNo=a),limit:l(t).pageSize,"onUpdate:limit":e[7]||(e[7]=a=>l(t).pageSize=a),onPagination:d},null,8,["total","page","limit"]),o(W,{ref_key:"routeFormRef",ref:f,"define-list":l(_),onSuccess:d},null,8,["define-list"])],64)}}}),[["__scopeId","data-v-eb86eb4d"]]);export{Y as default};
