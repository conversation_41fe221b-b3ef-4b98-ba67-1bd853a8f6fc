import{_ as ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as fe}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as ue}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as ce,j as v,y as me,f as he,u as t,aF as E,T as ve,o as y,h as L,w as c,l as D,k as e,m as pe,c as N,F as _,$ as P,v as z,x as Q,t as G,ax as ge,cj as T,dl as F,aD as ye,aq as we,aC as be,C as Ne,H as xe,G as Me,aa as ke}from"./index-C8b06LRn.js";import{R as g}from"./index-rrqDtE6o.js";import{c as Ce}from"./vxeCustom-D2Re1O-c.js";import{e as R,t as d,d as U,f as W,g as X,n as p}from"./Filter-Dzz2caxb.js";import{_ as De}from"./PermissionForm.vue_vue_type_script_setup_true_lang-BbCkMcuR.js";import{d as A}from"./download-D5Lb_h0f.js";const _e={class:"h-[calc(100vh-135px)] overflow-auto"},Ye={key:0},Se={class:"ml-30px"},ze={class:"h-[calc(100%-100px)]"},Re=ce({__name:"PC",setup(Ve){const i=v({docNo:[{data:""}],srcDocNo:[{data:""}],customerCode:[{data:""}],customerName:[{data:""}],sellerName:[{data:""}],remark:[{data:""}],bankAccount:[{data:""}],businessDate:[{data:[]}],itemCode:[{data:""}],itemName:[{data:""}],spec:[{data:""}],recpaymentMoney:[{data:{condition:"10",value:void 0}}],shipMoney:[{data:{condition:"10",value:void 0}}],rmaMoney:[{data:{condition:"10",value:void 0}}],totalMoney:[{data:{condition:"10",value:void 0}}],money:[{data:{condition:"10",value:void 0}}],domesticMoney:[{data:{condition:"10",value:void 0}}]}),u=v("balance"),s=v({pageNo:1,pageSize:30,hasStatistics:!0,sorting:[]}),x=v(0),M=v({}),k=v([]),H=v(),w=v(),O=v(!1),K=v(),m=v({visible:!1,type:"rec",list:[],statistics:{}}),$=async(r,a)=>{m.value.type=a;let o=ge(s.value);switch(o.org=r.org,o.seller=r.seller,o.customer=r.customer,o.pageSize=-1,a){case"rec":const f=await g.getRecPaymentPage(o);m.value.list=f.list,m.value.statistics=f.statistics;break;case"ship":const h=await g.getShipPage(o);m.value.list=h.list,m.value.statistics=h.statistics;break;case"rma":const b=await g.getRmaPage(o);m.value.list=b.list,m.value.statistics=b.statistics}m.value.visible=!0},Z=async()=>{setTimeout(()=>{var r;s.value={pageNo:1,pageSize:30,hasStatistics:!0,sorting:[]},i.value={docNo:[{data:""}],srcDocNo:[{data:""}],customerCode:[{data:""}],customerName:[{data:""}],sellerName:[{data:""}],remark:[{data:""}],bankAccount:[{data:""}],businessDate:[{data:[]}],itemCode:[{data:""}],itemName:[{data:""}],spec:[{data:""}],recpaymentMoney:[{data:{condition:"10",value:void 0}}],shipMoney:[{data:{condition:"10",value:void 0}}],rmaMoney:[{data:{condition:"10",value:void 0}}],totalMoney:[{data:{condition:"10",value:void 0}}],money:[{data:{condition:"10",value:void 0}}],domesticMoney:[{data:{condition:"10",value:void 0}}]},(r=w.value)==null||r.clearFilter(),B(),k.value=[],x.value=0,M.value=void 0,Y()},500)},V=async()=>{O.value=!0;try{if(u.value==="rec"){const r=await g.getRecPaymentPage(s.value);x.value=r.total,k.value=r.list,M.value=r.statistics}else if(u.value==="ship"){const r=await g.getShipPage(s.value);x.value=r.total,k.value=r.list,M.value=r.statistics}else if(u.value==="rma"){const r=await g.getRmaPage(s.value);x.value=r.total,k.value=r.list,M.value=r.statistics}else if(u.value==="balance"){const r=await g.getTotalPage(s.value);x.value=r.total,k.value=r.list,M.value=r.statistics}}finally{O.value=!1}},j=v(!1),ee=me(),te=async()=>{try{if(await ee.exportConfirm(),j.value=!0,u.value==="rec"){const r=await g.exportRecPayment(s.value);A.excel(r,`\u6536\u6B3E\u660E\u7EC6\u8868${T().unix()}.xls`)}else if(u.value==="ship"){const r=await g.exportShip(s.value);A.excel(r,`\u51FA\u8D27\u660E\u7EC6\u8868${T().unix()}.xls`)}else if(u.value==="rma"){const r=await g.exportRma(s.value);A.excel(r,`\u9000\u8D27\u660E\u7EC6\u8868${T().unix()}.xls`)}else if(u.value==="balance"){const r=await g.exportTotal(s.value);A.excel(r,`\u6536\u6B3E\u72B6\u51B5\u8868${T().unix()}.xls`)}}catch{}finally{j.value=!1}},Y=()=>{s.value.pageNo=1,V()},le=r=>{const a=["orgName","property","currency"],o={};r.filterList.forEach(f=>{const{field:h,values:b,datas:C}=f;a.includes(h)&&b.length>0?o[h]=b:C.length>0&&(o[h]=C[0])}),Object.keys(s.value).forEach(f=>{["pageNo","pageSize","hasStatistics","sorting","businessDate"].includes(f)||r.filterList.some(h=>h.field===f)||(s.value[f]=void 0)}),Object.assign(s.value,o),Y()},ie=({field:r,order:a})=>{if(a){const o=s.value.sorting.find(f=>f.field==r);o?o.order=a:s.value.sorting.push({field:r,order:a})}else s.value.sorting=s.value.sorting.filter(o=>o.field!=r);Y()},B=()=>{var a,o;const r=(a=t(w).getColumns().find(f=>f.field==="businessDate"))==null?void 0:a.filters[0];if(r?(r.data=[F().startOf("years").format("YYYY-MM-DD"),F().format("YYYY-MM-DD")],t(w).updateFilterOptionStatus(r,!0),s.value.businessDate=r.data):u.value==="balance"?s.value.dateType="all":s.value.businessDate=[F().startOf("years").format("YYYY-MM-DD"),F().format("YYYY-MM-DD")],u.value=="balance"){const f=(o=t(w).getColumns().find(h=>h.field==="totalMoney"))==null?void 0:o.filters[0];f&&(f.data={condition:"15",value:0},t(w).updateFilterOptionStatus(f,!0),s.value.totalMoney=f.data)}};return he(()=>{setTimeout(()=>{var r;(r=t(w))==null||r.connect(t(H)),B(),V()},500)}),(r,a)=>{const o=ye,f=we,h=be,b=Ne,C=xe,re=Me,ae=E("vxe-toolbar"),l=E("vxe-column"),q=ke,I=E("vxe-table"),se=ue,de=fe,oe=ne,J=ve("hasPermi");return y(),L(oe,null,{default:c(()=>[D("div",_e,[e(ae,{custom:"",ref_key:"toolbarRef",ref:H,size:"mini"},{buttons:c(()=>[a[10]||(a[10]=D("span",{class:"text-#000"},"\u5355\u636E\u7C7B\u578B\uFF1A",-1)),e(f,{modelValue:t(u),"onUpdate:modelValue":a[0]||(a[0]=n=>pe(u)?u.value=n:null),onChange:Z,size:"small"},{default:c(()=>[e(o,{label:"\u4F59\u989D\u660E\u7EC6",value:"balance"}),e(o,{label:"\u6536\u6B3E\u5355",value:"rec"}),e(o,{label:"\u51FA\u8D27\u5355",value:"ship"}),e(o,{label:"\u9000\u8D27\u5355",value:"rma"})]),_:1},8,["modelValue"]),a[11]||(a[11]=D("span",{class:"ml-30px text-#000"},"\u5F00\u542F\u7EDF\u8BA1\uFF1A",-1)),e(h,{modelValue:t(s).hasStatistics,"onUpdate:modelValue":a[1]||(a[1]=n=>t(s).hasStatistics=n),onChange:V,size:"small"},null,8,["modelValue"]),t(u)==="balance"?(y(),N(_,{key:0},[e(f,{class:"ml-30px",modelValue:t(s).dateType,"onUpdate:modelValue":a[2]||(a[2]=n=>t(s).dateType=n),size:"small"},{default:c(()=>[e(o,{label:"\u5168\u90E8",value:"all"}),e(o,{label:"\u65F6\u95F4\u8303\u56F4",value:"range"})]),_:1},8,["modelValue"]),t(s).dateType==="range"?(y(),N("div",Ye,[a[8]||(a[8]=D("span",{class:"ml-30px text-#000"},"\u4E1A\u52A1\u65E5\u671F\uFF1A",-1)),e(b,{type:"daterange","value-format":"YYYY-MM-DD",modelValue:t(s).businessDate,"onUpdate:modelValue":a[3]||(a[3]=n=>t(s).businessDate=n),class:"!w-240px",clearable:!1,onChange:Y,size:"small"},null,8,["modelValue"])])):P("",!0)],64)):P("",!0),D("div",Se,[e(C,{type:"primary",onClick:Y,size:"small"},{default:c(()=>a[9]||(a[9]=[z("\u5237\u65B0")])),_:1})])]),tools:c(()=>[Q((y(),L(C,{type:"primary",link:"",class:"mr-10px",onClick:a[4]||(a[4]=n=>{var S;return(S=t(K))==null?void 0:S.openForm()}),size:"small"},{default:c(()=>a[12]||(a[12]=[z(" \u6743\u9650\u7BA1\u7406 ")])),_:1})),[[J,["receiving:payment:permission"]]]),Q((y(),L(C,{circle:"",class:"mr-10px",onClick:te,loading:t(j),size:"small"},{default:c(()=>[e(re,{icon:"ep:download"})]),_:1},8,["loading"])),[[J,["receiving:payment:download"]]])]),_:1},512),D("div",ze,[e(I,{"custom-config":t(Ce),"header-cell-config":{height:24},"cell-config":{height:24},"column-config":{resizable:!0,maxFixedSize:0},data:t(k),loading:t(O),"show-footer":t(s).hasStatistics,"footer-data":[t(M)],"footer-cell-config":{height:30},"footer-cell-style":{background:"var(--el-color-warning-light-9)",border:"1px solid #ebeef5"},"filter-config":{remote:!0},"sort-config":{remote:!0,multiple:!0},align:"center",border:"","show-overflow":"","show-footer-overflow":"",stripe:"",height:"100%",ref_key:"tableRef",ref:w,id:"ReceivingPaymentTable",onFilterChange:le,onSortChange:ie,size:"mini"},{default:c(()=>[t(u)==="rec"?(y(),N(_,{key:0},[e(l,{title:"\u7EC4\u7EC7",field:"orgName",width:"80",filters:R},null,8,["filters"]),e(l,{title:"\u5355\u53F7",field:"docNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u65E5\u671F",field:"businessDate",width:"100",filters:t(i).businessDate,"filter-render":U},null,8,["filters","filter-render"]),e(l,{title:"\u6765\u6E90\u5355\u636E",field:"srcDocNo",width:"140",filters:t(i).srcDocNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u7F16\u7801",field:"customerCode",width:"100",filters:t(i).customerCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName","min-width":"200",align:"left",filters:t(i).customerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u7528\u9014",field:"property",width:"80",filters:W},null,8,["filters"]),e(l,{title:"\u5E01\u79CD",field:"currency",width:"80",filters:X},null,8,["filters"]),e(l,{title:"\u72B6\u6001",field:"status",width:"80"}),e(l,{title:"\u5907\u6CE8",field:"\u5907\u6CE8","min-width":"200",filters:t(i).remark,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u91D1\u989D",field:"money",width:"100",sortable:"",filters:t(i).money,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u672C\u5E01",field:"domesticMoney",width:"100",sortable:"",filters:t(i).domesticMoney,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u6C47\u7387",field:"exchangeRate",width:"80",sortable:""}),e(l,{title:"\u6536\u6B3E\u8D26\u53F7",field:"bankAccount",width:"200",filters:t(i).bankAccount,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u5458",field:"sellerName",width:"100",filters:t(i).sellerName,"filter-render":d},null,8,["filters","filter-render"])],64)):["ship","rma"].includes(t(u))?(y(),N(_,{key:1},[e(l,{title:"\u7EC4\u7EC7",field:"orgName",width:"80",filters:R},null,8,["filters"]),e(l,{title:"\u5355\u53F7",field:"docNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u65E5\u671F",field:"businessDate",width:"100",filters:t(i).businessDate,"filter-render":U},null,8,["filters","filter-render"]),e(l,{title:"\u6765\u6E90\u5355\u636E",field:"srcDocNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u7F16\u7801",field:"customerCode",width:"100",filters:t(i).customerCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName","min-width":"200",align:"left",filters:t(i).customerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u54C1\u53F7",field:"itemCode",width:"120",filters:t(i).itemCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u54C1\u540D",field:"itemName",width:"150",align:"left",filters:t(i).itemName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u89C4\u683C",field:"spec",width:"200",align:"left",filters:t(i).spec,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u6570\u91CF",field:"qty",width:"100",sortable:""}),e(l,{title:"\u5355\u4F4D",field:"unit",width:"80"}),e(l,{title:"\u5355\u4EF7",field:"price",width:"100",sortable:""}),e(l,{title:"\u91D1\u989D",field:"money",width:"100",sortable:"",filters:t(i).money,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u72B6\u6001",field:"status",width:"100"}),e(l,{title:"\u672C\u5E01",field:"domesticMoney",width:"100",sortable:"",filters:t(i).domesticMoney,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u6C47\u7387",field:"exchangeRate",width:"100",sortable:""}),e(l,{title:"\u7A0E\u7EC4\u5408",field:"taxName",width:"100"}),e(l,{title:"\u4E1A\u52A1\u5458",field:"sellerName",width:"100",filters:t(i).sellerName,"filter-render":d},null,8,["filters","filter-render"])],64)):t(u)==="balance"?(y(),N(_,{key:2},[e(l,{title:"\u7EC4\u7EC7",field:"orgName",filters:R},null,8,["filters"]),e(l,{title:"\u4E1A\u52A1\u5458",field:"sellerName",filters:t(i).sellerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237",field:"customerName",filters:t(i).customerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u6536\u6B3E\u91D1\u989D",field:"recpaymentMoney",sortable:"",filters:t(i).recpaymentMoney,"filter-render":p},{default:c(({row:n})=>[e(q,{type:"primary",onClick:S=>$(n,"rec")},{default:c(()=>[z(G(n.recpaymentMoney),1)]),_:2},1032,["onClick"])]),_:1},8,["filters","filter-render"]),e(l,{title:"\u51FA\u8D27\u91D1\u989D",field:"shipMoney",sortable:"",filters:t(i).shipMoney,"filter-render":p},{default:c(({row:n})=>[e(q,{type:"primary",onClick:S=>$(n,"ship")},{default:c(()=>[z(G(n.shipMoney),1)]),_:2},1032,["onClick"])]),_:1},8,["filters","filter-render"]),e(l,{title:"\u9000\u8D27\u91D1\u989D",field:"rmaMoney",sortable:"",filters:t(i).rmaMoney,"filter-render":p},{default:c(({row:n})=>[e(q,{type:"primary",onClick:S=>$(n,"rma")},{default:c(()=>[z(G(n.rmaMoney),1)]),_:2},1032,["onClick"])]),_:1},8,["filters","filter-render"]),e(l,{title:"\u4F59\u989D",field:"totalMoney",sortable:"",filters:t(i).totalMoney,"filter-render":p},null,8,["filters","filter-render"])],64)):P("",!0)]),_:1},8,["custom-config","data","loading","show-footer","footer-data"])]),e(se,{total:t(x),page:t(s).pageNo,"onUpdate:page":a[5]||(a[5]=n=>t(s).pageNo=n),limit:t(s).pageSize,"onUpdate:limit":a[6]||(a[6]=n=>t(s).pageSize=n),onPagination:V,size:"small"},null,8,["total","page","limit"])]),e(De,{ref_key:"permissionFormRef",ref:K},null,512),e(de,{title:"\u8BE6\u60C5",modelValue:t(m).visible,"onUpdate:modelValue":a[7]||(a[7]=n=>t(m).visible=n),width:"80%"},{default:c(()=>[e(I,{data:t(m).list,"filter-config":{},"header-cell-config":{height:30},"cell-config":{height:30},"footer-data":[t(m).statistics],"footer-cell-config":{height:30},"footer-cell-style":{background:"var(--el-color-warning-light-9)",border:"1px solid #ebeef5"},"show-footer":"",height:"500px","show-overflow":"",align:"center","show-footer-overflow":""},{default:c(()=>[t(m).type==="rec"?(y(),N(_,{key:0},[e(l,{title:"\u7EC4\u7EC7",field:"orgName",width:"80",filters:R},null,8,["filters"]),e(l,{title:"\u5355\u53F7",field:"docNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u65E5\u671F",field:"businessDate",width:"100",filters:t(i).businessDate,"filter-render":U},null,8,["filters","filter-render"]),e(l,{title:"\u6765\u6E90\u5355\u636E",field:"srcDocNo",width:"140",filters:t(i).srcDocNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u7F16\u7801",field:"customerCode",width:"100",filters:t(i).customerCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName","min-width":"200",align:"left",filters:t(i).customerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u7528\u9014",field:"property",width:"80",filters:W},null,8,["filters"]),e(l,{title:"\u5E01\u79CD",field:"currency",width:"80",filters:X},null,8,["filters"]),e(l,{title:"\u72B6\u6001",field:"status",width:"80"}),e(l,{title:"\u5907\u6CE8",field:"\u5907\u6CE8","min-width":"200",filters:t(i).remark,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u91D1\u989D",field:"money",width:"100",sortable:"",filters:t(i).money,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u672C\u5E01",field:"domesticMoney",width:"100",sortable:"",filters:t(i).domesticMoney,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u6C47\u7387",field:"exchangeRate",width:"80",sortable:""}),e(l,{title:"\u6536\u6B3E\u8D26\u53F7",field:"bankAccount",width:"200",filters:t(i).bankAccount,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u5458",field:"sellerName",width:"100",filters:t(i).sellerName,"filter-render":d},null,8,["filters","filter-render"])],64)):["ship","rma"].includes(t(m).type)?(y(),N(_,{key:1},[e(l,{title:"\u7EC4\u7EC7",field:"orgName",width:"80",filters:R},null,8,["filters"]),e(l,{title:"\u5355\u53F7",field:"docNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u4E1A\u52A1\u65E5\u671F",field:"businessDate",width:"100",filters:t(i).businessDate,"filter-render":U},null,8,["filters","filter-render"]),e(l,{title:"\u6765\u6E90\u5355\u636E",field:"srcDocNo",width:"140",filters:t(i).docNo,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u7F16\u7801",field:"customerCode",width:"100",filters:t(i).customerCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u5BA2\u6237\u540D\u79F0",field:"customerName","min-width":"200",align:"left",filters:t(i).customerName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u54C1\u53F7",field:"itemCode",width:"120",filters:t(i).itemCode,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u54C1\u540D",field:"itemName",width:"150",align:"left",filters:t(i).itemName,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u89C4\u683C",field:"spec",width:"200",align:"left",filters:t(i).spec,"filter-render":d},null,8,["filters","filter-render"]),e(l,{title:"\u6570\u91CF",field:"qty",width:"100",sortable:""}),e(l,{title:"\u5355\u4F4D",field:"unit",width:"80"}),e(l,{title:"\u5355\u4EF7",field:"price",width:"100",sortable:""}),e(l,{title:"\u91D1\u989D",field:"money",width:"100",sortable:"",filters:t(i).money,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u72B6\u6001",field:"status",width:"100"}),e(l,{title:"\u672C\u5E01",field:"domesticMoney",width:"100",sortable:"",filters:t(i).domesticMoney,"filter-render":p},null,8,["filters","filter-render"]),e(l,{title:"\u6C47\u7387",field:"exchangeRate",width:"100",sortable:""}),e(l,{title:"\u7A0E\u7EC4\u5408",field:"taxName",width:"100"}),e(l,{title:"\u4E1A\u52A1\u5458",field:"sellerName",width:"100",filters:t(i).sellerName,"filter-render":d},null,8,["filters","filter-render"])],64)):P("",!0)]),_:1},8,["data","footer-data"])]),_:1},8,["modelValue"])]),_:1})}}});export{Re as _};
