import{d as j,j as c,o as f,h as w,w as t,l as A,t as p,u as l,k as a,v as u,$ as v,D as K,m as L,y as B,aQ as U,G as E,H as F,J as G,K as H}from"./index-C8b06LRn.js";import{E as J}from"./el-drawer-C5TFtzfV.js";import{_ as M}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{d as b}from"./formatTime-COZ9Bl52.js";import{_ as P}from"./TaskSignDeleteForm.vue_vue_type_script_setup_true_lang-Dv-xFqke.js";const Q=j({name:"TaskSignList",__name:"TaskSignList",emits:["success"],setup(R,{expose:S,emit:T}){const x=B(),d=c(!1),o=c({});S({open:async s=>{U(s.children)?x.warning("\u8BE5\u4EFB\u52A1\u6CA1\u6709\u5B50\u4EFB\u52A1"):(o.value=s,d.value=!0)}});const _=c(),C=T,k=s=>{_.value.open(s.id)},N=()=>{C("success"),d.value=!1},g=s=>s&&s.children&&!U(s.children);return(s,n)=>{const h=E,y=F,i=G,V=M,z=H,D=J;return f(),w(D,{modelValue:l(d),"onUpdate:modelValue":n[1]||(n[1]=e=>L(d)?d.value=e:null),title:"\u5B50\u4EFB\u52A1",size:"880px"},{header:t(()=>{var e,r;return[A("h4",null,"\u3010"+p(l(o).name)+" \u3011\u5BA1\u6279\u4EBA\uFF1A"+p((r=(e=l(o))==null?void 0:e.assigneeUser)==null?void 0:r.nickname),1),g(l(o))?(f(),w(y,{key:0,style:{"margin-left":"5px"},type:"danger",plain:"",onClick:n[0]||(n[0]=m=>k(l(o)))},{default:t(()=>[a(h,{icon:"ep:remove"}),n[2]||(n[2]=u(" \u51CF\u7B7E "))]),_:1})):v("",!0)]}),default:t(()=>[a(z,{data:l(o).children,style:{width:"100%"},"row-key":"id",border:""},{default:t(()=>[a(i,{prop:"assigneeUser.nickname",label:"\u5BA1\u6279\u4EBA","min-width":"100"},{default:t(e=>{var r,m;return[u(p(((r=e.row.assigneeUser)==null?void 0:r.nickname)||((m=e.row.ownerUser)==null?void 0:m.nickname)),1)]}),_:1}),a(i,{prop:"assigneeUser.deptName",label:"\u6240\u5728\u90E8\u95E8","min-width":"100"},{default:t(e=>{var r,m;return[u(p(((r=e.row.assigneeUser)==null?void 0:r.deptName)||((m=e.row.ownerUser)==null?void 0:m.deptName)),1)]}),_:1}),a(i,{label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:t(e=>[a(V,{type:l(K).BPM_TASK_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(i,{label:"\u63D0\u4EA4\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:l(b)},null,8,["formatter"]),a(i,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:l(b)},null,8,["formatter"]),a(i,{label:"\u64CD\u4F5C",prop:"operation",width:"90"},{default:t(e=>[g(e.row)?(f(),w(y,{key:0,type:"danger",plain:"",size:"small",onClick:r=>k(e.row)},{default:t(()=>[a(h,{icon:"ep:remove"}),n[3]||(n[3]=u(" \u51CF\u7B7E "))]),_:2},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"]),a(P,{ref_key:"taskSignDeleteFormRef",ref:_,onSuccess:N},null,512)]),_:1},8,["modelValue"])}}});export{Q as _};
