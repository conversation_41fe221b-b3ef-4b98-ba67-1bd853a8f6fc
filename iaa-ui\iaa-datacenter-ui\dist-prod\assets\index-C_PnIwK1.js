import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as m}from"./IFrame.vue_vue_type_script_setup_true_lang-CBnGWZAU.js";import{_ as n}from"./index-CkzUfjB7.js";import{d as p,j as c,e2 as i,o as l,c as u,k as t,w as d,u as _,F as f}from"./index-C8b06LRn.js";import"./el-card-CaOo8U9P.js";const j=p({name:"JimuReport",__name:"index",setup(x){const r=c("https://sj.iaa360.cn:13141/jmreport/list?token="+i());return(b,h)=>{const s=n,o=m,a=e;return l(),u(f,null,[t(s,{title:"\u62A5\u8868\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/"}),t(a,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:d(()=>[t(o,{src:_(r)},null,8,["src"])]),_:1})],64)}}});export{j as default};
