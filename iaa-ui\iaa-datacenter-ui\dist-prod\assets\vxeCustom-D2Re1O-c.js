import{r as a,dI as c}from"./index-C8b06LRn.js";const m=a({storage:!0,restoreStore:({id:e})=>(t=>new Promise(o=>{setTimeout(()=>{try{localStorage.getItem(t)?o(JSON.parse(localStorage.getItem(t)||"")):o({})}catch{o({})}},300)}))(e),updateStore:({id:e,storeData:t})=>((o,s)=>new Promise(r=>{setTimeout(()=>{localStorage.setItem(o,JSON.stringify(s)),c.modal.message({status:"success",content:"\u4FDD\u5B58\u4E2A\u6027\u5316\u5217\u8BBE\u7F6E\u6210\u529F"}),r({})},200)}))(e,t),checkMethod:({column:e})=>{var t;return!((t=e.field)!=null&&t.startsWith("dynamic_"))}}),n=({row:e,column:t},o)=>{const s=o;s&&(s.setCurrentRow(e),s.setCurrentColumn(t))};export{m as c,n as t};
