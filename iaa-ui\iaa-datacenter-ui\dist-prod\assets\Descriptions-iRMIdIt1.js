import{d as Y,aj as p,e as Z,Y as w,bi as q,cx as B,j as H,o as r,c as y,n as f,u as t,l as g,v as m,t as u,h as k,w as s,k as b,$ as _,x as J,ar as $,c0 as K,F as L,g as N,bf as d,cj as Q,Z as R,X as S,G as U,bm as V,d6 as W,_ as ee}from"./index-C8b06LRn.js";import{E as ae,a as te}from"./el-descriptions-item-Ctb8GMnZ.js";import{_ as le}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";const re={class:"flex items-center"},se=ee(Y({name:"Descriptions",__name:"Descriptions",props:{title:p.string.def(""),message:p.string.def(""),collapse:p.bool.def(!0),columns:p.number.def(1),schema:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})}},setup(a){const F=Z(),j=w(()=>F.getMobile),I=q(),O=B(),v=a,{getPrefixCls:T}=S(),i=T("descriptions"),C=w(()=>{const l=["title","message","collapse","schema","data","class"],c={...I,...v};for(const o in c)l.indexOf(o)!==-1&&delete c[o];return c}),n=H(!0),D=()=>{v.collapse&&(n.value=!t(n))};return(l,c)=>{const o=U,z=V,A=le,E=ae,G=te,M=W;return r(),y("div",{class:f([t(i),"bg-[var(--el-color-white)] dark:bg-[var(--el-bg-color)] dark:border-[var(--el-border-color)] dark:border-1px"])},[a.title?(r(),y("div",{key:0,class:f([`${t(i)}-header`,"h-50px flex justify-between items-center b-b-1 border-solid border-[var(--el-border-color)] px-10px cursor-pointer dark:border-[var(--el-border-color)]"]),onClick:D},[g("div",{class:f([`${t(i)}-header__title`,"relative font-18px font-bold ml-10px"])},[g("div",re,[m(u(a.title)+" ",1),a.message?(r(),k(z,{key:0,content:a.message,placement:"right"},{default:s(()=>[b(o,{class:"ml-5px",icon:"ep:warning"})]),_:1},8,["content"])):_("",!0)])],2),a.collapse?(r(),k(o,{key:0,icon:t(n)?"ep:arrow-down":"ep:arrow-up"},null,8,["icon"])):_("",!0)],2)):_("",!0),b(M,null,{default:s(()=>[J(g("div",{class:f([`${t(i)}-content`,"p-10px"])},[b(G,$({column:v.columns,direction:t(j)?"vertical":"horizontal",border:""},t(C)),K({default:s(()=>[(r(!0),y(L,null,N(a.schema,e=>(r(),k(E,$({key:e.field,"min-width":"80",ref_for:!0},(P=>{const X=["field"],x={...P};for(const h in x)X.indexOf(h)!==-1&&delete x[h];return x})(e)),{label:s(()=>[d(l.$slots,`${e.field}-label`,{row:{label:e.label}},()=>[m(u(e.label),1)],!0)]),default:s(()=>[e.dateFormat?d(l.$slots,"default",{key:0},()=>[m(u(a.data[e.field]!==null?t(Q)(a.data[e.field]).format(e.dateFormat):""),1)],!0):e.dictType?d(l.$slots,"default",{key:1},()=>[b(A,{type:e.dictType,value:a.data[e.field]+""},null,8,["type","value"])],!0):d(l.$slots,e.field,{key:2,row:a.data},()=>[m(u(e.mappedField?a.data[e.mappedField]:a.data[e.field]),1)],!0)]),_:2},1040))),128))]),_:2},[t(O).extra?{name:"extra",fn:s(()=>[d(l.$slots,"extra",{},void 0,!0)]),key:"0"}:void 0]),1040,["column","direction"])],2),[[R,t(n)]])]),_:3})],2)}}}),[["__scopeId","data-v-74d4336e"]]);export{se as _};
