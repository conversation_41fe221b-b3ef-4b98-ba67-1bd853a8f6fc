import{aG as s}from"./index-C8b06LRn.js";const t={getProcessListenerPage:async e=>await s.get({url:"/bpm/process-listener/page",params:e}),getProcessListener:async e=>await s.get({url:"/bpm/process-listener/get?id="+e}),createProcessListener:async e=>await s.post({url:"/bpm/process-listener/create",data:e}),updateProcessListener:async e=>await s.put({url:"/bpm/process-listener/update",data:e}),deleteProcessListener:async e=>await s.delete({url:"/bpm/process-listener/delete?id="+e})};export{t as P};
