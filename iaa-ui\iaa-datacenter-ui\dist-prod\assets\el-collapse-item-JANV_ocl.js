import{b6 as H,ba as V,cm as z,bF as G,bG as W,b8 as D,bc as J,bQ as P,j as w,d4 as j,au as R,aL as U,bb as _,Y as t,d as x,o as E,c as L,bf as N,n as v,u as e,bg as M,b9 as X,cN as ee,av as ae,d5 as se,l as F,v as le,t as te,k as O,w as Q,h as ie,i as oe,be as ne,M as de,b4 as ce,x as re,Z as ue,d6 as pe,bh as ve,cS as be}from"./index-C8b06LRn.js";const T=r=>D(r)||J(r)||P(r),me=H({accordion:Boolean,modelValue:{type:V([Array,String,Number]),default:()=>z([])}}),fe={[G]:T,[W]:T},Y=Symbol("collapseContextKey"),he=x({name:"ElColl<PERSON><PERSON>"});var Ce=M(x({...he,props:me,emits:fe,setup(r,{expose:g,emit:f}){const h=r,{activeNames:k,setActiveNames:d}=((l,b)=>{const i=w(j(l.modelValue)),m=c=>{i.value=c;const o=l.accordion?i.value[0]:i.value;b(G,o),b(W,o)};return R(()=>l.modelValue,()=>i.value=j(l.modelValue),{deep:!0}),U(Y,{activeNames:i,handleItemClick:c=>{if(l.accordion)m([i.value[0]===c?"":c]);else{const o=[...i.value],C=o.indexOf(c);C>-1?o.splice(C,1):o.push(c),m(o)}}}),{activeNames:i,setActiveNames:m}})(h,f),{rootKls:A}=(()=>{const l=_("collapse");return{rootKls:t(()=>l.b())}})();return g({activeNames:k,setActiveNames:d}),(l,b)=>(E(),L("div",{class:v(e(A))},[N(l.$slots,"default")],2))}}),[["__file","collapse.vue"]]);const ye=H({title:{type:String,default:""},name:{type:V([String,Number]),default:void 0},icon:{type:X,default:ee},disabled:Boolean}),Ke=x({name:"ElCollapseItem"});var Z=M(x({...Ke,props:ye,setup(r,{expose:g}){const f=r,{focusing:h,id:k,isActive:d,handleFocus:A,handleHeaderClick:l,handleEnterClick:b}=(s=>{const n=ae(Y),{namespace:u}=_("collapse"),p=w(!1),a=w(!1),y=se(),K=t(()=>y.current++),$=t(()=>{var B;return(B=s.name)!=null?B:`${u.value}-id-${y.prefix}-${e(K)}`}),q=t(()=>n==null?void 0:n.activeNames.value.includes(e($)));return{focusing:p,id:K,isActive:q,handleFocus:()=>{setTimeout(()=>{a.value?a.value=!1:p.value=!0},50)},handleHeaderClick:()=>{s.disabled||(n==null||n.handleItemClick(e($)),p.value=!1,a.value=!0)},handleEnterClick:()=>{n==null||n.handleItemClick(e($))}}})(f),{arrowKls:i,headKls:m,rootKls:c,itemWrapperKls:o,itemContentKls:C,scopedContentId:I,scopedHeadId:S}=((s,{focusing:n,isActive:u,id:p})=>{const a=_("collapse"),y=t(()=>[a.b("item"),a.is("active",e(u)),a.is("disabled",s.disabled)]),K=t(()=>[a.be("item","header"),a.is("active",e(u)),{focusing:e(n)&&!s.disabled}]);return{arrowKls:t(()=>[a.be("item","arrow"),a.is("active",e(u))]),headKls:K,rootKls:y,itemWrapperKls:t(()=>a.be("item","wrap")),itemContentKls:t(()=>a.be("item","content")),scopedContentId:t(()=>a.b(`content-${e(p)}`)),scopedHeadId:t(()=>a.b(`head-${e(p)}`))}})(f,{focusing:h,isActive:d,id:k});return g({isActive:d}),(s,n)=>(E(),L("div",{class:v(e(c))},[F("button",{id:e(S),class:v(e(m)),"aria-expanded":e(d),"aria-controls":e(I),"aria-describedby":e(I),tabindex:s.disabled?-1:0,type:"button",onClick:e(l),onKeydown:de(ce(e(b),["stop","prevent"]),["space","enter"]),onFocus:e(A),onBlur:u=>h.value=!1},[N(s.$slots,"title",{},()=>[le(te(s.title),1)]),N(s.$slots,"icon",{isActive:e(d)},()=>[O(e(ne),{class:v(e(i))},{default:Q(()=>[(E(),ie(oe(s.icon)))]),_:1},8,["class"])])],42,["id","aria-expanded","aria-controls","aria-describedby","tabindex","onClick","onKeydown","onFocus","onBlur"]),O(e(pe),null,{default:Q(()=>[re(F("div",{id:e(I),role:"region",class:v(e(o)),"aria-hidden":!e(d),"aria-labelledby":e(S)},[F("div",{class:v(e(C))},[N(s.$slots,"default")],2)],10,["id","aria-hidden","aria-labelledby"]),[[ue,e(d)]])]),_:3})],2))}}),[["__file","collapse-item.vue"]]);const xe=ve(Ce,{CollapseItem:Z}),Ne=be(Z);export{Ne as E,xe as a};
