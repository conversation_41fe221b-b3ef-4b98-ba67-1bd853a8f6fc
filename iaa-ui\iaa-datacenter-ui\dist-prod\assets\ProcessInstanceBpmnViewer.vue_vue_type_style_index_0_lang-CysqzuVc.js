import{aG as d,d as u,aj as s,j as c,au as f,x,h as y,w as p,L as I,o as b,l as g,k,ar as v,u as e}from"./index-C8b06LRn.js";import{E as w}from"./el-card-CaOo8U9P.js";import{k as D}from"./bpmn-embedded-DWy7HXvQ.js";const _=u({name:"BpmProcessInstanceBpmnViewer",__name:"ProcessInstanceBpmnViewer",props:{loading:s.bool,id:s.string,processInstance:s.any,tasks:s.array,bpmnXml:s.string},setup(a){const n=a,r=c({prefix:"flowable"}),i=c([]);return f(()=>n.loading,async o=>{o&&n.id&&(i.value=await(async t=>await d.get({url:"/bpm/activity/list",params:t}))({processInstanceId:n.id}))}),(o,t)=>{const l=w,m=I;return x((b(),y(l,{class:"box-card"},{header:p(()=>t[0]||(t[0]=[g("span",{class:"el-icon-picture-outline"},"\u6D41\u7A0B\u56FE",-1)])),default:p(()=>[k(e(D),v({key:"designer",activityData:e(i),prefix:e(r).prefix,processInstanceData:a.processInstance,taskData:a.tasks,value:a.bpmnXml},e(r)),null,16,["activityData","prefix","processInstanceData","taskData","value"])]),_:1})),[[m,a.loading]])}}});export{_};
