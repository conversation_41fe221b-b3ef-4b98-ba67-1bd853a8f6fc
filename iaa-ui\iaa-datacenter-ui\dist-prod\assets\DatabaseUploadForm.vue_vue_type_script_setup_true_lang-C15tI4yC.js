import{_ as I}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{d as L,y as O,j as i,o as A,h as B,w as l,k as n,u,v as c,m as f,l as p,dP as D,aV as G,aO as H,G as P,aa as R,bn as T,H as q}from"./index-C8b06LRn.js";import{d as E}from"./download-D5Lb_h0f.js";import{I as J}from"./index-BX2KAvdx.js";const K={class:"el-upload__tip text-center"},M=L({__name:"DatabaseUploadForm",emits:["success"],setup(N,{expose:_,emit:y}){const t=O(),o=i(!1),r=i([]),s=i(),x=i(),b=y,m=i();_({open:()=>{o.value=!0,r.value=[],V()}});const h=()=>{t.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),s.value=!1},g=()=>{t.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},k=async()=>{if(r.value.length==0)return void t.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6");const a=r.value[0].name,e=a.slice(a.lastIndexOf(".")).toLowerCase();[".xls",".xlsx"].includes(e)?(x.value={Authorization:"Bearer "+D(),"tenant-id":G()},s.value=!0,m.value.submit()):t.error("\u53EA\u80FD\u4E0A\u4F20 xls \u6216 xlsx \u683C\u5F0F\u7684\u6587\u4EF6")},w=a=>{if(a.code!==0)return t.error(a.msg),void(s.value=!1);let e=a.data.join(`;
`);t.alert(e),s.value=!1,o.value=!1,b("success")},C=async()=>{const a=await J.exportTemplate();E.excel(a,"\u6536\u6B3E\u5BFC\u5165\u6A21\u677F.xlsx")},V=async()=>{var a;s.value=!1,await H(),(a=m.value)==null||a.clearFiles()};return(a,e)=>{const j=P,U=R,z=T,v=q,F=I;return A(),B(F,{modelValue:u(o),"onUpdate:modelValue":e[2]||(e[2]=d=>f(o)?o.value=d:null),title:"\u5BFC\u5165\u6536\u6B3E"},{footer:l(()=>[n(v,{disabled:u(s),type:"primary",onClick:k},{default:l(()=>e[6]||(e[6]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),n(v,{onClick:e[1]||(e[1]=d=>o.value=!1)},{default:l(()=>e[7]||(e[7]=[c("\u53D6 \u6D88")])),_:1})]),default:l(()=>[n(z,{ref_key:"uploadRef",ref:m,"file-list":u(r),"onUpdate:fileList":e[0]||(e[0]=d=>f(r)?r.value=d:null),action:"https://sj.iaa360.cn:13141/admin-api/collection/information/import","auto-upload":!1,disabled:u(s),headers:u(x),limit:1,"on-error":h,"on-exceed":g,"on-success":w,accept:".xlsx, .xls",drag:""},{tip:l(()=>[p("div",K,[e[4]||(e[4]=p("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),n(U,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:C},{default:l(()=>e[3]||(e[3]=[c(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:l(()=>[n(j,{icon:"ep:upload"}),e[5]||(e[5]=p("div",{class:"el-upload__text"},[c("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),p("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","disabled","headers"])]),_:1},8,["modelValue"])}}});export{M as _};
