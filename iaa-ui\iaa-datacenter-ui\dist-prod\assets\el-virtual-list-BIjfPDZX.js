import{Y as c,ck as le,aE as ne,cl as m,ba as b,b6 as z,cm as oe,d as se,bb as j,j as k,r as ie,cn as ue,u as E,au as ce,co as de,bv as H,b4 as ve,bo as me,bq as pe}from"./index-C8b06LRn.js";var P=Number.isNaN||function(e){return typeof e=="number"&&e!=e};function fe(e,a){if(e.length!==a.length)return!1;for(var l=0;l<e.length;l++)if(n=e[l],r=a[l],!(n===r||P(n)&&P(r)))return!1;var n,r;return!0}const ge=()=>{const e=ne().proxy.$props;return c(()=>{const a=(l,n,r)=>({});return e.perfMode?le(a):function(l,n){n===void 0&&(n=fe);var r=null;function p(){for(var u=[],d=0;d<arguments.length;d++)u[d]=arguments[d];if(r&&r.lastThis===this&&n(u,r.lastArgs))return r.lastResult;var f=l.apply(this,u);return r={lastResult:f,lastArgs:u,lastThis:this},f}return p.clear=function(){r=null},p}(a)})},he=50,be="itemRendered",ye="scroll",V="forward",K="backward",Se="auto",we="smart",xe="start",Ne="center",ze="end",y="horizontal",I="vertical",B="rtl",W="negative",Y="positive-ascending",_="positive-descending",Ee={[y]:"left",[I]:"top"},R=m({type:b([Number,Function]),required:!0}),T=m({type:Number}),q=m({type:Number,default:2}),Me=m({type:String,values:["ltr","rtl"],default:"ltr"}),O=m({type:Number,default:0}),M=m({type:Number,required:!0}),J=m({type:String,values:["horizontal","vertical"],default:I}),Q=z({className:{type:String,default:""},containerElement:{type:b([String,Object]),default:"div"},data:{type:b(Array),default:()=>oe([])},direction:Me,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:b([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),Le=z({cache:q,estimatedItemSize:T,layout:J,initScrollOffset:O,total:M,itemSize:R,...Q}),$={type:Number,default:6},U={type:Number,default:0},X={type:Number,default:2},Ce=z({columnCache:q,columnWidth:R,estimatedColumnWidth:T,estimatedRowHeight:T,initScrollLeft:O,initScrollTop:O,itemKey:{type:b(Function),default:({columnIndex:e,rowIndex:a})=>`${a}:${e}`},rowCache:q,rowHeight:R,totalColumn:M,totalRow:M,hScrollbarSize:$,vScrollbarSize:$,scrollbarStartGap:U,scrollbarEndGap:X,role:String,...Q}),Z=z({alwaysOn:Boolean,class:String,layout:J,total:M,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:$,startGap:U,endGap:X,visible:Boolean}),Ie=(e,a)=>e<a?V:K,Be=e=>e==="ltr"||e===B||e===y,Re=e=>e===B;let S=null;function Te(e=!1){if(S===null||e){const a=document.createElement("div"),l=a.style;l.width="50px",l.height="50px",l.overflow="scroll",l.direction="rtl";const n=document.createElement("div"),r=n.style;return r.width="100px",r.height="100px",a.appendChild(n),document.body.appendChild(a),a.scrollLeft>0?S=_:(a.scrollLeft=1,S=a.scrollLeft===0?W:Y),document.body.removeChild(a),S}return S}const qe=se({name:"ElVirtualScrollBar",props:Z,emits:["scroll","start-move","stop-move"],setup(e,{emit:a}){const l=c(()=>e.startGap+e.endGap),n=j("virtual-scrollbar"),r=j("scrollbar"),p=k(),u=k();let d=null,f=null;const i=ie({isDragging:!1,traveled:0}),o=c(()=>ue[e.layout]),D=c(()=>e.clientSize-E(l)),ee=c(()=>({position:"absolute",width:`${y===e.layout?D.value:e.scrollbarSize}px`,height:`${y===e.layout?e.scrollbarSize:D.value}px`,[Ee[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"})),L=c(()=>{const t=e.ratio,s=e.clientSize;if(t>=100)return Number.POSITIVE_INFINITY;if(t>=50)return t*s/100;const g=s/3;return Math.floor(Math.min(Math.max(t*s,20),g))}),te=c(()=>{if(!Number.isFinite(L.value))return{display:"none"};const t=`${L.value}px`;return function({move:g,size:N,bar:A},re){const v={},C=`translate${A.axis}(${g}px)`;return v[A.size]=N,v.transform=C,v.msTransform=C,v.webkitTransform=C,re==="horizontal"?v.height="100%":v.width="100%",v}({bar:o.value,size:t,move:i.traveled},e.layout)}),h=c(()=>Math.floor(e.clientSize-L.value-E(l))),F=()=>{window.removeEventListener("mousemove",x),window.removeEventListener("mouseup",w),document.onselectstart=f,f=null;const t=E(u);t&&(t.removeEventListener("touchmove",x),t.removeEventListener("touchend",w))},G=t=>{t.stopImmediatePropagation(),t.ctrlKey||[1,2].includes(t.button)||(i.isDragging=!0,i[o.value.axis]=t.currentTarget[o.value.offset]-(t[o.value.client]-t.currentTarget.getBoundingClientRect()[o.value.direction]),a("start-move"),(()=>{window.addEventListener("mousemove",x),window.addEventListener("mouseup",w);const s=E(u);s&&(f=document.onselectstart,document.onselectstart=()=>!1,s.addEventListener("touchmove",x,{passive:!0}),s.addEventListener("touchend",w))})())},w=()=>{i.isDragging=!1,i[o.value.axis]=0,a("stop-move"),F()},x=t=>{const{isDragging:s}=i;if(!s||!u.value||!p.value)return;const g=i[o.value.axis];if(!g)return;me(d);const N=-1*(p.value.getBoundingClientRect()[o.value.direction]-t[o.value.client])-(u.value[o.value.offset]-g);d=pe(()=>{i.traveled=Math.max(e.startGap,Math.min(N,h.value)),a("scroll",N,h.value)})},ae=t=>{const s=Math.abs(t.target.getBoundingClientRect()[o.value.direction]-t[o.value.client])-u.value[o.value.offset]/2;i.traveled=Math.max(0,Math.min(s,h.value)),a("scroll",s,h.value)};return ce(()=>e.scrollFrom,t=>{i.isDragging||(i.traveled=Math.ceil(t*h.value))}),de(()=>{F()}),()=>H("div",{role:"presentation",ref:p,class:[n.b(),e.class,(e.alwaysOn||i.isDragging)&&"always-on"],style:ee.value,onMousedown:ve(ae,["stop","prevent"]),onTouchstartPrevent:G},H("div",{ref:u,class:r.e("thumb"),style:te.value,onMousedown:G},[]))}});export{Se as A,K as B,Ne as C,he as D,ze as E,V as F,y as H,be as I,B as R,ye as S,I as V,Y as a,W as b,qe as c,Ie as d,_ as e,xe as f,Te as g,we as h,Be as i,Ce as j,Re as k,Z as l,ge as u,Le as v};
