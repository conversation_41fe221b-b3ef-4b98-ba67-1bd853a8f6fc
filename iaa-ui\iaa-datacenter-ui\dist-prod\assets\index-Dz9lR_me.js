import{aG as s}from"./index-C8b06LRn.js";const t=async a=>await s.get({url:"/system/post/page",params:a}),e=async()=>await s.get({url:"/system/post/simple-list"}),p=async a=>await s.get({url:"/system/post/get?id="+a}),o=async a=>await s.post({url:"/system/post/create",data:a}),r=async a=>await s.put({url:"/system/post/update",data:a}),y=async a=>await s.delete({url:"/system/post/delete?id="+a}),i=async a=>await s.download({url:"/system/post/export",params:a});export{p as a,t as b,o as c,y as d,i as e,e as g,r as u};
