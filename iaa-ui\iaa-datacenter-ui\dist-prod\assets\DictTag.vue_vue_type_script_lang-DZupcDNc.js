import{i as u}from"./color-DXkOL5Tu.js";import{d as n,Y as i,aS as o,cE as p,aZ as c,cF as y,cG as v,k as t,aM as f}from"./index-C8b06LRn.js";const d=n({name:"DictTag",props:{type:{type:String,required:!0},value:{type:[String,Number,Boolean,Array],required:!0},separator:{type:String,default:","},gutter:{type:String,default:"5px"}},setup(l){const r=i(()=>o(l.value)||p(l.value)?[String(l.value)]:c(l.value)?l.value.split(l.separator):y(l.value)?l.value.map(String):[]);return()=>(()=>{if(!l.type||l.value===void 0||l.value===null||l.value==="")return null;const s=v(l.type);return t("div",{class:"dict-tag",style:{display:"inline-flex",gap:l.gutter,justifyContent:"center",alignItems:"center"}},[s.map(e=>{if(r.value.includes(e.value))return e.colorType+""!="primary"&&e.colorType+""!="default"||(e.colorType=""),t(f,{style:e!=null&&e.cssClass?"color: #fff":"",type:(e==null?void 0:e.colorType)||null,color:e!=null&&e.cssClass&&u(e==null?void 0:e.cssClass)?e==null?void 0:e.cssClass:"",disableTransitions:!0},{default:()=>[e==null?void 0:e.label]})})])})()}});export{d as _};
