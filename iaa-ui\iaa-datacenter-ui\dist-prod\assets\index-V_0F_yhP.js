import{d as Ke,y as Be,p as Qe,e as Xe,j as y,Y as oe,r as ke,bU as Ze,au as ea,aO as F,f as aa,u as t,bj as la,aF as ne,T as ta,o as d,h as u,w as r,c as h,k as i,x as k,v,$ as p,t as m,l,F as M,g as X,N as de,D as W,dE as ia,dV as ge,ax as Te,H as oa,S as na,z as da,A as sa,C as ra,aM as ca,G as ua,a9 as pa,bm as ma,L as fa,_ as va}from"./index-C8b06LRn.js";import{E as ya}from"./el-infinite-scroll-fE_Jh_bm.js";import{_ as wa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ha}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as ba}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{_ as ka}from"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import{a as Z,f as ga}from"./dateUtil-D9m5ek6U.js";import{D as E}from"./index-SUTJ2GLg.js";import Ta from"./DayForm-Bh0FsbpL.js";import{d as Ce}from"./download-D5Lb_h0f.js";import{E as Ca}from"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";import"./color-DXkOL5Tu.js";import"./el-time-select-BrN8x4_E.js";const _a={key:0,class:"pc-view"},xa={class:"h-[calc(100vh-260px)]"},La={style:{display:"flex","flex-direction":"column",gap:"8px"}},Ra={key:0},Na={key:1},Va={key:1,class:"h-[calc(100vh-110px)] mobile-view"},Sa={class:"mobile-search-bar"},za={class:"mobile-action-bar"},Da={class:"btn-text"},Oa=["infinite-scroll-immediate"],Ea={class:"mobile-card-header"},Ua={class:"card-title-main"},Pa={key:0},Ia={class:"card-date"},Ya={class:"mobile-card-section"},Aa={class:"section-title"},Ma={class:"info-grid"},Wa={class:"info-item full-width"},$a={class:"info-value"},qa={class:"info-item"},Ha={class:"info-value"},ja={class:"info-item"},Fa={class:"info-value"},Ja={class:"info-item"},Ga={class:"info-value"},Ka={class:"info-item"},Ba={class:"info-value"},Qa={class:"mobile-card-section"},Xa={class:"section-title"},Za={class:"info-grid"},el={class:"info-item full-width"},al={class:"info-value"},ll={class:"info-item full-width"},tl={class:"info-value"},il={class:"info-item"},ol={class:"info-value"},nl={class:"info-item"},dl={class:"info-value"},sl={class:"info-item"},rl={class:"info-value"},cl={class:"info-item"},ul={class:"info-value"},pl={class:"mobile-card-section"},ml={class:"section-title"},fl={class:"info-grid"},vl={class:"info-item"},yl={class:"info-value"},wl={class:"info-item"},hl={class:"info-value"},bl={class:"info-item"},kl={class:"info-value"},gl={class:"info-item"},Tl={class:"info-value"},Cl={class:"info-item"},_l={class:"info-value"},xl={class:"info-item"},Ll={class:"info-value"},Rl={class:"mobile-card-section"},Nl={class:"section-title"},Vl={class:"info-grid"},Sl={class:"info-item"},zl={class:"info-item"},Dl={class:"info-item"},Ol={class:"info-item"},El={class:"info-value"},Ul={key:0,class:"info-item full-width"},Pl={class:"info-value text-truncate"},Il={class:"mobile-card-actions"},Yl={key:1,class:"table-view-wrapper"},Al={class:"table-scroll"},Ml={key:0},Wl={key:1},$l={style:{"margin-top":"10px"}},ql={key:2,class:"h-30px leading-30px border-t-#a8a8a8"},_e="productionDayTableScrollPosition",Hl=va(Ke({name:"Day",__name:"index",setup(jl){const g=Be(),{t:xe}=Qe(),Le=Xe(),b=y(),se=y(),z=y(!0),V=y([]),Y=y(0),S=y({scrollTop:0,scrollLeft:0}),$=y(!1),Re=s=>{const{scrollTop:e,scrollLeft:c}=s;(c>0||e>0)&&(clearTimeout(window.scrollSaveTimer),window.scrollSaveTimer=setTimeout(()=>{S.value={scrollTop:e,scrollLeft:c},localStorage.setItem(_e,JSON.stringify({scrollTop:e,scrollLeft:c}))},100))},q=()=>{var c;if(!b.value)return!1;const{scrollTop:s,scrollLeft:e}=S.value;if(e<=0&&s<=0)return!1;try{if(b.value.scrollTo)return b.value.scrollTo(e,s),F(()=>{var n;(n=b.value.$el)==null||n.querySelector(".vxe-table--body-wrapper")}),!0;const w=(c=b.value.$el)==null?void 0:c.querySelector(".vxe-table--body-wrapper");if(w)return w.scrollLeft=e,w.scrollTop=s,!0}catch{}return!1},Ne=()=>{if($.value&&b.value){const{scrollTop:s,scrollLeft:e}=S.value;(e>0||s>0)&&(setTimeout(()=>{q()},50),setTimeout(()=>{q()},150),setTimeout(()=>{var c;q(),(c=b.value.$el)==null||c.querySelector(".vxe-table--body-wrapper")},300)),$.value=!1}},J=oe(()=>Le.getMobile),re=y(!1),ce=()=>{J.value&&setTimeout(()=>{const s=document.querySelector('meta[name="viewport"]');if(s){const e=s.getAttribute("content");s.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"),setTimeout(()=>{s.setAttribute("content",e||"width=device-width, initial-scale=1.0")},100)}window.dispatchEvent(new Event("resize"))},300)},H=y(!1),D=y([]),o=ke({pageNo:1,pageSize:20,workType:0,teamLeader:void 0,productionOrderCode:void 0,salesOrderCode:void 0,productNo:void 0,dateStr:"",startDate:void 0,endDate:void 0,type:void 0,productionLine:void 0,number:void 0,isRework:void 0,all:void 0});y();const Ve=ke({body:{options:[[{code:"batchDelete",name:"\u6279\u91CF\u5220\u9664"}]]}}),Se={1:"L1",2:"L2",3:"L3",4:"L4",5:"L5",6:"L6",7:"L7",8:"L8",9:"L9",10:"L10",11:"L11",12:"\u5C0F\u5305\u88C5\u7EBF",13:"\u5305\u88C51\u7EBF",14:"\u5305\u88C52\u7EBF",15:"L15"},ee=oe(()=>s=>Se[s]||s),ze={1:"\u662F",0:"\u5426"},De=oe(()=>s=>ze[s]||s),Oe=({menu:s})=>{const e=b.value;if(e&&s.code==="batchDelete"){const c=e.getCheckboxRecords();if(c.length===0)return g.alertError("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6570\u636E"),void(D.value=[]);D.value=c,H.value=!0}},ue=y(),pe=y(0),Ee=y([{productionLine:"\u5408\u8BA1\u603B\u548C",actualWork:ue,hoursReportNum:pe}]),me=({row:s,column:e})=>{e.title!=="\u64CD\u4F5C"&&(s.audit!==1||ge(["production:day:auditupdate"])?U("update",s.batchesId):g.alertError("\u60A8\u6CA1\u6709\u6743\u9650\u64CD\u4F5C\u5DF2\u5BA1\u6838\u6570\u636E"))},_=()=>{o.pageNo=1,L(!0)},Ue=async()=>{z.value=!0;try{const s=Math.ceil(Y.value/o.pageSize);if(o.pageNo>=s)return void(re.value=!0);o.pageNo+=1;let e=Te(o);for(let w in e)Array.isArray(e[w])&&(e[w]=`${e[w].join(",")}`);const c=await E.getDayPage(e);V.value=V.value.concat(c.list),Y.value=c.total}finally{z.value=!1}},ae=s=>{if(!s)return"";if(typeof s=="number"){const e=new Date(s);return`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`}return s},L=async(s=!1)=>{z.value=!0;let e=null;s&&b.value&&(e=(()=>{var w;const c=(w=b.value.$el)==null?void 0:w.querySelector(".vxe-table--body-wrapper");return c?{scrollLeft:c.scrollLeft,scrollTop:c.scrollTop}:{scrollLeft:0,scrollTop:0}})(),$.value=!0);try{let c=Te(o);for(let n in c)Array.isArray(c[n])&&(c[n]=`${c[n].join(",")}`);const w=await E.getDayPage(c);V.value=w.list,Y.value=w.total,ue.value=V.value.reduce((n,R)=>n+R.actualWork,0).toFixed(3),pe.value=V.value.reduce((n,R)=>n+R.hoursReportNum,0),s&&e&&(S.value=e,F(()=>{setTimeout(()=>{q()},100)}))}finally{z.value=!1}},fe=Ze(()=>{var s;if(b.value){const e=(s=b.value.$el)==null?void 0:s.querySelector(".vxe-table--body-wrapper");if(e){const c=e.scrollLeft,w=e.scrollTop;S.value={scrollTop:w,scrollLeft:c}}}},300),Pe=s=>!!o.startDate&&s.getTime()<new Date(o.startDate).setHours(0,0,0,0),Ie=({row:s,column:e})=>{if(e.field==="workHourDiff"&&s.workHourDiff>1)return{color:"red"}},ve=y(),U=(s,e)=>{ve.value.open(s,e)},A=async s=>{try{await g.delConfirm(),await E.deleteDay(s),g.success(xe("common.delSuccess")),await L(!0)}catch{}},P=y(!1),G=y(!1),Ye=async()=>{G.value=!0,await F(),await new Promise(s=>setTimeout(s,100)),P.value=!P.value,o.pageNo=1;try{await L()}finally{setTimeout(()=>{G.value=!1},200)}},K=y(!1),le=Z(new Date),Ae=s=>{o.pageSize=s,o.pageNo=1,L()},Me=s=>{o.pageNo=s,L()};ea(()=>V.value,s=>{$.value&&s&&s.length>0&&F(()=>{setTimeout(()=>{if(S.value.scrollLeft>0||S.value.scrollTop>0){const{scrollTop:e,scrollLeft:c}=S.value;q()&&($.value=!1)}},150)})},{deep:!1});const I=y([]);return aa(()=>{(()=>{try{const s=localStorage.getItem(_e);if(s){const e=JSON.parse(s);S.value=e}}catch{}})(),L(),F(()=>{var s;(s=t(b))==null||s.connect(t(se))}),J.value&&window.addEventListener("orientationchange",ce),window.addEventListener("resize",fe)}),la(()=>{J.value&&window.removeEventListener("orientationchange",ce),window.removeEventListener("resize",fe),window.scrollSaveTimer&&clearTimeout(window.scrollSaveTimer)}),(s,e)=>{const c=oa,w=ne("vxe-toolbar"),n=ne("vxe-column"),R=na,j=da,B=sa,te=ra,ie=ka,Q=ca,ye=ne("vxe-table"),We=ba,x=ua,$e=pa,qe=ma,He=Ca,je=ha,Fe=wa,T=ta("hasPermi"),Je=ya,we=fa;return d(),u(Fe,null,{default:r(()=>[t(J)?k((d(),h("div",Va,[l("div",Sa,[i(R,{modelValue:t(o).all,"onUpdate:modelValue":e[17]||(e[17]=a=>t(o).all=a),"suffix-icon":t(ia),placeholder:"\u5168\u57DF\u67E5\u8BE2",onChange:_,class:"mobile-search-input",size:"default"},null,8,["modelValue","suffix-icon"])]),l("div",za,[k((d(),u(c,{type:"primary",onClick:e[18]||(e[18]=a=>U("create")),class:"mobile-action-btn mobile-add-btn"},{default:r(()=>[i(x,{icon:"ep:plus",class:"btn-icon"}),e[42]||(e[42]=l("span",{class:"btn-text"},"\u65B0\u589E",-1))]),_:1})),[[T,["production:day:create"]]]),i(c,{type:"success",onClick:e[19]||(e[19]=a=>(async()=>{if(I.value.length===0)return void g.warning("\u8BF7\u9009\u62E9\u8981\u5BA1\u6838\u7684\u884C");const f=I.value.map(C=>C.batchesId).filter(C=>C);g.confirm("\u5F53\u524D\u5DF2\u7ECF\u9009\u4E2D"+f.length+"\u6279\u6570\u636E\uFF0C\u786E\u5B9A\u8981\u5BA1\u6838\u901A\u8FC7\u5417\uFF1F").then(async()=>{await E.auditDay(f),g.success("\u5BA1\u6838\u6210\u529F"),await L(!0),I.value=[]})})()),class:"mobile-action-btn mobile-audit-btn"},{default:r(()=>[i(x,{icon:"ep:check",class:"btn-icon"}),e[43]||(e[43]=l("span",{class:"btn-text"},"\u5BA1\u6838",-1))]),_:1}),i(c,{type:P.value?"warning":"info",onClick:Ye,class:"mobile-action-btn mobile-view-btn",loading:G.value},{default:r(()=>[i(x,{icon:P.value?"ep:list":"ep:grid",class:"btn-icon"},null,8,["icon"]),l("span",Da,m(P.value?"\u5361\u7247":"\u8868\u683C"),1)]),_:1},8,["type","loading"])]),P.value?(d(),h("div",Yl,[l("div",Al,[i(ye,{onCellDblclick:me,ref_key:"tableRef",ref:b,"row-config":{height:30,isCurrent:!0},data:V.value,"header-cell-style":{padding:0},"cell-style":{padding:0,height:"30px",color:"#232323"},"virtual-y-config":{enabled:!0,gt:0},border:"",stripe:"","show-overflow":"",align:"center",height:"85%"},{default:r(()=>[i(n,{field:"teamLeader",width:"80",title:"\u73ED\u7EC4\u957F"}),i(n,{field:"productionLine",width:"100",title:"\u4EA7\u7EBF"},{header:r(()=>[e[74]||(e[74]=l("div",null,"\u4EA7\u7EBF",-1)),i(B,{modelValue:t(o).productionLine,"onUpdate:modelValue":e[20]||(e[20]=a=>t(o).productionLine=a),onChange:_,placeholder:"\u9009\u62E9\u4EA7\u7EBF",style:{width:"100%"},size:"small",clearable:""},{default:r(()=>[(d(!0),h(M,null,X(t(de)(t(W).PRODUCTION_REPORT_LINE),a=>(d(),u(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:r(({row:a})=>[v(m(t(ee)(a.productionLine)),1)]),_:1}),i(n,{field:"dateStr",width:"100",title:"\u65E5\u671F"},{header:r(()=>[e[75]||(e[75]=l("div",null,"\u65E5\u671F",-1)),i(te,{modelValue:t(o).dateStr,"onUpdate:modelValue":e[21]||(e[21]=a=>t(o).dateStr=a),onChange:_,type:"date",placeholder:"\u5F00\u59CB\u65E5\u671F",size:"small",clearable:"","value-format":"YYYY-MM-DD",class:"!w-100%"},null,8,["modelValue"])]),default:r(({row:a})=>[v(m(a.dateStr&&t(Z)(a.dateStr)),1)]),_:1}),i(n,{field:"productionTime",width:"100",title:"\u65F6\u95F4\u6BB5"}),i(n,{field:"productionOrderCode",width:"150",title:"\u751F\u4EA7\u5DE5\u5355\u53F7"}),i(n,{field:"salesOrderCode",width:"150",title:"\u9500\u552E\u8BA2\u5355\u53F7"}),i(n,{field:"productNo",width:"100",title:"\u54C1\u53F7"}),i(n,{field:"modelsOrColor",width:"120",title:"\u673A\u578B/\u989C\u8272"}),i(n,{field:"workOrderNum",width:"80",title:"\u5DE5\u5355\u6570\u91CF"}),i(n,{field:"units",width:"80",title:"\u5355\u4F4D"}),i(n,{field:"number",width:"80",title:"\u4EBA\u6570"}),i(n,{field:"type",width:"80",title:"\u5DE5\u5E8F"},{default:r(({row:a})=>[i(ie,{type:t(W).PRODUCTION_REPORT_TYPE,value:a.type},null,8,["type","value"])]),_:1}),i(n,{field:"hoursReportNum",width:"80",title:"\u5C0F\u65F6\u5B8C\u6210"}),i(n,{field:"totalReportNum",width:"80",title:"\u7D2F\u8BA1\u5B8C\u6210"}),i(n,{field:"avgWork",width:"80",title:"\u5E73\u5747\u5DE5\u65F6"}),i(n,{field:"actualWork",width:"80",title:"\u5B9E\u9645\u5DE5\u65F6"}),i(n,{field:"standardWork",width:"80",title:"\u6807\u51C6\u5DE5\u65F6"}),i(n,{title:"\u5F02\u5E38\u4EBA\u6570",width:"120",field:"abnormalNum"}),i(n,{title:"\u5F02\u5E38\u5DE5\u65F6",width:"120",field:"abnormalWork"}),i(n,{title:"\u5F02\u5E38\u95EE\u9898\u70B9",width:"120",field:"abnormalRemark"}),i(n,{title:"\u5F02\u5E38\u5BF9\u7B56",width:"120",field:"abnormalCountermeasures"}),i(n,{title:"\u64CD\u4F5C",width:"140"},{default:r(({row:a})=>[a.audit===0?(d(),h("div",Ml,[k((d(),u(c,{onClick:f=>U("update",a.batchesId),link:""},{default:r(()=>e[76]||(e[76]=[v("\u7F16\u8F91")])),_:2},1032,["onClick"])),[[T,["production:day:update"]]]),k((d(),u(c,{onClick:f=>A(a.id),link:"",type:"danger"},{default:r(()=>e[77]||(e[77]=[v("\u5220\u9664")])),_:2},1032,["onClick"])),[[T,["production:day:delete"]]])])):p("",!0),a.audit===1?(d(),h("div",Wl,[k((d(),u(c,{onClick:f=>U("update",a.batchesId),link:""},{default:r(()=>e[78]||(e[78]=[v("\u7F16\u8F91")])),_:2},1032,["onClick"])),[[T,["production:day:auditupdate"]]]),k((d(),u(c,{onClick:f=>A(a.id),link:"",type:"danger"},{default:r(()=>e[79]||(e[79]=[v("\u5220\u9664")])),_:2},1032,["onClick"])),[[T,["production:day:auditdelete"]]])])):p("",!0)]),_:1})]),_:1},8,["data"]),l("div",$l,[i(He,{"current-page":t(o).pageNo,"onUpdate:currentPage":e[22]||(e[22]=a=>t(o).pageNo=a),"page-size":t(o).pageSize,"onUpdate:pageSize":e[23]||(e[23]=a=>t(o).pageSize=a),"page-sizes":[10,20],total:Y.value,layout:"total, sizes, prev, pager, next",onSizeChange:Ae,onCurrentChange:Me,small:""},null,8,["current-page","page-size","total"])])])])):k((d(),h("div",{key:0,class:"mobile-card-container","infinite-scroll-distance":20,"infinite-scroll-immediate":re.value},[(d(!0),h(M,null,X(V.value,a=>(d(),h("div",{key:a.id,class:"mobile-data-card"},[l("div",Ea,[l("div",Ua,[i(x,{icon:"ep:user",class:"title-icon"}),l("span",null,m(a.teamLeader),1),a.audit===0?(d(),h("span",Pa,[i($e,{modelValue:a.checked,"onUpdate:modelValue":f=>a.checked=f,onChange:f=>((C,N)=>{C?I.value.some(O=>O.batchesId===N.batchesId)||I.value.push(N):I.value=I.value.filter(O=>O.batchesId!==N.batchesId)})(f,a),style:{"margin-right":"8px"}},null,8,["modelValue","onUpdate:modelValue","onChange"])])):p("",!0)]),l("div",Ia,[i(x,{icon:"ep:calendar",class:"date-icon"}),l("span",null,m(a.dateStr&&t(Z)(a.dateStr)),1)])]),l("div",Ya,[l("div",Aa,[i(x,{icon:"ep:info-filled",class:"section-icon"}),e[44]||(e[44]=l("span",null,"\u57FA\u672C\u4FE1\u606F",-1))]),l("div",Ma,[l("div",Wa,[e[45]||(e[45]=l("span",{class:"info-label"},"\u4EA7\u7EBF",-1)),l("span",$a,m(t(ee)(a.productionLine)),1)]),l("div",qa,[e[46]||(e[46]=l("span",{class:"info-label"},"\u5E94\u51FA\u52E4",-1)),l("span",Ha,m(a.requiredAttendanceNum||0)+"\u4EBA",1)]),l("div",ja,[e[47]||(e[47]=l("span",{class:"info-label"},"\u5B9E\u9645\u51FA\u52E4",-1)),l("span",Fa,m(a.actualAttendanceNum||0)+"\u4EBA",1)]),l("div",Ja,[e[48]||(e[48]=l("span",{class:"info-label"},"\u7EC4\u88C5\u4EBA\u6570",-1)),l("span",Ga,m(a.assembledNum||0)+"\u4EBA",1)]),l("div",Ka,[e[49]||(e[49]=l("span",{class:"info-label"},"\u65F6\u95F4\u6BB5",-1)),l("span",Ba,m(a.productionTime&&ae(a.productionTime)),1)])])]),l("div",Qa,[l("div",Xa,[i(x,{icon:"ep:document",class:"section-icon"}),e[50]||(e[50]=l("span",null,"\u8BA2\u5355\u4FE1\u606F",-1))]),l("div",Za,[l("div",el,[e[51]||(e[51]=l("span",{class:"info-label"},"\u751F\u4EA7\u5DE5\u5355\u53F7",-1)),l("span",al,m(a.productionOrderCode||"\u6682\u65E0"),1)]),l("div",ll,[e[52]||(e[52]=l("span",{class:"info-label"},"\u9500\u552E\u8BA2\u5355\u53F7",-1)),l("span",tl,m(a.salesOrderCode),1)]),l("div",il,[e[53]||(e[53]=l("span",{class:"info-label"},"\u54C1\u53F7",-1)),l("span",ol,m(a.productNo),1)]),l("div",nl,[e[54]||(e[54]=l("span",{class:"info-label"},"\u54C1\u540D",-1)),l("span",dl,m(a.modelsOrColor),1)]),l("div",sl,[e[55]||(e[55]=l("span",{class:"info-label"},"\u5355\u4F4D",-1)),l("span",rl,m(a.units),1)]),l("div",cl,[e[56]||(e[56]=l("span",{class:"info-label"},"\u5DE5\u5E8F",-1)),l("span",ul,[i(ie,{type:t(W).PRODUCTION_REPORT_TYPE,value:a.type},null,8,["type","value"])])])])]),l("div",pl,[l("div",ml,[i(x,{icon:"ep:data-analysis",class:"section-icon"}),e[57]||(e[57]=l("span",null,"\u751F\u4EA7\u6570\u636E",-1))]),l("div",fl,[l("div",vl,[e[58]||(e[58]=l("span",{class:"info-label"},"\u6570\u91CF",-1)),l("span",yl,m(a.workOrderNum),1)]),l("div",wl,[e[59]||(e[59]=l("span",{class:"info-label"},"\u4EBA\u6570",-1)),l("span",hl,m(a.number||0),1)]),l("div",bl,[e[60]||(e[60]=l("span",{class:"info-label"},"\u751F\u4EA7\u6570",-1)),l("span",kl,m(a.hoursReportNum),1)]),l("div",gl,[e[61]||(e[61]=l("span",{class:"info-label"},"\u7D2F\u8BA1\u5B8C\u6210",-1)),l("span",Tl,m(a.totalReportNum),1)]),l("div",Cl,[e[62]||(e[62]=l("span",{class:"info-label"},"\u5B9E\u9645\u5DE5\u65F6",-1)),l("span",_l,m(a.actualWork),1)]),l("div",xl,[e[63]||(e[63]=l("span",{class:"info-label"},"\u6807\u51C6\u5DE5\u65F6",-1)),l("span",Ll,m(a.standardWork),1)])])]),l("div",Rl,[l("div",Nl,[i(x,{icon:"ep:flag",class:"section-icon"}),e[64]||(e[64]=l("span",null,"\u72B6\u6001\u4FE1\u606F",-1))]),l("div",Vl,[l("div",Sl,[e[65]||(e[65]=l("span",{class:"info-label"},"\u8BD5\u4EA7",-1)),i(Q,{type:a.isTrial===0?"primary":"danger",size:"small",style:{width:"50px"}},{default:r(()=>[v(m(a.isTrial===0?"\u5426":"\u662F"),1)]),_:2},1032,["type"])]),l("div",zl,[e[66]||(e[66]=l("span",{class:"info-label"},"\u8FD4\u5DE5",-1)),i(Q,{type:a.isRework===0?"primary":"danger",size:"small",style:{width:"50px"}},{default:r(()=>[v(m(a.isRework===0?"\u5426":"\u662F"),1)]),_:2},1032,["type"])]),l("div",Dl,[e[67]||(e[67]=l("span",{class:"info-label"},"\u5BA1\u6838\u72B6\u6001",-1)),i(Q,{type:a.audit===0?"danger":"success",size:"small",style:{width:"50px"}},{default:r(()=>[v(m(a.audit===0?"\u672A\u5BA1\u6838":"\u5DF2\u5BA1\u6838"),1)]),_:2},1032,["type"])]),l("div",Ol,[e[68]||(e[68]=l("span",{class:"info-label"},"\u5F02\u5E38\u5DE5\u65F6",-1)),l("span",El,m(a.abnormalWork||0),1)]),a.pmcRemark?(d(),h("div",Ul,[e[69]||(e[69]=l("span",{class:"info-label"},"PMC\u5907\u6CE8",-1)),i(qe,{effect:"dark",content:a.pmcRemark,placement:"top"},{default:r(()=>[l("span",Pl,m(a.pmcRemark),1)]),_:2},1032,["content"])])):p("",!0)])]),l("div",Il,[a.audit===0?(d(),h(M,{key:0},[k((d(),u(c,{type:"primary",plain:"",onClick:f=>U("update",a.batchesId),class:"mobile-card-btn mobile-edit-btn",size:"small"},{default:r(()=>[i(x,{icon:"ep:edit",class:"btn-icon"}),e[70]||(e[70]=l("span",null,"\u7F16\u8F91",-1))]),_:2},1032,["onClick"])),[[T,["production:day:update"]]]),k((d(),u(c,{type:"danger",plain:"",onClick:f=>A(a.id),class:"mobile-card-btn mobile-delete-btn",size:"small"},{default:r(()=>[i(x,{icon:"ep:delete",class:"btn-icon"}),e[71]||(e[71]=l("span",null,"\u5220\u9664",-1))]),_:2},1032,["onClick"])),[[T,["production:day:delete"]]])],64)):p("",!0),a.audit===1?(d(),h(M,{key:1},[k((d(),u(c,{type:"primary",plain:"",onClick:f=>U("update",a.batchesId),class:"mobile-card-btn mobile-edit-btn"},{default:r(()=>[i(x,{icon:"ep:edit",class:"btn-icon"}),e[72]||(e[72]=l("span",null,"\u7F16\u8F91",-1))]),_:2},1032,["onClick"])),[[T,["production:day:auditupdate"]]]),k((d(),u(c,{type:"danger",plain:"",onClick:f=>A(a.id),class:"mobile-card-btn mobile-delete-btn"},{default:r(()=>[i(x,{icon:"ep:delete",class:"btn-icon"}),e[73]||(e[73]=l("span",null,"\u5220\u9664",-1))]),_:2},1032,["onClick"])),[[T,["production:day:auditdelete"]]])],64)):p("",!0)])]))),128))],8,Oa)),[[Je,Ue],[we,z.value]]),P.value?p("",!0):(d(),h("div",ql,"\u5171\u8BA1\uFF1A"+m(Y.value)+"\u6761\u8BB0\u5F55",1))])),[[we,G.value]]):(d(),h("div",_a,[i(w,{custom:"",ref_key:"toolbarRef",ref:se,size:"mini"},{buttons:r(()=>[k((d(),u(c,{type:"primary",size:"small",onClick:e[0]||(e[0]=a=>U("create"))},{default:r(()=>e[28]||(e[28]=[v(" \u65B0\u589E ")])),_:1})),[[T,["production:day:create"]]]),t(o).workType===0?k((d(),u(c,{key:0,onClick:e[1]||(e[1]=a=>(async()=>{const f=b.value;if(!f)return;const C=f.getCheckboxRecords(),N=f.getCheckboxReserveRecords(),O=new Map;[...C,...N].forEach(be=>{O.set(be.batchesId,be)});const he=Array.from(O.keys());if(he.length===0)return g.alertError("\u8BF7\u9009\u62E9\u8981\u5BA1\u6838\u7684\u6570\u636E"),void(D.value=[]);const Ge=C.length+N.length;g.confirm("\u5F53\u524D\u5DF2\u7ECF\u9009\u4E2D"+Ge+"\u6761\u6570\u636E\uFF0C\u786E\u5B9A\u8981\u5BA1\u6838\u901A\u8FC7\u5417\uFF1F").then(async()=>{await E.auditDay(he),g.success("\u5BA1\u6838\u6210\u529F"),await L(!0)})})()),type:"success",style:{"margin-left":"30px"},size:"small"},{default:r(()=>e[29]||(e[29]=[v("\u5BA1\u6838")])),_:1})),[[T,["production:day:audit"]]]):p("",!0),k((d(),u(c,{onClick:e[2]||(e[2]=a=>(async()=>{try{await g.exportConfirm(),K.value=!0;const f=await E.exportDay(o);Ce.excel(f,o.workType===0?`\u751F\u4EA7\u65E5\u62A5+\u4E34\u65F6\u5DE5\u51FA\u52E4_${le}.xls`:`\u5F02\u5E38\u65E5\u62A5_${le}.xls`)}catch{}finally{K.value=!1}})()),type:"warning",style:{"margin-left":"30px"},size:"small"},{default:r(()=>e[30]||(e[30]=[v("\u5BFC\u51FA")])),_:1})),[[T,["production:day:export"]]]),k((d(),u(c,{onClick:e[3]||(e[3]=a=>(async()=>{try{const f=b.value.getCheckboxRecords();if(f.length===0)return g.alertError("\u8BF7\u9009\u62E9\u8981\u5BFC\u51FA\u7684\u6570\u636E"),void(D.value=[]);const C=Array.from(new Set(f.map(O=>O.batchesId)));await g.exportConfirm("\u786E\u8BA4\u8981\u5BFC\u51FA\u51FA\u52E4\u5DE5\u65F6\u5417\uFF1F"),K.value=!0;const N=await E.exportAttendance(C);Ce.excel(N,`\u51FA\u52E4\u5DE5\u65F6_${le}.xls`)}catch{}finally{K.value=!1}})()),type:"warning",style:{"margin-left":"30px"},size:"small"},{default:r(()=>e[31]||(e[31]=[v("\u5BFC\u51FA\u51FA\u52E4\u5DE5\u65F6")])),_:1})),[[T,["production:day:export"]]]),i(c,{onClick:e[4]||(e[4]=a=>(async()=>{o.workType=o.workType===0?1:0,L()})()),style:{"margin-left":"30px"},size:"small"},{default:r(()=>[v(m(t(o).workType===0?"\u67E5\u770B\u5F02\u5E38\u5DE5\u65F6":"\u67E5\u770B\u751F\u4EA7\u65E5\u62A5"),1)]),_:1})]),_:1},512),l("div",xa,[(d(),u(ye,{key:"tableKey-"+t(o).workType,"row-config":{height:30,keyField:"id"},ref_key:"tableRef",ref:b,onScroll:Re,onRendered:Ne,data:V.value,"header-cell-style":{padding:0},onCellDblclick:me,"filter-config":{showIcon:!1},border:"",stripe:"",align:"center","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:z.value,"menu-config":t(Ve),onMenuClick:Oe,"checkbox-config":{reserve:!0,highlight:!0,range:!0},"edit-config":{trigger:"manual",mode:"row",autoClear:!1},height:"100%","max-height":"100%","cell-style":Ie,"footer-cell-config":{height:30},"footer-data":Ee.value,"show-footer":"","footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"}},{default:r(()=>[i(n,{type:"checkbox","min-width":"40",field:"id"}),i(n,{field:"teamLeader","min-width":"100"},{header:r(()=>[e[32]||(e[32]=l("div",null,"\u73ED\u7EC4\u957F",-1)),i(R,{modelValue:t(o).teamLeader,"onUpdate:modelValue":e[5]||(e[5]=a=>t(o).teamLeader=a),onChange:_,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),i(n,{field:"productionLine","min-width":"100"},{header:r(()=>[e[33]||(e[33]=l("div",null,"\u4EA7\u7EBF",-1)),i(B,{modelValue:t(o).productionLine,"onUpdate:modelValue":e[6]||(e[6]=a=>t(o).productionLine=a),onChange:_,placeholder:"\u9009\u62E9\u4EA7\u7EBF",style:{width:"100%"},size:"small",clearable:""},{default:r(()=>[(d(!0),h(M,null,X(t(de)(t(W).PRODUCTION_REPORT_LINE),a=>(d(),u(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:r(({row:a})=>[v(m(t(ee)(a.productionLine)),1)]),_:1}),i(n,{field:"dateStr","min-width":"100"},{header:r(()=>[l("div",La,[i(te,{modelValue:t(o).startDate,"onUpdate:modelValue":e[7]||(e[7]=a=>t(o).startDate=a),onChange:_,type:"date",placeholder:"\u5F00\u59CB\u65E5\u671F",size:"small",clearable:"","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"]),i(te,{modelValue:t(o).endDate,"onUpdate:modelValue":e[8]||(e[8]=a=>t(o).endDate=a),onChange:_,type:"date",placeholder:"\u7ED3\u675F\u65E5\u671F",size:"small",clearable:"","disabled-date":Pe,"value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])]),default:r(({row:a})=>[v(m(a.dateStr&&t(Z)(a.dateStr)),1)]),_:1}),t(o).workType===0?(d(),u(n,{key:0,title:"\u65F6\u95F4\u6BB5","min-width":"120",field:"productionTime"},{default:r(({row:a})=>[v(m(a.productionTime&&ae(a.productionTime)),1)]),_:1})):p("",!0),t(o).workType===1?(d(),u(n,{key:1,title:"\u5F02\u5E38\u65F6\u95F4\u6BB5","min-width":"120",field:"abnormalTime"},{default:r(({row:a})=>[v(m(a.abnormalTime&&ae(a.abnormalTime)),1)]),_:1})):p("",!0),i(n,{field:"productionOrderCode","min-width":"150"},{header:r(()=>[e[34]||(e[34]=l("div",null,"\u751F\u4EA7\u5DE5\u5355\u53F7",-1)),i(R,{modelValue:t(o).productionOrderCode,"onUpdate:modelValue":e[9]||(e[9]=a=>t(o).productionOrderCode=a),onChange:_,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),i(n,{field:"salesOrderCode","min-width":"150"},{header:r(()=>[e[35]||(e[35]=l("div",null,"\u9500\u552E\u8BA2\u5355\u53F7",-1)),i(R,{modelValue:t(o).salesOrderCode,"onUpdate:modelValue":e[10]||(e[10]=a=>t(o).salesOrderCode=a),onChange:_,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),i(n,{field:"productNo","min-width":"120"},{header:r(()=>[e[36]||(e[36]=l("div",null,"\u54C1\u53F7",-1)),i(R,{modelValue:t(o).productNo,"onUpdate:modelValue":e[11]||(e[11]=a=>t(o).productNo=a),onChange:_,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),t(o).workType===0?(d(),u(n,{key:2,field:"isRework","min-width":"50"},{header:r(()=>[e[37]||(e[37]=l("div",null,"\u8FD4\u5DE5",-1)),i(B,{modelValue:t(o).isRework,"onUpdate:modelValue":e[12]||(e[12]=a=>t(o).isRework=a),onChange:_,placeholder:"\u9009\u4E2D\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small",clearable:""},{default:r(()=>[i(j,{label:"\u662F",value:"1"}),i(j,{label:"\u5426",value:"0"})]),_:1},8,["modelValue"])]),default:r(({row:a})=>[v(m(t(De)(a.isRework)),1)]),_:1})):p("",!0),i(n,{title:"\u673A\u578B/\u989C\u8272(\u54C1\u540D)","min-width":"160",field:"modelsOrColor"}),t(o).workType===0?(d(),u(n,{key:3,title:"\u5DE5\u5355\u6570\u91CF","min-width":"70",field:"workOrderNum"})):p("",!0),i(n,{title:"\u5355\u4F4D","min-width":"50",field:"units"}),t(o).workType===0?(d(),u(n,{key:4,title:"\u751F\u4EA7\u6570","min-width":"70",field:"hoursReportNum"})):p("",!0),t(o).workType===0?(d(),u(n,{key:5,title:"\u7D2F\u8BA1\u5B8C\u6210\u6570\u91CF","min-width":"90",field:"totalReportNum"})):p("",!0),t(o).workType===0?(d(),u(n,{key:6,title:"\u5B9E\u9645\u5DE5\u65F6","min-width":"70",field:"actualWork"})):p("",!0),t(o).workType===0?(d(),u(n,{key:7,title:"\u6807\u51C6\u5DE5\u65F6","min-width":"70",field:"standardWork"})):p("",!0),t(o).workType===0?(d(),u(n,{key:8,title:"\u5E73\u5747\u5DE5\u65F6","min-width":"70",field:"avgWork"})):p("",!0),t(o).workType===0?(d(),u(n,{key:9,title:"\u7CFB\u7EDF\u603B\u5DE5\u65F6","min-width":"90",field:"systemTotal"})):p("",!0),t(o).workType===0?(d(),u(n,{key:10,title:"\u7EC4\u88C5\u603B\u5DE5\u65F6(\u65F6/\u5929)","min-width":"130",field:"assembledTotal"})):p("",!0),t(o).workType===0?(d(),u(n,{key:11,title:"\u5DE5\u65F6\u5DEE\u989D","min-width":"90",field:"workHourDiff"})):p("",!0),t(o).workType===0?(d(),u(n,{key:12,field:"number","min-width":"100"},{header:r(()=>[e[38]||(e[38]=l("div",null,"\u884C\u751F\u4EA7\u4EBA\u6570",-1)),i(R,{modelValue:t(o).number,"onUpdate:modelValue":e[13]||(e[13]=a=>t(o).number=a),modelModifiers:{number:!0},onChange:_,clearable:"",placeholder:"\u8F93\u5165\u4EBA\u6570",style:{width:"100%"},size:"small",type:"number",min:0},null,8,["modelValue"])]),_:1})):p("",!0),t(o).workType===0?(d(),u(n,{key:13,field:"type",width:"90"},{header:r(()=>[e[39]||(e[39]=l("div",null,"\u5DE5\u5E8F",-1)),i(B,{modelValue:t(o).type,"onUpdate:modelValue":e[14]||(e[14]=a=>t(o).type=a),onChange:_,placeholder:"\u9009\u62E9\u5DE5\u5E8F",style:{width:"100%"},size:"small",clearable:""},{default:r(()=>[(d(!0),h(M,null,X(t(de)(t(W).PRODUCTION_REPORT_TYPE),a=>(d(),u(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:r(({row:a})=>[i(ie,{type:t(W).PRODUCTION_REPORT_TYPE,value:a.type},null,8,["type","value"])]),_:1})):p("",!0),t(o).workType===0?(d(),u(n,{key:14,title:"\u5BA1\u6838\u72B6\u6001",width:"100"},{default:r(({row:a})=>[i(Q,{type:a.audit===0?"danger":"success"},{default:r(()=>[v(m(a.audit===0?"\u672A\u5BA1\u6838":"\u5DF2\u5BA1\u6838"),1)]),_:2},1032,["type"])]),_:1})):p("",!0),t(o).workType===0?(d(),u(n,{key:15,title:"\u5E94\u51FA\u52E4\u4EBA\u6570",width:"100",field:"requiredAttendanceNum"})):p("",!0),t(o).workType===0?(d(),u(n,{key:16,title:"\u5B9E\u9645\u51FA\u52E4\u4EBA\u6570",width:"100",field:"actualAttendanceNum"})):p("",!0),t(o).workType===0?(d(),u(n,{key:17,title:"\u7EC4\u88C5\u4EBA\u6570","min-width":"100",field:"assembledNum"})):p("",!0),t(o).workType===0?(d(),u(n,{key:18,title:"\u9700\u6C42\u5206\u7C7B\u53F7","min-width":"240",field:"demand"})):p("",!0),t(o).workType===0?(d(),u(n,{key:19,title:"PMC\u4EA4\u8D27\u5907\u6CE8","min-width":"240",field:"pmcRemark"})):p("",!0),t(o).workType===1?(d(),u(n,{key:20,title:"\u5F02\u5E38\u6570\u91CF","min-width":"100",field:"abnormalReportNum"})):p("",!0),t(o).workType===1?(d(),u(n,{key:21,title:"\u5F02\u5E38\u4EBA\u6570","min-width":"100",field:"abnormalNum"})):p("",!0),t(o).workType===1?(d(),u(n,{key:22,title:"\u5F02\u5E38\u5DE5\u65F6","min-width":"100",field:"abnormalWork"})):p("",!0),t(o).workType===1?(d(),u(n,{key:23,title:"\u5F02\u5E38\u95EE\u9898\u70B9","min-width":"120",field:"abnormalRemark"})):p("",!0),t(o).workType===1?(d(),u(n,{key:24,title:"\u5F02\u5E38\u5BF9\u7B56","min-width":"120",field:"abnormalCountermeasures"})):p("",!0),t(o).workType===0?(d(),u(n,{key:25,title:"\u6700\u540E\u6D3B\u52A8\u65F6\u95F4","min-width":"160",field:"createTime"},{default:r(({row:a})=>[v(m(a.createTime&&t(ga)(a.createTime)),1)]),_:1})):p("",!0),i(n,{title:"\u64CD\u4F5C","min-width":"100"},{default:r(({row:a})=>[a.audit===0?(d(),h("div",Ra,[k((d(),u(c,{onClick:f=>A(a.id),link:"",type:"danger"},{default:r(()=>e[40]||(e[40]=[v("\u5220\u9664")])),_:2},1032,["onClick"])),[[T,["production:day:delete"]]])])):p("",!0),a.audit===1?(d(),h("div",Na,[k((d(),u(c,{onClick:f=>A(a.id),link:"",type:"danger"},{default:r(()=>e[41]||(e[41]=[v("\u5220\u9664")])),_:2},1032,["onClick"])),[[T,["production:day:auditdelete"]]])])):p("",!0)]),_:1})]),_:1},8,["data","loading","menu-config","footer-data"]))]),i(We,{total:Y.value,page:t(o).pageNo,"onUpdate:page":e[15]||(e[15]=a=>t(o).pageNo=a),limit:t(o).pageSize,"onUpdate:limit":e[16]||(e[16]=a=>t(o).pageSize=a),onPagination:L},null,8,["total","page","limit"])])),i(je,{title:"\u6279\u91CF\u5220\u9664\u786E\u8BA4",modelValue:H.value,"onUpdate:modelValue":e[26]||(e[26]=a=>H.value=a)},{footer:r(()=>[i(c,{type:"danger",onClick:e[24]||(e[24]=a=>(async()=>{z.value=!0;try{let f=!0;if(D.value.map(C=>{C.audit!==1||ge(["production:day:auditdelete"])||(f=!1)}),f){const C=D.value.map(N=>N.id);await E.deleteDays(C),g.success("\u6279\u91CF\u5220\u9664\u6210\u529F"),H.value=!1,D.value=[]}else g.error("\u9009\u4E2D\u7684\u6570\u636E\u4E2D\u5305\u542B\u5DF2\u5BA1\u6838\u6570\u636E\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\uFF01");L(!0)}catch{g.error("\u6279\u91CF\u5220\u9664\u5931\u8D25")}finally{z.value=!1}})())},{default:r(()=>e[80]||(e[80]=[v("\u786E\u8BA4\u5220\u9664")])),_:1}),i(c,{onClick:e[25]||(e[25]=a=>H.value=!1)},{default:r(()=>e[81]||(e[81]=[v("\u53D6\u6D88")])),_:1})]),default:r(()=>[l("div",null,"\u5F53\u524D\u9009\u4E2D\uFF1A"+m(D.value.length)+" \u6761\u6570\u636E\uFF0C\u786E\u8BA4\u8981\u5220\u9664\u5417\uFF1F",1)]),_:1},8,["modelValue"]),i(Ta,{ref_key:"formRef",ref:ve,onSuccess:e[27]||(e[27]=a=>L(!0))},null,512)]),_:1})}}}),[["__scopeId","data-v-2517cbf0"]]);export{Hl as default};
