import{d as H,y as W,j as c,e2 as A,Y as b,e3 as F,b3 as M,f as P,aH as Y,o as m,c as h,k as t,l,w as r,u as s,v as y,t as x,m as g,F as k,g as $,h as q,aM as B,S as G,H as K,z as L,A as Q}from"./index-C8b06LRn.js";import{E as X}from"./el-card-CaOo8U9P.js";import{_ as Z}from"./index-CkzUfjB7.js";import{f as ee}from"./formatTime-COZ9Bl52.js";const le={class:"flex"},ae={class:"flex items-center"},se={class:"flex"},te={class:"max-h-80 overflow-auto"},re={class:"flex items-center"},ue=H({name:"InfraWebSocket",__name:"index",setup(oe){const w=W(),p=c("https://sj.iaa360.cn:13141/infra/ws".replace("http","ws")+"?token="+A()),o=b(()=>_.value==="OPEN"),D=b(()=>o.value?"success":"red"),{status:_,data:n,send:I,close:J,open:R}=F(p.value,{autoReconnect:!0,heartbeat:!0}),v=c([]),T=b(()=>v.value.slice().reverse());M(()=>{if(n.value)try{if(n.value==="pong")return;const d=JSON.parse(n.value),e=d.type,u=JSON.parse(d.content);if(!e)return void w.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+n.value);if(e==="demo-message-receive")return void(u.single?v.value.push({text:`\u3010\u5355\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}):v.value.push({text:`\u3010\u7FA4\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}));if(e==="notice-push")return void v.value.push({text:`\u3010\u7CFB\u7EDF\u901A\u77E5\u3011\uFF1A${u.title}`,time:new Date().getTime()});w.error("\u672A\u5904\u7406\u6D88\u606F\uFF1A"+n.value)}catch{w.error("\u5904\u7406\u6D88\u606F\u53D1\u751F\u5F02\u5E38\uFF1A"+n.value)}});const i=c(""),f=c(""),j=()=>{const d=JSON.stringify({text:i.value,toUserId:f.value}),e=JSON.stringify({type:"demo-message-send",content:d});I(e),i.value=""},z=()=>{o.value?J():R()},V=c([]);return P(async()=>{V.value=await Y()}),(d,e)=>{const u=Z,C=B,S=G,U=K,N=L,E=Q,O=X;return m(),h(k,null,[t(u,{title:"WebSocket \u5B9E\u65F6\u901A\u4FE1",url:"https://doc.iocoder.cn/websocket/"}),l("div",le,[t(O,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>e[3]||(e[3]=[l("div",{class:"card-header"},[l("span",null,"\u8FDE\u63A5")],-1)])),default:r(()=>[l("div",ae,[e[4]||(e[4]=l("span",{class:"mr-4 text-lg font-medium"}," \u8FDE\u63A5\u72B6\u6001: ",-1)),t(C,{color:s(D)},{default:r(()=>[y(x(s(_)),1)]),_:1},8,["color"])]),e[7]||(e[7]=l("hr",{class:"my-4"},null,-1)),l("div",se,[t(S,{modelValue:s(p),"onUpdate:modelValue":e[0]||(e[0]=a=>g(p)?p.value=a:null),disabled:""},{prepend:r(()=>e[5]||(e[5]=[y("\u670D\u52A1\u5730\u5740")])),_:1},8,["modelValue"]),t(U,{type:s(o)?"danger":"primary",onClick:z},{default:r(()=>[y(x(s(o)?"\u5173\u95ED\u8FDE\u63A5":"\u5F00\u542F\u8FDE\u63A5"),1)]),_:1},8,["type"])]),e[8]||(e[8]=l("p",{class:"mt-4 text-lg font-medium"},"\u6D88\u606F\u8F93\u5165\u6846",-1)),e[9]||(e[9]=l("hr",{class:"my-4"},null,-1)),t(S,{modelValue:s(i),"onUpdate:modelValue":e[1]||(e[1]=a=>g(i)?i.value=a:null),autosize:{minRows:2,maxRows:4},disabled:!s(o),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4F60\u8981\u53D1\u9001\u7684\u6D88\u606F",type:"textarea"},null,8,["modelValue","disabled"]),t(E,{modelValue:s(f),"onUpdate:modelValue":e[2]||(e[2]=a=>g(f)?f.value=a:null),class:"mt-4",placeholder:"\u8BF7\u9009\u62E9\u53D1\u9001\u4EBA"},{default:r(()=>[t(N,{key:"",label:"\u6240\u6709\u4EBA",value:""}),(m(!0),h(k,null,$(s(V),a=>(m(),q(N,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(U,{disabled:!s(o),block:"",class:"ml-2 mt-4",type:"primary",onClick:j},{default:r(()=>e[6]||(e[6]=[y(" \u53D1\u9001 ")])),_:1},8,["disabled"])]),_:1}),t(O,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>e[10]||(e[10]=[l("div",{class:"card-header"},[l("span",null,"\u6D88\u606F\u8BB0\u5F55")],-1)])),default:r(()=>[l("div",te,[l("ul",null,[(m(!0),h(k,null,$(s(T),a=>(m(),h("li",{key:a.time,class:"mt-2"},[l("div",re,[e[11]||(e[11]=l("span",{class:"text-primary mr-2 font-medium"},"\u6536\u5230\u6D88\u606F:",-1)),l("span",null,x(s(ee)(a.time)),1)]),l("div",null,x(a.text),1)]))),128))])])]),_:1})])],64)}}});export{ue as default};
