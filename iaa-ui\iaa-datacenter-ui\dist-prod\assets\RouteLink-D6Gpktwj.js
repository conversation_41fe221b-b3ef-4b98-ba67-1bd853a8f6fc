import{d as be,j as m,r as ee,y as we,f as _e,u as a,aF as M,o as u,c as f,k as o,w as t,m as F,l as x,F as g,g as k,h as r,t as j,v as V,$ as le,x as ke,ax as ae,aC as Ue,G as xe,H as Ne,S as Ie,z as ze,A as qe,aB as Le,B as $e,I as Re,az as Ee,L as Ae,_ as Te}from"./index-C8b06LRn.js";import{_ as Se}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as Be}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{R as N}from"./index-BqCcY1XT.js";import{d as oe}from"./download-D5Lb_h0f.js";import{_ as Fe}from"./RouteUpload.vue_vue_type_script_setup_true_lang-Dih6GUq-.js";import{f as je}from"./dateUtil-D9m5ek6U.js";import"./index-Cl43piKd.js";const Pe={class:"h-[calc(100%-100px)]"},Ge=Te(be({__name:"RouteLink",setup(He,{expose:te}){const I=m(),K=m(),O=m(),$=m(!1),R=m([]),E=m(void 0),A=m(void 0),T=m(!1),n=m({pageNo:1,pageSize:20,itemCode:"",itemName:"",spec:"",model:"",technologyId:void 0}),P=ee([{label:"\u7B49\u4E8E",value:"equal"},{label:"\u4E0D\u7B49\u4E8E",value:"notEqual"},{label:"\u5305\u542B",value:"like"},{label:"\u4E0D\u5305\u542B",value:"notLike"},{label:"\u5DE6\u5339\u914D",value:"likeLeft"},{label:"\u53F3\u5339\u914D",value:"likeRight"}]),d=m({itemCodeCondition:"likeLeft",itemCode:"",itemNameCondition:"like",itemName:"",specCondition:"like",spec:"",modelCondition:"equal",model:"",technologyCondition:"equal",technologyId:void 0,setTechnologyId:void 0,timeConsuming:void 0}),Q=m(0),W=m([]),b=m(!1),S=m(!1),z=we(),q=m([]),G=m(0),v=m(!1),de=ee({body:{options:[[{code:"selected",name:"\u6309\u9009\u4E2D\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF"},{code:"conditions",name:"\u6309\u6761\u4EF6\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF"}]]}}),ie=({menu:i})=>{const l=I.value;if(l)switch(i.code){case"selected":const p=l.getCheckboxRecords();if(p.length===0)return z.alertError("\u8BF7\u9009\u62E9\u8981\u8BBE\u7F6E\u7684\u6570\u636E"),void(R.value=[]);R.value=p,$.value=!0;break;case"conditions":T.value=!0}},ue=async()=>{v.value=!0;try{const i=await N.selectLinkCount(d.value);G.value=i}finally{v.value=!1}},ne=async()=>{v.value=!0;try{const i=await N.exportLink(d.value);oe.excel(i,"\u7B26\u5408\u6761\u4EF6\u7269\u6599\u5217\u8868")}finally{v.value=!1}},me=async()=>{v.value=!0;try{let i=ae(n.value);for(let p in i)Array.isArray(i[p])&&(i[p]=`${i[p].join(",")}`);const l=await N.exportLinkAll(i);oe.excel(l,"\u6240\u6709\u7269\u6599\u5217\u8868")}finally{v.value=!1}},se=async()=>{v.value=!0;try{if(await z.confirm("\u786E\u5B9A\u6279\u91CF\u4FEE\u6539\u7269\u6599\u5DE5\u827A\u8DEF\u7EBF\uFF1F"),!d.value.setTechnologyId)return z.alertError("\u8BF7\u9009\u62E9\u8981\u8BBE\u7F6E\u7684\u5DE5\u827A\u8DEF\u7EBF");await N.saveLinkConditions(d.value),z.success("\u4FDD\u5B58\u6210\u529F"),T.value=!1,ce(),y()}finally{v.value=!1}},ce=()=>{d.value={itemCodeCondition:"equal",itemCode:"",itemNameCondition:"equal",itemName:"",specCondition:"equal",spec:"",modelCondition:"equal",model:"",technologyCondition:"equal",technologyId:void 0,setTechnologyId:void 0,timeConsuming:void 0}},re=()=>{if(S.value){if(Q.value>1e3)return z.alertError("\u5217\u8868\u6570\u636E\u91CF\u8FC7\u5927\uFF0C\u8BF7\u5206\u9875\u67E5\u8BE2"),void(S.value=!1);n.value.pageSize=-1}else n.value.pageSize=20;y()},y=()=>{n.value.pageNo=1,H()},H=async()=>{b.value=!0;try{let i=ae(n.value);for(let p in i)Array.isArray(i[p])&&(i[p]=`${i[p].join(",")}`);const l=await N.getRouteLinkPage(i);W.value=l.list,Q.value=l.total}finally{b.value=!1}},ve=async()=>{b.value=!0;try{let i=[];R.value.forEach(l=>{i.push({id:l.id,itemCode:l.itemCode,technologyId:E.value,timeConsuming:A.value})}),await N.saveLink(i),z.success("\u4FDD\u5B58\u6210\u529F"),H(),$.value=!1,R.value=[],E.value=void 0,A.value=void 0}finally{b.value=!1}},pe=i=>{const l=I.value;if(l)return l.isEditByRow(i)};return te({setQueryParams:i=>{requestAnimationFrame(()=>{n.value.technologyId=i,setTimeout(()=>{y()},500)})}}),_e(()=>{(async()=>{b.value=!0;try{const i=await N.getRouteAllVersionList();q.value=i}finally{b.value=!1}})(),y(),a(I).connect(a(K))}),(i,l)=>{const p=Ue,X=xe,L=Ne,fe=M("vxe-toolbar"),h=M("vxe-column"),U=Ie,s=ze,C=qe,D=Le,J=M("vxe-button"),ge=M("vxe-table"),ye=Be,w=$e,Y=Re,Z=Se,he=Ee,Ce=Ae;return u(),f(g,null,[o(fe,{custom:"",ref_key:"toolbarRef",ref:K,size:"mini"},{buttons:t(()=>[o(p,{modelValue:a(S),"onUpdate:modelValue":l[0]||(l[0]=e=>F(S)?S.value=e:null),onChange:re,"active-text":"\u4E0D\u5206\u9875","inactive-text":"\u5206\u9875",size:"small"},null,8,["modelValue"])]),tools:t(()=>[o(L,{circle:"",onClick:l[1]||(l[1]=e=>{var c;return(c=a(O))==null?void 0:c.open()}),size:"small",class:"mr-5px !h-28px !w-28px"},{default:t(()=>[o(X,{icon:"ep:upload"})]),_:1}),o(L,{circle:"",onClick:me,size:"small",class:"mr-5px !h-28px !w-28px"},{default:t(()=>[o(X,{icon:"ep:download"})]),_:1})]),_:1},512),x("div",Pe,[o(ge,{"row-config":{height:30},ref_key:"tableRef",ref:I,data:a(W),"header-cell-style":{padding:0},"cell-style":{padding:0,height:"30px",color:"#232323"},"filter-config":{showIcon:!1},border:"",stripe:"","show-overflow":"",align:"center",loading:a(b),height:"100%","menu-config":a(de),onMenuClick:ie,"checkbox-config":{labelField:"name",highlight:!0,range:!0},"edit-config":{trigger:"manual",mode:"row",autoClear:!1}},{default:t(()=>[o(h,{type:"checkbox",width:"60",field:"name"}),o(h,{field:"itemCode",width:"100"},{header:t(()=>[l[26]||(l[26]=x("div",null,"\u7269\u6599\u7F16\u7801",-1)),o(U,{modelValue:a(n).itemCode,"onUpdate:modelValue":l[2]||(l[2]=e=>a(n).itemCode=e),onChange:y,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),o(h,{field:"itemName",width:"150"},{header:t(()=>[l[27]||(l[27]=x("div",null,"\u7269\u6599\u540D\u79F0",-1)),o(U,{modelValue:a(n).itemName,"onUpdate:modelValue":l[3]||(l[3]=e=>a(n).itemName=e),onChange:y,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),o(h,{field:"spec"},{header:t(()=>[l[28]||(l[28]=x("div",null,"\u89C4\u683C",-1)),o(U,{modelValue:a(n).spec,"onUpdate:modelValue":l[4]||(l[4]=e=>a(n).spec=e),onChange:y,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),o(h,{field:"model",width:"100"},{header:t(()=>[l[29]||(l[29]=x("div",null,"\u578B\u53F7",-1)),o(U,{modelValue:a(n).model,"onUpdate:modelValue":l[5]||(l[5]=e=>a(n).model=e),onChange:y,clearable:"",placeholder:"\u6309\u56DE\u8F66\u7B5B\u9009",style:{width:"100%"},size:"small"},null,8,["modelValue"])]),_:1}),o(h,{title:"\u5DE5\u827A\u8DEF\u7EBF",field:"technologyId",width:"200","edit-render":{}},{header:t(()=>[l[30]||(l[30]=x("div",null,"\u5DE5\u827A\u8DEF\u7EBF",-1)),o(C,{modelValue:a(n).technologyId,"onUpdate:modelValue":l[6]||(l[6]=e=>a(n).technologyId=e),onChange:y,size:"small",clearable:"",style:{width:"100%"}},{default:t(()=>[o(s,{label:"\u7A7A",value:-2}),o(s,{label:"\u975E\u7A7A",value:-1}),(u(!0),f(g,null,k(a(q),e=>(u(),r(s,{key:e.id,label:`${e.routeCode} ${e.routeName}`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),default:t(({row:e})=>[x("div",null,j(e.technologyName),1)]),edit:t(({row:e})=>[o(C,{modelValue:e.technologyId,"onUpdate:modelValue":c=>e.technologyId=c},{default:t(()=>[(u(!0),f(g,null,k(a(q),c=>(u(),r(s,{key:c.id,label:`${c.routeCode} ${c.routeName}`,value:c.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),o(h,{title:"\u6807\u51C6\u5DE5\u65F6",filed:"timeConsuming",width:"200","edit-render":{}},{default:t(({row:e})=>[V(j(e.timeConsuming),1)]),edit:t(({row:e})=>[o(D,{modelValue:e.timeConsuming,"onUpdate:modelValue":c=>e.timeConsuming=c,min:0,precision:2},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),o(h,{title:"\u6700\u540E\u4FEE\u6539\u4EBA",width:"160",field:"updateName"}),o(h,{title:"\u6700\u540E\u4FEE\u6539\u65F6\u95F4",width:"160",field:"updateTime"},{default:t(({row:e})=>[V(j(e.updateTime&&a(je)(e.updateTime)),1)]),_:1}),o(h,{title:"\u64CD\u4F5C",width:"160"},{default:t(({row:e})=>[pe(e)?(u(),f(g,{key:0},[o(J,{onClick:c=>(_=>{const B=I.value;b.value=!0;try{B&&B.clearEdit().then(async()=>{let Ve={id:_.id,itemCode:_.itemCode,technologyId:_.technologyId,timeConsuming:_.timeConsuming};await N.saveLink([Ve]),z.success("\u4FDD\u5B58\u6210\u529F"),H()})}finally{b.value=!1}})(e)},{default:t(()=>l[31]||(l[31]=[V("\u4FDD\u5B58")])),_:2},1032,["onClick"]),o(J,{onClick:l[7]||(l[7]=c=>(()=>{const _=I.value;_&&_.clearEdit()})())},{default:t(()=>l[32]||(l[32]=[V("\u53D6\u6D88")])),_:1})],64)):(u(),r(J,{key:1,onClick:c=>(_=>{const B=I.value;B&&B.setEditRow(_)})(e)},{default:t(()=>l[33]||(l[33]=[V("\u7F16\u8F91")])),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data","loading","menu-config"])]),o(ye,{total:a(Q),page:a(n).pageNo,"onUpdate:page":l[8]||(l[8]=e=>a(n).pageNo=e),limit:a(n).pageSize,"onUpdate:limit":l[9]||(l[9]=e=>a(n).pageSize=e),onPagination:H},null,8,["total","page","limit"]),o(Z,{title:"\u6309\u9009\u4E2D\u6570\u636E\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF",modelValue:a($),"onUpdate:modelValue":l[12]||(l[12]=e=>F($)?$.value=e:null)},{footer:t(()=>[o(L,{type:"primary",onClick:ve},{default:t(()=>l[34]||(l[34]=[V("\u4FDD\u5B58")])),_:1})]),default:t(()=>[x("div",null,"\u5F53\u524D\u9009\u4E2D\uFF1A"+j(a(R).length)+" \u6761\u6570\u636E",1),o(Y,{"label-width":"100px"},{default:t(()=>[o(w,{label:"\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF"},{default:t(()=>[o(C,{modelValue:a(E),"onUpdate:modelValue":l[10]||(l[10]=e=>F(E)?E.value=e:null),filterable:"",clearable:!1},{default:t(()=>[(u(!0),f(g,null,k(a(q),e=>(u(),r(s,{key:e.id,label:`${e.routeCode} ${e.routeName}`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(w,{label:"\u8BBE\u7F6E\u6807\u51C6\u5DE5\u65F6"},{default:t(()=>[o(D,{modelValue:a(A),"onUpdate:modelValue":l[11]||(l[11]=e=>F(A)?A.value=e:null),min:0,precision:2,class:"!w-100%"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),o(Z,{title:"\u6309\u6761\u4EF6\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF",modelValue:a(T),"onUpdate:modelValue":l[25]||(l[25]=e=>F(T)?T.value=e:null)},{footer:t(()=>[o(L,{type:"warning",onClick:ue,loading:a(v)},{default:t(()=>l[35]||(l[35]=[V(" \u9A8C\u8BC1\u6761\u4EF6 ")])),_:1},8,["loading"]),a(G)>0?(u(),r(L,{key:0,type:"warning",onClick:ne,loading:a(v),plain:""},{default:t(()=>l[36]||(l[36]=[V(" \u5BFC\u51FA\u67E5\u770B ")])),_:1},8,["loading"])):le("",!0),a(G)>0?(u(),r(L,{key:1,type:"primary",loading:a(v),onClick:se},{default:t(()=>l[37]||(l[37]=[V(" \u4FDD\u5B58 ")])),_:1},8,["loading"])):le("",!0)]),default:t(()=>[ke((u(),r(Y,{"label-width":"100px"},{default:t(()=>[o(w,{label:"\u7269\u6599\u7F16\u7801"},{default:t(()=>[o(C,{class:"!w-30%",modelValue:a(d).itemCodeCondition,"onUpdate:modelValue":l[13]||(l[13]=e=>a(d).itemCodeCondition=e)},{default:t(()=>[(u(!0),f(g,null,k(a(P),e=>(u(),r(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(U,{class:"!w-70%",modelValue:a(d).itemCode,"onUpdate:modelValue":l[14]||(l[14]=e=>a(d).itemCode=e)},null,8,["modelValue"])]),_:1}),o(w,{label:"\u7269\u6599\u540D\u79F0"},{default:t(()=>[o(C,{class:"!w-30%",modelValue:a(d).itemNameCondition,"onUpdate:modelValue":l[15]||(l[15]=e=>a(d).itemNameCondition=e)},{default:t(()=>[(u(!0),f(g,null,k(a(P),e=>(u(),r(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(U,{class:"!w-70%",modelValue:a(d).itemName,"onUpdate:modelValue":l[16]||(l[16]=e=>a(d).itemName=e)},null,8,["modelValue"])]),_:1}),o(w,{label:"\u89C4\u683C"},{default:t(()=>[o(C,{class:"!w-30%",modelValue:a(d).specCondition,"onUpdate:modelValue":l[17]||(l[17]=e=>a(d).specCondition=e)},{default:t(()=>[(u(!0),f(g,null,k(a(P),e=>(u(),r(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(U,{class:"!w-70%",modelValue:a(d).spec,"onUpdate:modelValue":l[18]||(l[18]=e=>a(d).spec=e)},null,8,["modelValue"])]),_:1}),o(w,{label:"\u578B\u53F7"},{default:t(()=>[o(C,{class:"!w-30%",modelValue:a(d).modelCondition,"onUpdate:modelValue":l[19]||(l[19]=e=>a(d).modelCondition=e)},{default:t(()=>[(u(!0),f(g,null,k(a(P),e=>(u(),r(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),o(U,{class:"!w-70%",modelValue:a(d).model,"onUpdate:modelValue":l[20]||(l[20]=e=>a(d).model=e)},null,8,["modelValue"])]),_:1}),o(w,{label:"\u5DE5\u827A\u8DEF\u7EBF"},{default:t(()=>[o(C,{class:"!w-30%",modelValue:a(d).technologyCondition,"onUpdate:modelValue":l[21]||(l[21]=e=>a(d).technologyCondition=e)},{default:t(()=>[o(s,{label:"\u7A7A",value:"isNull"}),o(s,{label:"\u975E\u7A7A",value:"nonNull"}),o(s,{label:"\u7B49\u4E8E",value:"equal"}),o(s,{label:"\u4E0D\u7B49\u4E8E",value:"notEqual"})]),_:1},8,["modelValue"]),o(C,{class:"!w-70%",modelValue:a(d).technologyId,"onUpdate:modelValue":l[22]||(l[22]=e=>a(d).technologyId=e),disabled:["isNull","nonNull"].includes(a(d).technologyCondition)},{default:t(()=>[(u(!0),f(g,null,k(a(q),e=>(u(),r(s,{key:e.id,label:`${e.routeCode} ${e.routeName}`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),o(he,null,{default:t(()=>[V("\u5F53\u524D\u7B26\u5408\u6761\u4EF6\u6570\u91CF\uFF1A"+j(a(G)),1)]),_:1}),o(w,{label:"\u8BBE\u7F6E\u5DE5\u827A\u8DEF\u7EBF"},{default:t(()=>[o(C,{class:"!w-100%",modelValue:a(d).setTechnologyId,"onUpdate:modelValue":l[23]||(l[23]=e=>a(d).setTechnologyId=e)},{default:t(()=>[(u(!0),f(g,null,k(a(q),e=>(u(),r(s,{key:e.id,label:`${e.routeCode} ${e.routeName}`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(w,{label:"\u8BBE\u7F6E\u6807\u51C6\u5DE5\u65F6"},{default:t(()=>[o(D,{modelValue:a(d).timeConsuming,"onUpdate:modelValue":l[24]||(l[24]=e=>a(d).timeConsuming=e),min:0,precision:2,class:"!w-100%"},null,8,["modelValue"])]),_:1})]),_:1})),[[Ce,a(v)]])]),_:1},8,["modelValue"]),o(Fe,{ref_key:"uploadRef",ref:O,onSuccess:y},null,512)],64)}}}),[["__scopeId","data-v-304ed1b3"]]);export{Ge as default};
