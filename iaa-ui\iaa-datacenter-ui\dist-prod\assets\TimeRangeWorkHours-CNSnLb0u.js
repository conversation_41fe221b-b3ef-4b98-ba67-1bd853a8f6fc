import{d as W,y as Q,e as X,Y as N,j as Z,au as Y,o as n,c,k as e,w as l,c0 as ee,F as v,g as w,l as U,v as I,t as $,h as i,u as b,N as h,D as E,$ as O,G as A,H as le,B as ae,a8 as te,ab as de,S as oe,z as ue,A as ne,I as me,J as re,K as pe,_ as ie}from"./index-C8b06LRn.js";import{E as se}from"./el-card-CaOo8U9P.js";import{E as Ve}from"./el-time-select-BrN8x4_E.js";const fe={style:{display:"flex","justify-content":"space-between","align-items":"center"}},ce={key:0},be={class:"card-title"},_e={class:"time-range-row"},ye={key:4,style:{"text-align":"center","margin-top":"10px"}},ke={class:"time-range-row"},ge=ie(W({__name:"TimeRangeWorkHours",props:{modelValue:{},type:{}},emits:["update:modelValue"],setup(j,{emit:z}){const M=Q(),P=j,S=z,G=X(),D=N(()=>G.getMobile),T=t=>{const u=new Date,[s,r]=t.split(":").map(Number);return new Date(u.getFullYear(),u.getMonth(),u.getDate(),s,r)},J=t=>{const u=[{start:"12:00",end:"13:00"},{start:"17:30",end:"18:00"}];for(const s of u){const r=T(s.start),g=T(s.end);if(t>=r&&t<g)return!0}return!1},H=(t,u)=>{const s=T(t),r=T(u);if(r<=s)return 0;let g=0,f=new Date(s);for(;f<r;){const p=new Date(f.getTime()+6e4),m=p>r?r:p;J(f)||(g+=(m.getTime()-f.getTime())/6e4),f=m}return g},V=Z(P.modelValue&&P.modelValue.length>0?P.modelValue.map(t=>({number:t.number,workingHours:t.workingHours||0,timeStart:t.timeStart||"",timeEnd:t.timeEnd||"",timeRange:t.timeRange||"",remark:t.remark||"",type:t.type,lendingProductionLine:t.lendingProductionLine||"",deptCode:t.deptCode||"",borrowProductionLine:t.borrowProductionLine||""})):P.type===1?h(E.PRODUCTION_REPORT_OFFICIALLY_TYPE).map(t=>({number:"",workingHours:0,timeStart:"08:30",timeEnd:"21:00",timeRange:"",remark:"",type:t.value})):[{timeStart:"08:30",timeEnd:"21:00",number:"",remark:"",workingHours:0,timeRange:""}]),K=N(()=>V.value.map(t=>{const u=H(t.timeStart,t.timeEnd);return{...t,workingHours:parseFloat((u/60*(t.number||0)).toFixed(3)),timeRange:`${t.timeStart}-${t.timeEnd}`}}));Y(()=>K.value,t=>{S("update:modelValue",t)},{deep:!0});const F=()=>{V.value.push({timeStart:"08:30",timeEnd:"21:00",number:"",remark:"",type:0,workingHours:0,timeRange:""}),S("update:modelValue",[...V.value])},x=t=>{M.confirm("\u786E\u5B9A\u8981\u7B2C"+(t+1)+"\u884C\u5220\u9664\u5417\uFF1F").then(()=>{V.value.splice(t,1),S("update:modelValue",[...V.value])})};return Y(V,t=>{t.forEach(u=>{const s=H(u.timeStart,u.timeEnd);u.workingHours=parseFloat((s/60*(u.number||0)).toFixed(3)),u.timeRange=`${u.timeStart}-${u.timeEnd}`})},{deep:!0,immediate:!0}),(t,u)=>{const s=A,r=le,g=A,f=Ve,p=ae,m=te,k=de,_=oe,C=ue,R=ne,L=me,y=re,q=pe,B=se;return n(),c("div",null,[e(L,{ref:"formRef","label-width":"120px"},{default:l(()=>[e(B,{class:"mb-4",shadow:"never"},ee({default:l(()=>[D.value?(n(),c("div",ce,[(n(!0),c(v,null,w(V.value,(a,o)=>(n(),c("div",{key:o,class:"card-item"},[U("div",be,[I("\u7B2C "+$(o+1)+" \u6761 / \u5171 "+$(V.value.length)+" \u6761 ",1),e(r,{style:{padding:"0",color:"#fff"},link:"",onClick:d=>x(o)},{default:l(()=>[e(g,{icon:"ep:delete"})]),_:2},1032,["onClick"])]),e(L,{class:"mobile-body-form"},{default:l(()=>[e(k,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[e(p,{label:"\u65F6\u95F4\u6BB5"},{default:l(()=>[U("div",_e,[e(f,{modelValue:a.timeStart,"onUpdate:modelValue":d=>a.timeStart=d,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:30",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),u[1]||(u[1]=U("span",{class:"time-range-separator"},"-",-1)),e(f,{modelValue:a.timeEnd,"onUpdate:modelValue":d=>a.timeEnd=d,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:30","min-time":a.timeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(k,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(p,{label:"\u4EBA\u6570"},{default:l(()=>[e(_,{modelValue:a.number,"onUpdate:modelValue":d=>a.number=d,type:"number",placeholder:"\u4EBA\u6570",min:"0",onInput:d=>a.number=Number(d)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1024)]),_:2},1024),e(m,{span:12},{default:l(()=>[e(p,{label:"\u5DE5\u65F6"},{default:l(()=>[e(_,{modelValue:a.workingHours,"onUpdate:modelValue":d=>a.workingHours=d,readonly:"",disabled:"",placeholder:"\u5DE5\u65F6"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024),t.type===1?(n(),i(k,{key:0},{default:l(()=>[t.type===1?(n(),i(m,{key:0,span:12},{default:l(()=>[e(p,{label:"\u7C7B\u578B"},{default:l(()=>[e(R,{modelValue:a.type,"onUpdate:modelValue":d=>a.type=d,placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",style:{width:"100%"}},{default:l(()=>[(n(!0),c(v,null,w(b(h)(b(E).PRODUCTION_REPORT_OFFICIALLY_TYPE),d=>(n(),i(C,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)):O("",!0),e(m,{span:12},{default:l(()=>[e(p,{label:"\u5907\u6CE8"},{default:l(()=>[e(_,{modelValue:a.remark,"onUpdate:modelValue":d=>a.remark=d,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):t.type===4?(n(),i(k,{key:1},{default:l(()=>[e(m,{span:12},{default:l(()=>[e(p,{label:"\u501F\u51FA\u4EA7\u7EBF"},{default:l(()=>[e(R,{modelValue:a.deptCode,"onUpdate:modelValue":d=>a.deptCode=d,style:{width:"100%"}},{default:l(()=>[(n(!0),c(v,null,w(b(h)(b(E).PRODUCTION_DEPT),d=>(n(),i(C,{clearable:"",key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(m,{span:12},{default:l(()=>[e(p,{label:"\u5907\u6CE8"},{default:l(()=>[e(_,{modelValue:a.remark,"onUpdate:modelValue":d=>a.remark=d,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):t.type===5?(n(),i(k,{key:2},{default:l(()=>[e(m,{span:12},{default:l(()=>[e(p,{label:"\u501F\u5165\u4EA7\u7EBF"},{default:l(()=>[e(R,{modelValue:a.deptCode,"onUpdate:modelValue":d=>a.deptCode=d,style:{width:"100%"}},{default:l(()=>[(n(!0),c(v,null,w(b(h)(b(E).PRODUCTION_DEPT),d=>(n(),i(C,{clearable:"",key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024),e(m,{span:12},{default:l(()=>[e(p,{label:"\u5907\u6CE8"},{default:l(()=>[e(_,{modelValue:a.remark,"onUpdate:modelValue":d=>a.remark=d,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)):(n(),i(k,{key:3},{default:l(()=>[e(m,{span:24},{default:l(()=>[e(p,{label:"\u5907\u6CE8"},{default:l(()=>[e(_,{modelValue:a.remark,"onUpdate:modelValue":d=>a.remark=d,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1024)),o===V.value.length-1?(n(),c("div",ye,[e(k,{gutter:10},{default:l(()=>[e(m,{span:24},{default:l(()=>[e(r,{type:"primary",plain:"",onClick:F,class:"w-full"},{default:l(()=>u[2]||(u[2]=[I("\u65B0\u589E\u884C")])),_:1})]),_:1})]),_:1})])):O("",!0)]),_:2},1024)]))),128))])):(n(),i(q,{key:1,data:V.value,border:""},{default:l(()=>[e(y,{label:"\u5E8F\u53F7",type:"index","min-width":"30",align:"center"}),e(y,{label:"\u65F6\u95F4\u6BB5","min-width":"180"},{default:l(({row:a})=>[U("div",ke,[e(f,{modelValue:a.timeStart,"onUpdate:modelValue":o=>a.timeStart=o,placeholder:"\u8D77\u59CB\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:30",placement:"top-start"},null,8,["modelValue","onUpdate:modelValue"]),u[3]||(u[3]=U("span",{class:"time-range-separator"},"-",-1)),e(f,{modelValue:a.timeEnd,"onUpdate:modelValue":o=>a.timeEnd=o,placeholder:"\u7ED3\u675F\u65F6\u95F4",start:"08:30",end:"23:30",step:"00:30","min-time":a.timeStart,placement:"top-start"},null,8,["modelValue","onUpdate:modelValue","min-time"])])]),_:1}),e(y,{label:"\u4EBA\u6570","min-width":"50"},{default:l(({row:a})=>[e(_,{modelValue:a.number,"onUpdate:modelValue":o=>a.number=o,type:"number",placeholder:"\u4EBA\u6570",min:"0",onInput:o=>a.number=Number(o)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:1}),e(y,{label:"\u5DE5\u65F6","min-width":"50"},{default:l(({row:a})=>[e(_,{modelValue:a.workingHours,"onUpdate:modelValue":o=>a.workingHours=o,readonly:"",placeholder:"\u5DE5\u65F6"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),t.type===1?(n(),i(y,{key:0,label:"\u7C7B\u578B","min-width":"90",prop:"type"},{default:l(({row:a})=>[e(R,{modelValue:a.type,"onUpdate:modelValue":o=>a.type=o,placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",style:{width:"100%"}},{default:l(()=>[(n(!0),c(v,null,w(b(h)(b(E).PRODUCTION_REPORT_OFFICIALLY_TYPE),o=>(n(),i(C,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1})):O("",!0),t.type===5||t.type===4?(n(),i(y,{key:1,label:"\u6765\u6E90","min-width":"90",prop:"deptCode"},{default:l(({row:a})=>[e(R,{modelValue:a.deptCode,"onUpdate:modelValue":o=>a.deptCode=o,placeholder:"\u8BF7\u9009\u62E9",style:{width:"100%"}},{default:l(()=>[(n(!0),c(v,null,w(b(h)(b(E).PRODUCTION_DEPT),o=>(n(),i(C,{clearable:"",key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1})):O("",!0),e(y,{label:"\u5907\u6CE8"},{default:l(({row:a})=>[e(_,{modelValue:a.remark,"onUpdate:modelValue":o=>a.remark=o,placeholder:"\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(y,{label:"\u64CD\u4F5C",width:"60",fixed:"right"},{default:l(({$index:a})=>[e(r,{type:"danger",link:"",onClick:o=>x(a)},{default:l(()=>u[4]||(u[4]=[I("\u5220\u9664")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]))]),_:2},[D.value?void 0:{name:"header",fn:l(()=>[U("div",fe,[e(r,{type:"primary",onClick:F,size:"small",plain:""},{default:l(()=>[e(s,{icon:"ep:plus",style:{"margin-right":"4px"}}),u[0]||(u[0]=I(" \u65B0\u589E\u884C "))]),_:1})])]),key:"0"}]),1024)]),_:1},512)])}}}),[["__scopeId","data-v-0f41e2ca"]]);export{ge as default};
