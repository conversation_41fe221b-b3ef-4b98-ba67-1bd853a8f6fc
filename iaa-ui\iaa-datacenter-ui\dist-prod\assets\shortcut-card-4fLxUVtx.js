import{d as _,aj as i,c as a,k as c,l as t,F as r,g as h,o as e,u as v,t as x,a as k,G as g,_ as j}from"./index-C8b06LRn.js";import{_ as y}from"./CardTitle-00NfZwLk.js";const C={class:"flex items-top justify-start flex-wrap gap-10px"},q=["onClick"],w={class:"shortcut-icon"},z={class:"ml-2px"},F=j(_({__name:"shortcut-card",props:{data:i.oneOf([]).isRequired,name:i.string.def("")},setup(d){const{push:u}=k(),o=d;return(G,I)=>{const m=y,p=g;return e(),a(r,null,[c(m,{title:o.name},null,8,["title"]),t("div",C,[(e(!0),a(r,null,h(o.data,(s,f)=>{var l,n;return e(),a("div",{class:"shortcut",key:f,onClick:O=>v(u)({name:s.name})},[t("div",w,[c(p,{icon:(l=s.meta)==null?void 0:l.icon,size:30,color:"#fff"},null,8,["icon"])]),t("div",z,x((n=s.meta)==null?void 0:n.title),1)],8,q)}),128))])],64)}}}),[["__scopeId","data-v-d2d061ae"]]);export{F as default};
