import{d as h,aj as v,j as R,r as f,au as k,h as q,w as t,u as s,p as j,I as x,o as M,k as o,y as U,B as C}from"./index-C8b06LRn.js";import{_ as I}from"./XButton-BOgar_Ex.js";import{I as n}from"./InputPassword-CcRd7dRE.js";import{a as B}from"./profile-BAixQBws.js";const E=h({name:"ResetPwd",__name:"ResetPwd",props:{oldPws:v.string.def("")},emits:["success"],setup(P,{emit:g}){const u=P,c=g,{t:e}=j(),b=U(),m=R(),a=f({oldPassword:"",newPassword:"",confirmPassword:""}),_=f({oldPassword:[{required:!0,message:e("profile.password.oldPwdMsg"),trigger:"blur"},{min:6,max:20,message:e("profile.password.pwdRules"),trigger:"blur"}],newPassword:[{required:!0,message:e("profile.password.newPwdMsg"),trigger:"blur"},{min:6,max:20,message:e("profile.password.pwdRules"),trigger:"blur"}],confirmPassword:[{required:!0,message:e("profile.password.cfPwdMsg"),trigger:"blur"},{required:!0,validator:(i,r,l)=>{a.newPassword!==r?l(new Error(e("profile.password.diffPwd"))):l()},trigger:"blur"}]});return k(()=>u.oldPws,i=>{i&&(a.oldPassword=i)},{immediate:!0}),(i,r)=>{const l=C,p=I,V=x;return M(),q(V,{ref_key:"formRef",ref:m,model:s(a),rules:s(_),"label-width":200},{default:t(()=>[o(l,{label:s(e)("profile.password.oldPassword"),prop:"oldPassword"},{default:t(()=>[o(s(n),{modelValue:s(a).oldPassword,"onUpdate:modelValue":r[0]||(r[0]=d=>s(a).oldPassword=d),disabled:u.oldPws},null,8,["modelValue","disabled"])]),_:1},8,["label"]),o(l,{label:s(e)("profile.password.newPassword"),prop:"newPassword"},{default:t(()=>[o(s(n),{modelValue:s(a).newPassword,"onUpdate:modelValue":r[1]||(r[1]=d=>s(a).newPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),o(l,{label:s(e)("profile.password.confirmPassword"),prop:"confirmPassword"},{default:t(()=>[o(s(n),{modelValue:s(a).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=d=>s(a).confirmPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),o(l,null,{default:t(()=>[o(p,{title:s(e)("common.save"),type:"primary",onClick:r[3]||(r[3]=d=>{var w;(w=s(m))&&w.validate(async y=>{y&&(await B(a.oldPassword,a.newPassword),b.success(e("common.updateSuccess")),c("success"))})})},null,8,["title"]),o(p,{title:s(e)("common.reset"),type:"danger",onClick:r[4]||(r[4]=d=>{var w;(w=s(m))&&w.resetFields()})},null,8,["title"])]),_:1})]),_:1},8,["model","rules"])}}});export{E as _};
