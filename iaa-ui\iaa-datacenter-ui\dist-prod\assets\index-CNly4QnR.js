import{_ as de}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as ne}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{d as ce,j as i,y as fe,r as Y,f as ue,au as me,aF as j,T as pe,o as h,h as k,w as d,k as t,u as l,m as ge,c as Z,x as A,v as y,l as B,t as G,$ as K,E as he,q as ve,H as be}from"./index-C8b06LRn.js";import{_ as ye,F as _}from"./DatabaseUploadForm.vue_vue_type_script_setup_true_lang-v5NMr-l_.js";import{t as w,d as M}from"./Filter-Dzz2caxb.js";import{d as we}from"./download-D5Lb_h0f.js";import"./el-card-CaOo8U9P.js";import"./index-Cl43piKd.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const xe={key:0},ke={class:"h-[calc(100vh-290px)]"},_e={key:1},ze={class:"h-[calc(100vh-290px)]"},Ne=ce({__name:"index",setup(Pe){const R=i([{data:""}]),L=i([{data:""}]),W=i([{data:""}]),U=i([{data:[]}]),ee=i([{label:"\u8BA2\u5355",value:1},{label:"\u8D39\u7528",value:2}]),te=i([{label:"\u8BA2\u5355",value:1},{label:"\u8D39\u7528",value:2}]),E=i(),z=fe(),N=i(!0),P=i([]),O=i(0),u=i("import"),le=i(),m=Y({pageNo:1,pageSize:30,customersCode:void 0}),T=i(0),ae=i([{receivableProject:"\u5408\u8BA1\u603B\u548C",amount:T}]),V=i(0),$=i(0),q=i(0),D=i(0),ie=i([{salesmanName:"\u5408\u8BA1\u603B\u548C",salesPrice:V,shipPrice:$,collectionAmount:q,receivableAmountLocal:D}]),oe=i(),S=i(!0),p=i([]),H=i(0),f=Y({pageNo:1,pageSize:30}),g=async()=>{N.value=!0;try{const r=await _.getReceivablePage(m);P.value=r.list,O.value=r.total,T.value=P.value.reduce((e,s)=>e+s.amount,0).toFixed(3)}finally{N.value=!1}},F=i(!1),x=async()=>{S.value=!0;try{const r=await _.getReceivableStatementsPage(f);p.value=r.list,H.value=r.total,V.value=p.value.reduce((e,s)=>e+s.salesPrice,0).toFixed(4),$.value=p.value.reduce((e,s)=>e+s.shipPrice,0).toFixed(4),q.value=p.value.reduce((e,s)=>e+s.collectionAmount,0).toFixed(4),D.value=p.value.reduce((e,s)=>e+s.receivableAmountLocal,0).toFixed(4)}finally{S.value=!1}};ue(()=>{u.value==="import"?g():u.value==="statements"&&x()});const I=r=>{const e={},s=["type"];r.filterList.forEach(n=>{const{field:c,values:v,datas:a}=n;s.includes(c)&&v.length>0?e[c]=v:a.length>0&&(e[c]=a[0])}),u.value==="import"?(Object.keys(m).forEach(n=>{["pageNo","pageSize"].includes(n)||r.filterList.some(c=>c.field===n)||(m[n]=void 0)}),Object.assign(m,e),g()):(Object.keys(f).forEach(n=>{["pageNo","pageSize"].includes(n)||r.filterList.some(c=>c.field===n)||(f[n]=void 0)}),Object.assign(f,e),x())};me(u,r=>{r==="import"?(J(m),g()):r==="statements"&&(J(f),x())});const J=r=>{Object.keys(r).forEach(e=>{["pageNo","pageSize"].includes(e)||(r[e]=void 0)}),r.pageNo=1};return(r,e)=>{const s=he,n=ve,c=be,v=j("vxe-toolbar"),a=j("vxe-column"),Q=j("vxe-table"),X=ne,re=de,C=pe("hasPermi");return h(),k(re,null,{default:d(()=>[t(n,{modelValue:l(u),"onUpdate:modelValue":e[0]||(e[0]=o=>ge(u)?u.value=o:null),class:"mb-20px"},{default:d(()=>[t(s,{label:"\u5E94\u6536\u91D1\u989D",name:"import"}),t(s,{label:"\u5E94\u6536\u62A5\u8868",name:"statements"})]),_:1},8,["modelValue"]),l(u)==="import"?(h(),Z("div",xe,[t(v,{custom:"",ref:"toolbarRef",size:"mini"},{buttons:d(()=>[A((h(),k(c,{plain:"",type:"success",class:"mr-10px",size:"small",onClick:e[1]||(e[1]=o=>{var b;return(b=l(E))==null?void 0:b.open()}),title:"\u4E0A\u4F20"},{default:d(()=>e[8]||(e[8]=[y(" \u4E0A\u4F20 ")])),_:1})),[[C,["collection:receivable:create"]]])]),_:1},512),B("div",ke,[t(Q,{"row-config":{height:25,keyField:"id"},ref_key:"tableRef",ref:le,data:l(P),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:l(N),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-data":l(ae),"footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},tabindex:"0",size:"mini",onFilterChange:I},{default:d(()=>[t(a,{type:"checkbox",width:"40",field:"id",fixed:"left"}),t(a,{field:"customersCode",width:"120",title:"\u5BA2\u6237\u7F16\u7801",filters:l(R),"filter-render":w},null,8,["filters","filter-render"]),t(a,{field:"customersName",title:"\u5BA2\u6237\u540D\u79F0",width:"120",filters:l(L),"filter-render":w},null,8,["filters","filter-render"]),t(a,{field:"dateStr",title:"\u65E5\u671F",width:"120",filters:l(U),"filter-render":M},null,8,["filters","filter-render"]),t(a,{field:"receivableType",title:"\u8D39\u7528\u7C7B\u522B",width:"120"},{default:d(({row:o})=>[y(G(o.receivableType===1?"\u8BA2\u5355":o.receivableType===2?"\u8D39\u7528":o.receivableType),1)]),_:1}),t(a,{field:"receivableProject",title:"\u9879\u76EE","min-width":"220"}),t(a,{field:"amount",title:"\u8D39\u7528\u91D1\u989D",width:"200"}),t(a,{title:"\u64CD\u4F5C",width:"150",fixed:"right"},{default:d(({row:o})=>[A((h(),k(c,{onClick:b=>(async se=>{z.confirm("\u662F\u5426\u786E\u8BA4\u5220\u9664\uFF1F").then(async()=>{await _.deleteReceivable([se]),z.success("\u5220\u9664\u6210\u529F"),g()})})(o.id),link:"",type:"danger"},{default:d(()=>e[9]||(e[9]=[y(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[C,["collection:receivable:delete"]]])]),_:1})]),_:1},8,["data","loading","footer-data"])]),t(X,{total:l(O),page:l(m).pageNo,"onUpdate:page":e[2]||(e[2]=o=>l(m).pageNo=o),limit:l(m).pageSize,"onUpdate:limit":e[3]||(e[3]=o=>l(m).pageSize=o),onPagination:g},null,8,["total","page","limit"])])):K("",!0),l(u)==="statements"?(h(),Z("div",_e,[t(v,{custom:"",ref:"toolbarRef",size:"mini"},{buttons:d(()=>[A((h(),k(c,{plain:"",type:"info",class:"mr-10px",size:"small",onClick:e[4]||(e[4]=o=>(async()=>{try{await z.exportConfirm(),F.value=!0;const b=await _.exportInformation(f);we.excel(b,"\u8D22\u52A1\u5E94\u6536\u62A5\u8868.xlsx")}catch{}finally{F.value=!1}})()),loading:l(F),title:"\u5BFC\u51FA"},{default:d(()=>e[10]||(e[10]=[y(" \u5BFC\u51FA ")])),_:1},8,["loading"])),[[C,["collection:receivable:export"]]])]),_:1},512),B("div",ze,[t(Q,{"row-config":{height:27,keyField:"id"},ref_key:"tableStatementsRef",ref:oe,data:l(p),"header-cell-style":{padding:0},border:"",stripe:"",align:"center",height:"100%","max-height":"100%","show-overflow":"title","column-config":{resizable:!0},"virtual-y-config":{enabled:!0,gt:0},loading:l(S),"checkbox-config":{reserve:!0,highlight:!0,range:!0},"filter-config":{},"show-footer":"","keep-source":"","footer-data":l(ie),"footer-cell-style":{padding:0,background:"#dcefdc",border:"1px solid #ebeef5"},"mouse-config":{selected:!0},tabindex:"0",size:"mini",onFilterChange:I},{default:d(()=>[t(a,{field:"salesmanName",width:"100",title:"\u4E1A\u52A1\u5458",filters:l(W),"filter-render":w},null,8,["filters","filter-render"]),t(a,{field:"customersCode",width:"100",title:"\u5BA2\u6237\u7F16\u7801",filters:l(R),"filter-render":w},null,8,["filters","filter-render"]),t(a,{field:"customersName",width:"100",title:"\u5BA2\u6237",filters:l(L),"filter-render":w},null,8,["filters","filter-render"]),t(a,{field:"claimDate",width:"100",title:"\u65E5\u671F",filters:l(U),"filter-render":M},null,8,["filters","filter-render"]),t(a,{field:"type",width:"100",title:"\u7C7B\u522B",filters:l(te),"edit-render":{name:"$select",options:l(ee),props:{value:"value",label:"label"}}},{default:d(({row:o})=>[y(G(o.type===1?"\u8BA2\u5355":o.type===2?"\u8D39\u7528":o.type),1)]),_:1},8,["filters","edit-render"]),t(a,{field:"orderNo",title:"\u9879\u76EE\uFF08\u8BA2\u5355\u53F7/\u8D39\u7528\u660E\u7EC6\uFF09",width:"200"}),t(a,{field:"currency",title:"\u5E01\u79CD",width:"120"}),t(a,{field:"salesPrice",title:"\u8BA2\u5355\u91D1\u989D",width:"120"}),t(a,{field:"shipPrice",title:"\u51FA\u8D27\u91D1\u989D",width:"120"}),t(a,{field:"collectionAmount",title:"\u6536\u6B3E\u91D1\u989D",width:"150"}),t(a,{field:"collectionAmountLocal",title:"\u6536\u6B3E\u91D1\u989D\uFF08\u672C\u5E01\uFF09",width:"150"}),t(a,{field:"receivableAmount",title:"\u5E94\u6536\u4F59\u989D","min-width":"120"}),t(a,{field:"receivableAmountLocal",title:"\u5E94\u6536\u4F59\u989D\uFF08\u672C\u5E01\uFF09","min-width":"120"})]),_:1},8,["data","loading","footer-data"])]),t(X,{total:l(H),page:l(f).pageNo,"onUpdate:page":e[5]||(e[5]=o=>l(f).pageNo=o),limit:l(f).pageSize,"onUpdate:limit":e[6]||(e[6]=o=>l(f).pageSize=o),onPagination:x},null,8,["total","page","limit"])])):K("",!0),t(ye,{ref_key:"databaseUploadFormRef",ref:E,onSuccess:e[7]||(e[7]=o=>g())},null,512)]),_:1})}}});export{Ne as default};
