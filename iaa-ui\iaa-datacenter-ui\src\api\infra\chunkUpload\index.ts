import request from '@/config/axios'

// 分片上传初始化请求
export interface ChunkUploadInitReqVO {
  fileName: string
  fileSize: number
  fileMd5: string
  chunkSize?: number
}

// 分片上传初始化响应
export interface ChunkUploadInitRespVO {
  uploadId: string
  totalChunks: number
  chunkSize: number
  uploadedChunks: number[]
  needUpload: boolean
  fileUrl?: string
  fileId?: number
}

// 分片上传请求
export interface ChunkUploadReqVO {
  uploadId: string
  chunkNumber: number
  chunkMd5: string
  chunkFile: File
}

// 分片上传响应
export interface ChunkUploadRespVO {
  chunkNumber: number
  success: boolean
  etag: string
  uploadedChunks: number
  totalChunks: number
}

// 分片合并请求
export interface ChunkMergeReqVO {
  uploadId: string
}

// 分片合并响应
export interface ChunkMergeRespVO {
  fileId: number
  fileUrl: string
  fileName: string
  fileSize: number
  success: boolean
}

// 初始化分片上传
export const initChunkUpload = (data: ChunkUploadInitReqVO) => {
  return request.post<ChunkUploadInitRespVO>({ url: '/infra/chunk-upload/init', data })
}

// 上传分片
export const uploadChunk = (data: ChunkUploadReqVO) => {
  const formData = new FormData()
  formData.append('uploadId', data.uploadId)
  formData.append('chunkNumber', data.chunkNumber.toString())
  formData.append('chunkMd5', data.chunkMd5)
  formData.append('chunkFile', data.chunkFile)

  return request.post<ChunkUploadRespVO>({
    url: '/infra/chunk-upload/upload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 合并分片
export const mergeChunks = (data: ChunkMergeReqVO) => {
  return request.post<ChunkMergeRespVO>({ url: '/infra/chunk-upload/merge', data })
}

// 取消上传
export const cancelUpload = (uploadId: string) => {
  return request.delete<boolean>({ url: '/infra/chunk-upload/cancel', params: { uploadId } })
}

// 查询上传进度
export const getUploadProgress = (uploadId: string) => {
  return request.get<ChunkUploadInitRespVO>({ url: '/infra/chunk-upload/progress', params: { uploadId } })
}
