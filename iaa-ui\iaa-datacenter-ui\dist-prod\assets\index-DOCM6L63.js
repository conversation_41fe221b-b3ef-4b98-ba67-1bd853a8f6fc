import{aG as r}from"./index-C8b06LRn.js";const s=a=>r.get({url:"/infra/job/page",params:a}),o=a=>r.get({url:"/infra/job/get?id="+a}),u=a=>r.post({url:"/infra/job/create",data:a}),i=a=>r.put({url:"/infra/job/update",data:a}),d=a=>r.delete({url:"/infra/job/delete?id="+a}),n=a=>r.download({url:"/infra/job/export-excel",params:a}),l=(a,t)=>{const e={id:a,status:t};return r.put({url:"/infra/job/update-status",params:e})},p=a=>r.put({url:"/infra/job/trigger?id="+a}),f=a=>r.get({url:"/infra/job/get_next_times?id="+a});export{f as a,s as b,u as c,l as d,n as e,d as f,o as g,p as r,i as u};
