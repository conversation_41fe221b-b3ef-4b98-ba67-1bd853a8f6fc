import{d as F,p as M,y as b,j as u,o as g,h as x,w as c,k as i,u as l,v as y,x as A,m as C,H as R,L as j}from"./index-C8b06LRn.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as H}from"./Form-CkLzRm65.js";import{g as L,c as U,a as q}from"./useCrudSchemas-HzMMRa-v.js";import{r as z,a as B}from"./account.data-D0GCpo8Z.js";const D=F({name:"SystemMailAccountForm",__name:"MailAccountForm",emits:["success"],setup(G,{expose:_,emit:w}){const{t:r}=M(),n=b(),e=u(!1),f=u(""),s=u(!1),v=u(""),o=u();_({open:async(t,a)=>{if(e.value=!0,f.value=r("action."+t),v.value=t,a){s.value=!0;try{const m=await L(a);o.value.setValues(m)}finally{s.value=!1}}}});const h=w,k=async()=>{if(o&&await o.value.getElFormRef().validate()){s.value=!0;try{const t=o.value.formModel;v.value==="create"?(await U(t),n.success(r("common.createSuccess"))):(await q(t),n.success(r("common.updateSuccess"))),e.value=!1,h("success")}finally{s.value=!1}}};return(t,a)=>{const m=H,d=R,S=E,V=j;return g(),x(S,{modelValue:l(e),"onUpdate:modelValue":a[1]||(a[1]=p=>C(e)?e.value=p:null),title:l(f)},{footer:c(()=>[i(d,{disabled:l(s),type:"primary",onClick:k},{default:c(()=>a[2]||(a[2]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),i(d,{onClick:a[0]||(a[0]=p=>e.value=!1)},{default:c(()=>a[3]||(a[3]=[y("\u53D6 \u6D88")])),_:1})]),default:c(()=>[A(i(m,{ref_key:"formRef",ref:o,rules:l(z),schema:l(B).formSchema},null,8,["rules","schema"]),[[V,l(s)]])]),_:1},8,["modelValue","title"])}}});export{D as _};
