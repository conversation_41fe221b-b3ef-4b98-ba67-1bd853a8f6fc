import{aG as t}from"./index-C8b06LRn.js";const d=async a=>await t.get({url:"/bpm/model/page",params:a}),p=async a=>await t.get({url:"/bpm/model/get?id="+a}),m=async a=>await t.put({url:"/bpm/model/update",data:a}),l=async a=>await t.put({url:"/bpm/model/update-bpmn",data:a}),o=async(a,e)=>{const s={id:a,state:e};return await t.put({url:"/bpm/model/update-state",data:s})},u=async a=>await t.post({url:"/bpm/model/create",data:a}),r=async a=>await t.delete({url:"/bpm/model/delete?id="+a}),i=async a=>await t.post({url:"/bpm/model/deploy?id="+a});export{m as a,d as b,u as c,r as d,o as e,i as f,p as g,l as u};
