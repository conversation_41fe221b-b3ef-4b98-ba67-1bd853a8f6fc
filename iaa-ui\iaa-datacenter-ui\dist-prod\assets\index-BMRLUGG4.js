import{d as ge,aN as ke,j as m,r as _e,au as Ee,f as Se,aH as he,o as d,h as v,w as i,c as z,g as le,x as oe,u as e,l as G,t as y,k as r,v as h,$ as g,F as ne,m as Te,O as Ue,y as Re,aO as Ve,aP as Ce,aQ as we,B as Ne,aM as xe,aF as De,S as Ae,z as Ie,A as Pe,I as Fe,G as Oe,H as Ge,a8 as je,L as Le,aE as Be}from"./index-C8b06LRn.js";import{_ as ze}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{E as Je}from"./el-card-CaOo8U9P.js";import{b as ie}from"./formCreate-CdPDb26P.js";import{a as $e}from"./index-DDzAA47d.js";import{g as qe}from"./index-czoBMNFJ.js";import{a as He,r as Ke,g as Me}from"./index-BwETMpJ2.js";import{_ as Qe}from"./ProcessInstanceBpmnViewer.vue_vue_type_style_index_0_lang-CysqzuVc.js";import{_ as Xe}from"./ProcessInstanceTaskList.vue_vue_type_script_setup_true_lang-Bwwj1KQr.js";import{_ as We}from"./TaskReturnForm.vue_vue_type_script_name_TaskRollbackDialogForm_setup_true_lang-DQTxc4W4.js";import{_ as Ye}from"./TaskDelegateForm.vue_vue_type_script_setup_true_lang-BfRGgF0r.js";import{_ as Ze}from"./TaskTransferForm.vue_vue_type_script_setup_true_lang-Hv3PPuJ8.js";import{_ as ea}from"./TaskSignCreateForm.vue_vue_type_script_setup_true_lang-BAQTRooy.js";import{s as p,O as A}from"./consts-DWqigJ3H.js";import"./bpmn-embedded-DWy7HXvQ.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import"./XTextButton-BSf0iZhI.js";import"./XButton-BOgar_Ex.js";import"./el-collapse-item-JANV_ocl.js";import"./index-CBsqkafF.js";import"./el-tree-select-E9FCZb0j.js";import"./index-C0LhU1J1.js";import"./index-Dz9lR_me.js";import"./index-Cgv48ZKs.js";import"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import"./index-Cl43piKd.js";import"./constants-D3f7Z3TX.js";import"./index-SMELiJYy.js";import"./el-drawer-C5TFtzfV.js";import"./DictTag.vue_vue_type_script_lang-DZupcDNc.js";import"./color-DXkOL5Tu.js";import"./index-B9cOKMOY.js";import"./formatTime-COZ9Bl52.js";import"./el-timeline-item-BqzIH3Db.js";import"./TaskSignList.vue_vue_type_script_setup_true_lang-DobJUWJd.js";import"./TaskSignDeleteForm.vue_vue_type_script_setup_true_lang-Dv-xFqke.js";const aa={class:"el-icon-picture-outline"},ta={class:"el-icon-picture-outline"},sa={style:{"margin-bottom":"20px","margin-left":"10%","font-size":"14px"}},la={class:"el-icon-document"},oa={key:1},na=ge({name:"BpmProcessInstanceDetail",__name:"index",setup(ia){const{query:re}=Ue(),j=Re(),{proxy:ue}=Be(),pe=ke().getUser.id,L=re.id,I=m(!1),c=m({}),J=m(""),B=m(!0),C=m([]),T=m([]),E=m([]),me=_e({reason:[{required:!0,message:"\u5BA1\u6279\u5EFA\u8BAE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=m([]),P=m([]),f=m(),R=m({rule:[],option:{},value:{}});Ee(()=>P.value,n=>{n==null||n.forEach(a=>{a.btn.show(!1),a.resetBtn.show(!1)})},{deep:!0});const $=async(n,a)=>{const s=T.value.indexOf(n),S=ue.$refs["form"+s][0],b=e(S);if(!b)return;let k=await b.validate();if(!k||f.value&&(k=await f.value.validate(),!k))return;const _={id:n.id,reason:E.value[s].reason,copyUserIds:E.value[s].copyUserIds};if(a){const V=P.value[s];V&&(await V.validate(),_.variables=U.value[s].value),f.value&&(_.variables=fe(n.fieldsPermission)),await He(_),j.success("\u5BA1\u6279\u901A\u8FC7\u6210\u529F")}else await Ke(_),j.success("\u5BA1\u6279\u4E0D\u901A\u8FC7\u6210\u529F");w()},q=m(),H=m(),K=m(),M=m(),w=async()=>{await X(),de()},Q=m(null),de=async()=>{var n;try{I.value=!0;const a=await qe(L);if(!a)return void j.error("\u67E5\u8BE2\u4E0D\u5230\u6D41\u7A0B\u4FE1\u606F\uFF01");c.value=a;const s=a.processDefinition;s.formType===10?(R.value.rule.length>0?R.value.value=a.formVariables:ie(R,s.formConf,s.formFields,a.formVariables),Ve().then(()=>{var S,b,k;if((S=f.value)==null||S.btn.show(!1),(b=f.value)==null||b.resetBtn.show(!1),(k=f.value)==null||k.disabled(!0),T.value.length>0){const _=T.value.at(0);_.fieldsPermission&&Object.keys(_.fieldsPermission).forEach(V=>{ce(V,_.fieldsPermission[V])})}})):Q.value=Ce(a.processDefinition.formCustomViewPath),J.value=(n=await $e(s.id))==null?void 0:n.bpmnXml}finally{I.value=!1}},X=async()=>{T.value=[],E.value=[],U.value=[],P.value=[];try{B.value=!0;const n=await Me(L);C.value=[],n.forEach(a=>{a.status!==4&&C.value.push(a)}),C.value.sort((a,s)=>a.endTime&&s.endTime?s.endTime-a.endTime:a.endTime?1:s.endTime?-1:s.createTime-a.createTime),W(C.value)}finally{B.value=!1}},W=n=>{n.forEach(a=>{if(we(a.children)||W(a.children),(a.status===1||a.status===6)&&a.assigneeUser&&a.assigneeUser.id===pe)if(T.value.push({...a}),E.value.push({reason:"",copyUserIds:[]}),a.formId&&a.formConf){const s={};ie(s,a.formConf,a.formFields,a.formVariables),U.value.push(s)}else U.value.push({})})},ce=(n,a)=>{var s,S,b;a==="1"&&((s=f.value)==null||s.disabled(!0,n)),a==="2"&&((S=f.value)==null||S.disabled(!1,n)),a==="3"&&((b=f.value)==null||b.hidden(!0,n))},fe=n=>{const a={};return n&&f.value&&Object.keys(n).forEach(s=>{n[s]==="2"&&(a[s]=f.value.getValue(s))}),a},Y=m([]);return Se(async()=>{w(),Y.value=await he()}),(n,a)=>{const s=Ne,S=xe,b=De("form-create"),k=Je,_=Ae,V=Ie,ve=Pe,ye=Fe,N=Oe,x=Ge,Z=je,be=ze,ee=Le;return d(),v(be,null,{default:i(()=>[(d(!0),z(ne,null,le(e(T),(l,u)=>oe((d(),v(k,{key:u,class:"box-card"},{header:i(()=>[G("span",aa,"\u5BA1\u6279\u4EFB\u52A1\u3010"+y(l.name)+"\u3011",1)]),default:i(()=>[r(Z,{offset:6,span:16},{default:i(()=>{var F,O,D,ae,te,se;return[r(ye,{ref_for:!0,ref:"form"+u,model:e(E)[u],rules:e(me),"label-width":"100px"},{default:i(()=>[e(c)&&e(c).name?(d(),v(s,{key:0,label:"\u6D41\u7A0B\u540D"},{default:i(()=>[h(y(e(c).name),1)]),_:1})):g("",!0),e(c)&&e(c).startUser?(d(),v(s,{key:1,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:i(()=>{var t;return[h(y((t=e(c))==null?void 0:t.startUser.nickname)+" ",1),r(S,{size:"small",type:"info"},{default:i(()=>{var o;return[h(y((o=e(c))==null?void 0:o.startUser.deptName),1)]}),_:1})]}),_:1})):g("",!0),e(T)[u].formId>0?(d(),v(k,{key:2,class:"mb-15px !-mt-10px"},{header:i(()=>{var t;return[G("span",ta," \u586B\u5199\u8868\u5355\u3010"+y((t=e(T)[u])==null?void 0:t.formName)+"\u3011 ",1)]}),default:i(()=>[r(b,{modelValue:e(U)[u].value,"onUpdate:modelValue":t=>e(U)[u].value=t,api:e(P)[u],"onUpdate:api":t=>e(P)[u]=t,option:e(U)[u].option,rule:e(U)[u].rule},null,8,["modelValue","onUpdate:modelValue","api","onUpdate:api","option","rule"])]),_:2},1024)):g("",!0),r(s,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:i(()=>[r(_,{modelValue:e(E)[u].reason,"onUpdate:modelValue":t=>e(E)[u].reason=t,placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),r(s,{label:"\u6284\u9001\u4EBA",prop:"copyUserIds"},{default:i(()=>[r(ve,{modelValue:e(E)[u].copyUserIds,"onUpdate:modelValue":t=>e(E)[u].copyUserIds=t,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6284\u9001\u4EBA"},{default:i(()=>[(d(!0),z(ne,null,le(e(Y),t=>(d(),v(V,{key:t.id,label:t.nickname,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["model","rules"]),G("div",sa,[!l.buttonsSetting||(F=l.buttonsSetting[e(p).APPROVE])!=null&&F.enable?(d(),v(x,{key:0,type:"success",onClick:t=>$(l,!0)},{default:i(()=>{var t,o;return[r(N,{icon:"ep:select"}),h(" "+y(((o=(t=l.buttonsSetting)==null?void 0:t[e(p).APPROVE])==null?void 0:o.displayName)||e(A).get(e(p).APPROVE)),1)]}),_:2},1032,["onClick"])):g("",!0),!l.buttonsSetting||(O=l.buttonsSetting[e(p).REJECT])!=null&&O.enable?(d(),v(x,{key:1,type:"danger",onClick:t=>$(l,!1)},{default:i(()=>{var t;return[r(N,{icon:"ep:close"}),h(" "+y(((t=l.buttonsSetting)==null?void 0:t[e(p).REJECT].displayName)||e(A).get(e(p).REJECT)),1)]}),_:2},1032,["onClick"])):g("",!0),!l.buttonsSetting||(D=l.buttonsSetting[e(p).TRANSFER])!=null&&D.enable?(d(),v(x,{key:2,type:"primary",onClick:t=>{return o=l.id,void q.value.open(o);var o}},{default:i(()=>{var t,o;return[r(N,{icon:"ep:edit"}),h(" "+y(((o=(t=l.buttonsSetting)==null?void 0:t[e(p).TRANSFER])==null?void 0:o.displayName)||e(A).get(e(p).TRANSFER)),1)]}),_:2},1032,["onClick"])):g("",!0),!l.buttonsSetting||(ae=l.buttonsSetting[e(p).DELEGATE])!=null&&ae.enable?(d(),v(x,{key:3,type:"primary",onClick:t=>(async o=>{H.value.open(o.id)})(l)},{default:i(()=>{var t,o;return[r(N,{icon:"ep:position"}),h(" "+y(((o=(t=l.buttonsSetting)==null?void 0:t[e(p).DELEGATE])==null?void 0:o.displayName)||e(A).get(e(p).DELEGATE)),1)]}),_:2},1032,["onClick"])):g("",!0),!l.buttonsSetting||(te=l.buttonsSetting[e(p).ADD_SIGN])!=null&&te.enable?(d(),v(x,{key:4,type:"primary",onClick:t=>(async o=>{M.value.open(o.id)})(l)},{default:i(()=>{var t,o;return[r(N,{icon:"ep:plus"}),h(" "+y(((o=(t=l.buttonsSetting)==null?void 0:t[e(p).ADD_SIGN])==null?void 0:o.displayName)||e(A).get(e(p).ADD_SIGN)),1)]}),_:2},1032,["onClick"])):g("",!0),!l.buttonsSetting||(se=l.buttonsSetting[e(p).RETURN])!=null&&se.enable?(d(),v(x,{key:5,type:"warning",onClick:t=>(async o=>{K.value.open(o.id)})(l)},{default:i(()=>{var t,o;return[r(N,{icon:"ep:back"}),h(" "+y(((o=(t=l.buttonsSetting)==null?void 0:t[e(p).RETURN])==null?void 0:o.displayName)||e(A).get(e(p).RETURN)),1)]}),_:2},1032,["onClick"])):g("",!0)])]}),_:2},1024)]),_:2},1024)),[[ee,e(I)]])),128)),oe((d(),v(k,{class:"box-card"},{header:i(()=>[G("span",la,"\u7533\u8BF7\u4FE1\u606F\u3010"+y(e(c).name)+"\u3011",1)]),default:i(()=>{var l,u,F,O;return[((u=(l=e(c))==null?void 0:l.processDefinition)==null?void 0:u.formType)===10?(d(),v(Z,{key:0,offset:6,span:16},{default:i(()=>[r(b,{modelValue:e(R).value,"onUpdate:modelValue":a[0]||(a[0]=D=>e(R).value=D),api:e(f),"onUpdate:api":a[1]||(a[1]=D=>Te(f)?f.value=D:null),option:e(R).option,rule:e(R).rule},null,8,["modelValue","api","option","rule"])]),_:1})):g("",!0),((O=(F=e(c))==null?void 0:F.processDefinition)==null?void 0:O.formType)===20?(d(),z("div",oa,[r(e(Q),{id:e(c).businessKey},null,8,["id"])])):g("",!0)]}),_:1})),[[ee,e(I)]]),r(Xe,{loading:e(B),"process-instance":e(c),tasks:e(C),onRefresh:X},null,8,["loading","process-instance","tasks"]),r(Qe,{id:`${e(L)}`,"bpmn-xml":e(J),loading:e(I),"process-instance":e(c),tasks:e(C)},null,8,["id","bpmn-xml","loading","process-instance","tasks"]),r(Ze,{ref_key:"taskTransferFormRef",ref:q,onSuccess:w},null,512),r(We,{ref_key:"taskReturnFormRef",ref:K,onSuccess:w},null,512),r(Ye,{ref_key:"taskDelegateForm",ref:H,onSuccess:w},null,512),r(ea,{ref_key:"taskSignCreateFormRef",ref:M,onSuccess:w},null,512)]),_:1})}}});export{na as default};
