import{j as E,f as N,au as j,b6 as b,d as u,bb as g,o as e,c as r,h,u as t,dh as B,$ as x,n as m,bg as S,aR as I,F as d,g as $,bf as w,k as T,ar as _,c1 as F,bh as L,cS as R}from"./index-C8b06LRn.js";const q=b({animated:{type:Boolean,default:!1},count:{type:Number,default:1},rows:{type:Number,default:3},loading:{type:Boolean,default:!0},throttle:{type:Number}}),z=b({variant:{type:String,values:["circle","rect","h1","h3","text","caption","p","image","button"],default:"text"}}),A=u({name:"ElSkeletonItem"});var c=S(u({...A,props:z,setup(v){const i=g("skeleton");return(o,s)=>(e(),r("div",{class:m([t(i).e("item"),t(i).e(o.variant)])},[o.variant==="image"?(e(),h(t(B),{key:0})):x("v-if",!0)],2))}}),[["__file","skeleton-item.vue"]]);const C=u({name:"ElSkeleton"}),D=L(S(u({...C,props:q,setup(v,{expose:i}){const o=v,s=g("skeleton"),f=((a,p=0)=>{if(p===0)return a;const l=E(!1);let n=null;const k=()=>{n&&clearTimeout(n),n=setTimeout(()=>{l.value=a.value},p)};return N(k),j(()=>a.value,y=>{y?k():l.value=y}),l})(I(o,"loading"),o.throttle);return i({uiLoading:f}),(a,p)=>t(f)?(e(),r("div",_({key:0,class:[t(s).b(),t(s).is("animated",a.animated)]},a.$attrs),[(e(!0),r(d,null,$(a.count,l=>(e(),r(d,{key:l},[a.loading?w(a.$slots,"template",{key:l},()=>[T(c,{class:m(t(s).is("first")),variant:"p"},null,8,["class"]),(e(!0),r(d,null,$(a.rows,n=>(e(),h(c,{key:n,class:m([t(s).e("paragraph"),t(s).is("last",n===a.rows&&a.rows>1)]),variant:"p"},null,8,["class"]))),128))]):x("v-if",!0)],64))),128))],16)):w(a.$slots,"default",F(_({key:1},a.$attrs)))}}),[["__file","skeleton.vue"]]),{SkeletonItem:c});R(c);export{D as E};
