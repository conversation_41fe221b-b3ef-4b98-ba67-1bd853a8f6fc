<template>
  <div class="drawing-control-container">
    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 左侧树形菜单 -->
      <div class="left-tree">
        <el-input v-model="typeName" class="mb-10px" clearable placeholder="请输入类型名称">
          <template #prefix>
            <Icon icon="ep:search" />
          </template>
        </el-input>
        <el-tree
          ref="treeRef"
          :data="treeData"
          node-key="id"
          :filter-node-method="filterNode"
          :props="treeProps"
          :default-expanded-keys="['1']"
          accordion
          highlight-current
          check-on-click-node
          @node-click="handleNodeClick"
        >
          <template #default="{ data }"> {{ data.kindName }},{{ data.kindKey }} </template>
        </el-tree>
      </div>

      <!-- 中间表格 -->
      <div class="center-table">
        <div class="h-[calc(100vh-180px)]">
          <div class="h-[calc(100%-5px)]">
            <vxe-table
              ref="tableRef"
              :data="list"
              :header-cell-style="{ padding: 0 }"
              border
              stripe
              align="center"
              height="100%"
              max-height="100%"
              show-overflow="title"
              :column-config="{ resizable: true }"
              :virtual-y-config="{ enabled: true, gt: 0 }"
              :loading="loading"
              :row-config="{ isCurrent: true, height: 25 }"
              :filter-config="{}"
              show-footer
              keep-source
              :footer-cell-style="{
                padding: 0,
                background: '#dcefdc',
                border: '1px solid #ebeef5'
              }"
              @filter-change="handleFilterChange"
              @cell-click="handleCellClick"
              tabindex="0"
              size="mini"
              :cell-class-name="'cursor-pointer'"
            >
              <vxe-column
                field="itemCode"
                title="物料编码"
                width="100"
                :filters="itemCodeOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column field="itemCode1" title="原件号" width="100" />
              <vxe-column field="itemVersion" title="版本" width="80" />
              <vxe-column
                field="itemName"
                title="物料名称"
                width="200"
                :filters="itemNameOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column field="attribute" title="属性" width="80" />
              <vxe-column
                field="spec"
                title="规格"
                min-width="200"
                :filters="specOptions"
                :filter-render="FilterValue.textFilterRender"
              />
            </vxe-table>
          </div>
          <!-- 分页 -->
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            size="small"
          />
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-detail">
        <div class="h-[calc(100vh-180px)]">
          <div class="h-[calc(100%-10px)]">
            <vxe-table
              ref="tableRef"
              :data="rightList"
              :header-cell-style="{ padding: 0 }"
              border
              stripe
              align="center"
              height="100%"
              max-height="100%"
              show-overflow="title"
              :column-config="{ resizable: true }"
              :virtual-y-config="{ enabled: true, gt: 0 }"
              :loading="rightLoading"
              :row-config="{ isCurrent: true, height: 25 }"
              :filter-config="{}"
              show-footer
              keep-source
              :footer-cell-style="{
                padding: 0,
                background: '#dcefdc',
                border: '1px solid #ebeef5'
              }"
              @cell-click="handleCellRightClick"
              tabindex="0"
              size="mini"
              :cell-class-name="'cursor-pointer'"
            >
              <vxe-column field="status" title="是否受控" width="100" />
              <vxe-column field="isSuance" title="是否发放" width="80" />
              <vxe-column field="format" title="是否存在pdf" width="80" />
              <vxe-column field="documentCode" title="文档编码" width="120" />
              <vxe-column field="documentName" title="名称" width="150" />
              <vxe-column field="documentType" title="类型" width="80" />
              <vxe-column field="createName" title="创建人" width="80" />
              <vxe-column field="updateName" title="修改人" width="80" />
              <vxe-column field="updateTime" title="修改时间" width="80" />
            </vxe-table>
          </div>
        </div>
      </div>

            <!-- 悬浮日志按钮 -->
      <div v-hasPermi="['development:drawings:querylog']" class="floating-log-btn" title="查看日志" @click="showLogPanel = true">
        <Icon icon="ep:view" />
      </div>
    </div>
  </div>

  
  <el-dialog
    v-model="showPdfPreview"
    title="文档预览"
    width="90%"
    :fullscreen="true"
  >
    <template #header>
      <div class="pdf-dialog-header">
        <span>文档预览：{{ documentName }}</span>
        <div class="pdf-controls">
          <el-button circle class="ml-10px" @click="handleDownload" title="下载">
            <Icon icon="ep:download" />
          </el-button>
        </div>
      </div>
    </template>
    <div class="pdf-container">
      <div
        class="pdf-wrapper"
        :style="{
          transform: `rotate(${rotation}deg)`,
          transition: 'transform 0.3s ease'
        }"
      >
        <!-- <iframe
          ref="pdfIframe"
          class="pdf-iframe"
          width="100%"
          height="100%"
          :src="pdfPreviewUrl"
          frameborder="0"
          allowfullscreen
          :style="{
            transform: `scale(${pdfScale})`,
            transformOrigin: 'top left',
            transition: 'transform 0.3s ease'
          }"
        ></iframe> -->
        <pdf-preview :pdfUrl="pdfPreviewUrl" />
      </div>
    </div>
  </el-dialog>
    <el-dialog
    v-model="showLogPanel"
    title="操作日志"
    width="60%"
    height="80%"
    draggable
    destroy-on-close
  >
  <DrawingsLog />
  </el-dialog>
</template>

<script setup lang="ts">
import * as FilterValue from '@/utils/Filter'
import { ElTree } from 'element-plus'
import { DrawingApi } from '@/api/development/drawings/index'
import { defaultProps, handleTree } from '@/utils/tree'
import PdfPreview from './componets/PdfPreview.vue'
import DrawingsLog from './componets/DrawingsLog.vue'
import {downloadByUrl,downloadByUrlBolb} from '@/utils/filt'

const treeRef = ref<InstanceType<typeof ElTree>>()

// 树组件的props配置
const treeProps = {
  label: 'kindName',
  children: 'children',
  disabled: 'disabled'
}

const queryParams = reactive({
  kind: [] as string[],
  pageNo: 1,
  pageSize: 30
})

const typeName = ref('')

//筛选相关
const itemCodeOptions = ref([{ data: '' }])
const itemNameOptions = ref([{ data: '' }])
const specOptions = ref([{ data: '' }])

const message = useMessage() // 消息弹窗
// 列表的加载中
const loading = ref(true)
// 中间列表的数据
const list = ref<any[]>([])
// 列表的总页数
const total = ref(0)

//右侧列表
const rightLoading = ref(true)
const rightList = ref<any[]>([])

// 树相关数据
const treeData = ref<any[]>([])

/** 基于名字过滤 */
const filterNode = (name: string, data: any) => {
  if (!name) return true
  console.log(name, data)
  return data.kindName.includes(name)
}
// 收集节点及其所有子孙节点的ID
const collectDescendantIds = (targetId: string, tree: any[]): string[] => {
  let targetNode = null

  // 查找目标节点
  const findNode = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children)
        if (found) return found
      }
    }
    return null
  }

  // 在整个树中查找目标节点
  for (const root of tree) {
    targetNode = findNode([root])
    if (targetNode) break
  }

  if (!targetNode) return []

  // 收集目标节点及其所有子孙节点的ID
  const result: string[] = []

  const collectIds = (node: any) => {
    result.push(node.id)
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => collectIds(child))
    }
  }

  collectIds(targetNode)

  return result
}

const handleNodeClick = async (row: { [key: string]: any }) => {
  if (row.id === '1') {
    queryParams.kind = []
  } else {
    // 获取选中节点及其所有子孙节点的ID
    const allIds = collectDescendantIds(row.id, treeData.value)
    console.log('选中节点及其子孙节点ID:', allIds)
    queryParams.kind = allIds // 将节点的kindKey作为查询条件
  }
  getList() // 重新查询列表
  rightList.value = []
}

//左侧树查询
const getTreeList = async () => {
  const res = await DrawingApi.getQueryPartKind()
  const tree = handleTree(res)
  treeData.value = tree.filter((item) => item.id == '1')
}

// 中间主表格查询
const getList = async () => {
  try {
    loading.value = true
    const res = await DrawingApi.getPage(queryParams)
    list.value = res.list
    total.value = res.total
  } catch (error) {
    console.log('查询失败:', error)
  } finally {
    loading.value = false
  }
}

const queryRightParams = reactive({
  itemCode: '',
  itemVersion: ''
})
// 右边主表格查询
const getRightList = async () => {
  try {
    rightLoading.value = true
    const res = await DrawingApi.getQueryDocumentByItemCode(queryRightParams)
    rightList.value = res
  } catch (error) {
    console.log('查询失败:', error)
  } finally {
    rightLoading.value = false
  }
}

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize', 'kind'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

//单击事件
const handleCellClick = async ({ row }) => {
  queryRightParams.itemCode = row.itemCode
  queryRightParams.itemVersion = row.itemVersion
  getRightList()
}

// 添加用于PDF预览的响应式变量
const pdfPreviewUrl = ref('')
const showPdfPreview = ref(false)

//日志弹出层
const showLogPanel = ref(false)
//文档名称
const documentName = ref('')
const documentCode = ref('')

//文档下载pdfID
const documentPdfId = ref('')

// 添加旋转相关变量
const rotation = ref(0) // 旋转角度

const handleCellRightClick = async ({ row }) => {
  // 原有校验逻辑（保留不变）
  if (row.format === '不存在') {
    message.error('当前选中文件不存在PDF，无法预览！')
    return
  }
  if (row.isSuance === '未发放') {
    message.error('当前选中文件未发放，无法预览！')
    return
  }
  if (row.status !== '受控完成') {
    message.error('当前选中文件受控未完成，无法预览！')
    return
  }

  try {
    rightLoading.value = true

    // 原有获取 PDF 地址逻辑（保留不变）
    const res = await DrawingApi.getDocPathOrWx({
      methodName: 'getDocPathForWX',
      docId: row.documentCode,
      docVer: row.documentVersion,
      docName: row.documentName,
      uuid: '1'
    })

    console.log('获取PDF路径返回结果:', res)

    if (res && typeof res === 'string') {
      // 原有赋值逻辑（保留不变）
      pdfPreviewUrl.value = res + '#printbar=0'
      console.log('设置PDF预览URL:', pdfPreviewUrl.value)

      documentPdfId.value = res.substring(res.lastIndexOf('/') + 1)
      documentName.value = row.documentName
      documentCode.value = row.documentCode
      showPdfPreview.value = true

      // 原有记录预览日志逻辑（保留不变）
      await DrawingApi.addDrawingLog({
        itemCode: queryRightParams.itemCode,
        itemVersion: queryRightParams.itemVersion,
        wordName: row.documentName,
        wordCode: row.documentCode,
        type: 0
      })
    } else {
      message.error('获取文件地址失败！')
    }
  } catch (error) {
    console.error('预览触发失败:', error)
    message.error('预览失败，请重试')
  } finally {
    rightLoading.value = false
  }
}

/** 监听deptName */
watch(typeName, (val) => {
  treeRef.value!.filter(val)
})

const handleDownload = async () => {
  //添加下载日志
  await DrawingApi.addDrawingLog({
    itemCode: queryRightParams.itemCode,
    itemVersion: queryRightParams.itemVersion,
    wordName: documentName.value,
    wordCode: documentCode.value,
    type: 1
  })
  //下载文件
  const downloadUrl = pdfPreviewUrl.value
  // downloadByUrl({ url: downloadUrl, target: '_blank', fileName: documentName.value+".pdf" })

  downloadByUrlBolb({url:downloadUrl,fileName: documentName.value+".pdf"})
  // alert(documentName.value+".pdf")
  // const link = document.createElement('a')
  // link.href = downloadUrl
  // link.download = documentName.value
  // link.style.display = 'none'
  // document.body.appendChild(link)
  // link.click()
  // document.body.removeChild(link)
  message.success(`文档 "${documentName.value}" 开始下载`)
}

onMounted(async () => {
  getTreeList()
  getList()
  getRightList()
})
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
.drawing-control-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 90vh;
}

.main-content {
  display: flex;
  gap: 5px;
  height: calc(100vh - 120px);
}

.left-tree {
  width: 300px;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.center-table {
  flex: 1;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.right-detail {
  width: 400px;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.detail-header {
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.detail-body {
  padding: 10px 0;
}

.document-icons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.el-table .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* PDF预览样式 */
.pdf-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.pdf-controls {
  display: flex;
  align-items: center;
}

.page-info {
  padding: 0 8px;
  font-size: 14px;
  color: #606266;
}

.pdf-container {
  width: 100%;
  height: calc(100vh - 100px);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: auto;
  position: relative;
}

.pdf-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pdf-iframe {
  min-width: 80%;
  min-height: 80%;
}

.page-info {
  margin: 0 8px;
  color: #606266;
}

/* 添加加载状态样式 */
.pdf-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.pdf-viewer-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  justify-content: center;
  padding: 20px;
}

.pdf-canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  border-radius: 4px;
}

.pdf-loading-overlay,
.pdf-error-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-loading-overlay p,
.pdf-error-overlay p {
  margin-top: 16px;
  font-size: 16px;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
}

/* 悬浮日志按钮样式 */
.floating-log-btn {
  position: absolute;
  top: 92%;
  right: 40px;
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}
</style>
