<template>
  <div class="h-[calc(90vh)] bg-white">
    <div class="flex gap-1 h-[calc(100vh-80px)]">
      <div class="center-table">
        <div class="h-[calc(100vh-140px)]">
          <div class="h-[calc(100%-5px)]">
            <vxe-table
              ref="centerTableRef"
              :data="list"
              :header-cell-style="{ padding: 0, fontSize: '14px' }"
              border
              stripe
              align="center"
              height="100%"
              max-height="100%"
              show-overflow="title"
              :column-config="{ resizable: true }"
              :virtual-y-config="{ enabled: true, gt: 0 }"
              :loading="loading"
              :row-config="{ isCurrent: true }"
              :filter-config="{remote:true}"
              :cell-style="{ fontSize: '14px' }"
              show-footer
              keep-source
              :footer-cell-style="{
                padding: 0,
                background: '#dcefdc',
                border: '1px solid #ebeef5'
              }"
              tabindex="0"
              size="mini"
              @filter-change="handleFilterChange"
              :cell-class-name="'cursor-pointer'"
            >
              <vxe-column
                field="docNo"
                title="预测订单号"
                width="150"
                :filters="selectOptions.docNoOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="deptName"
                title="部门"
                width="100"
                :filters="selectOptions.deptNameOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="businessDate"
                title="下单日期"
                width="110"
                :filters="selectOptions.businessDateOptions"
                :filter-render="FilterTemplate.dateRangeFilterRender"
              />
              <vxe-column
                field="projectName"
                title="项目"
                width="160"
                :filters="selectOptions.projectNameOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="projectNode"
                title="项目节点"
                width="100"
                :filters="selectOptions.projectNodeOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="orderItemCode"
                title="料号"
                width="100"
                :filters="selectOptions.orderItemCodeOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="orderItemName"
                title="品名"
                width="150"
                :filters="selectOptions.orderItemNameOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="orderSpecs"
                title="规格"
                width="160"
                :filters="selectOptions.orderSpecsOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column field="attribute" title="属性" width="80" />
              <vxe-column
                field="bomSpecical"
                title="BOM子项专项控制"
                width="160"
              />
              <vxe-column
                field="bomChildCode"
                title="BOM子料号"
                width="150"
                :filters="selectOptions.bomChildCodeOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="bomChildName"
                title="子料号品名"
                width="120"
                :filters="selectOptions.bomChildNameOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column
                field="bomChildSpecs"
                title="子料号规格"
                width="120"
                :filters="selectOptions.bomChildSpecsOptions"
                :filter-render="FilterValue.textFilterRender"
              />
              <vxe-column field="itemSpecical" title="料号专用料标志" width="120" />
              <vxe-column field="mrpDocNo" title="MRP" width="120" />
              <vxe-column field="mrpQty" title="MRP数量" width="120" />
              <vxe-column field="lrpDocNo" title="LRP" width="120" />
              <vxe-column field="prDocNo" title="请购单" width="120" />
              <vxe-column field="prQty" title="请购单数量" width="120" />
              <vxe-column field="poDocNo" title="采购单" width="120" />
              <vxe-column field="poQty" title="采购单数量" width="120" />
              <vxe-column field="rcvDocNo" title="采购入库单" width="120" />
              <vxe-column field="rcvQty" title="采购入库单数量" width="120" />
            </vxe-table>
          </div>
          <!-- 分页 -->
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            size="small"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SpecialApi } from '@/api/development/special/index'
import * as FilterValue from '@/utils/Filter'
import * as FilterTemplate from '@/utils/Filter'
import { get } from 'http'

const centerTableRef = ref()

const selectOptions = ref({
  docNoOptions: [{ data: '' }],
  deptNameOptions: [{ data: '' }],
  orderItemCodeOptions: [{ data: '' }],
  orderItemNameOptions: [{ data: '' }],
  businessDateOptions: [{ data: [] }],
  projectNameOptions: [{ data: '' }],
  projectNodeOptions: [{ data: '' }],
  orderSpecsOptions: [{ data: '' }],
  bomChildCodeOptions: [{ data: '' }],
  bomChildNameOptions: [{ data: '' }],
  bomChildSpecsOptions: [{ data: '' }],
})

const message = useMessage() // 消息弹窗
// 列表的加载中
const loading = ref(true)
// 中间列表的数据
const list = ref<any[]>([])
// 列表的总页数
const total = ref(0)

const getList = async () => {
  try {
    loading.value = true
    const res = await SpecialApi.getPage(queryParams)
    list.value = res.list
    total.value = res.total
  } catch (error) {
    console.log('查询失败:', error)
  } finally {
    loading.value = false
  }
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 50
})

const handleFilterChange = (params: any) => {
  // 特定字段列表
  const specialFields = ['currencyCode']
  // 初始化 filters 对象
  const filters = {}

  params.filterList.forEach((item: any) => {
    const { field, values, datas } = item
    if (specialFields.includes(field) && values.length > 0) {
      // 特定字段且 values 有值
      filters[field] = values
    } else if (datas.length > 0) {
      // 其他字段且 datas 有值
      filters[field] = datas[0]
    }
  })

  // 清空不在 params.filterList 中的字段
  Object.keys(queryParams).forEach((key) => {
    if (
      !['pageNo', 'pageSize'].includes(key) &&
      !params.filterList.some((item: any) => item.field === key)
    ) {
      queryParams[key] = undefined
    }
  })

  // 更新 queryParams
  Object.assign(queryParams, filters)

  // 调用后端接口获取数据
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.center-table {
  flex: 1;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
</style>
