import{d as x,j as u,f as T,x as j,u as a,o as B,h as H,w as t,k as e,m as L,v as p,p as O,y as S,a as F,O as G,E as J,q as K,H as M,B as U,I as z,L as A}from"./index-C8b06LRn.js";import{_ as D}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{u as N}from"./tagsView-D-HCnpxr.js";import{_ as P}from"./BasicInfoForm.vue_vue_type_script_setup_true_lang-CY2Xps9E.js";import{_ as Q}from"./ColumInfoForm.vue_vue_type_script_setup_true_lang-DL-z15kq.js";import{_ as W}from"./GenerateInfoForm.vue_vue_type_script_setup_true_lang-C_NIMTce.js";import{g as X,u as Y}from"./index-CAhgYtFg.js";import"./el-card-CaOo8U9P.js";import"./formRules-Upspu04w.js";import"./dict.type-Bqd7OQxQ.js";import"./el-tree-select-E9FCZb0j.js";import"./index-BwMxOnEu.js";const Z=x({name:"InfraCodegenEditTable",__name:"EditTable",setup($){const{t:_}=O(),b=S(),{push:y,currentRoute:g}=F(),{query:v}=G(),{delView:w}=N(),n=u(!1),r=u("colum"),f=u(),I=u(),i=u(),o=u({table:{},columns:[]}),k=async()=>{var s,l;if(a(o)){await((s=a(f))==null?void 0:s.validate()),await((l=a(i))==null?void 0:l.validate());try{await Y(o.value),b.success(_("common.updateSuccess")),c()}catch{}}},c=()=>{w(a(g)),y("/infra/codegen")};return T(()=>{(async()=>{const s=v.id;if(s){n.value=!0;try{o.value=await X(s)}finally{n.value=!1}}})()}),(s,l)=>{const m=J,h=K,d=M,R=U,V=z,C=D,E=A;return j((B(),H(C,null,{default:t(()=>[e(h,{modelValue:a(r),"onUpdate:modelValue":l[0]||(l[0]=q=>L(r)?r.value=q:null)},{default:t(()=>[e(m,{label:"\u57FA\u672C\u4FE1\u606F",name:"basicInfo"},{default:t(()=>[e(a(P),{ref_key:"basicInfoRef",ref:f,table:a(o).table},null,8,["table"])]),_:1}),e(m,{label:"\u5B57\u6BB5\u4FE1\u606F",name:"colum"},{default:t(()=>[e(a(Q),{ref_key:"columInfoRef",ref:I,columns:a(o).columns},null,8,["columns"])]),_:1}),e(m,{label:"\u751F\u6210\u4FE1\u606F",name:"generateInfo"},{default:t(()=>[e(a(W),{ref_key:"generateInfoRef",ref:i,table:a(o).table,columns:a(o).columns},null,8,["table","columns"])]),_:1})]),_:1},8,["modelValue"]),e(V,null,{default:t(()=>[e(R,{style:{float:"right"}},{default:t(()=>[e(d,{loading:a(n),type:"primary",onClick:k},{default:t(()=>l[1]||(l[1]=[p("\u4FDD\u5B58")])),_:1},8,["loading"]),e(d,{onClick:c},{default:t(()=>l[2]||(l[2]=[p("\u8FD4\u56DE")])),_:1})]),_:1})]),_:1})]),_:1})),[[E,a(n)]])}}});export{Z as default};
