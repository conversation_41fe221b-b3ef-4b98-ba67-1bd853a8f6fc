import{d as _e,r as ye,dl as w,j as c,aN as be,Y as xe,y as ke,f as ge,aO as te,u as o,bj as Ce,aF as P,T as le,o as _,c as C,k as a,w as l,l as oe,m as U,v as r,t as s,h as E,$ as R,x as ie,dk as re,F as Ye,g as Ie,di as de,dj as Se,C as Ve,aB as Fe,aa as Te,H as Me,B as Pe,S as Ee,cd as Le,I as ze,_ as De}from"./index-C8b06LRn.js";import{_ as Ue}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{_ as Re}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as Be}from"./CardTitle-00NfZwLk.js";import{D as V}from"./deptindicator-DmEoRemC.js";import{w as Ne,av as se}from"./echarts-cfVEL83D.js";import{a as $e}from"./filt-CBkj7zaY.js";import{c as L}from"./permission-DVzqLl93.js";import{f as ne,a as je}from"./dateUtil-D9m5ek6U.js";import{F as Oe}from"./index-6IF2xVYO.js";import"./el-card-CaOo8U9P.js";const Ae={class:"h-[calc(100vh-180px)] overflow-auto"},He={class:"min-h-[calc(100vh-400px)]"},qe={class:"h-400px"},We={key:0},Xe={key:1},Ge=De(_e({__name:"Main",setup(Je){const fe=ye({dept:"IT",year:w().format("YYYY")}),m=c(w().format("YYYY-MM-DD")),{getUser:B}=be(),N=xe(()=>!k.value||k.value.length===0?"0":(k.value.reduce((d,t)=>{const{timeliness:n,attitude:u,stability:p,whole:i}=t;return d+n+u+p+i},0)/k.value.length/20*40).toFixed(2)),$=c([]),k=c([]),j=c(),O=c(),A=c(),H=c(),f=c(),F=c(!1),q=ke(),W=c([]),X=c({}),G=c([]),T=c(!1),g=c(new Map),M=async()=>{const d=await V.getInformationList({year:w(m.value).format("YYYY"),month:w(m.value).format("M")});k.value=d,W.value=await V.getDeptIndicatorList({year:w(m.value).format("YYYY"),month:w(m.value).format("M"),dept:"IT"}),X.value=W.value.reduce((t,n)=>(t[n.indicator]=n.value,t),{})},J=async(d,t)=>{const n=await Oe.getFlowList({dateType:"month",date:m.value,person:d.personType,overdue:t});G.value=n,T.value=!0},ue=()=>{const d=(()=>{const t={};return de("IT_Indicator").forEach(n=>{t[n.value]={}}),$.value.forEach(n=>{if(!n.indicator||!n.month||n.value===void 0)return;const u=n.indicator,p=`${n.month}\u6708`;t[u]||(t[u]={}),t[u][p]?t[u][p]+=Number(n.value):t[u][p]=Number(n.value)}),t})();de("IT_Indicator").forEach(t=>{ce(t.value,t.label,d[t.value]||{})})},ce=(d,t,n)=>{const u=d,p=document.getElementById(u);if(!p)return;g.value.has(u)&&g.value.get(u).dispose();const i=Object.keys(n).sort((y,v)=>parseInt(y.replace("\u6708",""))-parseInt(v.replace("\u6708",""))),h=i.map(y=>n[y]||0);if(i.length===0)return void(p.innerHTML=`
      <div class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <div class="text-lg font-medium">${t}</div>
          <div class="text-sm mt-2">\u6682\u65E0\u6570\u636E</div>
        </div>
      </div>
    `);const Y=Se(Ne(p)),I={title:{text:t,left:"center",textStyle:{fontSize:14,fontWeight:"bold",color:"#333"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:y=>{const v=y[0];return`${v.axisValue}<br/>${v.marker}${t}: ${v.value}`}},grid:{left:"3%",right:"4%",bottom:"10%",top:"25%",containLabel:!0},xAxis:{type:"category",data:i,axisLabel:{fontSize:11,color:"#666",rotate:0},axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",name:"\u6307\u6807\u503C",nameTextStyle:{color:"#666",fontSize:11},axisLabel:{fontSize:11,color:"#666"},axisLine:{lineStyle:{color:"#e0e0e0"}},splitLine:{lineStyle:{color:"#f0f0f0",type:"dashed"}}},series:[{name:t,type:"bar",data:h,itemStyle:{color:new se(0,0,0,1,[{offset:0,color:"#4facfe"},{offset:1,color:"#00f2fe"}]),borderRadius:[4,4,0,0]},emphasis:{itemStyle:{color:new se(0,0,0,1,[{offset:0,color:"#667eea"},{offset:1,color:"#764ba2"}])}},label:{show:!0,position:"top",formatter:"{c}",fontSize:10,color:"#666"}}]};Y.setOption(I),g.value.set(u,Y)},K=()=>{g.value.forEach(d=>{d.resize()})},Q=({column:d})=>["year","month","personType","processBasePerformance","leaderScore","record","performanceScore","plan_roi","roi","roiScore","service_deducts","service_deduct","service_satisfaction","deducts","blackFlowerCount","deduct"].includes(d.field)?"basics-info":"basics-info-1",Z=({column:d})=>["baseInfo","process","year","month","personType","processBasePerformance","leaderScore","record","performanceScore","plan_roi","roi","roiScore","service_deducts","service_deduct","service_satisfaction","deducts","blackFlowerCount","deduct"].includes(d.field)?"basics-info-header":"basics-info-header-1",z=async()=>{await V.updateInformation(f.value),q.success("\u4FEE\u6539\u6210\u529F"),await M(),f.value=k.value.find(d=>d.id===f.value.id)};return ge(()=>{(async()=>{const d=await V.getDeptIndicatorList(fe);$.value=d,await te(),ue()})(),window.addEventListener("resize",K),M(),te(()=>{var d,t;(d=o(O))==null||d.connect(o(j)),(t=o(H))==null||t.connect(o(A))})}),Ce(()=>{window.removeEventListener("resize",K),g.value.forEach(d=>{d.dispose()}),g.value.clear()}),(d,t)=>{const n=Be,u=Ve,p=P("vxe-toolbar"),i=P("vxe-column"),h=P("vxe-colgroup"),Y=Fe,I=P("vxe-table"),y=Te,v=Me,me=Re,ee=Ue,S=Pe,ae=Ee,pe=Le,we=ze,he=le("esle"),ve=le("hasPermi");return _(),C("div",Ae,[a(me,null,{default:l(()=>[oe("div",He,[a(p,{ref_key:"toolbarRef",ref:j,export:"",size:"mini"},{buttons:l(()=>[a(n,{title:"\u4EBA\u5458\u6307\u6807"}),a(u,{type:"month",modelValue:o(m),"onUpdate:modelValue":t[0]||(t[0]=e=>U(m)?m.value=e:null),"value-format":"YYYY-MM-DD",onChange:M,size:"small",class:"ml-10px"},null,8,["modelValue"])]),_:1},512),oe("div",qe,[a(I,{align:"center",border:"",data:[o(X)],"header-cell-style":{padding:0},"header-cell-config":{height:30},"cell-config":{height:30},"show-overflow":"",stripe:"",ref_key:"tableRef",ref:O,"export-config":{},"cell-class-name":Q,"header-cell-class-name":Z},{default:l(()=>[a(h,{title:"\u57FA\u7840\u4FE1\u606F",field:"baseInfo"},{default:l(()=>[a(i,{title:"\u5E74",field:"year",width:"60"},{default:l(()=>[r(s(o(w)(o(m)).format("YYYY")),1)]),_:1}),a(i,{title:"\u6708",field:"month",width:"60"},{default:l(()=>[r(s(o(w)(o(m)).format("M")),1)]),_:1}),a(i,{title:"\u4EBA\u5458",field:"personType",width:"60"},{default:l(()=>t[7]||(t[7]=[r(" \u6842\u8D24\u660E ")])),_:1})]),_:1}),a(h,{title:"\u6EE1\u610F\u5EA6",field:"deliveryRate"},{default:l(()=>[a(i,{title:"\u57FA\u7840\u5206",field:"basePerformance",width:"80"},{default:l(()=>t[8]||(t[8]=[r(" 40 ")])),_:1}),a(i,{title:"\u5F97\u5206",field:"basePerformance",width:"80"},{default:l(()=>[r(s(o(N)),1)]),_:1})]),_:1}),a(h,{title:"\u4EBA\u529B\u6295\u4EA7\u6BD4\u589E\u957F\u7387",field:"process"},{default:l(()=>[a(i,{title:"\u57FA\u7840\u5206",field:"processBasePerformance",width:"80"},{default:l(()=>t[9]||(t[9]=[r(" 40 ")])),_:1}),a(i,{title:"\u8BA1\u5212\u6BD4\u7387",field:"plan_roi",width:"80"}),a(i,{title:"\u5B9E\u9645\u6BD4\u7387",field:"roi",width:"80"}),a(i,{title:"\u5F97\u5206",field:"roiScore",width:"80"},{default:l(({row:e})=>[r(s(e!=null&&e.plan_roi&&(e!=null&&e.roi)?(30*Math.min((e==null?void 0:e.roi)/(e==null?void 0:e.plan_roi),1)).toFixed(2):"0.00"),1)]),_:1})]),_:1}),a(h,{title:"\u9886\u5BFC\u8BC4\u5206",field:"delivery"},{default:l(()=>[a(i,{title:"\u57FA\u7840\u5206",field:"deliveryBasePerformance","min-width":"100"},{default:l(()=>t[10]||(t[10]=[r(" 20 ")])),_:1}),a(i,{title:"\u8BC4\u5206",field:"boss","min-width":"200"},{default:l(({row:e})=>[o(B).id===1437?(_(),E(Y,{key:0,modelValue:e.boss,"onUpdate:modelValue":b=>e.boss=b,class:"!w-100%",max:20,onChange:b=>(async x=>{const D={year:w(m.value).format("YYYY"),month:w(m.value).format("M"),dept:"IT",indicator:"boss",value:x.boss};await V.updateDeptLeader(D),q.success("\u4FDD\u5B58\u6210\u529F"),M()})(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])):R("",!0),ie((_(),C("span",null,[r(s(e==null?void 0:e.boss),1)])),[[he]])]),_:1})]),_:1}),a(i,{title:"\u603B\u5206",field:"performanceScore","min-width":"80"},{default:l(({row:e})=>[r(s((Number(o(N))+(e!=null&&e.plan_roi&&(e!=null&&e.roi)?40*Math.min((e==null?void 0:e.roi)/(e==null?void 0:e.plan_roi),1):0)+-(100-(e==null?void 0:e.service_satisfaction))).toFixed(2)),1)]),_:1})]),_:1},8,["data"]),a(p,{ref_key:"toolbarRef1",ref:A,export:"",size:"mini"},null,512),a(I,{height:"200px",align:"center",border:"",data:o(k),"header-cell-style":{padding:0},"header-cell-config":{height:30},"cell-config":{height:30},"show-overflow":"",stripe:"",ref_key:"tableRef1",ref:H,"export-config":{},"cell-class-name":Q,"header-cell-class-name":Z},{default:l(()=>[a(h,{title:"\u57FA\u7840\u4FE1\u606F",field:"baseInfo",key:"baseInfo"},{default:l(()=>[a(i,{title:"\u5E74",field:"year",width:"60",key:"baseInfo"}),a(i,{title:"\u6708",field:"month",width:"60",key:"baseInfo"}),a(i,{title:"\u4EBA\u5458",field:"personType",width:"60",key:"baseInfo"},{default:l(({row:e})=>[r(s(o(re)("information_person",e.personType)),1)]),_:1})]),_:1}),a(h,{title:"IT\u9879\u76EE\u53CA\u65F6\u4EA4\u4ED8\u7387",field:"deliveryRate"},{default:l(()=>[a(i,{title:"\u57FA\u7840\u5206",field:"basePerformance",width:"80"},{default:l(()=>t[11]||(t[11]=[r(" 60 ")])),_:1}),a(i,{title:"\u8D1F\u8D23\u6D41\u7A0B\u6570",field:"flowCount",width:"80"},{default:l(({row:e})=>[a(y,{type:"primary",onClick:b=>J(e,!1)},{default:l(()=>[r(s(e.flowCount),1)]),_:2},1032,["onClick"])]),_:1}),a(i,{title:"\u6309\u671F\u5B8C\u6210\u6570",field:"flowFinishCount",width:"80"},{default:l(({row:e})=>[a(y,{type:"primary",onClick:b=>J(e,!0)},{default:l(()=>[r(s(e.flowFinishCount),1)]),_:2},1032,["onClick"])]),_:1}),a(i,{title:"\u4EA4\u4ED8\u7387",field:"flowRate",width:"80"},{default:l(({row:e})=>[e.flowCount?(_(),C("span",We,s((e.flowFinishCount/e.flowCount*100).toFixed(2))+"% ",1)):(_(),C("span",Xe,"100.00%"))]),_:1}),a(i,{title:"\u5F97\u5206",field:"flowScore",width:"80"},{default:l(({row:e})=>[r(s(e.flowCount?(e.flowFinishCount/e.flowCount*60).toFixed(2):60),1)]),_:1})]),_:1}),a(h,{title:"\u9886\u5BFC\u8BC4\u5206",field:"process"},{default:l(()=>[a(i,{title:"\u57FA\u7840\u5206",field:"processBasePerformance",width:"80"},{default:l(()=>t[12]||(t[12]=[r(" 20 ")])),_:1}),a(i,{title:"\u9886\u5BFC\u8BC4\u5206",field:"leaderScore",width:"100"}),a(i,{title:"\u8BC4\u4F30\u8BB0\u5F55",field:"record",width:"100"},{default:l(({row:e})=>[a(v,{type:"primary",link:"",onClick:b=>(x=>{F.value=!0,f.value=x})(e)},{default:l(()=>t[13]||(t[13]=[r(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])]),_:1})]),_:1}),a(h,{title:"\u5DE5\u5355\u670D\u52A1\u8BC4\u5206",field:"flow"},{default:l(()=>[a(i,{title:"\u53CA\u65F6\u6027",field:"timeliness",width:"80"},{default:l(({row:e})=>[r(s((e.timeliness||0).toFixed(1)),1)]),_:1}),a(i,{title:"\u670D\u52A1\u6001\u5EA6",field:"attitude",width:"80"},{default:l(({row:e})=>[r(s((e.attitude||0).toFixed(1)),1)]),_:1}),a(i,{title:"\u7CFB\u7EDF\u7A33\u5B9A\u6027",field:"stability",width:"80"},{default:l(({row:e})=>[r(s((e.stability||0).toFixed(1)),1)]),_:1}),a(i,{title:"\u6574\u4F53\u8BC4\u5206",field:"whole",width:"80"},{default:l(({row:e})=>[r(s((e.whole||0).toFixed(1)),1)]),_:1})]),_:1}),a(i,{title:"\u603B\u5206",field:"performanceScore","min-width":"100",fixed:"right"},{default:l(({row:e})=>[r(s(((e.flowCount?e.flowFinishCount/e.flowCount*60:60)+(e.leaderScore||0)-2*e.blackFlowerCount+e.timeliness+e.attitude+e.stability+e.whole).toFixed(2)),1)]),_:1})]),_:1},8,["data"])])])]),_:1}),a(ee,{modelValue:o(T),"onUpdate:modelValue":t[1]||(t[1]=e=>U(T)?T.value=e:null),title:"\u8D1F\u8D23\u4EFB\u52A1\u8BE6\u60C5",width:"50%"},{default:l(()=>[a(I,{data:o(G),"show-overflow":""},{default:l(()=>[a(i,{title:"\u6D41\u7A0B\u540D\u79F0",field:"flowName","min-width":"300"},{default:l(({row:e})=>[a(y,{type:"primary",onClick:b=>{return x=e.flowId,void window.open("http://oa.iaa360.cn:8686/km/review/km_review_main/kmReviewMain.do?method=view&fdId="+x,"_blank");var x}},{default:l(()=>[r(s(e.flowName),1)]),_:2},1032,["onClick"])]),_:1}),a(i,{title:"\u6D41\u7A0B\u521B\u5EFA\u65F6\u95F4",field:"flowCreateTime",width:"160"},{default:l(({row:e})=>[r(s(o(ne)(e.flowCreateTime)),1)]),_:1}),a(i,{title:"\u6D41\u7A0B\u671F\u671B\u5B8C\u6210\u65F6\u95F4",field:"flowPlanDate",width:"160"},{default:l(({row:e})=>[r(s(o(je)(e.flowPlanDate)),1)]),_:1}),a(i,{title:"\u6D41\u7A0B\u5B9E\u9645\u5B8C\u6210\u65F6\u95F4",field:"flowPublishTime",width:"160"},{default:l(({row:e})=>[r(s(e.developmentDate||e.flowPublishTime?o(ne)(e.developmentDate||e.flowPublishTime):""),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),a(ee,{title:`${o(w)(o(m)).format("YYYY\u5E74MM\u6708")}\u8BC4\u4F30\u8BB0\u5F55`,modelValue:o(F),"onUpdate:modelValue":t[6]||(t[6]=e=>U(F)?F.value=e:null),width:"800px"},{footer:l(()=>[ie((_(),E(v,{type:"primary",plain:"",onClick:z},{default:l(()=>t[15]||(t[15]=[r("\u4FDD\u5B58")])),_:1})),[[ve,["report:information:save"]]])]),default:l(()=>[a(we,{class:"custom-form","label-position":"top"},{default:l(()=>[a(S,{label:"\u4EBA\u5458"},{default:l(()=>{var e;return[r(" \u4FE1\u606F\u90E8 - "+s(o(re)("information_person",(e=o(f))==null?void 0:e.personType)),1)]}),_:1}),a(S,{label:"\u672C\u6708\u4E3B\u8981\u5DE5\u4F5C\u6210\u679C\u53CA\u81EA\u8BC4"},{default:l(()=>[a(ae,{type:"textarea",rows:5,modelValue:o(f).selfEvaluation,"onUpdate:modelValue":t[2]||(t[2]=e=>o(f).selfEvaluation=e),disabled:!o(L)(["report:information:self-assessment"]),autosize:""},null,8,["modelValue","disabled"])]),_:1}),a(S,{label:"\u9886\u5BFC\u8BC4\u4EF7"},{default:l(()=>[a(ae,{type:"textarea",rows:5,modelValue:o(f).leaderEvaluation,"onUpdate:modelValue":t[3]||(t[3]=e=>o(f).leaderEvaluation=e),disabled:!o(L)(["report:information:leader-assessment"]),autosize:""},null,8,["modelValue","disabled"])]),_:1}),a(S,{label:"\u5206\u6570"},{default:l(()=>[a(Y,{min:0,max:40,modelValue:o(f).leaderScore,"onUpdate:modelValue":t[4]||(t[4]=e=>o(f).leaderScore=e),class:"!w-100%",disabled:!o(L)(["report:information:leader-assessment"])},null,8,["modelValue","disabled"])]),_:1}),a(S,{label:"\u9644\u4EF6"},{default:l(()=>[o(L)(["report:information:save"])?(_(),E(pe,{key:0,modelValue:o(f).attachmentIds,"onUpdate:modelValue":t[5]||(t[5]=e=>o(f).attachmentIds=e),"show-file-list":!1,"is-show-tip":!1,plain:"","result-type":"id",onSuccess:z},null,8,["modelValue"])):R("",!0),(_(!0),C(Ye,null,Ie(o(f).attachments,e=>(_(),C("div",{key:e.id,class:"flex justify-between w-full"},[a(v,{type:"primary",link:"",onClick:b=>o($e)({url:e.url,target:"_blank",fileName:e.name})},{default:l(()=>[r(s(e.name),1)]),_:2},1032,["onClick"]),e.creator===o(B).id?(_(),E(v,{key:0,type:"danger",link:"",onClick:b=>(async x=>{f.value.attachmentIds=[...f.value.attachmentIds.filter(D=>D!==x)],z()})(e.id)},{default:l(()=>t[14]||(t[14]=[r("X")])),_:2},1032,["onClick"])):R("",!0)]))),128))]),_:1})]),_:1})]),_:1},8,["title","modelValue"])])}}}),[["__scopeId","data-v-179062a2"]]);export{Ge as default};
