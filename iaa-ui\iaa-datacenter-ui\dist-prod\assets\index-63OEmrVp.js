import{d as P,y as j,p as q,j as d,r as B,f as E,T as G,o as i,c as J,k as e,w as t,u as r,M as S,v as s,x as T,h as u,d$ as $,F as A,e0 as O,e1 as Q,S as W,B as X,C as Z,G as ee,H as le,I as ae,J as te,aa as re,K as oe,L as pe}from"./index-C8b06LRn.js";import{_ as ie}from"./index.vue_vue_type_script_setup_true_lang-IHUxVD_V.js";import{E as se}from"./el-image-Dy1AcCSg.js";import{_ as ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CcyWcH4-.js";import{_ as de}from"./index-CkzUfjB7.js";import{d as ue}from"./formatTime-COZ9Bl52.js";import{_ as me}from"./FileForm.vue_vue_type_script_setup_true_lang-6AfHHgAt.js";import"./index-Cl43piKd.js";import"./el-card-CaOo8U9P.js";import"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";const ce=P({name:"InfraFile",__name:"index",setup(fe){const _=j(),{t:U}=q(),y=d(!0),g=d(0),v=d([]),o=B({pageNo:1,pageSize:10,name:void 0,type:void 0,path:void 0,createTime:[]}),b=d(),n=async()=>{y.value=!0;try{const h=await O(o);v.value=h.list,g.value=h.total}finally{y.value=!1}},m=()=>{o.pageNo=1,n()},z=()=>{b.value.resetFields(),m()},x=d(),D=()=>{x.value.open()};return E(()=>{n()}),(h,l)=>{const F=de,k=W,c=X,K=Z,w=ee,f=le,N=ae,V=ne,p=te,Y=se,C=re,H=oe,M=ie,R=G("hasPermi"),I=pe;return i(),J(A,null,[e(F,{title:"\u4E0A\u4F20\u4E0B\u8F7D",url:"https://doc.iocoder.cn/file/"}),e(V,null,{default:t(()=>[e(N,{class:"-mb-15px",model:r(o),ref_key:"queryFormRef",ref:b,inline:!0,"label-width":"68px"},{default:t(()=>[e(c,{label:"\u6587\u4EF6\u8DEF\u5F84",prop:"path"},{default:t(()=>[e(k,{modelValue:r(o).path,"onUpdate:modelValue":l[0]||(l[0]=a=>r(o).path=a),placeholder:"\u8BF7\u8F93\u5165\u6587\u4EF6\u8DEF\u5F84",clearable:"",onKeyup:S(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(c,{label:"\u6587\u4EF6\u7C7B\u578B",prop:"type",width:"80"},{default:t(()=>[e(k,{modelValue:r(o).type,"onUpdate:modelValue":l[1]||(l[1]=a=>r(o).type=a),placeholder:"\u8BF7\u8F93\u5165\u6587\u4EF6\u7C7B\u578B",clearable:"",onKeyup:S(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(c,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(K,{modelValue:r(o).createTime,"onUpdate:modelValue":l[2]||(l[2]=a=>r(o).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(c,null,{default:t(()=>[e(f,{onClick:m},{default:t(()=>[e(w,{icon:"ep:search",class:"mr-5px"}),l[5]||(l[5]=s(" \u641C\u7D22"))]),_:1}),e(f,{onClick:z},{default:t(()=>[e(w,{icon:"ep:refresh",class:"mr-5px"}),l[6]||(l[6]=s(" \u91CD\u7F6E"))]),_:1}),e(f,{type:"primary",plain:"",onClick:D},{default:t(()=>[e(w,{icon:"ep:upload",class:"mr-5px"}),l[7]||(l[7]=s(" \u4E0A\u4F20\u6587\u4EF6 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(V,null,{default:t(()=>[T((i(),u(H,{data:r(v)},{default:t(()=>[e(p,{label:"\u6587\u4EF6\u540D",align:"center",prop:"name","show-overflow-tooltip":!0}),e(p,{label:"\u6587\u4EF6\u8DEF\u5F84",align:"center",prop:"path","show-overflow-tooltip":!0}),e(p,{label:"URL",align:"center",prop:"url","show-overflow-tooltip":!0}),e(p,{label:"\u6587\u4EF6\u5927\u5C0F",align:"center",prop:"size",width:"120",formatter:r($)},null,8,["formatter"]),e(p,{label:"\u6587\u4EF6\u7C7B\u578B",align:"center",prop:"type",width:"180px"}),e(p,{label:"\u6587\u4EF6\u5185\u5BB9",align:"center",prop:"url",width:"110px"},{default:t(({row:a})=>[a.type.includes("image")?(i(),u(Y,{key:0,class:"h-80px w-80px",lazy:"",src:a.url,"preview-src-list":[a.url],"preview-teleported":"",fit:"cover"},null,8,["src","preview-src-list"])):a.type.includes("pdf")?(i(),u(C,{key:1,type:"primary",href:a.url,underline:!1,target:"_blank"},{default:t(()=>l[8]||(l[8]=[s("\u9884\u89C8")])),_:2},1032,["href"])):(i(),u(C,{key:2,type:"primary",download:"",href:a.url,underline:!1,target:"_blank"},{default:t(()=>l[9]||(l[9]=[s("\u4E0B\u8F7D")])),_:2},1032,["href"]))]),_:1}),e(p,{label:"\u4E0A\u4F20\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:r(ue)},null,8,["formatter"]),e(p,{label:"\u64CD\u4F5C",align:"center"},{default:t(a=>[T((i(),u(f,{link:"",type:"danger",onClick:ye=>(async L=>{try{await _.delConfirm(),await Q(L),_.success(U("common.delSuccess")),await n()}catch{}})(a.row.id)},{default:t(()=>l[10]||(l[10]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[R,["infra:file:delete"]]])]),_:1})]),_:1},8,["data"])),[[I,r(y)]]),e(M,{total:r(g),page:r(o).pageNo,"onUpdate:page":l[3]||(l[3]=a=>r(o).pageNo=a),limit:r(o).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>r(o).pageSize=a),onPagination:n},null,8,["total","page","limit"])]),_:1}),e(me,{ref_key:"formRef",ref:x,onSuccess:n},null,512)],64)}}});export{ce as default};
