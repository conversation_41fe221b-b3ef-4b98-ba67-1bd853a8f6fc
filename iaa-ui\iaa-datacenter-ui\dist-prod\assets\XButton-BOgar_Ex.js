import{d as f,aj as n,Y as p,h as t,w as u,ar as I,u as k,H as m,o as a,$ as c,v as b,t as _,bi as g,G as y,_ as C}from"./index-C8b06LRn.js";const x=C(f({name:"XButton",__name:"XButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def(""),link:n.bool.def(!1),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(o){const i=o,r=p(()=>{const l=["title","preIcon","postIcon","onClick"],s={...g(),...i};for(const e in s)l.indexOf(e)!==-1&&delete s[e];return s});return(l,s)=>{const e=y,d=m;return a(),t(d,I(k(r),{onClick:o.onClick}),{default:u(()=>[o.preIcon?(a(),t(e,{key:0,icon:o.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),b(" "+_(o.title?o.title:"")+" ",1),o.postIcon?(a(),t(e,{key:1,icon:o.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-9ff26ce2"]]);export{x as _};
