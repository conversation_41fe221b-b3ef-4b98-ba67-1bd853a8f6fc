import{d as R,p as S,y as T,j as v,r as G,o as m,h as f,w as o,k as r,u as t,v as g,x as J,c as I,F as k,g as w,di as b,m as K,z as N,A as O,B as P,S as Q,aB as W,I as X,H as Z,L as ee}from"./index-C8b06LRn.js";import{_ as ae}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{D as h}from"./deptindicator-DmEoRemC.js";import{d as D}from"./dateUtil-D9m5ek6U.js";const le=R({name:"DeptIndicatorForm",__name:"DeptIndicatorForm",emits:["success"],setup(te,{expose:U,emit:x}){const{t:Y}=S(),q=T(),n=v(!1),_=v(""),c=v(!1),F=v(""),e=v({dept:void 0,year:void 0,month:void 0,indicators:void 0}),C=G({dept:[{required:!0,message:"\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],year:[{required:!0,message:"\u5E74\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],month:[{required:!0,message:"\u6708\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=v();U({open:async(d,a,u)=>{if(n.value=!0,_.value=Y("action."+d),F.value=d,z(),e.value.year=D(a).format("YYYY"),e.value.month=D(a).format("M"),u){c.value=!0;try{const s=await h.getDeptIndicator({dept:u,date:a});e.value.indicators=s.indicators.reduce((i,p)=>(i[p.indicator]=p.value,i),{}),e.value.dept=s.dept}finally{c.value=!1}}}});const $=x,j=async()=>{await y.value.validate(),c.value=!0;try{const d=e.value;await h.saveDeptIndicator(d),q.success("\u4FDD\u5B58\u6210\u529F"),n.value=!1,$("success")}finally{c.value=!1}},B=async()=>{var a;if(e.value.indicators={},!e.value.dept)return;const d=await h.getDeptIndicator({dept:e.value.dept,date:`${e.value.year}-${e.value.month<10?"0":""}${e.value.month}-01`});d!=null&&d.indicators?e.value.indicators=(a=d==null?void 0:d.indicators)==null?void 0:a.reduce((u,s)=>(u[s.indicator]=s.value,u),{}):b(e.value.dept+"_Indicator").forEach(u=>{e.value.indicators[u.value]=0})},z=()=>{var d;e.value={dept:void 0,year:void 0,month:void 0,indicators:void 0},(d=y.value)==null||d.resetFields()};return(d,a)=>{const u=N,s=O,i=P,p=Q,A=W,E=X,V=Z,H=ae,L=ee;return m(),f(H,{title:t(_),modelValue:t(n),"onUpdate:modelValue":a[4]||(a[4]=l=>K(n)?n.value=l:null)},{footer:o(()=>[r(V,{onClick:j,type:"primary",disabled:t(c)},{default:o(()=>a[5]||(a[5]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),r(V,{onClick:a[3]||(a[3]=l=>n.value=!1)},{default:o(()=>a[6]||(a[6]=[g("\u53D6 \u6D88")])),_:1})]),default:o(()=>[J((m(),f(E,{ref_key:"formRef",ref:y,model:t(e),rules:t(C),"label-width":"150px"},{default:o(()=>[r(i,{label:"\u90E8\u95E8",prop:"dept"},{default:o(()=>[r(s,{modelValue:t(e).dept,"onUpdate:modelValue":a[0]||(a[0]=l=>t(e).dept=l),placeholder:"\u8BF7\u9009\u62E9\u90E8\u95E8",onChange:B},{default:o(()=>[(m(!0),I(k,null,w(t(b)("first_level_department"),l=>(m(),f(u,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(i,{label:"\u5E74",prop:"year"},{default:o(()=>[r(p,{modelValue:t(e).year,"onUpdate:modelValue":a[1]||(a[1]=l=>t(e).year=l),placeholder:"\u8BF7\u8F93\u5165\u5E74",disabled:!0},null,8,["modelValue"])]),_:1}),r(i,{label:"\u6708",prop:"month"},{default:o(()=>[r(p,{modelValue:t(e).month,"onUpdate:modelValue":a[2]||(a[2]=l=>t(e).month=l),placeholder:"\u8BF7\u8F93\u5165\u6708",disabled:!0},null,8,["modelValue"])]),_:1}),(m(!0),I(k,null,w(t(b)(t(e).dept+"_Indicator"),l=>(m(),f(i,{key:l.value,label:l.label},{default:o(()=>[r(A,{modelValue:t(e).indicators[l.value],"onUpdate:modelValue":M=>t(e).indicators[l.value]=M,class:"!w-100%",placeholder:"\u8BF7\u8F93\u5165\u6307\u6807\u503C",disabled:t(e).dept=="IT"&&!["roi"].includes(l.value)&&!l.value.includes("plan")},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["label"]))),128))]),_:1},8,["model","rules"])),[[L,t(c)]])]),_:1},8,["title","modelValue"])}}});export{le as _};
