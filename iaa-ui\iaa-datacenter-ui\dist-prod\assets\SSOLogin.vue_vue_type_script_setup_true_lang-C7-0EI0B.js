import{aG as b,d as B,j as x,r as L,Y as E,au as G,x as H,Z as J,u as n,o as p,c as m,k as l,w as c,l as N,v as S,F as R,g as Y,h as Z,t as A,b4 as I,O as D,a as K,E as M,q as P,a9 as Q,b5 as W,B as X,H as $,I as ee}from"./index-C8b06LRn.js";import{u as se,_ as ae,L as T}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";const te={class:"form-cont"},oe={key:0},re={key:1},ne=B({name:"SSOLogin",__name:"SSOLogin",setup(le){const i=D(),{currentRoute:U}=K(),{getLoginState:C,setLoginState:V}=se(),k=x({name:"",logo:""}),a=L({responseType:"",clientId:"",redirectUri:"",state:"",scopes:[]}),z=E(()=>n(C)===T.SSO),u=L({scopes:[]}),d=x(!1),F=async()=>{if(i.query.client_id===void 0)return;if(a.responseType=i.query.response_type,a.clientId=i.query.client_id,a.redirectUri=i.query.redirect_uri,a.state=i.query.state,i.query.scope&&(a.scopes=i.query.scope.split(" ")),a.scopes.length>0){const s=await q(!0,a.scopes,[]);if(s)return void(location.href=s)}const t=await(e=a.clientId,b.get({url:"/system/oauth2/authorize?clientId="+e}));var e;let o;if(k.value=t.client,a.scopes.length>0){o=[];for(const s of t.scopes)a.scopes.indexOf(s.key)>=0&&o.push(s)}else{o=t.scopes;for(const s of o)a.scopes.push(s.key)}for(const s of o)s.value&&u.scopes.push(s.key)},O=async t=>{let e,o;t?(e=u.scopes,o=a.scopes.filter(s=>e.indexOf(s)===-1)):(e=[],o=a.scopes),d.value=!0;try{const s=await q(!1,e,o);if(!s)return;location.href=s}finally{d.value=!1}},q=(t,e,o)=>((s,_,v,f,y,g,r)=>{const h={};for(const w of g)h[w]=!0;for(const w of r)h[w]=!1;return b.post({url:"/system/oauth2/authorize",headers:{"Content-Type":"application/x-www-form-urlencoded"},params:{response_type:s,client_id:_,redirect_uri:v,state:f,auto_approve:y,scope:JSON.stringify(h)}})})(a.responseType,a.clientId,a.redirectUri,a.state,t,e,o),j=t=>{switch(t){case"user.read":return"\u8BBF\u95EE\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";case"user.write":return"\u4FEE\u6539\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";default:return t}};return G(()=>U.value,t=>{t.name==="SSOLogin"&&(V(T.SSO),F())},{immediate:!0}),(t,e)=>{const o=M,s=P,_=Q,v=W,f=X,y=$,g=ee;return H((p(),m("div",te,[l(ae,{style:{width:"100%"}}),l(s,{class:"form",style:{float:"none"},value:"uname"},{default:c(()=>[l(o,{label:n(k).name,name:"uname"},null,8,["label"])]),_:1}),N("div",null,[l(g,{model:n(u),class:"login-form"},{default:c(()=>[e[4]||(e[4]=S(" \u6B64\u7B2C\u4E09\u65B9\u5E94\u7528\u8BF7\u6C42\u83B7\u5F97\u4EE5\u4E0B\u6743\u9650\uFF1A ")),l(f,{prop:"scopes"},{default:c(()=>[l(v,{modelValue:n(u).scopes,"onUpdate:modelValue":e[0]||(e[0]=r=>n(u).scopes=r)},{default:c(()=>[(p(!0),m(R,null,Y(n(a).scopes,r=>(p(),Z(_,{key:r,value:r,style:{display:"block","margin-bottom":"-10px"}},{default:c(()=>[S(A(j(r)),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,{class:"w-1/1"},{default:c(()=>[l(y,{loading:n(d),class:"w-6/10",type:"primary",onClick:e[1]||(e[1]=I(r=>O(!0),["prevent"]))},{default:c(()=>[n(d)?(p(),m("span",re,"\u6388 \u6743 \u4E2D...")):(p(),m("span",oe,"\u540C\u610F\u6388\u6743"))]),_:1},8,["loading"]),l(y,{class:"w-3/10",onClick:e[2]||(e[2]=I(r=>O(!1),["prevent"]))},{default:c(()=>e[3]||(e[3]=[S("\u62D2\u7EDD")])),_:1})]),_:1})]),_:1},8,["model"])])],512)),[[J,n(z)]])}}});export{ne as _};
