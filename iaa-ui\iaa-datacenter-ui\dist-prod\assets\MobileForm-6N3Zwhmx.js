import{d as Z,b as $,j as g,Y as A,r as N,au as D,x as G,Z as H,u as e,o as f,h as w,w as l,k as a,$ as h,c as S,t as C,p as J,y as K,a as Q,aX as W,ac as ee,ad as ae,a5 as V,aY as le,a2 as oe,B as te,a8 as ne,S as ie,ab as se,I as de,_ as re}from"./index-C8b06LRn.js";import{_ as me}from"./XButton-BOgar_Ex.js";import{u as y}from"./useIcon-CwemBubV.js";import{u as pe,_ as ue,L as ce,a as ge}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DCWGfriT.js";import{r as _}from"./formRules-Upspu04w.js";const fe={key:1,class:"getMobileCode",style:{cursor:"pointer"}},be=re(Z({name:"MobileForm",__name:"MobileForm",setup(he){const{t:i}=J(),L=K(),I=$(),{currentRoute:M,push:E}=Q(),x=g(),u=g(!1),B=y({icon:"ep:house"}),P=y({icon:"ep:cellphone"}),U=y({icon:"ep:circle-check"}),{validForm:j}=ge(x),{handleBackLogin:R,getLoginState:X}=pe(),Y=A(()=>e(X)===ce.MOBILE),q={tenantName:[_],mobileNumber:[_],code:[_]},o=N({codeImg:"",tenantEnable:"false",token:"",loading:{signIn:!1},loginForm:{uuid:"",tenantName:"\u828B\u9053\u6E90\u7801",mobileNumber:"",code:""}}),p=N({smsCode:{mobile:"",scene:21},loginSms:{mobile:"",code:""}}),s=g(0),c=g(""),z=async()=>{await v(),p.smsCode.mobile=o.loginForm.mobileNumber,await W(p.smsCode).then(async()=>{L.success(i("login.SmsSendMsg")),s.value=60;let n=setInterval(()=>{s.value=s.value-1,s.value<=0&&clearInterval(n)},1e3)})};D(()=>M.value,n=>{var t;c.value=(t=n==null?void 0:n.query)==null?void 0:t.redirect},{immediate:!0});const v=async()=>{if(o.tenantEnable==="true"){const n=await ee(o.loginForm.tenantName);ae(n)}};return(n,t)=>{const m=te,d=ne,b=ie,k=se,F=me,O=de;return G((f(),w(O,{ref_key:"formSmsLogin",ref:x,model:e(o).loginForm,rules:q,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[a(k,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(ue,{style:{width:"100%"}})]),_:1})]),_:1}),a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[e(o).tenantEnable==="true"?(f(),w(m,{key:0,prop:"tenantName"},{default:l(()=>[a(b,{modelValue:e(o).loginForm.tenantName,"onUpdate:modelValue":t[0]||(t[0]=r=>e(o).loginForm.tenantName=r),placeholder:e(i)("login.tenantNamePlaceholder"),"prefix-icon":e(B),type:"primary",link:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):h("",!0)]),_:1}),a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"mobileNumber"},{default:l(()=>[a(b,{modelValue:e(o).loginForm.mobileNumber,"onUpdate:modelValue":t[1]||(t[1]=r=>e(o).loginForm.mobileNumber=r),placeholder:e(i)("login.mobileNumberPlaceholder"),"prefix-icon":e(P)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"code"},{default:l(()=>[a(k,{gutter:5,justify:"space-between",style:{width:"100%"}},{default:l(()=>[a(d,{span:24},{default:l(()=>[a(b,{modelValue:e(o).loginForm.code,"onUpdate:modelValue":t[2]||(t[2]=r=>e(o).loginForm.code=r),placeholder:e(i)("login.codePlaceholder"),"prefix-icon":e(U)},{append:l(()=>[e(s)<=0?(f(),S("span",{key:0,class:"getMobileCode",style:{cursor:"pointer"},onClick:z},C(e(i)("login.getSmsCode")),1)):h("",!0),e(s)>0?(f(),S("span",fe,C(e(s))+"\u79D2\u540E\u53EF\u91CD\u65B0\u83B7\u53D6 ",1)):h("",!0)]),_:1},8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1})]),_:1})]),_:1}),a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(F,{loading:e(u),title:e(i)("login.login"),class:"w-[100%]",type:"primary",onClick:t[3]||(t[3]=r=>(async()=>{await v(),await j()&&(V.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),u.value=!0,p.loginSms.mobile=o.loginForm.mobileNumber,p.loginSms.code=o.loginForm.code,await le(p.loginSms).then(async T=>{oe(T),c.value||(c.value="/"),E({path:c.value||I.addRouters[0].path})}).catch(()=>{}).finally(()=>{u.value=!1,setTimeout(()=>{V.service().close()},400)}))})())},null,8,["loading","title"])]),_:1})]),_:1}),a(d,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(F,{loading:e(u),title:e(i)("login.backLogin"),class:"w-[100%]",onClick:t[4]||(t[4]=r=>e(R)())},null,8,["loading","title"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[H,e(Y)]])}}}),[["__scopeId","data-v-a3cd5b9c"]]);export{be as default};
