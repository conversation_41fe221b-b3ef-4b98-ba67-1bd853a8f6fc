import{d as o,aj as u,j as l,f as c,au as f,x as i,u as p,o as v,c as d,l as m,aO as h,L as g}from"./index-C8b06LRn.js";const w={class:"w-full h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-2px)]"},_=["src"],x=o({name:"IFrame",__name:"IFrame",props:{src:u.string.def("")},setup(t){const r=t,a=l(!0),e=l(null),s=()=>{h(()=>{a.value=!0,e.value&&(e.value.onload=()=>{a.value=!1})})};return c(()=>{s()}),f(()=>r.src,()=>{s()}),(b,j)=>{const n=g;return i((v(),d("div",w,[m("iframe",{ref_key:"frameRef",ref:e,src:r.src,frameborder:"0",scrolling:"auto",height:"100%",width:"100%",allowfullscreen:"true",webkitallowfullscreen:"true",mozallowfullscreen:"true"},null,8,_)])),[[n,p(a)]])}}});export{x as _};
