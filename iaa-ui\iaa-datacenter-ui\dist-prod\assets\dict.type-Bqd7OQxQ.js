import{aG as e}from"./index-C8b06LRn.js";const s=()=>e.get({url:"/system/dict-type/list-all-simple"}),a=t=>e.get({url:"/system/dict-type/page",params:t}),d=t=>e.get({url:"/system/dict-type/get?id="+t}),p=t=>e.post({url:"/system/dict-type/create",data:t}),l=t=>e.put({url:"/system/dict-type/update",data:t}),r=t=>e.delete({url:"/system/dict-type/delete?id="+t}),y=t=>e.download({url:"/system/dict-type/export",params:t});export{d as a,a as b,p as c,r as d,y as e,s as g,l as u};
