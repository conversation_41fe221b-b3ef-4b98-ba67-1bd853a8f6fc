import{d as z,p as B,y as H,j as u,r as J,o as f,h,w as o,k as s,u as a,v as n,x as K,t as C,c as L,F as Y,g as $,N as G,D as Q,m as y,aA as W,$ as X,aJ as Z,aI as ee,aO as ae,el as le,aM as te,B as de,z as oe,A as se,I as ue,aC as ce,cq as ne,H as pe,L as ie}from"./index-C8b06LRn.js";import{_ as me}from"./Dialog.vue_vue_type_style_index_0_lang-DCo5_Oc1.js";import{E as re}from"./el-card-CaOo8U9P.js";import{d as w}from"./constants-D3f7Z3TX.js";const ve=z({name:"SystemRoleDataPermissionForm",__name:"RoleDataPermissionForm",emits:["success"],setup(fe,{expose:D,emit:I}){const{t:U}=B(),E=H(),p=u(!1),_=u(!1),d=J({id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]}),V=u(),x=u([]),i=u(!0),m=u(),r=u(!1),v=u(!0);D({open:async l=>{var e;p.value=!0,O(),x.value=Z(await ee()),d.id=l.id,d.name=l.name,d.code=l.code,d.dataScope=l.dataScope,await ae(),(e=l.dataScopeDeptIds)==null||e.forEach(c=>{m.value.setChecked(c,!0,!1)})}});const T=I,M=async()=>{_.value=!0;try{const l={roleId:d.id,dataScope:d.dataScope,dataScopeDeptIds:d.dataScope!==w.DEPT_CUSTOM?[]:m.value.getCheckedKeys(!1)};await le(l),E.success(U("common.updateSuccess")),p.value=!1,T("success")}finally{_.value=!1}},O=()=>{var l,e;r.value=!1,i.value=!0,v.value=!0,d.value={id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]},(l=m.value)==null||l.setCheckedNodes([]),(e=V.value)==null||e.resetFields()},P=()=>{var e;const l=(e=m.value)==null?void 0:e.store.nodesMap;for(let c in l)l[c].expanded!==i.value&&(l[c].expanded=i.value)};return(l,e)=>{const c=te,S=de,g=oe,A=se,F=ue,k=ce,N=ne,R=re,b=pe,q=me,j=ie;return f(),h(q,{modelValue:a(p),"onUpdate:modelValue":e[6]||(e[6]=t=>y(p)?p.value=t:null),title:"\u6570\u636E\u6743\u9650",width:"800"},{footer:o(()=>[s(b,{disabled:a(_),type:"primary",onClick:M},{default:o(()=>e[10]||(e[10]=[n("\u786E \u5B9A")])),_:1},8,["disabled"]),s(b,{onClick:e[5]||(e[5]=t=>p.value=!1)},{default:o(()=>e[11]||(e[11]=[n("\u53D6 \u6D88")])),_:1})]),default:o(()=>[K((f(),h(F,{ref_key:"formRef",ref:V,model:a(d),"label-width":"80px"},{default:o(()=>[s(S,{label:"\u89D2\u8272\u540D\u79F0"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).name),1)]),_:1})]),_:1}),s(S,{label:"\u89D2\u8272\u6807\u8BC6"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).code),1)]),_:1})]),_:1}),s(S,{label:"\u6743\u9650\u8303\u56F4"},{default:o(()=>[s(A,{modelValue:a(d).dataScope,"onUpdate:modelValue":e[0]||(e[0]=t=>a(d).dataScope=t)},{default:o(()=>[(f(!0),L(Y,null,$(a(G)(a(Q).SYSTEM_DATA_SCOPE),t=>(f(),h(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[j,a(_)]]),a(d).dataScope===a(w).DEPT_CUSTOM?(f(),h(S,{key:0,label:"\u6743\u9650\u8303\u56F4","label-width":"80px"},{default:o(()=>[s(R,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:o(()=>[e[7]||(e[7]=n(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(k,{modelValue:a(r),"onUpdate:modelValue":e[1]||(e[1]=t=>y(r)?r.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:e[2]||(e[2]=t=>{m.value.setCheckedNodes(r.value?x.value:[])})},null,8,["modelValue"]),e[8]||(e[8]=n(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(k,{modelValue:a(i),"onUpdate:modelValue":e[3]||(e[3]=t=>y(i)?i.value=t:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:P},null,8,["modelValue"]),e[9]||(e[9]=n(" \u7236\u5B50\u8054\u52A8(\u9009\u4E2D\u7236\u8282\u70B9\uFF0C\u81EA\u52A8\u9009\u62E9\u5B50\u8282\u70B9): ")),s(k,{modelValue:a(v),"onUpdate:modelValue":e[4]||(e[4]=t=>y(v)?v.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue"])]),default:o(()=>[s(N,{ref_key:"treeRef",ref:m,"check-strictly":!a(v),data:a(x),props:a(W),"default-expand-all":"","empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E","node-key":"id","show-checkbox":""},null,8,["check-strictly","data","props"])]),_:1})]),_:1})):X("",!0)]),_:1},8,["modelValue"])}}});export{ve as _};
